Files in dukancard-app project with direct Supabase calls (supabase.) - excluding service files:
Total files found: 81

app\(dashboard)\customer\index.tsx
  Line 48: const { data: subscriptions } = await supabase
  Line 49: .from('subscriptions')
  Line 76: supabase
  Line 77: .from('customer_profiles')
  Line 81: supabase
  Line 82: .from('business_profiles')

app\(onboarding)\_layout.tsx
  Line 19: await supabase.auth.signOut();

app\product\[productId].tsx
  Line 126: const { data, error } = await supabase
  Line 127: .from("products_services")
  Line 184: const { data, error } = await supabase
  Line 185: .from("business_profiles")
  Line 220: const { data: subscriptionData, error: subscriptionError } = await supabase
  Line 221: .from("payment_subscriptions")
  Line 259: const { data, error } = await supabase
  Line 260: .from("products_services")
  Line 313: const { data: validBusinesses, error: businessError } = await supabase
  Line 314: .from("business_profiles")
  Line 326: const { data, error } = await supabase
  Line 327: .from("products_services")

backend\supabase\services\activities\activityService.ts
  Line 109: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 173: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 189: let query = supabase
  Line 190: .from('business_activities')
  Line 322: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 333: let query = supabase
  Line 334: .from('business_activities')
  Line 361: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 372: const { count, error } = await supabase
  Line 373: .from('business_activities')

backend\supabase\services\ads\adService.ts
  Line 12: const { count, error: tableCheckError } = await supabase
  Line 13: .from('custom_ads')
  Line 21: const { data: adData, error: adError } = await supabase.rpc(
  Line 56: const { data: customAd } = await supabase
  Line 57: .from('custom_ads')
  Line 76: const { data: globalAd } = await supabase
  Line 77: .from('custom_ads')

backend\supabase\services\auth\authService.ts
  Line 6: const result = await supabase.auth.getUser();
  Line 16: await supabase.auth.signOut();
  Line 31: await supabase.auth.signOut();
  Line 42: return await supabase
  Line 43: .from('customer_profiles')
  Line 50: const { data, error } = await supabase
  Line 51: .from('customer_profiles')
  Line 59: const { data, error } = await supabase
  Line 60: .from('business_profiles')
  Line 68: return await supabase
  Line 69: .from('customer_profiles')
  Line 76: return await supabase.auth.signOut();
  Line 80: return await supabase.auth.signInWithPassword({

backend\supabase\services\auth\emailOtpService.ts
  Line 14: const { error } = await supabase.auth.signInWithOtp({
  Line 62: const { error } = await supabase.auth.verifyOtp({

backend\supabase\services\auth\googleAuthService.ts
  Line 21: const { data, error } = await supabase.auth.signInWithOAuth({
  Line 74: const { error: sessionError } = await supabase.auth.setSession({
  Line 121: const { error } = await supabase.auth.signInWithOAuth({

backend\supabase\services\auth\mobileAuthService.ts
  Line 6: return await supabase.auth.signInWithPassword({

backend\supabase\services\auth\nativeGoogleAuth2025.ts
  Line 147: const { data, error } = await supabase.auth.signInWithIdToken({

backend\supabase\services\business\businessAnalyticsService.ts
  Line 23: const { data, error } = await supabase
  Line 24: .from("business_profiles")
  Line 33: const { count: total_products, error: productsError } = await supabase
  Line 34: .from("products_services")
  Line 49: await supabase.rpc("get_monthly_unique_visits", {
  Line 79: const { data: profile, error: profileError } = await supabase
  Line 80: .from("business_profiles")
  Line 110: const { data: trend7Result, error: trend7Error } = await supabase.rpc(
  Line 124: const { data: trend30Result, error: trend30Error } = await supabase.rpc(
  Line 139: await supabase.rpc("get_hourly_unique_visit_trend", {
  Line 151: await supabase.rpc("get_monthly_unique_visits", {
  Line 162: await supabase.rpc("get_monthly_unique_visits", {
  Line 178: await supabase.rpc("get_available_years_for_monthly_metrics", {
  Line 196: await supabase.rpc("get_monthly_unique_visit_trend", {
  Line 226: const { data: totalCount, error: totalError } = await supabase.rpc(

backend\supabase\services\business\businessCardDataService.ts
  Line 23: const { data, error } = await supabase
  Line 24: .from("products_services")
  Line 56: const { data: reviewsData, error } = await supabase
  Line 57: .from("ratings_reviews")
  Line 87: supabase
  Line 88: .from("customer_profiles")
  Line 91: supabase
  Line 92: .from("business_profiles")
  Line 163: const { data: businessData, error: businessError } = await supabase
  Line 164: .from("business_profiles")
  Line 179: const { data: subscriptionData, error: subscriptionError } = await supabase
  Line 180: .from("payment_subscriptions")
  Line 236: const { data, error } = await supabase
  Line 237: .from("ratings_reviews")
  Line 320: let countQuery = supabase
  Line 321: .from("products_services")
  Line 347: let dataQuery = supabase
  Line 348: .from("products_services")
  Line 427: const { count: totalCount, error: countError } = await supabase
  Line 428: .from("ratings_reviews")
  Line 448: let reviewsQuery = supabase
  Line 449: .from("ratings_reviews")
  Line 498: supabase
  Line 499: .from("customer_profiles")
  Line 502: supabase
  Line 503: .from("business_profiles")

backend\supabase\services\business\businessCardService.ts
  Line 18: } = await supabase.auth.getUser();
  Line 25: const { data, error } = await supabase
  Line 26: .from("business_profiles")
  Line 56: const { error: updateError } = await supabase
  Line 57: .from("business_profiles")
  Line 100: } = await supabase.auth.getUser();
  Line 108: const { data: existingProfile, error: profileError } = await supabase
  Line 109: .from("business_profiles")
  Line 174: const { data: updatedProfile, error: updateError } = await supabase
  Line 175: .from("business_profiles")
  Line 211: const { error: authUpdateError } = await supabase.auth.updateUser({
  Line 253: } = await supabase.auth.getUser();
  Line 260: const { data: updatedProfile, error: updateError } = await supabase
  Line 261: .from("business_profiles")
  Line 304: } = await supabase.auth.getUser();
  Line 311: const { data: existingProfile, error: profileError } = await supabase
  Line 312: .from("business_profiles")
  Line 322: const { data: updatedProfile, error: updateError } = await supabase
  Line 323: .from("business_profiles")
  Line 347: const { error: authUpdateError } = await supabase.auth.updateUser({
  Line 376: } = await supabase.auth.getUser();
  Line 383: const { data: updatedProfile, error: updateError } = await supabase
  Line 384: .from("business_profiles")
  Line 415: } = await supabase.auth.getUser();
  Line 422: const { data: updatedProfile, error: updateError } = await supabase
  Line 423: .from("business_profiles")
  Line 453: } = await supabase.auth.getUser();
  Line 460: const { data: updatedProfile, error: updateError } = await supabase
  Line 461: .from("business_profiles")
  Line 490: } = await supabase.auth.getUser();
  Line 501: const { data: currentProfile, error: profileError } = await supabase
  Line 502: .from("business_profiles")
  Line 534: const { data: updatedProfile, error: updateError } = await supabase
  Line 535: .from("business_profiles")
  Line 576: } = await supabase.auth.getUser();
  Line 582: const { data, error } = await supabase
  Line 583: .from("business_profiles")
  Line 619: } = await supabase.auth.getUser();
  Line 625: const { data, error } = await supabase
  Line 626: .from("business_profiles")
  Line 654: } = await supabase.auth.getUser();
  Line 660: const { data, error } = await supabase
  Line 661: .from("business_profiles")
  Line 690: } = await supabase.auth.getUser();
  Line 696: const { data, error } = await supabase
  Line 697: .from("business_profiles")
  Line 725: } = await supabase.auth.getUser();
  Line 731: const { data, error } = await supabase
  Line 732: .from("business_profiles")
  Line 759: } = await supabase.auth.getUser();
  Line 765: const { data, error } = await supabase
  Line 766: .from("business_profiles")
  Line 794: } = await supabase.auth.getUser();
  Line 800: const { data, error } = await supabase
  Line 801: .from("business_profiles")

backend\supabase\services\business\businessDiscovery.ts
  Line 33: const { data: businessProfile, error } = await supabase
  Line 34: .from("business_profiles")
  Line 103: const { data: subscriptionData, error: subscriptionError } = await supabase
  Line 104: .from("public_subscription_status")
  Line 142: const { data, error } = await supabase
  Line 143: .from("business_profiles")
  Line 174: const { data, error } = await supabase
  Line 175: .from("business_profiles")

backend\supabase\services\business\businessInteractions.ts
  Line 32: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 37: supabase
  Line 38: .from('likes')
  Line 41: supabase
  Line 42: .from('subscriptions')
  Line 45: supabase
  Line 46: .from('ratings_reviews')
  Line 72: supabase
  Line 73: .from('likes')
  Line 78: supabase
  Line 79: .from('subscriptions')
  Line 84: supabase
  Line 85: .from('ratings_reviews')
  Line 90: supabase
  Line 91: .from('likes')
  Line 94: supabase
  Line 95: .from('subscriptions')
  Line 98: supabase
  Line 99: .from('ratings_reviews')
  Line 137: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 148: const { data: existingLike, error: checkError } = await supabase
  Line 149: .from('likes')
  Line 161: const { error: deleteError } = await supabase
  Line 162: .from('likes')
  Line 174: const { error: insertError } = await supabase
  Line 175: .from('likes')
  Line 205: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 216: const { data: existingSubscription, error: checkError } = await supabase
  Line 217: .from('subscriptions')
  Line 229: const { error: deleteError } = await supabase
  Line 230: .from('subscriptions')
  Line 242: const { error: insertError } = await supabase
  Line 243: .from('subscriptions')
  Line 271: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 277: const { data: business, error } = await supabase
  Line 278: .from('business_profiles')
  Line 300: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 311: const { data: existingReview, error: checkError } = await supabase
  Line 312: .from('ratings_reviews')
  Line 324: const { error: updateError } = await supabase
  Line 325: .from('ratings_reviews')
  Line 341: const { error: insertError } = await supabase
  Line 342: .from('ratings_reviews')

backend\supabase\services\business\businessOnboardingService.ts
  Line 81: const { data: result, error } = await supabase.rpc('create_business_profile_atomic', {
  Line 111: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 137: const { data: existingBusiness, error: slugCheckError } = await supabase
  Line 138: .from('business_profiles')
  Line 224: const { data: existingBusiness, error } = await supabase
  Line 225: .from('business_profiles')
  Line 247: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 253: const { data: profile, error } = await supabase
  Line 254: .from('business_profiles')
  Line 317: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 346: const { data: profile, error } = await supabase
  Line 347: .from('business_profiles')

backend\supabase\services\business\businessPostService.ts
  Line 9: return await supabase
  Line 10: .from('business_profiles')
  Line 20: return await supabase
  Line 21: .from('business_posts')
  Line 31: return await supabase
  Line 32: .from('business_posts')
  Line 43: return await supabase
  Line 44: .from('business_posts')
  Line 55: return await supabase
  Line 56: .from('business_posts')
  Line 65: return await supabase
  Line 66: .from('business_posts')
  Line 89: return await supabase
  Line 90: .from('business_posts')

backend\supabase\services\business\businessProductsService.ts
  Line 63: } = await supabase.auth.getUser();
  Line 69: let query = supabase
  Line 70: .from("products_services")
  Line 156: } = await supabase.auth.getUser();
  Line 162: let query = supabase
  Line 163: .from("products_services")
  Line 239: } = await supabase.auth.getUser();
  Line 257: const { data: insertedProduct, error: insertError } = await supabase
  Line 258: .from("products_services")
  Line 291: const { error: uploadError } = await supabase.storage
  Line 302: const { data: urlData } = supabase.storage
  Line 309: const { data: updatedProduct, error: updateError } = await supabase
  Line 310: .from("products_services")
  Line 365: } = await supabase.auth.getUser();
  Line 393: const { error: uploadError } = await supabase.storage
  Line 404: const { data: urlData } = supabase.storage
  Line 423: const { data: updatedProduct, error: updateError } = await supabase
  Line 424: .from("products_services")
  Line 478: } = await supabase.auth.getUser();
  Line 484: const { data: product, error: fetchError } = await supabase
  Line 485: .from("products_services")
  Line 505: const { error: deleteImagesError } = await supabase.storage
  Line 515: const { error: deleteError } = await supabase
  Line 516: .from("products_services")

backend\supabase\services\business\businessProfileService.ts
  Line 36: } = await supabase.auth.getUser();
  Line 42: const { data: profile, error } = await supabase
  Line 43: .from("business_profiles")
  Line 97: } = await supabase.auth.getUser();
  Line 134: const { data: result, error: rpcError } = await supabase.rpc('create_business_profile_atomic', {
  Line 174: } = await supabase.auth.getUser();
  Line 181: const { data: profile, error } = await supabase
  Line 182: .from("business_profiles")
  Line 214: const { data: profile, error } = await supabase
  Line 215: .from("business_profiles")
  Line 247: const { data: profiles, error } = await supabase
  Line 248: .from("business_profiles")
  Line 274: const { data: existing, error } = await supabase
  Line 275: .from("business_profiles")
  Line 304: } = await supabase.auth.getUser();
  Line 311: const { error } = await supabase
  Line 312: .from("business_profiles")

backend\supabase\services\business\businessSocialService.ts
  Line 60: const { count: totalCount, error: countError } = await supabase
  Line 61: .from("likes")
  Line 80: const { data: likes, error: likesError } = await supabase
  Line 81: .from("likes")
  Line 104: supabase
  Line 105: .from("customer_profiles")
  Line 108: supabase
  Line 109: .from("business_profiles")
  Line 173: let query = supabase
  Line 174: .from("likes")
  Line 198: let countQuery = supabase
  Line 199: .from("likes")
  Line 333: const { count: totalCount, error: countError } = await supabase
  Line 334: .from("subscriptions")
  Line 352: const { data: subscriptions, error: subsError } = await supabase
  Line 353: .from("subscriptions")
  Line 377: supabase
  Line 378: .from("customer_profiles")
  Line 381: supabase
  Line 382: .from("business_profiles")
  Line 475: let query = supabase
  Line 476: .from("subscriptions")

backend\supabase\services\business\planLimitService.ts
  Line 27: } = await supabase.auth.getUser();
  Line 34: const { data: subscriptionData, error: subscriptionError } = await supabase
  Line 35: .from("payment_subscriptions")
  Line 85: const { count: availableCount, error: availableCountError } = await supabase
  Line 86: .from("products_services")
  Line 96: const { count: totalCount, error: totalCountError } = await supabase
  Line 97: .from("products_services")
  Line 159: } = await supabase.auth.getUser();
  Line 165: const { data: product, error: productError } = await supabase
  Line 166: .from("products_services")

backend\supabase\services\business\utils\slugUtils.ts
  Line 17: const { data, error } = await supabase
  Line 18: .from("business_profiles")
  Line 37: const { data, error } = await supabase
  Line 38: .from("business_profiles")

backend\supabase\services\business\utils\subscriptionChecker.ts
  Line 7: const { data, error } = await supabase
  Line 8: .from("business_profiles")

backend\supabase\services\business\variantService.ts
  Line 24: } = await supabase.auth.getUser();
  Line 30: const { data: product, error: productError } = await supabase
  Line 31: .from("products_services")
  Line 53: const { data: insertedVariant, error: insertError } = await supabase
  Line 54: .from("product_variants")
  Line 79: const { error: uploadError } = await supabase.storage
  Line 91: const { data: urlData } = supabase.storage
  Line 109: const { data: updatedVariant, error: updateError } = await supabase
  Line 110: .from("product_variants")
  Line 145: } = await supabase.auth.getUser();
  Line 151: const { data: variant, error: variantError } = await supabase
  Line 152: .from("product_variants")
  Line 193: const { error: uploadError } = await supabase.storage
  Line 205: const { data: urlData } = supabase.storage
  Line 224: const { data: updatedVariant, error: updateError } = await supabase
  Line 225: .from("product_variants")
  Line 252: } = await supabase.auth.getUser();
  Line 258: const { data: variant, error: variantError } = await supabase
  Line 259: .from("product_variants")
  Line 286: const { error: deleteError } = await supabase.storage
  Line 298: const { error: deleteError } = await supabase
  Line 299: .from("product_variants")
  Line 324: } = await supabase.auth.getUser();
  Line 330: const { data: product, error: productError } = await supabase
  Line 331: .from("products_services")
  Line 342: const { data: variants, error: variantsError } = await supabase
  Line 343: .from("product_variants")

backend\supabase\services\common\metricsService.ts
  Line 27: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 39: supabase
  Line 40: .from('ratings_reviews')
  Line 45: supabase
  Line 46: .from('subscriptions')
  Line 51: supabase
  Line 52: .from('likes')
  Line 105: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 111: const { count, error } = await supabase
  Line 112: .from(table)

backend\supabase\services\common\onboardingService.ts
  Line 112: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 118: const { data: existingProfile, error } = await supabase
  Line 119: .from('business_profiles')
  Line 144: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 166: const { data: existingProfile } = await supabase
  Line 167: .from('business_profiles')
  Line 238: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 243: const { data: profile, error } = await supabase
  Line 244: .from('business_profiles')

backend\supabase\services\common\profileCheckService.ts
  Line 34: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 43: const { data: profile, error } = await supabase
  Line 44: .from(tableName)
  Line 88: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 96: supabase
  Line 97: .from('customer_profiles')
  Line 101: supabase
  Line 102: .from('business_profiles')
  Line 148: const { data: profile, error } = await supabase
  Line 149: .from(tableName)
  Line 187: const { data: profiles, error } = await supabase
  Line 188: .from(tableName)

backend\supabase\services\common\profileService.ts
  Line 48: } = await supabase.auth.getUser();
  Line 56: const { error: authUpdateError } = await supabase.auth.updateUser({
  Line 78: const { data: result, error } = await supabase
  Line 79: .from("customer_profiles")
  Line 111: } = await supabase.auth.getUser();
  Line 126: const { data: result, error } = await supabase
  Line 127: .from("customer_profiles")
  Line 161: } = await supabase.auth.getUser();
  Line 168: const { data: profile, error } = await supabase
  Line 169: .from("customer_profiles")
  Line 198: } = await supabase.auth.getUser();
  Line 205: const { data: profile, error } = await supabase
  Line 206: .from("business_profiles")
  Line 237: } = await supabase.auth.getUser();
  Line 243: const { data: result, error } = await supabase
  Line 244: .from("customer_profiles")
  Line 273: const { data: pincodeData, error } = await supabase
  Line 274: .from("pincodes")
  Line 313: const { data: pincodeData, error } = await supabase
  Line 314: .from("pincodes")
  Line 354: } = await supabase.auth.getUser();
  Line 360: const { data: profile, error } = await supabase
  Line 361: .from("customer_profiles")

backend\supabase\services\common\settingsService.ts
  Line 21: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 34: const { data: existingUser, error: checkError } = await supabase
  Line 35: .from('auth.users')
  Line 53: const { error: otpError } = await supabase.auth.signInWithOtp({
  Line 73: const { error: updateError } = await supabase.auth.updateUser({
  Line 94: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 101: const { error: verifyError } = await supabase.auth.verifyOtp({
  Line 113: const { error: updateError } = await supabase.auth.updateUser({
  Line 134: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 146: const { data: existingUser, error: checkError } = await supabase
  Line 147: .from('auth.users')
  Line 163: const { error: otpError } = await supabase.auth.signInWithOtp({
  Line 191: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 198: const { error: verifyError } = await supabase.auth.verifyOtp({
  Line 210: const { error: updateError } = await supabase.auth.updateUser({
  Line 231: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 264: const { error: signInError } = await supabase.auth.signInWithPassword({
  Line 275: const { error: updateError } = await supabase.auth.updateUser({
  Line 296: const { error } = await supabase.auth.resetPasswordForEmail(email, {

backend\supabase\services\common\userRoleStatusService.ts
  Line 42: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 50: supabase
  Line 51: .from('customer_profiles')
  Line 55: supabase
  Line 56: .from('business_profiles')

backend\supabase\services\customer\batchProfileService.ts
  Line 72: const { data: customerProfiles, error: customerError } = await supabase
  Line 73: .from('customer_profiles_public')
  Line 87: const { data: businessProfiles, error: businessError } = await supabase
  Line 88: .from('business_profiles')
  Line 118: const { data: profiles, error } = await supabase
  Line 119: .from('customer_profiles_public')
  Line 146: const { data: profiles, error } = await supabase
  Line 147: .from('business_profiles')

backend\supabase\services\customer\customerPostService.ts
  Line 5: return await supabase
  Line 6: .from('customer_profiles_public')
  Line 13: return await supabase
  Line 14: .from('customer_posts')
  Line 21: return await supabase
  Line 22: .from('customer_posts')
  Line 30: return await supabase
  Line 31: .from('customer_posts')
  Line 39: return await supabase
  Line 40: .from('customer_posts')
  Line 46: return await supabase
  Line 47: .from('customer_posts')

backend\supabase\services\customer\customerProfileService.ts
  Line 18: return await supabase.auth.updateUser({
  Line 31: return await supabase
  Line 32: .from(TABLES.CUSTOMER_PROFILES)
  Line 46: const { error: updateError } = await supabase
  Line 47: .from(TABLES.CUSTOMER_PROFILES)
  Line 56: const { error: authUpdateError } = await supabase.auth.updateUser({
  Line 64: return await supabase.auth.updateUser({
  Line 71: return await supabase
  Line 72: .from(TABLES.CUSTOMER_PROFILES)
  Line 80: const { data, error } = await supabase
  Line 81: .from(TABLES.CUSTOMER_PROFILES)
  Line 102: const { data, error } = await supabase
  Line 103: .from(TABLES.CUSTOMER_PROFILES)
  Line 115: return await supabase.from(TABLES.CUSTOMER_PROFILES).insert<TablesInsert<"customer_profiles">>({
  Line 125: return await supabase
  Line 126: .from(TABLES.CUSTOMER_PROFILES)
  Line 151: const { data, error } = await supabase
  Line 152: .from(TABLES.CUSTOMER_PROFILES)
  Line 177: return await supabase.from("customer_profiles").select("*").in("id", userIds);
  Line 181: return await supabase.from("customer_profiles").delete().eq("id", userId);
  Line 192: } = await supabase.auth.getUser();

backend\supabase\services\gallery\galleryService.ts
  Line 9: const { data: profileData, error } = await supabase
  Line 10: .from("business_profiles")

backend\supabase\services\location\locationService.ts
  Line 9: const query = supabase
  Line 10: .from("pincodes")
  Line 89: const query = supabase
  Line 90: .from("pincodes")
  Line 223: const { data: result, error } = await supabase.rpc(

backend\supabase\services\posts\postInteractions.ts
  Line 28: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 44: const { error: insertError } = await supabase
  Line 45: .from(tableName)
  Line 90: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 106: const { error: deleteError } = await supabase
  Line 107: .from(tableName)
  Line 145: const { data: { user } } = await supabase.auth.getUser();
  Line 153: const { count: likeCount } = await supabase
  Line 154: .from(tableName)
  Line 161: const { data: userLike } = await supabase
  Line 162: .from(tableName)

backend\supabase\services\posts\postService.ts
  Line 5: return await supabase
  Line 6: .from('unified_posts')

backend\supabase\services\posts\socialService.ts
  Line 86: let query = supabase
  Line 87: .from("subscriptions")
  Line 153: const { error } = await supabase
  Line 154: .from("subscriptions")
  Line 183: let query = supabase
  Line 184: .from("likes")
  Line 202: let countQuery = supabase
  Line 203: .from("likes")
  Line 281: const { error } = await supabase.from("likes").delete().eq("id", likeId);
  Line 312: let query = supabase
  Line 313: .from("ratings_reviews")
  Line 401: const { error } = await supabase
  Line 402: .from("ratings_reviews")
  Line 424: const { error } = await supabase
  Line 425: .from("ratings_reviews")
  Line 455: supabase
  Line 456: .from("likes")
  Line 459: supabase
  Line 460: .from("ratings_reviews")
  Line 463: supabase
  Line 464: .from("subscriptions")

backend\supabase\services\posts\unifiedFeedService.ts
  Line 18: return await supabase
  Line 19: .from("subscriptions")
  Line 27: return await supabase
  Line 28: .from("customer_profiles_public")
  Line 37: return await supabase
  Line 38: .from("business_profiles")

backend\supabase\services\products\productService.ts
  Line 10: return await supabase
  Line 11: .from("products_services")
  Line 32: return await supabase
  Line 33: .from("products_services")
  Line 56: return await supabase
  Line 57: .from("products_services")
  Line 78: return await supabase
  Line 79: .from("products_services")
  Line 100: return await supabase
  Line 101: .from("products_services")

backend\supabase\services\realtime\realtimeService.ts
  Line 47: const channel = supabase
  Line 48: .channel(`realtime:${subscriptionId}`)

backend\supabase\services\storage\avatarUploadService.ts
  Line 296: const { error } = await supabase.storage
  Line 311: const { data: publicUrlData } = supabase.storage
  Line 347: const { error } = await supabase
  Line 348: .from("customer_profiles")

backend\supabase\services\storage\businessPostImageUploadService.ts
  Line 27: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 81: const { error: uploadError } = await supabase.storage
  Line 97: const { data: urlData } = supabase.storage
  Line 128: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 134: const { data: profile, error: profileError } = await supabase
  Line 135: .from('business_profiles')

backend\supabase\services\storage\customerPostImageUploadService.ts
  Line 27: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 81: const { error: uploadError } = await supabase.storage
  Line 97: const { data: urlData } = supabase.storage
  Line 128: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 134: const { data: profile, error: profileError } = await supabase
  Line 135: .from('customer_profiles')

backend\supabase\services\storage\imageUploadService.ts
  Line 273: const { data, error } = await supabase.storage
  Line 286: const { data: urlData } = supabase.storage

backend\supabase\services\storage\storageService.ts
  Line 88: } = await supabase.auth.getUser();
  Line 99: const { data, error } = await supabase.storage
  Line 112: const { data: publicUrlData } = supabase.storage
  Line 142: } = await supabase.auth.getUser();
  Line 149: const { error } = await supabase.storage.from(bucket).remove([filePath]);
  Line 183: const { data: publicUrlData } = supabase.storage
  Line 212: const { data, error } = await supabase.storage
  Line 255: } = await supabase.auth.getUser();
  Line 266: const { data, error } = await supabase.storage
  Line 302: } = await supabase.auth.getUser();
  Line 322: await supabase.storage.listBuckets();
  Line 349: "[STORAGE_SERVICE] About to call supabase.storage.upload with:",
  Line 359: const { data, error } = await supabase.storage
  Line 384: const { data: publicUrlData } = supabase.storage

backend\supabase\utils\businessSlugValidation.ts
  Line 48: const { data: businessProfile, error } = await supabase
  Line 49: .from('business_profiles')

dependency-graph.json
  Line 31: "resolved": "lib/supabase.ts",
  Line 85: "source": "lib/supabase.ts",
  Line 96: "resolved": "src/config/supabase.ts",
  Line 144: "source": "src/config/supabase.ts",
  Line 165: "lib/supabase.ts",
  Line 181: "src/config/supabase.ts",
  Line 185: "lib/config/supabase.ts",
  Line 1447: "resolved": "lib/supabase.ts",
  Line 1744: "resolved": "lib/supabase.ts",
  Line 2546: "resolved": "lib/supabase.ts",
  Line 2621: "resolved": "lib/supabase.ts",
  Line 2692: "resolved": "lib/supabase.ts",
  Line 3447: "resolved": "lib/supabase.ts",
  Line 3637: "resolved": "lib/supabase.ts",
  Line 4361: "resolved": "lib/supabase.ts",
  Line 4469: "resolved": "lib/supabase.ts",
  Line 4593: "resolved": "lib/supabase.ts",
  Line 4773: "resolved": "lib/supabase.ts",
  Line 4916: "resolved": "lib/supabase.ts",
  Line 4980: "resolved": "lib/supabase.ts",
  Line 5012: "resolved": "lib/supabase.ts",
  Line 5140: "resolved": "lib/supabase.ts",
  Line 5269: "resolved": "lib/supabase.ts",
  Line 5359: "resolved": "lib/supabase.ts",
  Line 5489: "resolved": "lib/supabase.ts",
  Line 5563: "resolved": "lib/supabase.ts",
  Line 6234: "resolved": "lib/supabase.ts",
  Line 9820: "resolved": "src/config/supabase.ts",
  Line 10299: "resolved": "src/config/supabase.ts",
  Line 10632: "resolved": "src/config/supabase.ts",
  Line 10695: "resolved": "src/config/supabase.ts",
  Line 10757: "resolved": "src/config/supabase.ts",
  Line 10835: "resolved": "src/config/supabase.ts",
  Line 10862: "resolved": "src/config/supabase.ts",
  Line 11472: "resolved": "lib/supabase.ts",
  Line 12064: "resolved": "lib/supabase.ts",
  Line 13442: "resolved": "lib/supabase.ts",
  Line 13767: "resolved": "lib/supabase.ts",
  Line 14465: "resolved": "lib/supabase.ts",
  Line 15869: "resolved": "lib/supabase.ts",
  Line 17565: "resolved": "lib/supabase.ts",
  Line 18094: "resolved": "lib/supabase.ts",
  Line 18124: "resolved": "lib/supabase.ts",
  Line 18651: "resolved": "lib/supabase.ts",
  Line 18710: "source": "lib/config/supabase.ts",
  Line 20348: "resolved": "lib/supabase.ts",
  Line 21781: "resolved": "lib/supabase.ts",

docs\auth-docs\GOOGLE_OAUTH_SETUP.md
  Line 199: - [Supabase Auth with Google](https://supabase.com/docs/guides/auth/social-login/auth-google)

docs\auth-docs\KEYSTORE_CREDENTIALS.md
  Line 107: Supabase URL: https://rnjolcoecogzgglnblqn.supabase.co

docs\brownfield-architecture-app.md
  Line 89: | Supabase | Backend Platform | SDK | `lib/supabase.ts` |

lib\actions\businessPosts.ts
  Line 25: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 121: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 175: const { data: { user }, error: userError } = await supabase.auth.getUser();

lib\actions\customer\profileActions.ts
  Line 73: } = await supabase.auth.getUser();
  Line 121: } = await supabase.auth.getUser();
  Line 170: } = await supabase.auth.getUser();
  Line 219: } = await supabase.auth.getUser();
  Line 321: } = await supabase.auth.getUser();

lib\actions\customerPosts.ts
  Line 26: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 68: const { data: customerProfile, error: profileError } = await supabase
  Line 69: .from('customer_profiles')
  Line 127: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 190: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 265: const { data: { user }, error: userError } = await supabase.auth.getUser();

lib\actions\posts\unifiedFeed.ts
  Line 67: } = await supabase.auth.getUser();
  Line 70: let query = supabase.from("unified_posts").select("*", { count: "exact" });

lib\actions\products.ts
  Line 39: } = await supabase.auth.getUser();
  Line 98: } = await supabase.auth.getUser();
  Line 150: } = await supabase.auth.getUser();
  Line 208: } = await supabase.auth.getUser();

lib\auth\customerAuth.ts
  Line 47: await supabase.auth.signOut();
  Line 72: await supabase.auth.signOut();

lib\supabase.ts
  Line 28: supabase.auth.startAutoRefresh();
  Line 30: supabase.auth.stopAutoRefresh();
  Line 77: const { data: { session } } = await supabase.auth.getSession();
  Line 88: const { error } = await supabase.auth.signOut();

scripts\test-secure-keys.js
  Line 47: 'lib/supabase.ts'

src\components\discovery\FullScreenLocationSelector.tsx
  Line 182: const { data: rpcData, error: rpcError } = await supabase.rpc(
  Line 203: const { data: cityData, error } = await supabase
  Line 204: .from("pincodes")

src\components\feed\BusinessPostCreator.tsx
  Line 42: const { data: { user } } = await supabase.auth.getUser();
  Line 45: const { data: profile } = await supabase
  Line 46: .from('business_profiles')

src\components\feed\BusinessPostEditModal.tsx
  Line 90: const { data: { user } } = await supabase.auth.getUser();
  Line 93: const { data: profile } = await supabase
  Line 94: .from('business_profiles')

src\components\feed\BusinessPostModal.tsx
  Line 71: const { data: { user } } = await supabase.auth.getUser();
  Line 74: const { data: profile } = await supabase
  Line 75: .from('business_profiles')
  Line 177: const { data: { user } } = await supabase.auth.getUser();

src\components\feed\CustomerPostCreator.tsx
  Line 31: const { data: { user } } = await supabase.auth.getUser();

src\components\feed\CustomerPostEditModal.tsx
  Line 82: const { data: { user } } = await supabase.auth.getUser();
  Line 85: const { data: profile } = await supabase
  Line 86: .from('customer_profiles_public')

src\components\feed\CustomerPostModal.tsx
  Line 65: const { data: { user } } = await supabase.auth.getUser();
  Line 68: const { data: profile } = await supabase
  Line 69: .from('customer_profiles_public')
  Line 143: const { data: { user } } = await supabase.auth.getUser();

src\components\modals\customer\EditProfileModal.tsx
  Line 414: const { error: authUpdateError } = await supabase.auth.updateUser({

src\components\ui\LocationDisplay.tsx
  Line 37: const { data: businessProfile } = await supabase
  Line 38: .from('business_profiles')
  Line 50: const { data: customerProfile } = await supabase
  Line 51: .from('customer_profiles')

src\config\publicKeys.ts
  Line 18: url: 'https://rnjolcoecogzgglnblqn.supabase.co',

src\contexts\AuthContext.tsx
  Line 62: supabase.auth.getSession().then(({ data: { session } }) => {
  Line 74: } = supabase.auth.onAuthStateChange(async (event, session) => {
  Line 141: const { error } = await supabase.auth.signInWithPassword({
  Line 179: const { error } = await supabase.auth.signUp({
  Line 226: const { error } = await supabase.auth.signOut();
  Line 235: const { error } = await supabase.auth.signInWithOAuth({
  Line 274: const { data, error } = await supabase
  Line 275: .from("customer_profiles")
  Line 281: const { data, error } = await supabase
  Line 282: .from("business_profiles")
  Line 344: } = await supabase.auth.getSession();
  Line 351: } = await supabase.auth.getUser();
  Line 355: await supabase.auth.signOut();
  Line 381: await supabase.auth.signOut();

src\contexts\DiscoveryContext.tsx
  Line 101: const { data: customerProfile } = await supabase
  Line 102: .from("customer_profiles")
  Line 121: const { data: businessProfile } = await supabase
  Line 122: .from("business_profiles")

src\hooks\useBusinessInteractions.ts
  Line 25: const { data: { user }, error: authError } = await supabase.auth.getUser();

src\hooks\usePincodeDetails.ts
  Line 40: const { data: pincodeData, error: pincodeError } = await supabase
  Line 41: .from("pincodes")

src\hooks\usePostOwnership.ts
  Line 31: const { data: { user }, error } = await supabase.auth.getUser();

src\services\discovery\locationActions.ts
  Line 84: const { data } = await supabase
  Line 85: .from("pincodes")
  Line 135: let countQuery = supabase
  Line 136: .from("business_profiles")
  Line 154: let businessQuery = supabase
  Line 155: .from("business_profiles")
  Line 363: let businessIdsQuery = supabase
  Line 364: .from("business_profiles")
  Line 421: let countQuery = supabase
  Line 422: .from("products_services")
  Line 457: let productsQuery = supabase
  Line 458: .from("products_services")

src\services\discovery\productActions.ts
  Line 176: let businessQuery = supabase
  Line 177: .from("business_profiles")
  Line 224: let countQuery = supabase
  Line 225: .from("products_services")
  Line 250: let productsQuery = supabase
  Line 251: .from("products_services")
  Line 369: const { data: validBusinesses, error: businessError } = await supabase
  Line 370: .from("business_profiles")
  Line 401: let countQuery = supabase
  Line 402: .from("products_services")
  Line 422: let productsQuery = supabase
  Line 423: .from("products_services")

src\services\discovery\utils\locationUtils.ts
  Line 21: const { data: pincodeData, error: pincodeError } = await supabase
  Line 22: .from("pincodes")

src\services\discovery\utils\secureBusinessProfiles.ts
  Line 128: let countQuery = supabase
  Line 129: .from("business_profiles")
  Line 133: let businessQuery = supabase
  Line 134: .from("business_profiles")
  Line 281: let countQuery = supabase
  Line 282: .from("business_profiles")
  Line 315: let businessQuery = supabase
  Line 316: .from("business_profiles")
  Line 402: let validBusinessQuery = supabase
  Line 403: .from("business_profiles")

src\utils\apiClient.ts
  Line 40: const { data: { session }, error: sessionError } = await supabase.auth.getSession();

src\utils\deletePostMedia.ts
  Line 18: const { data: files, error: listError } = await supabase.storage
  Line 43: const { error: deleteError } = await supabase.storage
  Line 80: const { data: files, error: listError } = await supabase.storage
  Line 105: const { error: deleteError } = await supabase.storage

src\utils\userProfileUtils.ts
  Line 31: const { data: businessProfile, error: businessError } = await supabaseClient
  Line 32: .from("business_profiles")
  Line 47: const { data: customerProfile, error: customerError } = await supabaseClient
  Line 48: .from("customer_profiles")
