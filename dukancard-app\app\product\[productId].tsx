import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  Image,
  TouchableOpacity,
  Linking,
  Share,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { supabase } from "@/lib/supabase";
import { fetchProductById, fetchBusinessProfileById, fetchMoreProductsFromBusiness, fetchProductsFromOtherBusinesses } from "@/src/config/supabase/services/businessService";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import {
  ArrowLeft,
  Heart,
  Share2,
  Phone,
  MessageCircle,
  User,
  ShoppingBag,
  Star,
  MapPin,
  Home,
  Search,
  Users,
  Store,
} from "lucide-react-native";
import { useColorScheme } from "@/src/hooks/useColorScheme";
import { useTheme } from "@/src/hooks/useTheme";
import { BusinessDiscoveryData } from '@/src/types/business';
import { Tables } from "@/src/types/supabase";
import { logError } from "@/src/utils/errorHandling";
import { useToast } from "@/src/components/ui/Toast";
import { formatIndianNumberShort } from "@/lib/utils";
import { fetchCustomAd } from "@/backend/supabase/services/ads/adService";
import { AdData, BusinessCustomAd } from "@/src/types/ad";
import EnhancedAdSection from "@/src/components/ads/EnhancedAdSection";
import UnifiedBottomNavigation from "@/src/components/shared/navigation/UnifiedBottomNavigation";
import { createSingleProductStyles } from "@/styles/product/single-product-styles";
import ImageCarousel from "@/src/components/product/ImageCarousel";
import VariantSelector from "@/src/components/product/VariantSelector";
import ProductRecommendations from "@/src/components/product/ProductRecommendations";
import CollapsibleDescription from "@/src/components/product/CollapsibleDescription";
import WhatsAppIcon from "@/src/components/icons/WhatsAppIcon";
import { SingleProductSkeleton } from "@/src/components/ui/ProductSkeleton";
import { useLocation } from "@/src/contexts/LocationContext";
import {
  calculateDistanceWithFallback,
  formatDistance,
} from "@/src/utils/distanceCalculation";

// Product variant interface
interface ProductVariant {
  id: string;
  variant_name: string;
  variant_values: Record<string, string>;
  base_price?: number | null;
  discounted_price?: number | null;
  is_available: boolean;
  images: string[];
  featured_image_index: number;
}

// Temporary type for product data
interface Product extends Tables<'products_services'> {
  product_variants?: ProductVariant[] | null;
  base_price: number | null;
  discounted_price: number | null;
  image_url: string | null;
  images: string[] | null;
  featured_image_index: number | null;
}

// Business profile interface for this component
interface BusinessProfile extends Tables<'business_profiles'> {
  user_plan?: string;
}

interface ProductPageData {
  product: Product;
  business: BusinessProfile;
}

// Temporary function to format currency
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat("en-IN", {
    style: "currency",
    currency: "INR",
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount);
};



export default function SingleProductPage() {
  const { productId } = useLocalSearchParams<{ productId: string }>();
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === "dark";
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const { currentLocation } = useLocation();

  // Styles
  const styles = createSingleProductStyles(theme);

  // State management
  const [data, setData] = useState<ProductPageData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const toast = useToast();
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(
    null
  );
  const [currentProduct, setCurrentProduct] = useState<Product | null>(null);
  const [businessProducts, setBusinessProducts] = useState<Product[]>([]);
  const [otherBusinessProducts, setOtherBusinessProducts] = useState<
    (Product & { business_slug: string })[]
  >([]);
  const [recommendationsLoading, setRecommendationsLoading] = useState(false);
  const [adData, setAdData] = useState<AdData>(null);
  const [adLoading, setAdLoading] = useState(false);

  // Theme colors
  const backgroundColor = isDark ? "#000000" : "#FFFFFF";
  const textColor = isDark ? "#FFFFFF" : "#000000";
  const subtitleColor = isDark ? "#9CA3AF" : "#6B7280";
  const borderColor = isDark ? "#374151" : "#E5E7EB";
  const cardBackgroundColor = isDark ? "#1F2937" : "#F9FAFB";

  // Fetch product data
  useEffect(() => {
    // Validate productId parameter
    if (!productId || typeof productId !== "string") {
      router.replace("/");
      return;
    }
    const fetchProductData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch product details
        const productResult = await fetchProductById(productId);
        if (productResult.error || !productResult.data) {
          throw new Error(productResult.error || "Failed to fetch product");
        }

        const product = productResult.data;

        // Fetch business profile
        const businessResult = await fetchBusinessProfileById(
          product.business_id
        );
        if (businessResult.error || !businessResult.data) {
          throw new Error(
            businessResult.error || "Failed to fetch business profile"
          );
        }

        const business = businessResult.data;

        setData({ product, business });
        setCurrentProduct(product);

        // Fetch custom ads in the background
        setAdLoading(true);
        try {
          const customAdData = await fetchCustomAd(
            business.pincode || undefined
          );
          setAdData(customAdData);
        } catch (adError) {
          console.error("Error fetching custom ads:", adError);
          setAdData(null);
        } finally {
          setAdLoading(false);
        }

        // Fetch recommendations in the background
        setRecommendationsLoading(true);
        try {
          const [businessProductsResult, otherProductsResult] =
            await Promise.all([
              fetchMoreProductsFromBusiness(product.business_id, product.id),
              fetchProductsFromOtherBusinesses(product.business_id),
            ]);

          if (!businessProductsResult.error && businessProductsResult.data) {
            // Filter out products with null base_price to match component expectations
            const validProducts = (businessProductsResult.data as Product[]).filter(
              (product) => product.base_price !== null
            );
            setBusinessProducts(validProducts);
          }

          if (!otherProductsResult.error && otherProductsResult.data) {
            const transformedData = otherProductsResult.data
              .map((item: any) => {
                const businessSlug = Array.isArray(item.business_profiles)
                  ? item.business_profiles[0]?.business_slug
                  : item.business_profiles?.business_slug;

                const { business_profiles, ...productData } = item;
                return {
                  ...productData,
                  business_slug: businessSlug || "",
                } as Product & { business_slug: string };
              })
              .filter((product) => product.base_price !== null); // Filter out products with null base_price
            setOtherBusinessProducts(transformedData);
          }
        } catch (err) {
          console.error("Error fetching recommendations:", err);
        } finally {
          setRecommendationsLoading(false);
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "An unexpected error occurred";
        logError("SingleProductPage", errorMessage);
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchProductData();
  }, [productId, router]);

  // Early return for invalid productId (after hooks)
  if (!productId || typeof productId !== "string") {
    return null;
  }

  // Navigation handlers
  const handleBack = () => {
    if (router.canGoBack()) {
      router.back();
    } else {
      router.replace("/");
    }
  };

  const handleBusinessPress = () => {
    if (data?.business.business_slug) {
      router.push(`/business/${data.business.business_slug}`);
    }
  };

  const handleShare = async () => {
    try {
      if (!data?.product || !data?.business) {
        toast.error("Product information not available");
        return;
      }

      const { product, business } = data;
      const displayProduct = currentProduct || product;

      // Create the product URL using business slug and product slug (or ID as fallback)
      const productUrl = `https://dukancard.in/${
        business.business_slug
      }/product/${displayProduct.slug || displayProduct.id}`;

      // Create share message without price
      const shareMessage = `Check out "${displayProduct.name}" by ${business.business_name}!\n\n${productUrl}`;

      const result = await Share.share({
        message: shareMessage,
        url: productUrl,
        title: `${displayProduct.name} - ${business.business_name}`,
      });

      if (result.action === Share.sharedAction) {
        toast.success("Product shared successfully!");
      }
    } catch (err) {
      logError(err, "SingleProductPage - Share failed");
      toast.error("Failed to share product");
    }
  };

  const handleWhatsApp = () => {
    if (data?.business.whatsapp_number && currentProduct) {
      // Format WhatsApp number (remove any non-digit characters)
      const formattedNumber = data.business.whatsapp_number.replace(/\D/g, "");

      // Create detailed message with product information
      const message = `Hi ${
        data.business.business_name || "there"
      }, I'm interested in your product "${
        currentProduct.name
      }" that I saw on your Dukancard. Can you provide more information?`;

      const url = `whatsapp://send?phone=${formattedNumber}&text=${encodeURIComponent(
        message
      )}`;
      Linking.openURL(url).catch(() => {
        toast.error("WhatsApp not installed");
      });
    }
  };

  const handleCall = () => {
    if (data?.business.phone) {
      const url = `tel:${data.business.phone}`;
      Linking.openURL(url).catch(() => {
        Linking.openURL(url).catch(() => {
          toast.error("Unable to make call");
        });
      });
    }
  };

  // Handle variant selection and deselection
  const handleVariantSelect = (variant: ProductVariant | null) => {
    setSelectedVariant(variant);

    if (variant && data?.product) {
      // Update current product data with variant-specific information
      const updatedProduct = {
        ...data.product,
        base_price: variant.base_price || data.product.base_price,
        discounted_price:
          variant.discounted_price || data.product.discounted_price,
        images:
          variant.images && variant.images.length > 0
            ? variant.images
            : data.product.images,
        featured_image_index:
          variant.images && variant.images.length > 0
            ? variant.featured_image_index
            : data.product.featured_image_index,
      };

      setCurrentProduct(updatedProduct);
    } else if (data?.product) {
      // Revert to base product when no variant is selected
      setCurrentProduct(data.product);
    }
  };

  // Calculate distance to business
  const getDistanceText = () => {
    if (!data?.business.latitude || !data?.business.longitude) {
      return null;
    }

    const distance = calculateDistanceWithFallback(
      currentLocation,
      null, // No profile location fallback for single product
      null,
      data.business.latitude,
      data.business.longitude
    );

    if (distance !== null) {
      return formatDistance(distance);
    }

    return null;
  };

  // Loading state
  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor }]}>
        <StatusBar style={isDark ? "light" : "dark"} />
        <View style={{ flex: 1, paddingTop: insets.top }}>
          <SingleProductSkeleton />
        </View>
        <UnifiedBottomNavigation showQRScanner={true} />
      </View>
    );
  }

  // Error state
  if (error || !data) {
    return (
      <View
        style={[styles.container, { backgroundColor, paddingTop: insets.top }]}
      >
        <StatusBar style={isDark ? "light" : "dark"} />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorTitle, { color: textColor }]}>
            Product Not Found
          </Text>
          <Text style={[styles.errorMessage, { color: subtitleColor }]}>
            {error || "The product you are looking for could not be found."}
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={handleBack}>
            <Text style={styles.retryButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  const { product, business } = data;
  const displayProduct = currentProduct || product;

  return (
    <View style={[styles.container, { backgroundColor }]}>
      <StatusBar style={isDark ? "light" : "dark"} />

      <View style={{ flex: 1, paddingTop: insets.top }}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: borderColor }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBack}
            testID="back-button"
          >
            <ArrowLeft size={24} color={textColor} />
          </TouchableOpacity>
          <View style={styles.headerSpacer} />
          <View style={styles.headerSpacer} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Product Image Carousel */}
          <ImageCarousel
            images={
              displayProduct.images && displayProduct.images.length > 0
                ? displayProduct.images
                : displayProduct.image_url
                ? [displayProduct.image_url]
                : []
            }
            featuredImageIndex={displayProduct.featured_image_index || 0}
            productName={displayProduct.name}
          />

          {/* Product Info */}
          <View style={styles.productInfo}>
            <Text style={[styles.productName, { color: textColor }]}>
              {displayProduct.name}
            </Text>

            {/* Price */}
            <View style={styles.priceContainer}>
              {displayProduct.discounted_price &&
              displayProduct.base_price &&
              displayProduct.discounted_price < displayProduct.base_price ? (
                <>
                  <Text style={[styles.discountedPrice, { color: "#D4AF37" }]}>
                    {formatCurrency(displayProduct.discounted_price)}
                  </Text>
                  <Text
                    style={[styles.originalPrice, { color: subtitleColor }]}
                  >
                    {formatCurrency(displayProduct.base_price)}
                  </Text>
                </>
              ) : displayProduct.base_price ? (
                <Text style={[styles.price, { color: "#D4AF37" }]}>
                  {formatCurrency(displayProduct.base_price)}
                </Text>
              ) : null}
            </View>

            {/* Collapsible Description */}
            {displayProduct.description && (
              <CollapsibleDescription
                description={displayProduct.description}
                textColor={subtitleColor}
              />
            )}
          </View>

          {/* Variant Selector */}
          {product.product_variants && product.product_variants.length > 0 && (
            <VariantSelector
              variants={product.product_variants}
              selectedVariant={selectedVariant}
              onVariantSelect={handleVariantSelect}
            />
          )}

          {/* Business Info */}
          <TouchableOpacity
            style={[
              styles.businessInfo,
              { backgroundColor: cardBackgroundColor },
            ]}
            onPress={handleBusinessPress}
            activeOpacity={0.7}
            testID="business-profile"
          >
            <View style={styles.businessHeader}>
              <View style={styles.businessMainInfo}>
                <View style={styles.businessAvatar}>
                  {business.logo_url ? (
                    <Image
                      source={{ uri: business.logo_url }}
                      style={styles.businessAvatarImage}
                      resizeMode="cover"
                    />
                  ) : (
                    <User size={24} color={subtitleColor} />
                  )}
                </View>
                <View style={styles.businessDetails}>
                  <Text style={[styles.businessName, { color: textColor }]}>
                    {business.business_name || "Business"}
                  </Text>
                  {business.business_slug && (
                    <Text
                      style={[styles.businessSlug, { color: subtitleColor }]}
                    >
                      @{business.business_slug}
                    </Text>
                  )}
                </View>
              </View>
              {getDistanceText() && (
                <View style={styles.distanceContainer}>
                  <Text style={[styles.distanceText, { color: subtitleColor }]}>
                    {getDistanceText()}
                  </Text>
                </View>
              )}
            </View>
          </TouchableOpacity>

          {/* Contact Buttons */}
          <View style={styles.contactButtons}>
            {business.phone && (
              <TouchableOpacity
                style={styles.callButton}
                onPress={handleCall}
                testID="phone-button"
              >
                <Phone size={20} color="#FFFFFF" />
                <Text style={styles.contactButtonText}>Call</Text>
              </TouchableOpacity>
            )}
            {business.whatsapp_number && (
              <TouchableOpacity
                style={styles.whatsappButton}
                onPress={handleWhatsApp}
                testID="whatsapp-button"
              >
                <WhatsAppIcon size={20} color="#FFFFFF" />
                <Text style={styles.contactButtonText}>WhatsApp</Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Share Button */}
          <View style={styles.shareButtonContainer}>
            <TouchableOpacity
              style={styles.shareButtonRelocated}
              onPress={handleShare}
              testID="share-button"
            >
              <Share2 size={20} color="#FFFFFF" />
              <Text style={styles.contactButtonText}>Share</Text>
            </TouchableOpacity>
          </View>

          {/* Custom Ad Section */}
          <EnhancedAdSection
            topAdData={adData}
            businessCustomAd={business.custom_ads as BusinessCustomAd | undefined}
            userPlan={business.user_plan}
            loading={adLoading} // Show skeleton while ads are being fetched
          />

          {/* Product Recommendations */}
          <ProductRecommendations
            businessProducts={businessProducts as any}
            otherBusinessProducts={otherBusinessProducts as any}
            businessSlug={business.business_slug || ""}
            loading={recommendationsLoading}
          />
        </ScrollView>
      </View>

      <UnifiedBottomNavigation showQRScanner={true} />
    </View>
  );
}
