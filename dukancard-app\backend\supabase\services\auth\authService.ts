import { getCurrentUser, signOutUser, signInWithMobilePassword } from '@/src/config/supabase/services/sharedService';
import { getCustomerProfile, hasCustomerProfile, getCustomerProfileForValidation } from '@/src/config/supabase/services/customerService';
import { hasBusinessProfile } from '@/src/config/supabase/services/businessService';

export class AuthService {
  static async getCurrentUser() {
    try {
      const user = await getCurrentUser();

      // Return in the same format as the original function
      return { data: { user }, error: null };
    } catch (error) {
      console.error('AuthService.getCurrentUser error:', error);

      // Handle unexpected errors
      if (error instanceof Error &&
          (error.message?.includes('User from sub claim in JWT does not exist') ||
           error.message?.includes('session_not_found') ||
           error.message?.includes('invalid_token'))) {
        // Clear invalid session
        try {
          await signOutUser();
        } catch (signOutError) {
          console.error('Error signing out:', signOutError);
        }
      }

      throw error;
    }
  }

  static async getCustomerProfile(userId: string) {
    return await getCustomerProfile(userId);
  }

  static async hasCustomerProfile(userId: string) {
    return await hasCustomerProfile(userId);
  }

  static async hasBusinessProfile(userId: string) {
    return await hasBusinessProfile(userId);
  }

  static async getCustomerProfileForValidation(userId: string) {
    return await getCustomerProfileForValidation(userId);
  }

  static async signOut() {
    await signOutUser();
    return { error: null };
  }

  static async signInWithMobilePassword(mobile: string, password: string) {
    return await signInWithMobilePassword(mobile, password);
  }
}