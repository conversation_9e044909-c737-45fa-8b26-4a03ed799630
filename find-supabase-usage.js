const fs = require('fs');
const path = require('path');

// Function to recursively find all files in a directory
function getAllFiles(dirPath, arrayOfFiles = []) {
    const files = fs.readdirSync(dirPath);

    files.forEach(file => {
        const fullPath = path.join(dirPath, file);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory()) {
            // Skip node_modules, .git, and other common directories
            if (!['node_modules', '.git', '.next', 'coverage', 'dist', 'build', '__tests__', '__mocks__', 'test-results', 'playwright-report'].includes(file)) {
                arrayOfFiles = getAllFiles(fullPath, arrayOfFiles);
            }
        } else {
            // Only check relevant file types
            if (file.match(/\.(ts|tsx|js|jsx|json|md)$/)) {
                arrayOfFiles.push(fullPath);
            }
        }
    });

    return arrayOfFiles;
}

// Function to check if a file contains direct supabase calls (supabase.)
function checkSupabaseUsage(filePath, projectType) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n');
        const supabaseReferences = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const trimmedLine = line.trim();

            // Skip commented lines
            if (trimmedLine.startsWith('//') || trimmedLine.startsWith('*') || trimmedLine.startsWith('/*')) {
                continue;
            }

            // Check for direct supabase calls: supabase.
            if (trimmedLine.includes('supabase.')) {
                supabaseReferences.push({
                    line: i + 1,
                    content: trimmedLine
                });
            }
            // Check for supabase followed by dot on next line
            else if (trimmedLine.includes('supabase') && !trimmedLine.includes('.')) {
                // Check if next line starts with a dot
                if (i + 1 < lines.length) {
                    const nextLine = lines[i + 1].trim();
                    if (nextLine.startsWith('.')) {
                        supabaseReferences.push({
                            line: i + 1,
                            content: trimmedLine
                        });
                        supabaseReferences.push({
                            line: i + 2,
                            content: nextLine
                        });
                    }
                }
            }
        }

        return supabaseReferences;
    } catch (error) {
        console.error(`Error reading file ${filePath}:`, error.message);
        return [];
    }
}

// Function to process a project
function processProject(projectPath, projectName, excludedServiceFiles = []) {
    console.log(`\n=== Processing ${projectName} ===`);
    
    if (!fs.existsSync(projectPath)) {
        console.log(`Project path ${projectPath} does not exist.`);
        return [];
    }

    const allFiles = getAllFiles(projectPath);
    const filesWithSupabase = [];

    allFiles.forEach(filePath => {
        // Skip excluded service files
        const isExcluded = excludedServiceFiles.some(excludedFile => 
            filePath.replace(/\\/g, '/').includes(excludedFile)
        );
        
        if (isExcluded) {
            return;
        }

        const supabaseRefs = checkSupabaseUsage(filePath, projectName);
        if (supabaseRefs.length > 0) {
            const relativePath = path.relative(projectPath, filePath);
            filesWithSupabase.push({
                file: relativePath,
                fullPath: filePath,
                references: supabaseRefs
            });
        }
    });

    return filesWithSupabase;
}

// Main execution
function main() {
    const dukancardPath = path.join(__dirname, 'dukancard');
    const dukancardAppPath = path.join(__dirname, 'dukancard-app');

    // Excluded service files for dukancard
    const dukancardExcluded = [
        'lib/supabase/services/businessService.ts',
        'lib/supabase/services/customerService.ts',
        'lib/supabase/services/sharedService.ts'
    ];

    // Excluded service files for dukancard-app
    const dukancardAppExcluded = [
        'src/config/supabase/services/businessService.ts',
        'src/config/supabase/services/customerService.ts',
        'src/config/supabase/services/sharedService.ts'
    ];

    // Process dukancard project
    const dukancardResults = processProject(dukancardPath, 'dukancard', dukancardExcluded);
    
    // Process dukancard-app project
    const dukancardAppResults = processProject(dukancardAppPath, 'dukancard-app', dukancardAppExcluded);

    // Write results to files
    const dukancardOutput = dukancardResults.map(item => {
        const lines = item.references.map(ref => `  Line ${ref.line}: ${ref.content}`).join('\n');
        return `${item.file}\n${lines}\n`;
    }).join('\n');

    const dukancardAppOutput = dukancardAppResults.map(item => {
        const lines = item.references.map(ref => `  Line ${ref.line}: ${ref.content}`).join('\n');
        return `${item.file}\n${lines}\n`;
    }).join('\n');

    // Write to separate files
    fs.writeFileSync('dukancard-direct-supabase-calls.txt',
        `Files in dukancard project with direct Supabase calls (supabase.) - excluding service files:\n` +
        `Total files found: ${dukancardResults.length}\n\n` +
        dukancardOutput
    );

    fs.writeFileSync('dukancard-app-direct-supabase-calls.txt',
        `Files in dukancard-app project with direct Supabase calls (supabase.) - excluding service files:\n` +
        `Total files found: ${dukancardAppResults.length}\n\n` +
        dukancardAppOutput
    );

    // Console summary
    console.log(`\n=== SUMMARY ===`);
    console.log(`dukancard: ${dukancardResults.length} files with direct Supabase calls`);
    console.log(`dukancard-app: ${dukancardAppResults.length} files with direct Supabase calls`);
    console.log(`\nResults written to:`);
    console.log(`- dukancard-direct-supabase-calls.txt`);
    console.log(`- dukancard-app-direct-supabase-calls.txt`);

    // Show first few results for verification
    console.log(`\n=== SAMPLE RESULTS (dukancard) ===`);
    dukancardResults.slice(0, 5).forEach(item => {
        console.log(`${item.file}:`);
        item.references.slice(0, 2).forEach(ref => {
            console.log(`  Line ${ref.line}: ${ref.content}`);
        });
        console.log('');
    });

    console.log(`\n=== SAMPLE RESULTS (dukancard-app) ===`);
    dukancardAppResults.slice(0, 5).forEach(item => {
        console.log(`${item.file}:`);
        item.references.slice(0, 2).forEach(ref => {
            console.log(`  Line ${ref.line}: ${ref.content}`);
        });
        console.log('');
    });
}

main();
