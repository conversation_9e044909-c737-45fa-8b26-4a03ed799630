module.exports = {

"[project]/components/ui/card.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Card": (()=>Card),
    "CardAction": (()=>CardAction),
    "CardContent": (()=>CardContent),
    "CardDescription": (()=>CardDescription),
    "CardFooter": (()=>CardFooter),
    "CardHeader": (()=>CardHeader),
    "CardTitle": (()=>CardTitle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript)");
;
;
function Card({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/card.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
}
function CardHeader({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-header",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/card.tsx",
        lineNumber: 20,
        columnNumber: 5
    }, this);
}
function CardTitle({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-title",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("leading-none font-semibold", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/card.tsx",
        lineNumber: 33,
        columnNumber: 5
    }, this);
}
function CardDescription({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-description",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("text-muted-foreground text-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/card.tsx",
        lineNumber: 43,
        columnNumber: 5
    }, this);
}
function CardAction({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-action",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("col-start-2 row-span-2 row-start-1 self-start justify-self-end", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/card.tsx",
        lineNumber: 53,
        columnNumber: 5
    }, this);
}
function CardContent({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-content",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("px-6", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/card.tsx",
        lineNumber: 66,
        columnNumber: 5
    }, this);
}
function CardFooter({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-footer",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex items-center px-6 [.border-t]:pt-6", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/card.tsx",
        lineNumber: 76,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/app/(main)/login/data:547167 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"4039928e7548d26b67a82d94754953987e64086b42":"sendOTP"},"app/(main)/login/actions.ts",""] */ __turbopack_context__.s({
    "sendOTP": (()=>sendOTP)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var sendOTP = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("4039928e7548d26b67a82d94754953987e64086b42", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "sendOTP"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/app/(main)/login/data:d4c5c2 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"408a3fc0038eb1b725d89d6b69dcc55b2b1cd06dd3":"verifyOTP"},"app/(main)/login/actions.ts",""] */ __turbopack_context__.s({
    "verifyOTP": (()=>verifyOTP)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var verifyOTP = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("408a3fc0038eb1b725d89d6b69dcc55b2b1cd06dd3", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "verifyOTP"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/app/(main)/login/data:64a4f2 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"404769c266f0c071880e95e6a0dfe29621565ed871":"loginWithMobilePassword"},"app/(main)/login/actions.ts",""] */ __turbopack_context__.s({
    "loginWithMobilePassword": (()=>loginWithMobilePassword)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var loginWithMobilePassword = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("404769c266f0c071880e95e6a0dfe29621565ed871", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "loginWithMobilePassword"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/components/ui/label.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Label": (()=>Label)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-label/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
function Label({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"], {
        "data-slot": "label",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/label.tsx",
        lineNumber: 13,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/components/ui/form.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Form": (()=>Form),
    "FormControl": (()=>FormControl),
    "FormDescription": (()=>FormDescription),
    "FormField": (()=>FormField),
    "FormItem": (()=>FormItem),
    "FormLabel": (()=>FormLabel),
    "FormMessage": (()=>FormMessage),
    "useFormField": (()=>useFormField)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-slot/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/label.tsx [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
const Form = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormProvider"];
const FormFieldContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])({});
const FormField = ({ ...props })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(FormFieldContext.Provider, {
        value: {
            name: props.name
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Controller"], {
            ...props
        }, void 0, false, {
            fileName: "[project]/components/ui/form.tsx",
            lineNumber: 40,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/ui/form.tsx",
        lineNumber: 39,
        columnNumber: 5
    }, this);
};
const useFormField = ()=>{
    const fieldContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(FormFieldContext);
    const itemContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(FormItemContext);
    const { getFieldState } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useFormContext"])();
    const formState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useFormState"])({
        name: fieldContext.name
    });
    const fieldState = getFieldState(fieldContext.name, formState);
    if (!fieldContext) {
        throw new Error("useFormField should be used within <FormField>");
    }
    const { id } = itemContext;
    return {
        id,
        name: fieldContext.name,
        formItemId: `${id}-form-item`,
        formDescriptionId: `${id}-form-item-description`,
        formMessageId: `${id}-form-item-message`,
        ...fieldState
    };
};
const FormItemContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])({});
function FormItem({ className, ...props }) {
    const id = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useId"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItemContext.Provider, {
        value: {
            id
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            "data-slot": "form-item",
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("grid gap-2", className),
            ...props
        }, void 0, false, {
            fileName: "[project]/components/ui/form.tsx",
            lineNumber: 81,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/ui/form.tsx",
        lineNumber: 80,
        columnNumber: 5
    }, this);
}
function FormLabel({ className, ...props }) {
    const { error, formItemId } = useFormField();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Label"], {
        "data-slot": "form-label",
        "data-error": !!error,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("data-[error=true]:text-destructive", className),
        htmlFor: formItemId,
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/form.tsx",
        lineNumber: 97,
        columnNumber: 5
    }, this);
}
function FormControl({ ...props }) {
    const { error, formItemId, formDescriptionId, formMessageId } = useFormField();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Slot"], {
        "data-slot": "form-control",
        id: formItemId,
        "aria-describedby": !error ? `${formDescriptionId}` : `${formDescriptionId} ${formMessageId}`,
        "aria-invalid": !!error,
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/form.tsx",
        lineNumber: 111,
        columnNumber: 5
    }, this);
}
function FormDescription({ className, ...props }) {
    const { formDescriptionId } = useFormField();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
        "data-slot": "form-description",
        id: formDescriptionId,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("text-muted-foreground text-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/form.tsx",
        lineNumber: 129,
        columnNumber: 5
    }, this);
}
function FormMessage({ className, ...props }) {
    const { error, formMessageId } = useFormField();
    const body = error ? String(error?.message ?? "") : props.children;
    if (!body) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
        "data-slot": "form-message",
        id: formMessageId,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("text-destructive text-sm", className),
        ...props,
        children: body
    }, void 0, false, {
        fileName: "[project]/components/ui/form.tsx",
        lineNumber: 147,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/components/ui/input.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Input": (()=>Input)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript)");
;
;
function Input({ className, type, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
        type: type,
        "data-slot": "input",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm", "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]", "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/input.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/components/ui/input-otp.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "InputOTP": (()=>InputOTP),
    "InputOTPGroup": (()=>InputOTPGroup),
    "InputOTPSeparator": (()=>InputOTPSeparator),
    "InputOTPSlot": (()=>InputOTPSlot)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$input$2d$otp$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/input-otp/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$minus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MinusIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/minus.js [app-ssr] (ecmascript) <export default as MinusIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
function InputOTP({ className, containerClassName, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$input$2d$otp$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["OTPInput"], {
        "data-slot": "input-otp",
        containerClassName: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex items-center gap-2 has-disabled:opacity-50", containerClassName),
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("disabled:cursor-not-allowed", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/input-otp.tsx",
        lineNumber: 17,
        columnNumber: 5
    }, this);
}
function InputOTPGroup({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "input-otp-group",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex items-center", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/input-otp.tsx",
        lineNumber: 31,
        columnNumber: 5
    }, this);
}
function InputOTPSlot({ index, className, ...props }) {
    const inputOTPContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$input$2d$otp$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["OTPInputContext"]);
    const { char, hasFakeCaret, isActive } = inputOTPContext?.slots[index] ?? {};
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "input-otp-slot",
        "data-active": isActive,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("data-[active=true]:border-ring data-[active=true]:ring-ring/50 data-[active=true]:aria-invalid:ring-destructive/20 dark:data-[active=true]:aria-invalid:ring-destructive/40 aria-invalid:border-destructive data-[active=true]:aria-invalid:border-destructive dark:bg-input/30 border-input relative flex h-9 w-9 items-center justify-center border-y border-r text-sm shadow-xs transition-all outline-none first:rounded-l-md first:border-l last:rounded-r-md data-[active=true]:z-10 data-[active=true]:ring-[3px]", className),
        ...props,
        children: [
            char,
            hasFakeCaret && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "pointer-events-none absolute inset-0 flex items-center justify-center",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "animate-caret-blink bg-foreground h-4 w-px duration-1000"
                }, void 0, false, {
                    fileName: "[project]/components/ui/input-otp.tsx",
                    lineNumber: 62,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/ui/input-otp.tsx",
                lineNumber: 61,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/ui/input-otp.tsx",
        lineNumber: 50,
        columnNumber: 5
    }, this);
}
function InputOTPSeparator({ ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "input-otp-separator",
        role: "separator",
        ...props,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$minus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MinusIcon$3e$__["MinusIcon"], {}, void 0, false, {
            fileName: "[project]/components/ui/input-otp.tsx",
            lineNumber: 72,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/ui/input-otp.tsx",
        lineNumber: 71,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/app/(main)/login/components/EmailOTPForm.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "EmailOTPForm": (()=>EmailOTPForm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/form.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/input.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2d$otp$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/input-otp.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-right.js [app-ssr] (ecmascript) <export default as ArrowRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2>");
"use client";
;
;
;
;
;
;
;
;
;
// Email schema for step 
const emailSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].object({
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().min(1, {
        message: "Email is required"
    }).email({
        message: "Please enter a valid email address"
    })
});
// OTP schema for step 
const otpSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].object({
    otp: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().min(6, {
        message: "OTP must be 6 digits"
    }).max(6, {
        message: "OTP must be 6 digits"
    }).regex(/^\d{6}$/, {
        message: "OTP must be 6 digits"
    })
});
function EmailOTPForm({ step, email, countdown, isPending, onEmailSubmit, onOTPSubmit, onResendOTP, onBackToEmail }) {
    const emailForm = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["zodResolver"])(emailSchema),
        defaultValues: {
            email: ""
        }
    });
    const otpForm = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["zodResolver"])(otpSchema),
        defaultValues: {
            otp: ""
        }
    });
    if (step === 'email') {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Form"], {
            ...emailForm,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                onSubmit: emailForm.handleSubmit(onEmailSubmit),
                className: "space-y-4 sm:space-y-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormField"], {
                        control: emailForm.control,
                        name: "email",
                        render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormItem"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormLabel"], {
                                        className: "text-foreground text-sm sm:text-base",
                                        children: "Email Address"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                                        lineNumber: 81,
                                        columnNumber: 17
                                    }, void 0),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormControl"], {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Input"], {
                                            id: "email-login-field",
                                            placeholder: "<EMAIL>",
                                            type: "email",
                                            ...field,
                                            className: "bg-background border-border focus:border-primary dark:bg-neutral-800 dark:border-neutral-700 dark:focus:border-[var(--brand-gold)] h-10 sm:h-11 text-sm sm:text-base"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                                            lineNumber: 85,
                                            columnNumber: 19
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                                        lineNumber: 84,
                                        columnNumber: 17
                                    }, void 0),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormMessage"], {}, void 0, false, {
                                        fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                                        lineNumber: 93,
                                        columnNumber: 17
                                    }, void 0)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                                lineNumber: 80,
                                columnNumber: 15
                            }, void 0)
                    }, void 0, false, {
                        fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                        lineNumber: 76,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                        type: "submit",
                        className: "cursor-pointer w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-sm sm:text-base",
                        disabled: isPending,
                        children: isPending ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                    className: "w-5 h-5 mr-2 animate-spin"
                                }, void 0, false, {
                                    fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                                    lineNumber: 104,
                                    columnNumber: 17
                                }, this),
                                "Sending OTP..."
                            ]
                        }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                            children: [
                                "Continue ",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__["ArrowRight"], {
                                    className: "w-5 h-5 ml-2"
                                }, void 0, false, {
                                    fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                                    lineNumber: 109,
                                    columnNumber: 26
                                }, this)
                            ]
                        }, void 0, true)
                    }, void 0, false, {
                        fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                        lineNumber: 97,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center mt-4 text-xs sm:text-sm",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-muted-foreground",
                            children: "New to Dukancard? No worries! We'll create your account automatically."
                        }, void 0, false, {
                            fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                            lineNumber: 114,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                        lineNumber: 113,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                lineNumber: 72,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
            lineNumber: 71,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Form"], {
        ...otpForm,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            onSubmit: otpForm.handleSubmit((values)=>{
                onOTPSubmit({
                    email,
                    otp: values.otp
                });
            }),
            className: "space-y-4 sm:space-y-6",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center mb-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm text-muted-foreground",
                            children: "We've sent a 6-digit code to"
                        }, void 0, false, {
                            fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                            lineNumber: 131,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm font-medium text-foreground",
                            children: email
                        }, void 0, false, {
                            fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                            lineNumber: 134,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-xs text-muted-foreground mt-2",
                            children: "Code expires in 24 hours"
                        }, void 0, false, {
                            fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                            lineNumber: 135,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                    lineNumber: 130,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormField"], {
                    control: otpForm.control,
                    name: "otp",
                    render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormItem"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormLabel"], {
                                    className: "text-foreground text-sm sm:text-base text-center block",
                                    children: "Enter Verification Code"
                                }, void 0, false, {
                                    fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                                    lineNumber: 144,
                                    columnNumber: 15
                                }, void 0),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormControl"], {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex justify-center",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2d$otp$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputOTP"], {
                                            id: "otp-login-field",
                                            maxLength: 6,
                                            value: field.value,
                                            onChange: (value)=>field.onChange(value),
                                            className: "gap-2",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2d$otp$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputOTPGroup"], {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2d$otp$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputOTPSlot"], {
                                                        index: 0,
                                                        className: "w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                                                        lineNumber: 157,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2d$otp$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputOTPSlot"], {
                                                        index: 1,
                                                        className: "w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                                                        lineNumber: 161,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2d$otp$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputOTPSlot"], {
                                                        index: 2,
                                                        className: "w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                                                        lineNumber: 165,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2d$otp$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputOTPSlot"], {
                                                        index: 3,
                                                        className: "w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                                                        lineNumber: 169,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2d$otp$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputOTPSlot"], {
                                                        index: 4,
                                                        className: "w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                                                        lineNumber: 173,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2d$otp$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputOTPSlot"], {
                                                        index: 5,
                                                        className: "w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                                                        lineNumber: 177,
                                                        columnNumber: 23
                                                    }, void 0)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                                                lineNumber: 156,
                                                columnNumber: 21
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                                            lineNumber: 149,
                                            columnNumber: 19
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                                        lineNumber: 148,
                                        columnNumber: 17
                                    }, void 0)
                                }, void 0, false, {
                                    fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                                    lineNumber: 147,
                                    columnNumber: 15
                                }, void 0),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormMessage"], {}, void 0, false, {
                                    fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                                    lineNumber: 185,
                                    columnNumber: 15
                                }, void 0)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                            lineNumber: 143,
                            columnNumber: 13
                        }, void 0)
                }, void 0, false, {
                    fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                    lineNumber: 139,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    className: "cursor-pointer w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-sm sm:text-base",
                    disabled: isPending,
                    children: isPending ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                className: "w-5 h-5 mr-2 animate-spin"
                            }, void 0, false, {
                                fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                                lineNumber: 196,
                                columnNumber: 15
                            }, this),
                            "Verifying..."
                        ]
                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            "Verify & Sign In ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__["ArrowRight"], {
                                className: "w-5 h-5 ml-2"
                            }, void 0, false, {
                                fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                                lineNumber: 201,
                                columnNumber: 32
                            }, this)
                        ]
                    }, void 0, true)
                }, void 0, false, {
                    fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                    lineNumber: 189,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col items-center space-y-3 mt-4 text-xs sm:text-sm",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            type: "button",
                            onClick: onResendOTP,
                            disabled: countdown > 0,
                            className: `${countdown > 0 ? "text-muted-foreground cursor-not-allowed" : "text-primary dark:text-[var(--brand-gold)] hover:underline cursor-pointer"}`,
                            children: countdown > 0 ? `Resend OTP in ${countdown}s` : "Resend OTP"
                        }, void 0, false, {
                            fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                            lineNumber: 206,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            type: "button",
                            onClick: onBackToEmail,
                            className: "text-muted-foreground hover:text-foreground cursor-pointer",
                            children: "← Change email address"
                        }, void 0, false, {
                            fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                            lineNumber: 218,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
                    lineNumber: 205,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
            lineNumber: 124,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/(main)/login/components/EmailOTPForm.tsx",
        lineNumber: 123,
        columnNumber: 5
    }, this);
}
}}),
"[project]/lib/schemas/authSchemas.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "EmailOTPSchema": (()=>EmailOTPSchema),
    "IndianMobileSchema": (()=>IndianMobileSchema),
    "MobilePasswordLoginSchema": (()=>MobilePasswordLoginSchema),
    "PasswordComplexitySchema": (()=>PasswordComplexitySchema),
    "VerifyOTPSchema": (()=>VerifyOTPSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-ssr] (ecmascript)");
;
const IndianMobileSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().trim().min(10, {
    message: "Mobile number must be 10 digits"
}).max(10, {
    message: "Mobile number must be 10 digits"
}).regex(/^[6-9]\d{9}$/, {
    message: "Please enter a valid Indian mobile number"
});
const EmailOTPSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].object({
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().trim().min(1, {
        message: "Email is required"
    }).email({
        message: "Please enter a valid email address"
    })
});
const VerifyOTPSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].object({
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().trim().min(1, {
        message: "Email is required"
    }).email({
        message: "Please enter a valid email address"
    }),
    otp: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().trim().min(6, {
        message: "OTP must be 6 digits"
    }).max(6, {
        message: "OTP must be 6 digits"
    }).regex(/^\d{6}$/, {
        message: "OTP must be 6 digits"
    })
});
const PasswordComplexitySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().min(6, "Password must be at least 6 characters long").regex(/[A-Z]/, "Password must contain at least one uppercase letter").regex(/[a-z]/, "Password must contain at least one lowercase letter").regex(/\d/, "Password must contain at least one number").regex(/[^a-zA-Z0-9]/, "Password must contain at least one special character");
const MobilePasswordLoginSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].object({
    mobile: IndianMobileSchema,
    password: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().trim().min(1, {
        message: "Password is required"
    })
});
}}),
"[project]/app/(main)/login/components/MobilePasswordForm.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MobilePasswordForm": (()=>MobilePasswordForm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$schemas$2f$authSchemas$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/schemas/authSchemas.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/form.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/input.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-right.js [app-ssr] (ecmascript) <export default as ArrowRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
function MobilePasswordForm({ isPending, onSubmit }) {
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$schemas$2f$authSchemas$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MobilePasswordLoginSchema"]),
        defaultValues: {
            mobile: "",
            password: ""
        }
    });
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Form"], {
        ...form,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            onSubmit: form.handleSubmit(onSubmit),
            className: "space-y-4 sm:space-y-6",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormField"], {
                    control: form.control,
                    name: "mobile",
                    render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormItem"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormLabel"], {
                                    className: "text-foreground text-sm sm:text-base",
                                    children: "Mobile Number"
                                }, void 0, false, {
                                    fileName: "[project]/app/(main)/login/components/MobilePasswordForm.tsx",
                                    lineNumber: 38,
                                    columnNumber: 15
                                }, void 0),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormControl"], {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "relative",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-sm text-muted-foreground",
                                                children: "+91"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(main)/login/components/MobilePasswordForm.tsx",
                                                lineNumber: 43,
                                                columnNumber: 19
                                            }, void 0),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Input"], {
                                                placeholder: "9876543210",
                                                type: "tel",
                                                ...field,
                                                onChange: (e)=>{
                                                    let value = e.target.value;
                                                    // Remove any +91 prefix if user enters it
                                                    value = value.replace(/^\+91/, '');
                                                    // Only allow numeric input
                                                    value = value.replace(/\D/g, '');
                                                    // Limit to 10 digits for mobile numbers
                                                    if (value.length > 10) {
                                                        value = value.slice(0, 10);
                                                    }
                                                    field.onChange(value);
                                                },
                                                onKeyDown: (e)=>{
                                                    const isNumeric = /^[0-9]$/.test(e.key);
                                                    const isControl = [
                                                        'Backspace',
                                                        'Delete',
                                                        'ArrowLeft',
                                                        'ArrowRight',
                                                        'Tab'
                                                    ].includes(e.key);
                                                    if (!isNumeric && !isControl) {
                                                        e.preventDefault();
                                                    }
                                                },
                                                className: "pl-12 bg-background border-border focus:border-primary dark:bg-neutral-800 dark:border-neutral-700 dark:focus:border-[var(--brand-gold)] h-10 sm:h-11 text-sm sm:text-base",
                                                maxLength: 10
                                            }, void 0, false, {
                                                fileName: "[project]/app/(main)/login/components/MobilePasswordForm.tsx",
                                                lineNumber: 46,
                                                columnNumber: 19
                                            }, void 0)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(main)/login/components/MobilePasswordForm.tsx",
                                        lineNumber: 42,
                                        columnNumber: 17
                                    }, void 0)
                                }, void 0, false, {
                                    fileName: "[project]/app/(main)/login/components/MobilePasswordForm.tsx",
                                    lineNumber: 41,
                                    columnNumber: 15
                                }, void 0),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormMessage"], {}, void 0, false, {
                                    fileName: "[project]/app/(main)/login/components/MobilePasswordForm.tsx",
                                    lineNumber: 74,
                                    columnNumber: 15
                                }, void 0)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(main)/login/components/MobilePasswordForm.tsx",
                            lineNumber: 37,
                            columnNumber: 13
                        }, void 0)
                }, void 0, false, {
                    fileName: "[project]/app/(main)/login/components/MobilePasswordForm.tsx",
                    lineNumber: 33,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormField"], {
                    control: form.control,
                    name: "password",
                    render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormItem"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormLabel"], {
                                    className: "text-foreground text-sm sm:text-base",
                                    children: "Password"
                                }, void 0, false, {
                                    fileName: "[project]/app/(main)/login/components/MobilePasswordForm.tsx",
                                    lineNumber: 84,
                                    columnNumber: 15
                                }, void 0),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormControl"], {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Input"], {
                                        placeholder: "••••••••",
                                        type: "password",
                                        ...field,
                                        className: "bg-background border-border focus:border-primary dark:bg-neutral-800 dark:border-neutral-700 dark:focus:border-[var(--brand-gold)] h-10 sm:h-11 text-sm sm:text-base"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(main)/login/components/MobilePasswordForm.tsx",
                                        lineNumber: 88,
                                        columnNumber: 17
                                    }, void 0)
                                }, void 0, false, {
                                    fileName: "[project]/app/(main)/login/components/MobilePasswordForm.tsx",
                                    lineNumber: 87,
                                    columnNumber: 15
                                }, void 0),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormMessage"], {}, void 0, false, {
                                    fileName: "[project]/app/(main)/login/components/MobilePasswordForm.tsx",
                                    lineNumber: 95,
                                    columnNumber: 15
                                }, void 0)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(main)/login/components/MobilePasswordForm.tsx",
                            lineNumber: 83,
                            columnNumber: 13
                        }, void 0)
                }, void 0, false, {
                    fileName: "[project]/app/(main)/login/components/MobilePasswordForm.tsx",
                    lineNumber: 79,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    className: "cursor-pointer w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-sm sm:text-base",
                    disabled: isPending,
                    children: isPending ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                className: "w-5 h-5 mr-2 animate-spin"
                            }, void 0, false, {
                                fileName: "[project]/app/(main)/login/components/MobilePasswordForm.tsx",
                                lineNumber: 107,
                                columnNumber: 15
                            }, this),
                            "Signing in..."
                        ]
                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            "Sign In ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__["ArrowRight"], {
                                className: "w-5 h-5 ml-2"
                            }, void 0, false, {
                                fileName: "[project]/app/(main)/login/components/MobilePasswordForm.tsx",
                                lineNumber: 112,
                                columnNumber: 23
                            }, this)
                        ]
                    }, void 0, true)
                }, void 0, false, {
                    fileName: "[project]/app/(main)/login/components/MobilePasswordForm.tsx",
                    lineNumber: 100,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center mt-4 text-xs sm:text-sm",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-muted-foreground",
                        children: [
                            "Don't have an account?",
                            " ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                href: "/register",
                                className: "text-primary dark:text-[var(--brand-gold)] hover:underline font-medium",
                                children: "Register here"
                            }, void 0, false, {
                                fileName: "[project]/app/(main)/login/components/MobilePasswordForm.tsx",
                                lineNumber: 120,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(main)/login/components/MobilePasswordForm.tsx",
                        lineNumber: 118,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/app/(main)/login/components/MobilePasswordForm.tsx",
                    lineNumber: 117,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/(main)/login/components/MobilePasswordForm.tsx",
            lineNumber: 29,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/(main)/login/components/MobilePasswordForm.tsx",
        lineNumber: 28,
        columnNumber: 5
    }, this);
}
}}),
"[project]/app/(main)/login/components/AuthMethodToggle.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthMethodToggle": (()=>AuthMethodToggle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
"use client";
;
function AuthMethodToggle({ authMethod, step, onMethodChange }) {
    // Only show toggle when on email step or mobile-password method
    if (!(authMethod === 'email-otp' && step === 'email') && authMethod !== 'mobile-password') {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex rounded-lg bg-muted p-1 mb-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                type: "button",
                onClick: ()=>onMethodChange('email-otp'),
                className: `flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${authMethod === 'email-otp' ? 'bg-background text-foreground shadow-sm' : 'text-muted-foreground hover:text-foreground'}`,
                children: "Email OTP"
            }, void 0, false, {
                fileName: "[project]/app/(main)/login/components/AuthMethodToggle.tsx",
                lineNumber: 17,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                type: "button",
                onClick: ()=>onMethodChange('mobile-password'),
                className: `flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${authMethod === 'mobile-password' ? 'bg-background text-foreground shadow-sm' : 'text-muted-foreground hover:text-foreground'}`,
                children: "Mobile + Password"
            }, void 0, false, {
                fileName: "[project]/app/(main)/login/components/AuthMethodToggle.tsx",
                lineNumber: 28,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/(main)/login/components/AuthMethodToggle.tsx",
        lineNumber: 16,
        columnNumber: 5
    }, this);
}
}}),
"[project]/lib/supabase/constants.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// lib/supabase/constants.ts
__turbopack_context__.s({
    "BUCKETS": (()=>BUCKETS),
    "COLUMNS": (()=>COLUMNS),
    "RPC_FUNCTIONS": (()=>RPC_FUNCTIONS),
    "RPC_PARAMS": (()=>RPC_PARAMS),
    "TABLES": (()=>TABLES)
});
const TABLES = {
    BLOGS: "blogs",
    BUSINESS_ACTIVITIES: "business_activities",
    BUSINESS_PROFILES: "business_profiles",
    CARD_VISITS: "card_visits",
    CUSTOM_ADS: "custom_ads",
    CUSTOM_AD_TARGETS: "custom_ad_targets",
    CUSTOMER_POSTS: "customer_posts",
    CUSTOMER_PROFILES: "customer_profiles",
    CUSTOMER_PROFILES_PUBLIC: "customer_profiles_public",
    LIKES: "likes",
    PAYMENT_SUBSCRIPTIONS: "payment_subscriptions",
    PINCODES: "pincodes",
    PRODUCTS_SERVICES: "products_services",
    PRODUCT_VARIANTS: "product_variants",
    PUBLIC_SUBSCRIPTION_STATUS: "public_subscription_status",
    STORAGE_CLEANUP_CONFIG: "storage_cleanup_config",
    STORAGE_CLEANUP_PROGRESS: "storage_cleanup_progress",
    SUBSCRIPTIONS: "subscriptions",
    SYSTEM_ALERTS: "system_alerts",
    UNIFIED_POSTS: "unified_posts",
    RATINGS_REVIEWS: "ratings_reviews"
};
const BUCKETS = {
    BUSINESS: "business",
    CUSTOMERS: "customers"
};
const COLUMNS = {
    ID: "id",
    CREATED_AT: "created_at",
    UPDATED_AT: "updated_at",
    NAME: "name",
    EMAIL: "email",
    PHONE: "phone",
    CITY: "city",
    STATE: "state",
    PINCODE: "pincode",
    PLAN_ID: "plan_id",
    LOCALITY: "locality",
    CITY_SLUG: "city_slug",
    STATE_SLUG: "state_slug",
    LOCALITY_SLUG: "locality_slug",
    OFFICE_NAME: "office_name",
    AVATAR_URL: "avatar_url",
    LOGO_URL: "logo_url",
    IMAGE_URL: "image_url",
    IMAGES: "images",
    SLUG: "slug",
    STATUS: "status",
    CONTENT: "content",
    GALLERY: "gallery",
    DESCRIPTION: "description",
    TITLE: "title",
    USER_ID: "user_id",
    BUSINESS_ID: "business_id",
    BUSINESS_NAME: "business_name",
    BUSINESS_SLUG: "business_slug",
    PRODUCT_ID: "product_id",
    LATITUDE: "latitude",
    LONGITUDE: "longitude",
    PRODUCT_TYPE: "product_type",
    BASE_PRICE: "base_price",
    DISCOUNTED_PRICE: "discounted_price",
    IS_AVAILABLE: "is_available",
    CUSTOM_AD_TARGETS: "custom_ad_targets",
    AD_IMAGE_URL: "ad_image_url",
    AD_LINK_URL: "ad_link_url",
    IS_ACTIVE: "is_active",
    TARGETING_LOCATIONS: "targeting_locations",
    RATINGS_REVIEWS: "ratings_reviews",
    BUSINESS_PROFILE_ID: "business_profile_id",
    RAZORPAY_SUBSCRIPTION_ID: "razorpay_subscription_id",
    SUBSCRIPTION_STATUS: "subscription_status",
    TOTAL_LIKES: "total_likes",
    TOTAL_SUBSCRIPTIONS: "total_subscriptions",
    AVERAGE_RATING: "average_rating",
    TOTAL_VISITS: "total_visits",
    TODAY_VISITS: "today_visits",
    YESTERDAY_VISITS: "yesterday_visits",
    VISITS_7_DAYS: "visits_7_days",
    VISITS_30_DAYS: "visits_30_days",
    CUSTOM_ADS: "custom_ads",
    CUSTOM_BRANDING: "custom_branding",
    CONTACT_EMAIL: "contact_email",
    HAS_ACTIVE_SUBSCRIPTION: "has_active_subscription",
    TRIAL_END_DATE: "trial_end_date",
    MEMBER_NAME: "member_name",
    ADDRESS_LINE: "address_line",
    INSTAGRAM_URL: "instagram_url",
    FACEBOOK_URL: "facebook_url",
    WHATSAPP_NUMBER: "whatsapp_number",
    ABOUT_BIO: "about_bio",
    THEME_COLOR: "theme_color",
    DELIVERY_INFO: "delivery_info",
    BUSINESS_HOURS: "business_hours",
    BUSINESS_CATEGORY: "business_category",
    ESTABLISHED_YEAR: "established_year",
    VARIANT_VALUES: "variant_values",
    VARIANT_NAME: "variant_name",
    FEATURED_IMAGE_INDEX: "featured_image_index",
    STATE_NAME: "StateName",
    DIVISION_NAME: "DivisionName"
};
const RPC_FUNCTIONS = {
    GET_DAILY_UNIQUE_VISIT_TREND: "get_daily_unique_visit_trend",
    GET_HOURLY_UNIQUE_VISIT_TREND: "get_hourly_unique_visit_trend",
    GET_MONTHLY_UNIQUE_VISITS: "get_monthly_unique_visits",
    GET_MONTHLY_UNIQUE_VISIT_TREND: "get_monthly_unique_visit_trend",
    GET_AVAILABLE_YEARS_FOR_MONTHLY_METRICS: "get_available_years_for_monthly_metrics",
    GET_TOTAL_UNIQUE_VISITS: "get_total_unique_visits",
    GET_AD_FOR_PINCODE: "get_ad_for_pincode",
    GET_PRODUCT_WITH_VARIANTS: "get_product_with_variants",
    GET_AVAILABLE_PRODUCT_VARIANTS: "get_available_product_variants",
    GET_BUSINESS_VARIANT_STATS: "get_business_variant_stats",
    IS_VARIANT_COMBINATION_UNIQUE: "is_variant_combination_unique"
};
const RPC_PARAMS = {
    BUSINESS_ID: "business_id",
    START_DATE: "start_date",
    END_DATE: "end_date",
    TARGET_DATE: "target_date",
    TARGET_YEAR: "target_year",
    TARGET_MONTH: "target_month",
    START_YEAR: "start_year",
    START_MONTH: "start_month",
    END_YEAR: "end_year",
    END_MONTH: "end_month",
    TARGET_PINCODE: "target_pincode"
};
}}),
"[project]/lib/supabase/services/sharedService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "fetchPincodeAddress": (()=>fetchPincodeAddress),
    "fetchUserSubscriptions": (()=>fetchUserSubscriptions),
    "getAuthenticatedUser": (()=>getAuthenticatedUser),
    "getPublicUrlFromStorage": (()=>getPublicUrlFromStorage),
    "getStateNameByCity": (()=>getStateNameByCity),
    "getUnifiedPosts": (()=>getUnifiedPosts),
    "listStorageFiles": (()=>listStorageFiles),
    "removeFileFromStorage": (()=>removeFileFromStorage),
    "signInWithOAuth": (()=>signInWithOAuth),
    "signInWithOtp": (()=>signInWithOtp),
    "signInWithPassword": (()=>signInWithPassword),
    "signOutUser": (()=>signOutUser),
    "subscribeToTableChanges": (()=>subscribeToTableChanges),
    "updateAuthUserPhone": (()=>updateAuthUserPhone),
    "uploadFileToStorage": (()=>uploadFileToStorage),
    "verifyOtp": (()=>verifyOtp)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/constants.ts [app-ssr] (ecmascript)");
;
async function getAuthenticatedUser(supabase) {
    try {
        const { data: { user }, error } = await supabase.auth.getUser();
        if (error) {
            console.error(`Error fetching authenticated user: ${error.message}`);
            return {
                user: null,
                error: "User not found or authentication error."
            };
        }
        return {
            user,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching authenticated user: ${err}`);
        return {
            user: null,
            error: "An unexpected error occurred."
        };
    }
}
async function uploadFileToStorage(supabase, bucketName, path, fileBuffer, contentType, upsert = true) {
    try {
        const { error } = await supabase.storage.from(bucketName).upload(path, fileBuffer, {
            contentType,
            upsert
        });
        if (error) {
            console.error(`Error uploading file to storage: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error uploading file to storage: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function getPublicUrlFromStorage(supabase, bucketName, path) {
    try {
        const { data } = supabase.storage.from(bucketName).getPublicUrl(path);
        if (!data?.publicUrl) {
            return {
                publicUrl: null,
                error: "Could not retrieve public URL."
            };
        }
        return {
            publicUrl: data.publicUrl,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error getting public URL: ${err}`);
        return {
            publicUrl: null,
            error: "An unexpected error occurred."
        };
    }
}
async function removeFileFromStorage(supabase, bucketName, paths) {
    try {
        const { error } = await supabase.storage.from(bucketName).remove(paths);
        if (error) {
            console.error(`Error removing file from storage: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error removing file from storage: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function updateAuthUserPhone(supabase, phone) {
    try {
        const { error } = await supabase.auth.updateUser({
            phone: `+91${phone}`
        });
        if (error) {
            console.error(`Error updating auth user phone: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error updating auth user phone: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function listStorageFiles(supabase, bucketName, path, options) {
    try {
        const { data, error } = await supabase.storage.from(bucketName).list(path, options);
        if (error) {
            console.error(`Error listing storage files: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error listing storage files: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function signOutUser(supabase) {
    try {
        const { error } = await supabase.auth.signOut();
        if (error) {
            console.error(`Error signing out user: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error signing out user: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function signInWithOtp(supabase, email, shouldCreateUser = true) {
    try {
        const { error } = await supabase.auth.signInWithOtp({
            email: email,
            options: {
                shouldCreateUser: shouldCreateUser,
                data: {
                    auth_type: "email"
                }
            }
        });
        if (error) {
            console.error(`Error sending OTP: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error sending OTP: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function verifyOtp(supabase, email, token) {
    try {
        const { data, error } = await supabase.auth.verifyOtp({
            email: email,
            token: token,
            type: 'email'
        });
        if (error) {
            console.error(`Error verifying OTP: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error verifying OTP: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function signInWithPassword(supabase, phone, password) {
    try {
        const { data, error } = await supabase.auth.signInWithPassword({
            phone: phone,
            password: password
        });
        if (error) {
            console.error(`Error signing in with password: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error signing in with password: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function signInWithOAuth(supabase, provider, redirectTo, queryParams) {
    try {
        const { data, error } = await supabase.auth.signInWithOAuth({
            provider: provider,
            options: {
                redirectTo: redirectTo,
                skipBrowserRedirect: true,
                queryParams: queryParams
            }
        });
        if (error) {
            console.error(`Error initiating OAuth sign-in: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error initiating OAuth sign-in: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
function subscribeToTableChanges(supabase, tableName, filter, callback) {
    const channel = supabase.channel(`public:${tableName}`).on("postgres_changes", {
        event: "*",
        schema: "public",
        table: tableName,
        filter: filter
    }, callback).subscribe();
    return ()=>{
        supabase.removeChannel(channel);
    };
}
async function fetchUserSubscriptions(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].SUBSCRIPTIONS).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID, userId);
        if (error) {
            console.error(`Error fetching user subscriptions: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching user subscriptions: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getStateNameByCity(supabase, city) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PINCODES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].STATE_NAME).ilike(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DIVISION_NAME, `%${city}%`).limit(1);
        if (error) {
            console.error(`Error fetching state name for city ${city}: ${error.message}`);
            return {
                stateName: null,
                error: error.message
            };
        }
        return {
            stateName: data && data.length > 0 ? data[0].StateName : null,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching state name for city ${city}: ${err}`);
        return {
            stateName: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getUnifiedPosts(supabase, from, to, conditions = []) {
    let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].UNIFIED_POSTS).select('*', {
        count: 'exact'
    });
    if (conditions.length > 0) {
        query = query.or(conditions.join(','));
    }
    const { data, error, count } = await query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
        ascending: false
    }).range(from, to);
    if (error) {
        console.error("Error fetching unified posts:", error);
        return {
            data: null,
            error: error.message,
            count: null
        };
    }
    return {
        data,
        error: null,
        count
    };
}
async function fetchPincodeAddress(supabase, pincode, locality_slug, city_slug, state_slug) {
    try {
        let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PINCODES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].OFFICE_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DIVISION_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].STATE_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE}`);
        if (pincode) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE, pincode);
        }
        if (city_slug) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CITY_SLUG, city_slug);
        }
        if (state_slug) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].STATE_SLUG, state_slug);
        }
        if (locality_slug) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY_SLUG, locality_slug);
        }
        const { data, error } = await query.limit(1);
        if (error) {
            console.error(`Error fetching pincode address: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data: data[0] || null,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching pincode address: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
}}),
"[project]/app/(main)/login/components/SocialLoginButton.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SocialLoginButton": (()=>SocialLoginButton)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$sharedService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/services/sharedService.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
function SocialLoginButton({ redirectSlug, message, disabled }) {
    const [isSocialLoading, setIsSocialLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // Initiates the Supabase OAuth flow for social login.
    // This function gets the authorization URL and opens it in a new tab.
    // After successful authentication, Supabase will redirect to /auth/callback,
    // where the unified post-login redirection logic is executed.
    async function handleSocialLogin(provider) {
        try {
            setIsSocialLoading(provider);
            const supabase = createClient();
            // Construct the callback URL with the redirect and message parameters if available
            // Add closeWindow=true to indicate this window should close after auth
            let callbackUrl = `${window.location.origin}/auth/callback?closeWindow=true`;
            // Add redirect parameter if available
            if (redirectSlug) {
                callbackUrl += `&redirect=${encodeURIComponent(redirectSlug)}`;
            }
            // Add message parameter if available
            if (message) {
                callbackUrl += `&message=${encodeURIComponent(message)}`;
            }
            // Get the authorization URL but don't redirect automatically
            const { data, error } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$sharedService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["signInWithOAuth"])(supabase, provider, callbackUrl, {
                access_type: "offline",
                prompt: "select_account"
            });
            if (error) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Login failed", {
                    description: error.message
                });
                setIsSocialLoading(null);
                return;
            }
            // If we have the URL, open it in a new tab
            if (data?.url) {
                // Open the authorization URL in a new tab
                window.open(data.url, "_blank");
                // Show a toast to guide the user
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].info("Google sign-in opened in a new tab", {
                    description: "Please complete the sign-in process in the new tab.",
                    duration: 5000
                });
                // Reset loading state after a short delay
                setTimeout(()=>{
                    setIsSocialLoading(null);
                }, 1000);
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Failed to start Google sign-in", {
                    description: "Please try again or use email login."
                });
                setIsSocialLoading(null);
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred. Please try again.";
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Login failed", {
                description: errorMessage
            });
            setIsSocialLoading(null);
        }
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex justify-center mb-5 sm:mb-6",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
            variant: "outline",
            className: "cursor-pointer bg-background hover:bg-muted border-border dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:border-neutral-800 w-full py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-foreground",
            onClick: ()=>handleSocialLogin("google"),
            disabled: !!isSocialLoading || disabled,
            children: isSocialLoading === "google" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                className: "w-4 h-4 mr-2 animate-spin"
            }, void 0, false, {
                fileName: "[project]/app/(main)/login/components/SocialLoginButton.tsx",
                lineNumber: 97,
                columnNumber: 11
            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                        xmlns: "http://www.w3.org/2000/svg",
                        viewBox: "0 0 24 24",
                        className: "w-4 h-4 mr-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                fill: "#4285F4",
                                d: "M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                            }, void 0, false, {
                                fileName: "[project]/app/(main)/login/components/SocialLoginButton.tsx",
                                lineNumber: 105,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                fill: "#34A853",
                                d: "M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                            }, void 0, false, {
                                fileName: "[project]/app/(main)/login/components/SocialLoginButton.tsx",
                                lineNumber: 109,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                fill: "#FBBC05",
                                d: "M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                            }, void 0, false, {
                                fileName: "[project]/app/(main)/login/components/SocialLoginButton.tsx",
                                lineNumber: 113,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                fill: "#EA4335",
                                d: "M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                            }, void 0, false, {
                                fileName: "[project]/app/(main)/login/components/SocialLoginButton.tsx",
                                lineNumber: 117,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(main)/login/components/SocialLoginButton.tsx",
                        lineNumber: 100,
                        columnNumber: 13
                    }, this),
                    "Login with Google"
                ]
            }, void 0, true)
        }, void 0, false, {
            fileName: "[project]/app/(main)/login/components/SocialLoginButton.tsx",
            lineNumber: 90,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/(main)/login/components/SocialLoginButton.tsx",
        lineNumber: 89,
        columnNumber: 5
    }, this);
}
}}),
"[project]/lib/supabase/services/customerService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "checkIfCustomerProfileExists": (()=>checkIfCustomerProfileExists),
    "createUserProfile": (()=>createUserProfile),
    "getCustomerProfileLocation": (()=>getCustomerProfileLocation),
    "getCustomerProfilesByIds": (()=>getCustomerProfilesByIds),
    "getPublicCustomerProfileById": (()=>getPublicCustomerProfileById),
    "getPublicCustomerProfilesByIds": (()=>getPublicCustomerProfilesByIds),
    "getUserProfile": (()=>getUserProfile),
    "updateUserProfile": (()=>updateUserProfile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/constants.ts [app-ssr] (ecmascript)");
;
async function getUserProfile(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES).select("*").eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error(`Error fetching user profile: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching user profile: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function createUserProfile(supabase, profile) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES).insert([
            profile
        ]).select().single();
        if (error) {
            console.error(`Error creating user profile: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data: Array.isArray(data) ? data[0] || null : data || null,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error creating user profile: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function checkIfCustomerProfileExists(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).maybeSingle();
        if (error) {
            console.error(`Error checking existing profile: ${error.message}`);
            return {
                exists: false,
                error: "Database error checking profile."
            };
        }
        return {
            exists: !!data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error checking profile: ${err}`);
        return {
            exists: false,
            error: "An unexpected error occurred."
        };
    }
}
async function updateUserProfile(supabase, userId, updates) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES).update(updates).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).select().single();
        if (error) {
            console.error(`Error updating user profile: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error updating user profile: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getCustomerProfileLocation(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY_SLUG}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CITY_SLUG}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].STATE_SLUG}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error(`Error fetching customer profile location: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching customer profile location: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getCustomerProfilesByIds(supabase, userIds) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].EMAIL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].AVATAR_URL}`).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userIds);
        if (error) {
            console.error(`Error fetching customer profiles by IDs: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching customer profiles by IDs: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getPublicCustomerProfileById(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES_PUBLIC).select('*').eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).maybeSingle();
        if (error) {
            console.error(`Error fetching public customer profile: ${error.message}`);
            return {
                data: null,
                error: `Failed to fetch customer profile: ${error.message}`
            };
        }
        return {
            data,
            error: null
        };
    } catch (e) {
        console.error(`Exception in getPublicCustomerProfileById: ${e}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getPublicCustomerProfilesByIds(supabase, userIds) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES_PUBLIC).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].AVATAR_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT}`).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userIds);
        if (error) {
            console.error(`Error fetching public customer profiles by IDs: ${error.message}`);
            return {
                data: null,
                error: `Failed to fetch customer profiles: ${error.message}`
            };
        }
        return {
            data,
            error: null
        };
    } catch (e) {
        console.error(`Exception in getPublicCustomerProfilesByIds: ${e}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
}}),
"[project]/lib/supabase/services/businessService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "bulkUpdateProductVariants": (()=>bulkUpdateProductVariants),
    "bulkUpdateProductVariantsData": (()=>bulkUpdateProductVariantsData),
    "checkBusinessSlugUniqueness": (()=>checkBusinessSlugUniqueness),
    "checkIfBusinessProfileExists": (()=>checkIfBusinessProfileExists),
    "createBusinessProfileAtomic": (()=>createBusinessProfileAtomic),
    "deleteProductByIdAndBusinessId": (()=>deleteProductByIdAndBusinessId),
    "deleteProductVariant": (()=>deleteProductVariant),
    "deleteProductVariantById": (()=>deleteProductVariantById),
    "deleteProductVariantsByProductId": (()=>deleteProductVariantsByProductId),
    "getAdDataForPincode": (()=>getAdDataForPincode),
    "getAvailableYearsForMonthlyMetrics": (()=>getAvailableYearsForMonthlyMetrics),
    "getBusinessIdsByCity": (()=>getBusinessIdsByCity),
    "getBusinessLikes": (()=>getBusinessLikes),
    "getBusinessLikesCount": (()=>getBusinessLikesCount),
    "getBusinessLogoUrl": (()=>getBusinessLogoUrl),
    "getBusinessProfileAnalyticsData": (()=>getBusinessProfileAnalyticsData),
    "getBusinessProfileById": (()=>getBusinessProfileById),
    "getBusinessProfileCustomAds": (()=>getBusinessProfileCustomAds),
    "getBusinessProfileCustomBranding": (()=>getBusinessProfileCustomBranding),
    "getBusinessProfileForOnboarding": (()=>getBusinessProfileForOnboarding),
    "getBusinessProfileGallery": (()=>getBusinessProfileGallery),
    "getBusinessProfileIdAndStatusBySlug": (()=>getBusinessProfileIdAndStatusBySlug),
    "getBusinessProfileLocation": (()=>getBusinessProfileLocation),
    "getBusinessProfilePhone": (()=>getBusinessProfilePhone),
    "getBusinessProfileStatus": (()=>getBusinessProfileStatus),
    "getBusinessProfileSubscriptionInfo": (()=>getBusinessProfileSubscriptionInfo),
    "getBusinessProfileWithAllDetails": (()=>getBusinessProfileWithAllDetails),
    "getBusinessProfileWithInteractionMetrics": (()=>getBusinessProfileWithInteractionMetrics),
    "getBusinessProfilesByCity": (()=>getBusinessProfilesByCity),
    "getBusinessProfilesByIds": (()=>getBusinessProfilesByIds),
    "getDailyUniqueVisitTrend": (()=>getDailyUniqueVisitTrend),
    "getExistingProductVariants": (()=>getExistingProductVariants),
    "getFilteredProductVariants": (()=>getFilteredProductVariants),
    "getHourlyUniqueVisitTrend": (()=>getHourlyUniqueVisitTrend),
    "getLatestSubscription": (()=>getLatestSubscription),
    "getLatestSubscriptionStatus": (()=>getLatestSubscriptionStatus),
    "getMonthlyUniqueVisitTrend": (()=>getMonthlyUniqueVisitTrend),
    "getMonthlyUniqueVisits": (()=>getMonthlyUniqueVisits),
    "getMyLikes": (()=>getMyLikes),
    "getMyLikesCount": (()=>getMyLikesCount),
    "getPaymentSubscriptionByBusinessProfileId": (()=>getPaymentSubscriptionByBusinessProfileId),
    "getProductBusinessId": (()=>getProductBusinessId),
    "getProductById": (()=>getProductById),
    "getProductByIdAndBusinessId": (()=>getProductByIdAndBusinessId),
    "getProductCountByBusinessIds": (()=>getProductCountByBusinessIds),
    "getProductDetailsByIdAndBusinessId": (()=>getProductDetailsByIdAndBusinessId),
    "getProductsByBusinessIds": (()=>getProductsByBusinessIds),
    "getProductsForBusiness": (()=>getProductsForBusiness),
    "getProductsWithFiltersAndPagination": (()=>getProductsWithFiltersAndPagination),
    "getProductsWithVariantInfo": (()=>getProductsWithVariantInfo),
    "getPublicSubscriptionStatus": (()=>getPublicSubscriptionStatus),
    "getReviewsCountForBusiness": (()=>getReviewsCountForBusiness),
    "getRpcAvailableProductVariants": (()=>getRpcAvailableProductVariants),
    "getRpcBusinessVariantStats": (()=>getRpcBusinessVariantStats),
    "getRpcIsVariantCombinationUnique": (()=>getRpcIsVariantCombinationUnique),
    "getRpcProductWithVariants": (()=>getRpcProductWithVariants),
    "getSecureBusinessProfileBySlug": (()=>getSecureBusinessProfileBySlug),
    "getSecureBusinessProfileWithProductsBySlug": (()=>getSecureBusinessProfileWithProductsBySlug),
    "getTotalUniqueVisits": (()=>getTotalUniqueVisits),
    "getVariantDetailsWithProductBusinessId": (()=>getVariantDetailsWithProductBusinessId),
    "getVariantsByProductId": (()=>getVariantsByProductId),
    "getVariantsWithProductBusinessId": (()=>getVariantsWithProductBusinessId),
    "insertCardVisit": (()=>insertCardVisit),
    "insertMultipleProductVariants": (()=>insertMultipleProductVariants),
    "insertProduct": (()=>insertProduct),
    "insertProductVariant": (()=>insertProductVariant),
    "updateBusinessLogoUrl": (()=>updateBusinessLogoUrl),
    "updateBusinessProfile": (()=>updateBusinessProfile),
    "updateProduct": (()=>updateProduct),
    "updateProductVariant": (()=>updateProductVariant),
    "updateProductVariantData": (()=>updateProductVariantData),
    "updateProductVariantImages": (()=>updateProductVariantImages)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/constants.ts [app-ssr] (ecmascript)");
;
async function checkIfBusinessProfileExists(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).maybeSingle();
        if (error) {
            console.error(`Error checking existing business profile: ${error.message}`);
            return {
                exists: false,
                error: "Database error checking business profile."
            };
        }
        return {
            exists: !!data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error checking business profile: ${err}`);
        return {
            exists: false,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileAnalyticsData(supabase, businessProfileId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_VISITS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].TODAY_VISITS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].YESTERDAY_VISITS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].VISITS_7_DAYS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].VISITS_30_DAYS}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, businessProfileId).single();
        if (error) {
            console.error(`Error fetching business profile analytics data: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data: data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile analytics data: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getDailyUniqueVisitTrend(supabase, businessId, startDate, endDate) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_DAILY_UNIQUE_VISIT_TREND, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_PARAMS"].BUSINESS_ID]: businessId,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_PARAMS"].START_DATE]: startDate,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_PARAMS"].END_DATE]: endDate
        });
        if (error) {
            console.error(`Error fetching daily unique visit trend: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching daily unique visit trend: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getHourlyUniqueVisitTrend(supabase, businessId, targetDate) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_HOURLY_UNIQUE_VISIT_TREND, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_PARAMS"].BUSINESS_ID]: businessId,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_PARAMS"].TARGET_DATE]: targetDate
        });
        if (error) {
            console.error(`Error fetching hourly unique visit trend: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching hourly unique visit trend: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getMonthlyUniqueVisits(supabase, businessId, targetYear, targetMonth) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_MONTHLY_UNIQUE_VISITS, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_PARAMS"].BUSINESS_ID]: businessId,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_PARAMS"].TARGET_YEAR]: targetYear,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_PARAMS"].TARGET_MONTH]: targetMonth
        });
        if (error) {
            console.error(`Error fetching monthly unique visits: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching monthly unique visits: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getMonthlyUniqueVisitTrend(supabase, businessId, startYear, startMonth, endYear, endMonth) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_MONTHLY_UNIQUE_VISIT_TREND, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_PARAMS"].BUSINESS_ID]: businessId,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_PARAMS"].START_YEAR]: startYear,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_PARAMS"].START_MONTH]: startMonth,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_PARAMS"].END_YEAR]: endYear,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_PARAMS"].END_MONTH]: endMonth
        });
        if (error) {
            console.error(`Error fetching monthly unique visit trend: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching monthly unique visit trend: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getAvailableYearsForMonthlyMetrics(supabase, businessId) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_AVAILABLE_YEARS_FOR_MONTHLY_METRICS, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_PARAMS"].BUSINESS_ID]: businessId
        });
        if (error) {
            console.error(`Error fetching available years for monthly metrics: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching available years for monthly metrics: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getTotalUniqueVisits(supabase, businessId) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_TOTAL_UNIQUE_VISITS, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_PARAMS"].BUSINESS_ID]: businessId
        });
        if (error) {
            console.error(`Error fetching total unique visits: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching total unique visits: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileWithInteractionMetrics(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_LIKES}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_SUBSCRIPTIONS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].AVERAGE_RATING}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_VISITS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].TODAY_VISITS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].YESTERDAY_VISITS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].VISITS_7_DAYS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].VISITS_30_DAYS}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error(`Error fetching business profile with interaction metrics: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile with interaction metrics: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getLatestSubscription(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PAYMENT_SUBSCRIPTIONS).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PLAN_ID).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, userId).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
            ascending: false
        }).limit(1).maybeSingle();
        if (error) {
            console.error(`Error fetching latest subscription: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching latest subscription: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileCustomAds(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CUSTOM_ADS).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error(`Error fetching business profile custom ads: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data: data.custom_ads,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile custom ads: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function updateBusinessProfile(supabase, userId, updates) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).update(updates).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).select().single();
        if (error) {
            console.error(`Error updating business profile: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error updating business profile: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileCustomBranding(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CUSTOM_BRANDING).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error(`Error fetching business profile custom branding: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data: data.custom_branding,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile custom branding: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfilePhone(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PHONE).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error(`Error fetching business profile phone: ${error.message}`);
            return {
                phone: null,
                error: error.message
            };
        }
        return {
            phone: data.phone,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile phone: ${err}`);
        return {
            phone: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileWithAllDetails(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CONTACT_EMAIL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].HAS_ACTIVE_SUBSCRIPTION},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].TRIAL_END_DATE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].MEMBER_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].TITLE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ADDRESS_LINE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CITY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].STATE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PHONE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].INSTAGRAM_URL},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].FACEBOOK_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].WHATSAPP_NUMBER}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ABOUT_BIO}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_LIKES}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_SUBSCRIPTIONS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].AVERAGE_RATING}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].THEME_COLOR}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DELIVERY_INFO}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_HOURS},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_CATEGORY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CUSTOM_BRANDING}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CUSTOM_ADS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ESTABLISHED_YEAR}
      `).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error(`Error fetching business profile with all details: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile with all details: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getLatestSubscriptionStatus(supabase, userId) {
    try {
        const { data: subscription, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PAYMENT_SUBSCRIPTIONS).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PLAN_ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].SUBSCRIPTION_STATUS}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, userId).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
            ascending: false
        }).limit(1).maybeSingle();
        if (error) {
            console.error(`Error fetching latest subscription status: ${error.message}`);
            return {
                subscriptionStatus: null,
                error: error.message
            };
        }
        return {
            subscriptionStatus: subscription,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching latest subscription status: ${err}`);
        return {
            subscriptionStatus: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getSecureBusinessProfileBySlug(supabase, slug) {
    try {
        const { data: profileData, error: profileError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`
        *,
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PAYMENT_SUBSCRIPTIONS}!${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID} (
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PLAN_ID},
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].SUBSCRIPTION_STATUS}
        )
      `).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG, slug).maybeSingle();
        if (profileError) {
            console.error(`Error fetching secure business profile by slug: ${profileError.message}`);
            return {
                data: null,
                error: profileError.message
            };
        }
        if (!profileData) {
            return {
                data: null,
                error: "Profile not found."
            };
        }
        const safeData = {
            ...profileData,
            subscription_status: profileData[__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PAYMENT_SUBSCRIPTIONS]?.subscription_status || null,
            plan_id: profileData[__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PAYMENT_SUBSCRIPTIONS]?.plan_id || null
        };
        return {
            data: safeData,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching secure business profile by slug: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getSecureBusinessProfileWithProductsBySlug(supabase, slug) {
    try {
        const { data: profileData, error: profileError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`
        *,
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES} (
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DESCRIPTION}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGE_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE}
        )
      `).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG, slug).maybeSingle();
        if (profileError) {
            console.error(`Error fetching secure business profile with products by slug: ${profileError.message}`);
            return {
                data: null,
                error: profileError.message
            };
        }
        if (!profileData) {
            return {
                data: null,
                error: "Profile not found."
            };
        }
        const safeData = {
            ...profileData,
            products_services: profileData[__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES] || []
        };
        return {
            data: safeData,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching secure business profile with products by slug: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getAdDataForPincode(supabase, pincode) {
    try {
        // First, check if the custom_ad_targets table exists (for backward compatibility)
        const { count, error: tableCheckError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].CUSTOM_AD_TARGETS).select("*", {
            count: "exact",
            head: true
        });
        if (tableCheckError) {
            console.error(`Error checking custom_ad_targets table: ${tableCheckError.message}`);
            // Fallback to old approach if table check fails
            return {
                adData: null,
                error: tableCheckError.message
            };
        }
        // If the table exists and migration has been applied
        if (count !== null) {
            const { data: adData, error: adError } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_AD_FOR_PINCODE, {
                [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_PARAMS"].TARGET_PINCODE]: pincode
            });
            if (adError) {
                console.error(`Error fetching ad for pincode ${pincode}: ${adError.message}`);
                return {
                    adData: null,
                    error: adError.message
                };
            }
            return {
                adData: adData && adData.length > 0 ? adData[0] : null,
                error: null
            };
        } else {
            // Fallback to old approach if migration hasn't been applied yet
            const { data: customAd, error: customAdError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].CUSTOM_ADS).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].AD_IMAGE_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].AD_LINK_URL}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IS_ACTIVE, true).or(`targeting_locations.eq.'"global"',targeting_locations.cs.'["${pincode}"]'`).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                ascending: false
            }).limit(1).maybeSingle();
            if (customAdError) {
                console.error(`Error fetching custom ad (fallback): ${customAdError.message}`);
                return {
                    adData: null,
                    error: customAdError.message
                };
            }
            return {
                adData: customAd,
                error: null
            };
        }
    } catch (err) {
        console.error(`Unexpected error fetching ad data for pincode: ${err}`);
        return {
            adData: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductsForBusiness(supabase, businessId, limit) {
    try {
        const { data, error, count } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select("*", {
            count: "exact"
        }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessId).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE, true).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
            ascending: false
        }).limit(limit);
        if (error) {
            console.error(`Error fetching products for business ${businessId}: ${error.message}`);
            return {
                products: null,
                count: 0,
                error: error.message
            };
        }
        return {
            products: data,
            count: count || 0,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching products for business: ${err}`);
        return {
            products: null,
            count: 0,
            error: "An unexpected error occurred."
        };
    }
}
async function getReviewsCountForBusiness(supabase, businessId) {
    try {
        const { count, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].RATINGS_REVIEWS).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, {
            count: "exact",
            head: true
        }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, businessId).neq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID, businessId); // Don't count self-reviews
        if (error) {
            console.error(`Error fetching reviews count for business ${businessId}: ${error.message}`);
            return {
                count: 0,
                error: error.message
            };
        }
        return {
            count: count || 0,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching reviews count for business: ${err}`);
        return {
            count: 0,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileGallery(supabase, businessId) {
    try {
        const { data: profileData, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].GALLERY).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, businessId).single();
        if (error) {
            console.error(`Error fetching business profile gallery: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data: profileData.gallery
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile gallery: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileIdAndStatusBySlug(supabase, businessSlug) {
    try {
        const { data: business, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG, businessSlug).single();
        if (error) {
            console.error(`Error fetching business ID and status by slug: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data: business,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business ID and status by slug: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getPublicSubscriptionStatus(supabase, businessProfileId) {
    try {
        const { data: subscription, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PUBLIC_SUBSCRIPTION_STATUS).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PLAN_ID).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, businessProfileId).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
            ascending: false
        }).limit(1).maybeSingle();
        if (error) {
            console.error(`Error fetching public subscription status: ${error.message}`);
            return {
                planId: null,
                error: error.message
            };
        }
        return {
            planId: subscription?.plan_id || null,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching public subscription status: ${err}`);
        return {
            planId: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductsWithFiltersAndPagination(supabase, businessId, page = 1, sortBy = "created_desc", pageSize = 20, searchTerm, productType) {
    const offset = (page - 1) * pageSize;
    let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(`
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].NAME},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DESCRIPTION},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGE_URL},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].SLUG}
    `, {
        count: "exact"
    }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessId).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE, true);
    if (searchTerm && searchTerm.trim().length > 0) {
        query = query.ilike(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].NAME, `%${searchTerm.trim()}%`);
    }
    if (productType && productType !== "all") {
        query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE, productType);
    }
    switch(sortBy){
        case "created_asc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                ascending: true
            });
            break;
        case "updated_desc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT, {
                ascending: false
            });
            break;
        case "price_asc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE, {
                ascending: true,
                nullsFirst: false
            }).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE, {
                ascending: true,
                nullsFirst: false
            });
            break;
        case "price_desc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE, {
                ascending: false,
                nullsFirst: false
            }).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE, {
                ascending: false,
                nullsFirst: false
            });
            break;
        case "name_asc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].NAME, {
                ascending: true
            });
            break;
        case "name_desc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].NAME, {
                ascending: false
            });
            break;
        case "created_desc":
        default:
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                ascending: false
            });
            break;
    }
    query = query.range(offset, offset + pageSize - 1);
    const { data, error, count } = await query;
    if (error) {
        console.error(`Error fetching products: ${error.message}`);
        return {
            data: null,
            error: error.message,
            totalCount: 0
        };
    }
    return {
        data: data,
        error: null,
        totalCount: count || 0
    };
}
async function getBusinessProfileStatus(supabase, businessProfileId) {
    try {
        const { data: profile, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, businessProfileId).single();
        if (error) {
            console.error(`Error fetching business profile status: ${error.message}`);
            return {
                status: null,
                error: error.message
            };
        }
        return {
            status: profile.status,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile status: ${err}`);
        return {
            status: null,
            error: "An unexpected error occurred."
        };
    }
}
async function insertCardVisit(supabase, visitData) {
    try {
        const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].CARD_VISITS).insert([
            visitData
        ]);
        if (error) {
            console.error(`Error inserting card visit: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error inserting card visit: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function updateBusinessLogoUrl(supabase, userId, logoUrl) {
    const { error: updateError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).update({
        [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL]: logoUrl,
        [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT]: new Date().toISOString()
    }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId);
    if (updateError) {
        console.error("Update Business Logo URL Error:", updateError);
        return {
            success: false,
            error: `Failed to update business logo URL: ${updateError.message}`
        };
    }
    return {
        success: true
    };
}
async function getBusinessLogoUrl(supabase, userId) {
    const { data, error: fetchError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
    if (fetchError) {
        console.error("Error fetching business logo URL:", fetchError);
        return {
            logoUrl: null,
            error: "Failed to fetch business logo URL."
        };
    }
    return {
        logoUrl: data?.logo_url || null
    };
}
async function checkBusinessSlugUniqueness(supabase, slug, excludeUserId = null) {
    try {
        let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID).ilike(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG, slug);
        if (excludeUserId) {
            query = query.neq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, excludeUserId);
        }
        const { data, error } = await query.maybeSingle();
        if (error) {
            console.error(`Error checking business slug uniqueness: ${error.message}`);
            return {
                available: false,
                error: error.message
            };
        }
        return {
            available: !data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error checking business slug uniqueness: ${err}`);
        return {
            available: false,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfilesByCity(supabase, city, status, category, page, limit, sortBy, ascending) {
    try {
        const offset = (page - 1) * limit;
        let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].MEMBER_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].TITLE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ADDRESS_LINE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CITY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].STATE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PHONE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_CATEGORY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].INSTAGRAM_URL},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].FACEBOOK_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].WHATSAPP_NUMBER}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ABOUT_BIO}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].THEME_COLOR},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DELIVERY_INFO}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_LIKES}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_SUBSCRIPTIONS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].AVERAGE_RATING}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_HOURS},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].TRIAL_END_DATE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CONTACT_EMAIL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ESTABLISHED_YEAR}
        `, {
            count: "exact"
        }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CITY, city).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS, status);
        if (category && category.trim()) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_CATEGORY, category.trim());
        }
        const { data, count, error } = await query.range(offset, offset + limit - 1).order(sortBy, {
            ascending
        });
        if (error) {
            console.error(`Error fetching business profiles by city: ${error.message}`);
            return {
                data: null,
                count: null,
                error: error.message
            };
        }
        return {
            data,
            count,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profiles by city: ${err}`);
        return {
            data: null,
            count: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessIdsByCity(supabase, city, status, category) {
    try {
        let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CITY, city).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS, status);
        if (category && category.trim()) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_CATEGORY, category.trim());
        }
        const { data, error } = await query;
        if (error) {
            console.error(`Error fetching business IDs by city: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data: data.map((item)=>item.id),
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business IDs by city: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductCountByBusinessIds(supabase, businessIds, productType) {
    try {
        let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, {
            count: "exact"
        }).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessIds).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE, true);
        if (productType) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE, productType);
        }
        const { count, error } = await query;
        if (error) {
            console.error(`Error counting products by business IDs: ${error.message}`);
            return {
                count: null,
                error: error.message
            };
        }
        return {
            count,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error counting products by business IDs: ${err}`);
        return {
            count: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductsByBusinessIds(supabase, businessIds, page, limit, sortBy, ascending, productType) {
    try {
        const from = (page - 1) * limit;
        const to = from + limit - 1;
        let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(`
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DESCRIPTION}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGE_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].SLUG},
        business_profiles!${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID}(${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG})
        `).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessIds).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE, true);
        if (productType) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE, productType);
        }
        // Apply sorting based on the sortBy parameter
        if (sortBy === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE) {
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE, {
                ascending,
                nullsFirst: false
            }).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE, {
                ascending,
                nullsFirst: false
            });
        } else {
            query = query.order(sortBy, {
                ascending
            });
        }
        const { data, error } = await query.range(from, to);
        if (error) {
            console.error(`Error fetching products by business IDs: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching products by business IDs: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductByIdAndBusinessId(supabase, productId, businessId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, productId).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessId).single();
        if (error) {
            console.error(`Error fetching product by ID and business ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching product by ID and business ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getExistingProductVariants(supabase, productId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].VARIANT_VALUES}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_ID, productId);
        if (error) {
            console.error(`Error fetching existing product variants: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching existing product variants: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function insertProductVariant(supabase, variantData) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).insert(variantData).select().single();
        if (error) {
            console.error(`Error inserting product variant: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error inserting product variant: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function deleteProductVariant(supabase, variantId) {
    try {
        const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).delete().eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantId);
        if (error) {
            console.error(`Error deleting product variant: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error deleting product variant: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function updateProductVariantImages(supabase, variantId, imageUrls, featuredImageIndex) {
    try {
        const updateData = {
            images: imageUrls,
            featured_image_index: featuredImageIndex
        };
        const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).update(updateData).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantId);
        if (error) {
            console.error(`Error updating product variant images: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error updating product variant images: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function insertMultipleProductVariants(supabase, variantDataArray) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).insert(variantDataArray).select();
        if (error) {
            console.error(`Error inserting multiple product variants: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error inserting multiple product variants: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileLocation(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY_SLUG}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CITY_SLUG}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].STATE_SLUG}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error(`Error fetching business profile location: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile location: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessLikesCount(supabase, businessId) {
    const { count, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].LIKES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, {
        count: 'exact',
        head: true
    }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, businessId);
    if (error) {
        console.error('Error fetching business likes count:', error);
        throw new Error('Failed to get total count');
    }
    return count || 0;
}
async function getBusinessLikes(supabase, businessId, page, limit) {
    const from = (page - 1) * limit;
    const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].LIKES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, businessId).range(from, from + limit - 1);
    if (error) {
        console.error('Error fetching business likes:', error);
        throw new Error('Failed to fetch likes');
    }
    return data;
}
async function getMyLikesCount(supabase, businessId, searchTerm) {
    let countQuery = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].LIKES).select(`
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}!inner (
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}
      )
    `, {
        count: 'exact',
        head: true
    }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID, businessId);
    if (searchTerm) {
        countQuery = countQuery.ilike(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}.${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}`, `%${searchTerm}%`);
    }
    const { count, error } = await countQuery;
    if (error) {
        console.error('Error fetching my likes count:', error);
        throw new Error('Failed to get total count');
    }
    return count || 0;
}
async function getMyLikes(supabase, businessId, page, limit, searchTerm) {
    const from = (page - 1) * limit;
    let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].LIKES).select(`
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}!inner (
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CITY},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].STATE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ADDRESS_LINE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY}
      )
    `).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID, businessId);
    if (searchTerm) {
        query = query.ilike(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}.${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}`, `%${searchTerm}%`);
    }
    const { data, error } = await query.range(from, from + limit - 1);
    if (error) {
        console.error('Error fetching my likes:', error);
        throw new Error('Failed to fetch likes');
    }
    return data;
}
async function getBusinessProfilesByIds(supabase, userIds) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CITY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].STATE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ADDRESS_LINE}`).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userIds);
        if (error) {
            console.error(`Error fetching business profiles by IDs: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profiles by IDs: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function insertProduct(supabase, productData) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).insert(productData).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DESCRIPTION}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGE_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGES}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].FEATURED_IMAGE_INDEX}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].SLUG}`).single();
        if (error) {
            console.error(`Error inserting product: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error inserting product: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function updateProduct(supabase, productId, updates) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).update(updates).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, productId).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGE_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGES}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].FEATURED_IMAGE_INDEX}`).single();
        if (error) {
            console.error(`Error updating product: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error updating product: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductById(supabase, productId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DESCRIPTION}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGE_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGES}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].FEATURED_IMAGE_INDEX}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].SLUG}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, productId).single();
        if (error) {
            console.error(`Error fetching product by ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching product by ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getVariantsWithProductBusinessId(supabase, variantIds) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).select(`
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_ID},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES}!inner(${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID})
      `).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantIds);
        if (error) {
            console.error(`Error fetching variants with product business ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching variants with product business ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileById(supabase, businessId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, businessId).single();
        if (error) {
            console.error(`Error fetching business profile by ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile by ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileSubscriptionInfo(supabase, businessId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].HAS_ACTIVE_SUBSCRIPTION}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].TRIAL_END_DATE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, businessId).maybeSingle();
        if (error) {
            console.error(`Error fetching business profile subscription info: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile subscription info: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getPaymentSubscriptionByBusinessProfileId(supabase, businessProfileId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PAYMENT_SUBSCRIPTIONS).select("*").eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, businessProfileId).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
            ascending: false
        }).limit(1).maybeSingle();
        if (error) {
            console.error(`Error fetching payment subscription: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching payment subscription: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function bulkUpdateProductVariants(supabase, variantIds, updateData) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).update(updateData).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantIds).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID);
        if (error) {
            console.error(`Error bulk updating product variants: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error bulk updating product variants: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function updateProductVariant(supabase, variantId, updateData) {
    try {
        const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).update(updateData).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantId);
        if (error) {
            console.error(`Error updating product variant: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error updating product variant: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductDetailsByIdAndBusinessId(supabase, productId, businessId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGES}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, productId).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessId).single();
        if (error) {
            console.error(`Error fetching product details by ID and business ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching product details by ID and business ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function deleteProductByIdAndBusinessId(supabase, productId, businessId) {
    try {
        const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).delete().eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, productId).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessId);
        if (error) {
            console.error(`Error deleting product by ID and business ID: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error deleting product by ID and business ID: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function deleteProductVariantsByProductId(supabase, productId) {
    try {
        const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).delete().eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_ID, productId);
        if (error) {
            console.error(`Error deleting product variants by product ID: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error deleting product variants by product ID: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function getRpcProductWithVariants(supabase, productId) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_PRODUCT_WITH_VARIANTS, {
            product_uuid: productId
        });
        if (error) {
            console.error(`Error calling get_product_with_variants: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error in getRpcProductWithVariants: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductBusinessId(supabase, productId, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, productId).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, userId).single();
        if (error) {
            console.error(`Error fetching product business ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching product business ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getRpcAvailableProductVariants(supabase, productId) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_AVAILABLE_PRODUCT_VARIANTS, {
            product_uuid: productId
        });
        if (error) {
            console.error(`Error calling get_available_product_variants: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error in getRpcAvailableProductVariants: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getFilteredProductVariants(supabase, productId, options = {}) {
    try {
        let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).select("*", {
            count: "exact"
        }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_ID, productId);
        if (!options.includeUnavailable) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE, true);
        }
        switch(options.sortBy){
            case "created_asc":
                query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                    ascending: true
                });
                break;
            case "created_desc":
                query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                    ascending: false
                });
                break;
            case "name_asc":
                query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].VARIANT_NAME, {
                    ascending: true
                });
                break;
            case "name_desc":
                query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].VARIANT_NAME, {
                    ascending: false
                });
                break;
            case "price_asc":
                query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE, {
                    ascending: true,
                    nullsFirst: false
                });
                break;
            case "price_desc":
                query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE, {
                    ascending: false,
                    nullsFirst: true
                });
                break;
            default:
                query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                    ascending: false
                });
        }
        if (options.limit) {
            query = query.limit(options.limit);
        }
        if (options.offset) {
            query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
        }
        const { data, error: queryError, count } = await query;
        if (queryError) {
            console.error(`Error fetching filtered product variants: ${queryError.message}`);
            return {
                data: null,
                error: queryError.message,
                count: null
            };
        }
        return {
            data,
            error: null,
            count
        };
    } catch (error) {
        console.error(`Unexpected error in getFilteredProductVariants: ${error}`);
        return {
            data: null,
            error: "An unexpected error occurred.",
            count: null
        };
    }
}
async function getRpcBusinessVariantStats(supabase, businessId) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_BUSINESS_VARIANT_STATS, {
            business_uuid: businessId
        });
        if (error) {
            console.error(`Error calling get_business_variant_stats: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error in getRpcBusinessVariantStats: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getRpcIsVariantCombinationUnique(supabase, productId, variantValues, excludeVariantId) {
    try {
        const { data: result, error: functionError } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].IS_VARIANT_COMBINATION_UNIQUE, {
            product_uuid: productId,
            variant_vals: variantValues,
            exclude_variant_id: excludeVariantId || undefined
        });
        if (functionError) {
            console.error(`Error calling is_variant_combination_unique: ${functionError.message}`);
            return {
                isUnique: undefined,
                error: functionError.message
            };
        }
        return {
            isUnique: result || undefined,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error in getRpcIsVariantCombinationUnique: ${err}`);
        return {
            isUnique: undefined,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductsWithVariantInfo(supabase, userId, page = 1, limit = 10, filters = {}, sortBy = "created_desc") {
    const offset = (page - 1) * limit;
    let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(`
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].NAME},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DESCRIPTION},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGE_URL},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGES},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].FEATURED_IMAGE_INDEX},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].SLUG},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS}(${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE})
    `, {
        count: "exact"
    }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, userId);
    // Apply Filters
    if (filters.searchTerm) query = query.or(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}.ilike.%${filters.searchTerm}%,${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DESCRIPTION}.ilike.%${filters.searchTerm}%`);
    if (filters.hasVariants !== undefined) {
        if (filters.hasVariants) {
            // Only products that have variants
            query = query.not(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS, "is", null);
        } else {
            // Only products that don't have variants
            query = query.is(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS, null);
        }
    }
    if (filters.productType) query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE, filters.productType);
    // Apply Sorting
    switch(sortBy){
        case "created_asc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                ascending: true
            });
            break;
        case "price_asc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE, {
                ascending: true,
                nullsFirst: false
            }).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE, {
                ascending: true,
                nullsFirst: false
            });
            break;
        case "price_desc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE, {
                ascending: false,
                nullsFirst: false
            }).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE, {
                ascending: false,
                nullsFirst: false
            });
            break;
        case "name_asc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].NAME, {
                ascending: true
            });
            break;
        case "name_desc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].NAME, {
                ascending: false
            });
            break;
        case "available_first":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE, {
                ascending: false
            }).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                ascending: false
            });
            break;
        case "unavailable_first":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE, {
                ascending: true
            }).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                ascending: false
            });
            break;
        case "created_desc":
        case "variant_count_asc":
        case "variant_count_desc":
        default:
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                ascending: false
            });
            break;
    }
    query = query.range(offset, offset + limit - 1);
    const { data, error, count } = await query;
    if (error) {
        console.error("Fetch Products Error:", error);
        return {
            data: null,
            error: error.message,
            count: null
        };
    }
    return {
        data,
        count,
        error: null
    };
}
async function getVariantDetailsWithProductBusinessId(supabase, variantId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).select(`
        *,
        products_services!inner(business_id)
      `).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantId).single();
        if (error) {
            console.error(`Error fetching variant details by ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching variant details by ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function updateProductVariantData(supabase, variantId, updateData) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).update(updateData).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantId).select().single();
        if (error) {
            console.error(`Error updating product variant data: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error updating product variant data: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function bulkUpdateProductVariantsData(supabase, updates) {
    try {
        const updatePromises = updates.map((update)=>supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).update(update.data).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, update.id));
        const results = await Promise.all(updatePromises);
        const errors = results.filter((result)=>result.error).map((result)=>result.error?.message);
        if (errors.length > 0) {
            console.error(`Errors during bulk update of product variants: ${errors.join(", ")}`);
            return {
                success: false,
                error: `Failed to update some variants: ${errors.join(", ")}`
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error during bulk update of product variants: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function getVariantsByProductId(supabase, productId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].VARIANT_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGES}, products_services!inner(${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID}))`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_ID, productId);
        if (error) {
            console.error(`Error fetching variants by product ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        // Explicitly map the data to the correct type
        const typedData = data.map((item)=>({
                ...item,
                products_services: item.products_services
            }));
        return {
            data: typedData,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching variants by product ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileForOnboarding(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CONTACT_EMAIL},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].MEMBER_NAME},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].TITLE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PHONE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_CATEGORY},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ADDRESS_LINE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].CITY},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].STATE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS}
      `).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).maybeSingle();
        if (error) {
            console.error(`Error fetching business profile for onboarding: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile for onboarding: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function createBusinessProfileAtomic(supabase, businessData, subscriptionData) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].CREATE_BUSINESS_PROFILE_ATOMIC, {
            p_business_data: businessData,
            p_subscription_data: subscriptionData
        });
        if (error) {
            console.error(`Error calling create_business_profile_atomic: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error in createBusinessProfileAtomic: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function deleteProductVariantById(supabase, variantId) {
    try {
        const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).delete().eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantId);
        if (error) {
            console.error(`Error deleting product variant by ID: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error deleting product variant by ID: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
}}),
"[project]/utils/supabase/data:7d41f3 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"00c2d9624329cded09d08193b88cb56d91c74b75b9":"createClient"},"utils/supabase/server.ts",""] */ __turbopack_context__.s({
    "createClient": (()=>createClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var createClient = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("00c2d9624329cded09d08193b88cb56d91c74b75b9", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "createClient"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/lib/actions/redirectAfterLogin.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Shared utility to determine the correct post-login redirect path for a user.
 * Checks both customer_profiles and business_profiles, and returns the appropriate dashboard, onboarding, or choose-role path.
 * Returns "/" as a fallback in case of errors.
 */ __turbopack_context__.s({
    "getPostLoginRedirectPath": (()=>getPostLoginRedirectPath)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$customerService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/services/customerService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/services/businessService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$data$3a$7d41f3__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/utils/supabase/data:7d41f3 [app-ssr] (ecmascript) <text/javascript>");
;
;
;
async function getPostLoginRedirectPath(userId) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$data$3a$7d41f3__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["createClient"])();
    try {
        // Check both profiles concurrently
        const [customerRes, businessRes] = await Promise.all([
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$customerService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["checkIfCustomerProfileExists"])(supabase, userId),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["checkIfBusinessProfileExists"])(supabase, userId)
        ]);
        if (customerRes.error || businessRes.error) {
            console.error("[redirectAfterLogin] Supabase query error:", customerRes.error, businessRes.error);
            return "/?view=home";
        }
        if (customerRes.exists) {
            return "/dashboard/customer";
        }
        if (businessRes.exists) {
            // Need to fetch the business_slug separately as checkIfBusinessProfileExists only returns a boolean
            // This means we need a new function in businessService to get the business profile with slug
            // For now, I'll assume if it exists, it's onboarded, and will create a new task to address this.
            return "/dashboard/business";
        }
        return "/choose-role";
    } catch (err) {
        console.error("[redirectAfterLogin] Unexpected error:", err);
        return "/?view=home";
    }
}
}}),
"[project]/app/(main)/login/LoginForm.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "LoginForm": (()=>LoginForm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/card.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$data$3a$547167__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/app/(main)/login/data:547167 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$data$3a$d4c5c2__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/app/(main)/login/data:d4c5c2 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$data$3a$64a4f2__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/app/(main)/login/data:64a4f2 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$components$2f$EmailOTPForm$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/login/components/EmailOTPForm.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$components$2f$MobilePasswordForm$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/login/components/MobilePasswordForm.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$components$2f$AuthMethodToggle$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/login/components/AuthMethodToggle.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$components$2f$SocialLoginButton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/login/components/SocialLoginButton.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$sharedService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/services/sharedService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$redirectAfterLogin$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/redirectAfterLogin.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
function LoginForm() {
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSearchParams"])();
    const [isPending, startTransition] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTransition"])();
    const [redirectSlug, setRedirectSlug] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [message, setMessage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [authMethod, setAuthMethod] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('email-otp');
    const [step, setStep] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('email');
    const [email, setEmail] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [countdown, setCountdown] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    // Get the redirect and message parameters from the URL
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const redirect = searchParams.get("redirect");
        if (redirect) {
            setRedirectSlug(redirect);
        }
        const messageParam = searchParams.get("message");
        if (messageParam) {
            setMessage(messageParam);
        }
    }, [
        searchParams
    ]);
    // Countdown timer for resend OTP
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (countdown > 0) {
            const timer = setTimeout(()=>setCountdown(countdown - 1), 1000);
            return ()=>clearTimeout(timer);
        }
    }, [
        countdown
    ]);
    // Helper function to handle post-login redirect
    async function handlePostLoginRedirect() {
        try {
            const supabase = await createClient();
            const { user } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$sharedService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAuthenticatedUser"])(supabase);
            if (!user) {
                console.error("No user found after login");
                router.push("/?view=home");
                return;
            }
            const redirectPath = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$redirectAfterLogin$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getPostLoginRedirectPath"])(user.id);
            // Handle redirect logic
            if (redirectSlug) {
                // IMPORTANT: Prevent open redirect vulnerabilities.
                // Ensure the redirectSlug is a relative path and not an external URL.
                if (redirectSlug.includes('://') || redirectSlug.startsWith('//')) {
                    console.warn('Attempted redirect to an external or malformed URL. Redirecting to default path.');
                    router.push(redirectPath);
                    return;
                }
                // If it's a relative path, we can proceed.
                // The application's routing will handle the validity of the path (e.g., 404 for invalid slugs).
                router.push(`/${redirectSlug}${message ? `?message=${encodeURIComponent(message)}` : ""}`);
            } else {
                router.push(redirectPath);
            }
        } catch (error) {
            console.error("Error determining redirect path:", error);
            router.push("/?view=home");
        }
    }
    function onEmailSubmit(values) {
        startTransition(async ()=>{
            try {
                const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$data$3a$547167__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["sendOTP"])(values);
                if (!result.success) {
                    // Check if this is a configuration error (email rate limit)
                    if ('isConfigurationError' in result && result.isConfigurationError) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Configuration Error", {
                            description: result.error,
                            duration: 10000
                        });
                        // Don't proceed to OTP step for configuration errors
                        return;
                    }
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Failed to send OTP", {
                        description: result.error
                    });
                    return;
                }
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("OTP sent!", {
                    description: result.message
                });
                setEmail(values.email);
                setStep('otp');
                setCountdown(60); // 60 second countdown (Supabase rate limit)
            } catch (_error) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Failed to send OTP", {
                    description: "An unexpected error occurred. Please try again."
                });
            }
        });
    }
    function onOTPSubmit(values) {
        startTransition(async ()=>{
            try {
                const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$data$3a$d4c5c2__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["verifyOTP"])({
                    email: values.email,
                    otp: values.otp
                });
                if (!result.success) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("OTP verification failed", {
                        description: result.error
                    });
                    return;
                }
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("Sign in successful!", {
                    description: "Redirecting to your dashboard..."
                });
                // Use proper post-login redirect logic
                await handlePostLoginRedirect();
            } catch (_error) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("OTP verification failed", {
                    description: "An unexpected error occurred. Please try again."
                });
            }
        });
    }
    function handleResendOTP() {
        if (countdown > 0) return;
        // Use the same logic as onEmailSubmit for resending
        startTransition(async ()=>{
            try {
                const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$data$3a$547167__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["sendOTP"])({
                    email
                });
                if (!result.success) {
                    // Check if this is a configuration error (email rate limit)
                    if ('isConfigurationError' in result && result.isConfigurationError) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Configuration Error", {
                            description: result.error,
                            duration: 10000
                        });
                        return;
                    }
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Failed to resend OTP", {
                        description: result.error
                    });
                    return;
                }
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("OTP resent!", {
                    description: result.message
                });
                setCountdown(60); // Reset countdown
            } catch (_error) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Failed to resend OTP", {
                    description: "An unexpected error occurred. Please try again."
                });
            }
        });
    }
    function handleBackToEmail() {
        setStep('email');
        setEmail('');
    }
    function onMobilePasswordSubmit(values) {
        startTransition(async ()=>{
            try {
                const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$data$3a$64a4f2__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["loginWithMobilePassword"])(values);
                if (!result.success) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Login failed", {
                        description: result.error
                    });
                    return;
                }
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("Sign in successful!", {
                    description: "Redirecting to your dashboard..."
                });
                // Use proper post-login redirect logic
                await handlePostLoginRedirect();
            } catch (_error) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Login failed", {
                    description: "An unexpected error occurred. Please try again."
                });
            }
        });
    }
    function handleAuthMethodChange(method) {
        if (method !== authMethod) {
            setAuthMethod(method);
            setStep('email');
        // Don't reset forms - let each component manage its own state
        }
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full max-w-[90%] sm:max-w-md md:max-w-lg",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
            initial: {
                opacity: 0,
                scale: 0.95
            },
            animate: {
                opacity: 1,
                scale: 1
            },
            transition: {
                duration: 0.6,
                delay: 0.2
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                className: "bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-4 sm:p-6 md:p-8 rounded-xl sm:rounded-2xl shadow-lg dark:shadow-[var(--brand-gold)]/10",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center mb-6 sm:mb-8",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-xl sm:text-2xl font-bold text-foreground mb-1 sm:mb-2",
                                children: authMethod === 'email-otp' && step === 'otp' ? 'Enter Verification Code' : 'Welcome to Dukancard'
                            }, void 0, false, {
                                fileName: "[project]/app/(main)/login/LoginForm.tsx",
                                lineNumber: 235,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm sm:text-base text-muted-foreground",
                                children: authMethod === 'email-otp' && step === 'otp' ? 'Check your email for the 6-digit code' : authMethod === 'email-otp' ? 'Sign in or create your account with email' : 'Sign in with your mobile number and password'
                            }, void 0, false, {
                                fileName: "[project]/app/(main)/login/LoginForm.tsx",
                                lineNumber: 238,
                                columnNumber: 15
                            }, this),
                            message && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `mt-4 p-2 sm:p-3 rounded-lg ${message.toLowerCase().includes("error") || message.toLowerCase().includes("failed") ? "bg-destructive/10 text-destructive" : "bg-green-500/10 text-green-600 dark:text-green-400"}`,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-xs sm:text-sm",
                                    children: message
                                }, void 0, false, {
                                    fileName: "[project]/app/(main)/login/LoginForm.tsx",
                                    lineNumber: 253,
                                    columnNumber: 19
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/(main)/login/LoginForm.tsx",
                                lineNumber: 248,
                                columnNumber: 17
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(main)/login/LoginForm.tsx",
                        lineNumber: 234,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$components$2f$AuthMethodToggle$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthMethodToggle"], {
                        authMethod: authMethod,
                        step: step,
                        onMethodChange: handleAuthMethodChange
                    }, void 0, false, {
                        fileName: "[project]/app/(main)/login/LoginForm.tsx",
                        lineNumber: 259,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$components$2f$SocialLoginButton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SocialLoginButton"], {
                        redirectSlug: redirectSlug,
                        message: message,
                        disabled: isPending
                    }, void 0, false, {
                        fileName: "[project]/app/(main)/login/LoginForm.tsx",
                        lineNumber: 266,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "relative mb-5 sm:mb-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute inset-0 flex items-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-full border-t border-border"
                                }, void 0, false, {
                                    fileName: "[project]/app/(main)/login/LoginForm.tsx",
                                    lineNumber: 274,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/(main)/login/LoginForm.tsx",
                                lineNumber: 273,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "relative flex justify-center text-xs uppercase",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "bg-card px-2 text-muted-foreground",
                                    children: authMethod === 'email-otp' && step === 'email' ? 'Or continue with email' : authMethod === 'mobile-password' ? 'Or continue with mobile' : 'Or use Google instead'
                                }, void 0, false, {
                                    fileName: "[project]/app/(main)/login/LoginForm.tsx",
                                    lineNumber: 277,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/(main)/login/LoginForm.tsx",
                                lineNumber: 276,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(main)/login/LoginForm.tsx",
                        lineNumber: 272,
                        columnNumber: 13
                    }, this),
                    authMethod === 'email-otp' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$components$2f$EmailOTPForm$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmailOTPForm"], {
                        step: step,
                        email: email,
                        countdown: countdown,
                        isPending: isPending,
                        onEmailSubmit: onEmailSubmit,
                        onOTPSubmit: onOTPSubmit,
                        onResendOTP: handleResendOTP,
                        onBackToEmail: handleBackToEmail
                    }, void 0, false, {
                        fileName: "[project]/app/(main)/login/LoginForm.tsx",
                        lineNumber: 287,
                        columnNumber: 15
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$components$2f$MobilePasswordForm$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MobilePasswordForm"], {
                        isPending: isPending,
                        onSubmit: onMobilePasswordSubmit
                    }, void 0, false, {
                        fileName: "[project]/app/(main)/login/LoginForm.tsx",
                        lineNumber: 298,
                        columnNumber: 15
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(main)/login/LoginForm.tsx",
                lineNumber: 233,
                columnNumber: 11
            }, this)
        }, void 0, false, {
            fileName: "[project]/app/(main)/login/LoginForm.tsx",
            lineNumber: 228,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/(main)/login/LoginForm.tsx",
        lineNumber: 227,
        columnNumber: 5
    }, this);
}
}}),
"[project]/app/(main)/components/auth/AuthPageBackground.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>AuthPageBackground)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$animation$2f$hooks$2f$use$2d$animation$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/animation/hooks/use-animation.mjs [app-ssr] (ecmascript)");
"use client";
;
;
;
function AuthPageBackground() {
    const [isClient, setIsClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isMobile, setIsMobile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Animation controls
    const gradientControls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$animation$2f$hooks$2f$use$2d$animation$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAnimation"])();
    const blob1Controls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$animation$2f$hooks$2f$use$2d$animation$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAnimation"])();
    const blob2Controls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$animation$2f$hooks$2f$use$2d$animation$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAnimation"])();
    const blob3Controls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$animation$2f$hooks$2f$use$2d$animation$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAnimation"])();
    const lineControls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$animation$2f$hooks$2f$use$2d$animation$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAnimation"])();
    const nodeControls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$animation$2f$hooks$2f$use$2d$animation$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAnimation"])();
    // Only render on client side and detect mobile
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setIsClient(true);
        setIsMobile(window.innerWidth < 768);
        const handleResize = ()=>{
            setIsMobile(window.innerWidth < 768);
        };
        window.addEventListener("resize", handleResize);
        return ()=>window.removeEventListener("resize", handleResize);
    }, []);
    // Setup animations
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isClient) {
            // Gradient animation
            gradientControls.start({
                scale: 1.1,
                transition: {
                    duration: 8,
                    repeat: Infinity,
                    repeatType: "reverse",
                    ease: "easeInOut"
                }
            });
            // Blob animations
            blob1Controls.start({
                scale: 1.2,
                x: 20,
                transition: {
                    duration: 10,
                    repeat: Infinity,
                    repeatType: "reverse",
                    ease: "easeInOut"
                }
            });
            blob2Controls.start({
                scale: 1.15,
                x: -15,
                transition: {
                    duration: 12,
                    repeat: Infinity,
                    repeatType: "reverse",
                    ease: "easeInOut"
                }
            });
            blob3Controls.start({
                scale: 1.25,
                y: 25,
                transition: {
                    duration: 14,
                    repeat: Infinity,
                    repeatType: "reverse",
                    ease: "easeInOut"
                }
            });
            // Line animation
            lineControls.start({
                scale: 1.1,
                transition: {
                    duration: 6,
                    repeat: Infinity,
                    repeatType: "reverse",
                    ease: "easeInOut"
                }
            });
            // Node animation
            nodeControls.start({
                scale: 1.3,
                transition: {
                    duration: 4,
                    repeat: Infinity,
                    repeatType: "reverse",
                    ease: "easeInOut"
                }
            });
        }
    }, [
        isClient,
        gradientControls,
        blob1Controls,
        blob2Controls,
        blob3Controls,
        lineControls,
        nodeControls
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "absolute inset-0 -z-10 overflow-hidden",
        children: isClient && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                    animate: gradientControls,
                    className: "absolute inset-0 opacity-40 dark:opacity-30",
                    style: {
                        background: `radial-gradient(circle at 50% 50%,
                var(--brand-gold) 0%,
                rgba(var(--brand-gold-rgb), 0.3) 25%,
                rgba(var(--brand-gold-rgb), 0.1) 50%,
                rgba(0, 0, 255, 0.1) 75%,
                rgba(0, 0, 255, 0.05) 100%)`,
                        filter: isMobile ? "blur(60px)" : "blur(80px)"
                    }
                }, void 0, false, {
                    fileName: "[project]/app/(main)/components/auth/AuthPageBackground.tsx",
                    lineNumber: 108,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                    animate: blob1Controls,
                    className: "absolute top-0 right-0 w-[500px] h-[500px] rounded-full bg-[var(--brand-gold-rgb)]/5 blur-3xl dark:bg-[var(--brand-gold-rgb)]/10 opacity-70"
                }, void 0, false, {
                    fileName: "[project]/app/(main)/components/auth/AuthPageBackground.tsx",
                    lineNumber: 123,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                    animate: blob2Controls,
                    className: "absolute -top-20 -left-20 w-[300px] h-[300px] rounded-full bg-blue-500/5 blur-3xl dark:bg-blue-500/10 opacity-70"
                }, void 0, false, {
                    fileName: "[project]/app/(main)/components/auth/AuthPageBackground.tsx",
                    lineNumber: 129,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].div, {
                    animate: blob3Controls,
                    className: "absolute bottom-0 left-1/4 w-[400px] h-[400px] rounded-full bg-purple-500/5 blur-3xl dark:bg-purple-500/10 opacity-60"
                }, void 0, false, {
                    fileName: "[project]/app/(main)/components/auth/AuthPageBackground.tsx",
                    lineNumber: 135,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                    className: "absolute inset-0 w-full h-full opacity-10 dark:opacity-20 pointer-events-none",
                    xmlns: "http://www.w3.org/2000/svg",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("filter", {
                                id: "glow",
                                x: "-50%",
                                y: "-50%",
                                width: "200%",
                                height: "200%",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("feGaussianBlur", {
                                        stdDeviation: "2",
                                        result: "blur"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(main)/components/auth/AuthPageBackground.tsx",
                                        lineNumber: 147,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("feComposite", {
                                        in: "SourceGraphic",
                                        in2: "blur",
                                        operator: "over",
                                        result: "glow"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(main)/components/auth/AuthPageBackground.tsx",
                                        lineNumber: 148,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(main)/components/auth/AuthPageBackground.tsx",
                                lineNumber: 146,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/(main)/components/auth/AuthPageBackground.tsx",
                            lineNumber: 145,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].line, {
                            animate: lineControls,
                            x1: "20%",
                            y1: "20%",
                            x2: "80%",
                            y2: "80%",
                            stroke: "var(--brand-gold)",
                            strokeWidth: "0.5",
                            strokeOpacity: "0.3",
                            filter: "url(#glow)"
                        }, void 0, false, {
                            fileName: "[project]/app/(main)/components/auth/AuthPageBackground.tsx",
                            lineNumber: 158,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["motion"].circle, {
                            animate: nodeControls,
                            cx: "50%",
                            cy: "50%",
                            r: "2",
                            fill: "var(--brand-gold)",
                            filter: "url(#glow)"
                        }, void 0, false, {
                            fileName: "[project]/app/(main)/components/auth/AuthPageBackground.tsx",
                            lineNumber: 171,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/(main)/components/auth/AuthPageBackground.tsx",
                    lineNumber: 141,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true)
    }, void 0, false, {
        fileName: "[project]/app/(main)/components/auth/AuthPageBackground.tsx",
        lineNumber: 104,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=_fb3890d8._.js.map