"use server";


import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
import {
  getProductByIdAndBusinessId,
  getExistingProductVariants,
  insertProductVariant,
  deleteProductVariant,
  updateProductVariantImages,
  insertMultipleProductVariants,
} from "@/lib/supabase/services/businessService";
import { revalidatePath } from "next/cache";
import { addVariantFormSchema, VariantData } from "./schemas";
import { handleVariantImageUpload } from "./imageHandlers";

import { Tables, TablesInsert } from "@/types/supabase";
import { createClient } from "@/utils/supabase/server";
import { PostgrestError } from "@supabase/supabase-js";



// Add a new product variant
export async function addProductVariant(
  formData: FormData
): Promise<{ success: boolean; error?: string; data?: VariantData }> {
  const supabase = await createClient();
  const { user, error: authError } = await getAuthenticatedUser(supabase);
  if (authError || !user) {
    return { success: false, error: authError || "User not authenticated." };
  }

  try {
    // Extract form values
    const product_id = formData.get("product_id") as string;
    const variant_name = formData.get("variant_name") as string;
    const variant_values_json = formData.get("variant_values") as string;
    const base_price = formData.get("base_price") as string;
    const discounted_price = formData.get("discounted_price") as string;
    const is_available = formData.get("is_available") === "true";
    const featured_image_index = parseInt(formData.get("featured_image_index") as string) || 0;

    // Parse variant values JSON
    let variant_values: Record<string, string>;
    try {
      variant_values = JSON.parse(variant_values_json || "{}");
    } catch (_error) {
      return { success: false, error: "Invalid variant values format." };
    }

    // Prepare form values for validation
    const formValues = {
      product_id,
      variant_name,
      variant_values,
      base_price: base_price && base_price.trim() !== '' && base_price !== 'undefined' ? parseFloat(base_price) : null,
      discounted_price: discounted_price && discounted_price.trim() !== '' && discounted_price !== 'undefined' ? parseFloat(discounted_price) : null,
      is_available,
      featured_image_index,
    };

    // Validate form data
    const validatedFields = addVariantFormSchema.safeParse(formValues);
    if (!validatedFields.success) {
      console.error(
        "Add Variant Validation Error:",
        validatedFields.error.flatten().fieldErrors
      );
      const errors = validatedFields.error.flatten().fieldErrors;
      const errorMessages = Object.entries(errors)
        .map(
          ([field, messages]) =>
            `${field}: ${
              Array.isArray(messages) ? messages.join(", ") : messages
            }`
        )
        .join("; ");
      return { success: false, error: `Invalid data: ${errorMessages}` };
    }

    // Verify that the product belongs to the authenticated user
    const { data: product, error: productError } = await getProductByIdAndBusinessId(supabase, product_id, user.id);

    if (productError || !product) {
      return { success: false, error: "Product not found or access denied." };
    }

    // Check if variant combination already exists
    // Use a more robust JSONB comparison
    const { data: existingVariants, error: checkError } = await getExistingProductVariants(supabase, product_id);

    if (checkError) {
      console.error("Error checking existing variants:", checkError);
      return { success: false, error: "Failed to validate variant uniqueness." };
    }

    // Check for duplicate variant combinations manually
    if (existingVariants && existingVariants.length > 0) {
      const duplicateVariant = existingVariants.find((existing) => {
        // Compare the variant_values objects
        const existingValues = typeof existing.variant_values === 'string'
          ? JSON.parse(existing.variant_values)
          : existing.variant_values;

        // Deep comparison of variant values
        const existingKeys = Object.keys(existingValues).sort();
        const newKeys = Object.keys(variant_values).sort();

        if (existingKeys.length !== newKeys.length) return false;

        return existingKeys.every(key =>
          newKeys.includes(key) && existingValues[key] === variant_values[key]
        );
      });

      if (duplicateVariant) {
        return { success: false, error: "A variant with this combination already exists." };
      }
    }

    // Handle image uploads
    let imageUrls: string[] = [];
    const imageFiles: File[] = [];

    // Debug: Log all FormData entries to see what keys are being sent
    console.log("=== AddVariant FormData Debug ===");
    for (const [key, value] of formData.entries()) {
      console.log(`FormData key: ${key}, value type: ${typeof value}, value:`, value);
      if (key.startsWith("images[") && value instanceof File && value.size > 0) {
        console.log(`Found image file: ${key}, size: ${value.size}, name: ${value.name}`);
        imageFiles.push(value);
      }
    }
    console.log(`Total image files extracted: ${imageFiles.length}`);
    console.log("=== End AddVariant FormData Debug ===");

    // First, create the variant without images to get the variant ID
    const variantData: TablesInsert<'product_variants'> = {
      product_id,
      variant_name,
      variant_values: variant_values as any,
      base_price: validatedFields.data.base_price ?? null,
      discounted_price: validatedFields.data.discounted_price ?? null,
      is_available,
      images: [] as any,
      featured_image_index: validatedFields.data.featured_image_index ?? 0,
    };

    const { data: newVariant, error: insertError }: { data: Tables<'product_variants'> | null; error: string | PostgrestError | null } = await insertProductVariant(supabase, variantData);

    if (insertError || !newVariant) {
      console.error("Error inserting variant:", insertError);
      return { success: false, error: `Failed to create variant: ${typeof insertError === 'object' && insertError !== null && 'message' in insertError ? (insertError as any).message : insertError || "Unknown error"}` };
    }

    // Now handle image uploads if any
    if (imageFiles.length > 0) {
      console.log(`Uploading ${imageFiles.length} images for variant ${newVariant.id!} in product ${product_id}`);

      const uploadResult = await handleVariantImageUpload(
        user.id,
        product_id,
        newVariant.id!,
        imageFiles
      );

      if (uploadResult.error) {
        console.error("Image upload failed:", uploadResult.error);
        // If image upload fails, delete the variant we just created
        await deleteProductVariant(supabase, newVariant.id as string);
        return { success: false, error: uploadResult.error || "Failed to upload images." };
      }

      imageUrls = uploadResult.urls || [];
      console.log(`Successfully uploaded ${imageUrls.length} images:`, imageUrls);

      // Update the variant with the uploaded images
      const updateData = {
        images: imageUrls,
        featured_image_index: Math.min(featured_image_index, Math.max(0, imageUrls.length - 1)),
      };

      console.log(`Updating variant ${newVariant.id!} with:`, updateData);

      const { success: _updateSuccess, error: updateError } = await updateProductVariantImages(
        supabase,
        newVariant.id! as string,
        imageUrls,
        Math.min(featured_image_index, Math.max(0, imageUrls.length - 1))
      );

      if (updateError) {
        console.error("Error updating variant with images:", updateError);
        return { success: false, error: "Failed to update variant with images." };
      }

      console.log("Successfully updated variant with images");
    }

    // Prepare final variant data
    const finalVariant = {
      ...newVariant,
      images: imageUrls,
      featured_image_index: imageUrls.length > 0 ? Math.min(featured_image_index, Math.max(0, imageUrls.length - 1)) : 0,
      is_available: newVariant.is_available ?? false, // Ensure is_available is always boolean
    };

    // Revalidate the products page
    revalidatePath("/dashboard/business/products");
    revalidatePath(`/dashboard/business/products/${product_id}`);

    return {
      success: true,
      data: {
        ...finalVariant,
        variant_values: typeof finalVariant.variant_values === 'string'
          ? JSON.parse(finalVariant.variant_values)
          : finalVariant.variant_values,
        created_at: new Date(finalVariant.created_at as string),
        updated_at: new Date(finalVariant.updated_at as string),
      },
    };
  } catch (error) {
    console.error("Unexpected error in addProductVariant:", error);
    return { success: false, error: "An unexpected error occurred." };
  }
}

// Bulk add variants (for generating multiple combinations)
export async function addMultipleVariants(
  product_id: string,
  variants: Array<{
    variant_name: string;
    variant_values: Record<string, string>;
    base_price?: number;
    discounted_price?: number;
    is_available?: boolean;
  }>
): Promise<{ success: boolean; error?: string; data?: VariantData[]; failed_count?: number }> {
  const supabase = await createClient();
  const { user, error: authError } = await getAuthenticatedUser(supabase);
  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  try {
    // Verify that the product belongs to the authenticated user
    const { data: product, error: productError } = await getProductByIdAndBusinessId(supabase, product_id, user.id);

    if (productError || !product) {
      return { success: false, error: "Product not found or access denied." };
    }

    // Prepare variant data for bulk insertion
    const variantDataArray = variants.map(variant => ({
      product_id,
      variant_name: variant.variant_name,
      variant_values: JSON.stringify(variant.variant_values) as any,
      base_price: variant.base_price || null,
      discounted_price: variant.discounted_price || null,
      is_available: variant.is_available ?? true,
      images: [] as any,
      featured_image_index: (variant as any).featured_image_index !== undefined ? (variant as any).featured_image_index : null,
    }));

    // Insert variants in bulk
    const { data: newVariants, error: insertError } = await insertMultipleProductVariants(supabase, variantDataArray);

    if (insertError) {
      console.error("Error inserting variants:", insertError);
      return { success: false, error: "Failed to create variants." };
    }

    // Revalidate the products page
    revalidatePath("/dashboard/business/products");
    revalidatePath(`/dashboard/business/products/${product_id}`);

    return {
      success: true,
      data: newVariants?.map((variant) => ({
        ...variant,
        variant_values: typeof variant.variant_values === 'string'
          ? JSON.parse(variant.variant_values)
          : variant.variant_values,
        created_at: new Date(variant.created_at as string),
        updated_at: new Date(variant.updated_at as string),
      })) || [],
    };
  } catch (error) {
    console.error("Unexpected error in addMultipleVariants:", error);
    return { success: false, error: "An unexpected error occurred." };
  }
}

// Generate variant combinations from variant types and values
export async function generateVariantCombinations(
  product_id: string,
  variant_types_values: Record<string, string[]>
): Promise<{ success: boolean; error?: string; combinations?: Array<{ variant_name: string; variant_values: Record<string, string> }> }> {
  try {
    // Validate input
    if (!product_id || !variant_types_values || Object.keys(variant_types_values).length === 0) {
      return { success: false, error: "Invalid input parameters." };
    }

    if (Object.keys(variant_types_values).length > 5) {
      return { success: false, error: "Maximum of 5 variant types allowed per product." };
    }

    // Generate all possible combinations
    const keys = Object.keys(variant_types_values);
    const values = keys.map(key => variant_types_values[key]);
    
    function cartesianProduct(arrays: string[][]): string[][] {
      return arrays.reduce((acc, curr) => 
        acc.flatMap(a => curr.map(c => [...a, c])), 
        [[]] as string[][]
      );
    }

    const combinations = cartesianProduct(values);
    
    const result = combinations.map(combination => {
      const variant_values: Record<string, string> = {};
      keys.forEach((key, index) => {
        variant_values[key] = combination[index];
      });
      
      const variant_name = combination.join(' ');
      
      return {
        variant_name,
        variant_values,
      };
    });

    return {
      success: true,
      combinations: result,
    };
  } catch (error) {
    console.error("Error generating variant combinations:", error);
    return { success: false, error: "Failed to generate combinations." };
  }
}