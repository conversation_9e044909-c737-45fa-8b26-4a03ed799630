/**
 * CENTRALIZED SUBSCRIPTION SERVICE
 * 
 * This service provides a unified interface for all subscription-related operations.
 * All components, actions, and webhook handlers should use this service to ensure consistency.
 */

import { createClient } from "@/utils/supabase/server";
import { SupabaseClient } from "@supabase/supabase-js";
import {
  SubscriptionStateManager
} from "@/lib/razorpay/webhooks/handlers/subscription-state-manager";
import {
  SUBSCRIPTION_STATUS,
  type SubscriptionStatus
} from "@/lib/razorpay/webhooks/handlers/subscription-constants";
import { getPaymentSubscriptionByBusinessProfileId, getBusinessProfileSubscriptionInfo } from "@/lib/supabase/services/businessService";

export interface SubscriptionInfo {
  id: string;
  business_profile_id: string;
  razorpay_subscription_id: string | null;
  plan_id: string;
  plan_cycle: string;
  subscription_status: string;
  has_active_subscription: boolean;
  trial_end_date: string | null;
  subscription_start_date: string | null;
  subscription_expiry_time: string | null;
  cancelled_at: string | null;
  created_at: string;
  updated_at: string;
}

export interface BusinessProfileInfo {
  id: string;
  has_active_subscription: boolean;
  trial_end_date: string | null;
  status: string;
}

/**
 * Centralized subscription service class
 */
export class SubscriptionService {
  private supabase: SupabaseClient | null = null;

  private async getClient(): Promise<SupabaseClient> {
    if (!this.supabase) {
      this.supabase = await createClient();
    }
    return this.supabase;
  }

  /**
   * Get user's current subscription with business profile info
   */
  async getUserSubscription(userId: string): Promise<{
    subscription: SubscriptionInfo | null;
    profile: BusinessProfileInfo | null;
    error: string | null;
  }> {
    try {
      const supabase = await this.getClient();
      const { data: subscription, error: subError } = await getPaymentSubscriptionByBusinessProfileId(supabase, userId);

      if (subError) {
        console.error("[SUBSCRIPTION_SERVICE] Error fetching subscription:", subError);
        return { subscription: null, profile: null, error: subError };
      }

      const { data: profile, error: profileError } = await getBusinessProfileSubscriptionInfo(supabase, userId);

      if (profileError) {
        console.error("[SUBSCRIPTION_SERVICE] Error fetching profile:", profileError);
        return { subscription: subscription as SubscriptionInfo, profile: null, error: profileError };
      }

      return { subscription: subscription as SubscriptionInfo, profile: profile as BusinessProfileInfo, error: null };
    } catch (error) {
      console.error("[SUBSCRIPTION_SERVICE] Unexpected error:", error);
      return { 
        subscription: null, 
        profile: null, 
        error: error instanceof Error ? error.message : "Unknown error" 
      };
    }
  }

  /**
   * Check if user has active subscription using centralized logic
   */
  async hasActiveSubscription(userId: string): Promise<boolean> {
    const { subscription, profile } = await this.getUserSubscription(userId);

    if (!subscription || !profile) {
      return false;
    }

    // Use centralized SubscriptionStateManager
    return SubscriptionStateManager.shouldHaveActiveSubscription(
      subscription.subscription_status,
      subscription.plan_id || 'free'
    );
  }

  /**
   * Get user's current subscription status using centralized logic
   */
  async getSubscriptionStatus(userId: string): Promise<{
    status: SubscriptionStatus;
    isActive: boolean;
    isPaid: boolean;
    isTrial: boolean;
    isTerminal: boolean;
  }> {
    const { subscription } = await this.getUserSubscription(userId);

    if (!subscription) {
      return {
        status: SUBSCRIPTION_STATUS.PENDING,
        isActive: false,
        isPaid: false,
        isTrial: false,
        isTerminal: false
      };
    }

    const status = subscription.subscription_status as SubscriptionStatus;
    const planId = subscription.plan_id || 'free';

    return {
      status,
      isActive: SubscriptionStateManager.shouldHaveActiveSubscription(status, planId),
      isPaid: SubscriptionStateManager.isActivePaidSubscription(status, planId),
      isTrial: SubscriptionStateManager.isTrialStatus(status),
      isTerminal: SubscriptionStateManager.isTerminalStatus(status)
    };
  }

  /**
   * Update subscription status with business profile consistency
   */
  async updateSubscriptionStatus(
    subscriptionId: string,
    newStatus: SubscriptionStatus,
    additionalData: Record<string, unknown> = {}
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const supabase = await this.getClient();
      // First get the subscription to find business profile
      const { data: subscription, error: fetchError } = await supabase
        .from("payment_subscriptions")
        .select("business_profile_id")
        .eq("razorpay_subscription_id", subscriptionId)
        .maybeSingle();

      if (fetchError || !subscription) {
        return { success: false, error: "Subscription not found" };
      }

      // Get the subscription to determine plan_id
      const { data: subscriptionData, error: planFetchError } = await supabase
        .from("payment_subscriptions")
        .select("plan_id")
        .eq("razorpay_subscription_id", subscriptionId)
        .single();

      if (planFetchError || !subscriptionData) {
        console.error("[SUBSCRIPTION_SERVICE] Error fetching subscription plan:", planFetchError);
        return { success: false, error: planFetchError?.message || "Subscription data not found" };
      }

      // Determine if this status should grant active subscription based on status and plan
      const hasActiveSubscription = SubscriptionStateManager.shouldHaveActiveSubscription(
        newStatus,
        subscriptionData.plan_id || 'free'
      );

      // ENHANCED: Use atomic RPC function for transaction safety
      const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {
        p_subscription_id: subscriptionId,
        p_new_status: newStatus,
        p_business_profile_id: subscription.business_profile_id,
        p_has_active_subscription: hasActiveSubscription,
        p_additional_data: additionalData,
        p_webhook_timestamp: null
      });

      if (atomicError || !atomicResult?.success) {
        console.error("[SUBSCRIPTION_SERVICE] Error updating subscription atomically:", atomicError || atomicResult?.error);
        return { success: false, error: atomicError?.message || atomicResult?.error || 'Unknown RPC error' };
      }

      console.log(`[SUBSCRIPTION_SERVICE] Successfully updated subscription ${subscriptionId} atomically`);
      return { success: true };
    } catch (error) {
      console.error("[SUBSCRIPTION_SERVICE] Unexpected error:", error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : "Unknown error" 
      };
    }
  }

  /**
   * Check if user is currently on trial
   */
  async isUserOnTrial(userId: string): Promise<boolean> {
    const { subscription, profile } = await this.getUserSubscription(userId);
    
    if (!subscription || !profile) {
      return false;
    }

    // Check if subscription status is trial
    if (subscription.subscription_status === SUBSCRIPTION_STATUS.TRIAL) {
      // Also verify trial hasn't expired
      if (profile.trial_end_date) {
        const trialEnd = new Date(profile.trial_end_date);
        const now = new Date();
        return trialEnd > now;
      }
    }

    return false;
  }

  /**
   * Get active subscriptions for a user (excluding terminal states)
   */
  async getActiveSubscriptions(userId: string): Promise<SubscriptionInfo[]> {
    const supabase = await this.getClient();
    const { data: subscriptions, error } = await supabase
      .from("payment_subscriptions")
      .select("*")
      .eq("business_profile_id", userId)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("[SUBSCRIPTION_SERVICE] Error fetching active subscriptions:", error);
      return [];
    }

    // Filter out terminal subscriptions using centralized logic
    return subscriptions?.filter((sub: any) =>
      !SubscriptionStateManager.isTerminalStatus(sub.subscription_status) &&
      sub.plan_id !== 'free'
    ) || [];
  }

  /**
   * Validate subscription consistency between tables
   */
  async validateSubscriptionConsistency(userId: string): Promise<{
    isConsistent: boolean;
    issues: string[];
  }> {
    const { subscription, profile } = await this.getUserSubscription(userId);
    const issues: string[] = [];

    if (!subscription || !profile) {
      issues.push("Missing subscription or profile data");
      return { isConsistent: false, issues };
    }

    // Check if has_active_subscription flag matches subscription status and plan
    const shouldBeActive = SubscriptionStateManager.shouldHaveActiveSubscription(
      subscription.subscription_status,
      subscription.plan_id || 'free'
    );
    if (profile.has_active_subscription !== shouldBeActive) {
      issues.push(
        `has_active_subscription mismatch: profile=${profile.has_active_subscription}, should_be=${shouldBeActive} based on status=${subscription.subscription_status} and plan=${subscription.plan_id}`
      );
    }

    // Check trial consistency
    if (subscription.subscription_status === SUBSCRIPTION_STATUS.TRIAL) {
      if (!profile.trial_end_date) {
        issues.push("Trial status but no trial_end_date");
      } else {
        const trialEnd = new Date(profile.trial_end_date);
        const now = new Date();
        if (trialEnd <= now) {
          issues.push("Trial status but trial period has expired");
        }
      }
    }

    return { isConsistent: issues.length === 0, issues };
  }
}

// Export singleton instance
export const subscriptionService = new SubscriptionService();
