import { createClient } from "@/utils/supabase/server";


import { revalidatePath } from "next/cache";
import { createErrorResponse, createSuccessResponse } from "./utils";
import type { ActionResponse } from "./types";
import type { PlanType, PlanCycle } from "@/lib/config/plans";
import {
  SubscriptionStateManager,
} from "@/lib/razorpay/webhooks/handlers/subscription-state-manager";
import {
  SUBSCRIPTION_STATUS
} from "@/lib/razorpay/webhooks/handlers/subscription-constants";

/**
 * Activates a trial for a first-time paid subscriber
 * This is used when a user on the free plan wants to upgrade to a paid plan
 * and has never had a paid plan before (trial_end_date is null)
 *
 * Important implementation details:
 * 1. Even if the user selects a yearly plan, we'll always use the monthly plan for the trial period
 *    since we're only offering a 1-month free trial. They can switch to yearly after the trial.
 * 2. We set has_active_subscription to false in business_profiles since trial is not an active subscription.
 *    The trial status is determined by checking if trial_end_date is in the future.
 * 3. We don't set subscription_start_date and subscription_expiry_time for trial users as these
 *    fields are only relevant for active subscriptions.
 *
 * @param planId The plan ID to activate trial for
 * @param planCycle The plan cycle selected by the user (stored for future reference but not used during trial)
 * @returns Success or error response
 */
export async function activateTrialForFirstTimePaidSubscriber(
  planId: PlanType,
  planCycle: PlanCycle
): Promise<ActionResponse> {
  // Only allow paid plans
  if (planId === "free") {
    return createErrorResponse("Cannot activate trial for free plan");
  }

  // Get the authenticated user
  const supabase = await createClient();
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    console.error("Error fetching user:", userError);
    return createErrorResponse("User not authenticated");
  }

  // Get the user's business profile
  const { data: profile, error: profileError } = await supabase
    .from("business_profiles")
    .select("trial_end_date")
    .eq("id", user.id)
    .single();

  if (profileError) {
    console.error("Error fetching profile:", profileError);
    return createErrorResponse("Error fetching user profile");
  }

  // Check if the user is eligible for a trial (trial_end_date is null)
  if (profile.trial_end_date !== null) {
    return createErrorResponse(
      "You are not eligible for a free trial. You have previously subscribed to a paid plan."
    );
  }

  // FIXED: Calculate trial end date (30 days from now using milliseconds)
  // This approach avoids month boundary issues that can occur with setDate()
  const trialEndDate = new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)); // 30 days in milliseconds

  

  // CENTRALIZED LOGIC: Trial users do NOT have active subscription (they're testing, not paying)
  // CRITICAL: This ensures consistency with webhook handlers and subscription flow
  const hasActiveSubscription = SubscriptionStateManager.shouldHaveActiveSubscription(SUBSCRIPTION_STATUS.TRIAL, planId);

  console.log(`[TRIAL_ACTIVATION] Trial activation for plan ${planId}: has_active_subscription = ${hasActiveSubscription}`);

  // Start a transaction to update both tables
  try {
    // ENHANCED: Use atomic RPC function for transaction safety
    const subscriptionData = {
      business_profile_id: user.id,
      subscription_status: "trial",
      plan_id: planId, // Use the selected plan for trial
      plan_cycle: "monthly", // Always monthly for trials
      subscription_start_date: new Date().toISOString(),
      subscription_expiry_time: trialEndDate.toISOString(),
      // Note: razorpay_subscription_id remains NULL for trials
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Use atomic RPC for trial activation (no razorpay_subscription_id needed)
    const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {
      p_subscription_id: null as any, // NULL for trial activation - no Razorpay subscription yet
      p_new_status: 'trial',
      p_business_profile_id: user.id,
      p_has_active_subscription: hasActiveSubscription, // FALSE for trial users - they're not on paid subscription
      p_additional_data: {
        ...subscriptionData,
        trial_end_date: trialEndDate.toISOString() // RPC function now handles this for business_profiles
      } as any,
      p_webhook_timestamp: undefined
    });

    if (atomicError || !(atomicResult as any)?.success) {
      console.error("Error activating trial atomically:", atomicError || (atomicResult as any)?.error);
      return createErrorResponse("Error activating trial");
    }

    // Trial activation completed successfully via atomic RPC (now handles both tables)

    // Revalidate paths
    revalidatePath("/dashboard/business/plan");
    revalidatePath("/dashboard/business");

    return createSuccessResponse({
      message: "Trial activated successfully",
      trialEndDate: trialEndDate.toISOString(),
      planId,
      planCycle: "monthly", // Always return monthly as the active plan cycle for trials
      userSelectedPlanCycle: planCycle // Include the user's selected plan cycle for reference
    });
  } catch (error) {
    console.error("Error in trial activation transaction:", error);
    return createErrorResponse("An unexpected error occurred while activating your trial");
  }
}
