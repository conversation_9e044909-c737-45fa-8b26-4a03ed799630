# Dukancard Supabase Refactoring Product Requirements Document (PRD)

## Goals and Background Context

### Goals

*   Centralize Supabase function calls for improved maintainability.
*   Accelerate future development by providing a single source of truth for Supabase interactions.
*   Reduce the likelihood of bugs by standardizing Supabase call patterns.

### Background Context

The current codebase contains numerous direct Supabase function calls scattered across various files. This decentralized approach leads to challenges in maintaining consistency, introduces potential for redundant code, and complicates debugging efforts. By centralizing these calls into dedicated service files (`customerService.ts`, `businessService.ts`, and `sharedService.ts`), we aim to streamline development workflows, enhance code readability, and establish a more robust and scalable architecture for Supabase interactions within the platform.

### Change Log

| Date       | Version | Description        | Author |
| :--------- | :------ | :----------------- | :----- |
| 2025-07-23 | 1.0     | Initial Draft      | John   |

## Requirements

### Functional

*   **FR1:** All direct Supabase calls in the specified files (`@dukancard-direct-supabase-calls.txt`) shall be refactored to use functions from the new centralized service files.
*   **FR2:** Existing functions within `customerService.ts` (under `@dukancard/lib/supabase/services/`) shall be utilized or new functions appended to encapsulate all Supabase calls related to customer data and operations.
*   **FR3:** Existing functions within `businessService.ts` (under `@dukancard/lib/supabase/services/`) shall be utilized or new functions appended to encapsulate all Supabase calls related to business data and operations.
*   **FR4:** Existing functions within `sharedService.ts` (under `@dukancard/lib/supabase/services/`) shall be utilized or new functions appended to encapsulate all Supabase calls that are common to both customer and business contexts, or general Supabase operations (e.g., authentication, storage).
*   **FR5:** Each function within the new service files shall clearly define its purpose, input parameters, and expected return values.
*   **FR6:** The new service functions shall handle Supabase query building and execution, returning only the relevant data or error information to the calling components.

### Non Functional

*   **NFR1:** The refactored codebase shall demonstrate improved maintainability, as evidenced by reduced code duplication and clearer separation of concerns.
*   **NFR2:** The new service files shall be designed to facilitate faster future development by providing reusable and well-documented Supabase interaction patterns.
*   **NFR3:** The refactoring shall aim to reduce the introduction of new bugs related to Supabase interactions by enforcing consistent patterns and centralized error handling.
*   **NFR4:** The service files shall adhere to existing project coding standards and best practices for TypeScript.
*   **NFR5:** The refactored code should maintain or improve current application performance.

## User Interface Design Goals

#### Overall UX Vision
N/A - This refactoring is primarily a backend code structure change and is not expected to have direct user interface implications. The user experience should remain unchanged.

#### Key Interaction Paradigms
N/A - No new interaction paradigms are introduced or altered by this refactoring.

#### Core Screens and Views
N/A - This refactoring does not introduce or modify any core screens or views.

#### Accessibility
N/A - No specific accessibility requirements are introduced or altered by this refactoring.

#### Branding
N/A - No branding elements or style guides are affected by this refactoring.

#### Target Device and Platforms
N/A - The target devices and platforms remain unchanged by this refactoring.

## Technical Assumptions

#### Repository Structure
The `dukancard` and `dukancard-app` are separate repositories, each potentially utilizing different framework platforms. This refactoring effort is specifically targeted at the `dukancard` project, as indicated by the `dukancard-direct-supabase-calls.txt` file. The existing repository structure for `dukancard` will be maintained.

#### Service Architecture
The refactoring focuses on centralizing Supabase calls within the existing `dukancard` project's application architecture. We will assume the current service architecture (e.g., Next.js API routes, server components within `dukancard`) will be maintained, with the new service files integrating seamlessly into this structure. The goal is to abstract Supabase interactions, not to change the overall application's service architecture.

#### Testing Requirements
**No testing is required for this refactoring effort.** The focus is solely on the code reorganization and centralization of Supabase calls.

#### Additional Technical Assumptions and Requests
*   **Supabase Client Initialization:** The existing method of initializing the Supabase client (e.g., `createClient` from `@supabase/supabase-js`) will be used within the new service files.
*   **Error Handling:** Errors returned by Supabase calls will be caught and handled gracefully within the service functions, potentially transforming them into a standardized error format for consumption by calling components.
*   **TypeScript:** The new service files will be written in TypeScript, adhering to strict typing for all function parameters and return values.
*   **No Supabase Admin Usage:** The refactoring should ensure that `supabase.auth.admin` usage is removed from the client-side or API routes and, if necessary, moved to secure server-side environments or replaced with appropriate user-level operations.

## Epic List

*   **Epic 1: Batch 1 Refactoring & Foundational Shared Calls (Files 1-15)**
    *   **Goal:** Refactor direct Supabase calls in the first batch of files (approximately 15 files) by creating new functions in `sharedService.ts` for common operations like `supabase.auth.getUser()`, and utilizing or appending to existing functions in `customerService.ts` and `businessService.ts` as needed. This epic will establish the initial refactoring pattern without interfering with existing service file functions.

*   **Epic 2: Batch 2 Refactoring (Files 16-30)**
    *   **Goal:** Continue refactoring direct Supabase calls in the next batch of files (approximately 15 files), categorizing calls into `customerService.ts`, `businessService.ts`, or `sharedService.ts` as appropriate, always appending new functions or utilizing existing ones without modification.

*   **Epic 3: Batch 3 Refactoring (Files 31-45)**
    *   **Goal:** Refactor direct Supabase calls in the third batch of files (approximately 15 files), continuing to centralize calls into the appropriate service files.

*   **Epic 4: Batch 4 Refactoring (Files 46-60)**
    *   **Goal:** Refactor direct Supabase calls in the fourth batch of files (approximately 15 files).

*   **Epic 5: Batch 5 Refactoring (Files 61-75)**
    *   **Goal:** Refactor direct Supabase calls in the fifth batch of files (approximately 15 files).

*   **Epic 6: Batch 6 Refactoring (Files 76-90)**
    *   **Goal:** Refactor direct Supabase calls in the sixth batch of files (approximately 15 files).

*   **Epic 7: Batch 7 Refactoring (Files 91-105)**
    *   **Goal:** Refactor direct Supabase calls in the seventh batch of files (approximately 15 files).

*   **Epic 8: Batch 8 Refactoring (Files 106-120)**
    *   **Goal:** Refactor direct Supabase calls in the eighth batch of files (approximately 15 files).

*   **Epic 9: Batch 9 Refactoring (Files 121-138) & Finalization**
    *   **Goal:** Refactor the remaining direct Supabase calls in the final batch of files and perform a comprehensive review to ensure all direct Supabase calls are removed from the codebase, completing the centralization effort.

## Epic 1: Batch 1 Refactoring & Foundational Shared Calls (Files 1-15)

**Expanded Goal:** This epic will initiate the refactoring process by focusing on the first 15 files from `dukancard-direct-supabase-calls.txt`. The primary objective is to identify *all* direct Supabase calls within these files, categorize them as customer-related, business-related, or shared/general, and then refactor them into the appropriate service file (`customerService.ts`, `businessService.ts`, or `sharedService.ts`). This will involve utilizing existing functions within these service files or appending new functions as needed, ensuring no interference with existing service file functions. This epic will establish the pattern for subsequent refactoring batches.

#### Story 1.1: Refactor All Supabase Calls in Batch 1 Files

As a developer,
I want to identify and refactor all direct Supabase calls within the first 15 files from `dukancard-direct-supabase-calls.txt` into the appropriate centralized service files (`customerService.ts`, `businessService.ts`, or `sharedService.ts`),
so that all Supabase interaction logic is centralized, consistent, and easier to maintain across these initial files, without interfering with existing service file functions.

**Acceptance Criteria:**
1.  For each of the following files, all direct `supabase.*` calls are identified:
    *   `app\(dashboard)\dashboard\business\overview\page.tsx`
    *   `app\(dashboard)\dashboard\business\page.tsx`
    *   `app\(dashboard)\dashboard\business\plan\components\EnhancedInvoiceHistoryCard.tsx`
    *   `app\(dashboard)\dashboard\business\plan\page.tsx`
    *   `app\(dashboard)\dashboard\business\products\add\page.tsx`
    *   `app\(dashboard)\dashboard\business\products\edit\[productId]\page.tsx`
    *   `app\(dashboard)\dashboard\business\products\page.tsx`
    *   `app\(dashboard)\dashboard\business\reviews\page.tsx`
    *   `app\(dashboard)\dashboard\business\settings\actions.ts`
    *   `app\(dashboard)\dashboard\business\settings\page.tsx`
    *   `app\(dashboard)\dashboard\business\subscriptions\actions.ts`
    *   `app\(dashboard)\dashboard\business\subscriptions\page.tsx`
    *   `app\(dashboard)\dashboard\customer\layout.tsx`
    *   `app\(dashboard)\dashboard\customer\likes\actions.ts`
    *   `app\(dashboard)\dashboard\customer\likes\page.tsx`
2.  Each identified `supabase.*` call is categorized as belonging to the customer domain, business domain, or a shared/general domain.
3.  For each categorized call, a corresponding function is identified or appended to the appropriate service file (`customerService.ts`, `businessService.ts`, or `sharedService.ts`).
    *   **Before appending a new function:** Confirm that no existing function within the target service file already serves the purpose of encapsulating the identified Supabase call.
    *   **If a suitable existing function is found:** Utilize that function.
    *   **If no suitable existing function is found:** A new function will be appended to the target service file. This new function will not modify any existing functions in the service file.
4.  All instances of direct `supabase.*` calls in the listed files are replaced with calls to the identified or newly appended service functions.
5.  The new or utilized service functions correctly handle potential errors from Supabase calls and return standardized responses.
6.  The refactored files correctly import and use the appropriate service functions.

## Epic 2: Batch 2 Refactoring (Files 16-30)

**Expanded Goal:** This epic will continue the refactoring process by focusing on the next 15 files from `dukancard-direct-supabase-calls.txt`. The objective is to identify all direct Supabase calls within these files, categorize them, and refactor them into the appropriate service file (`customerService.ts`, `businessService.ts`, or `sharedService.ts`), utilizing existing functions or appending new ones as needed, without interfering with existing service file functions.

#### Story 2.1: Refactor All Supabase Calls in Batch 2 Files

As a developer,
I want to identify and refactor all direct Supabase calls within files 16-30 from `dukancard-direct-supabase-calls.txt` into the appropriate centralized service files,
so that all Supabase interaction logic is centralized, consistent, and easier to maintain across these files.

**Acceptance Criteria:**
1.  For each of the following files, all direct `supabase.*` calls are identified:
    *   `app\(dashboard)\dashboard\customer\overview\page.tsx`
    *   `app\(dashboard)\dashboard\customer\page.tsx`
    *   `app\(dashboard)\dashboard\customer\profile\actions.ts`
    *   `app\(dashboard)\dashboard\customer\profile\avatar-actions.ts`
    *   `app\(dashboard)\dashboard\customer\profile\page.tsx`
    *   `app\(dashboard)\dashboard\customer\reviews\page.tsx`
    *   `app\(dashboard)\dashboard\customer\settings\actions.ts`
    *   `app\(dashboard)\dashboard\customer\settings\page.tsx`
    *   `app\(dashboard)\dashboard\customer\subscriptions\actions.ts`
    *   `app\(dashboard)\dashboard\customer\subscriptions\page.tsx`
    *   `app\(main)\actions\getHomepageBusinessCard.ts`
    *   `app\(main)\auth\callback\AuthCallbackClient.tsx`
    *   `app\(main)\blog\sitemap.ts`
    *   `app\(main)\blog\[blogSlug]\page.tsx`
    *   `app\(main)\discover\actions\businessActions.ts`
2.  Each identified `supabase.*` call is categorized as belonging to the customer domain, business domain, or a shared/general domain.
3.  For each categorized call, a corresponding function is identified or appended to the appropriate service file (`customerService.ts`, `businessService.ts`, or `sharedService.ts`).
    *   **Before appending a new function:** Confirm that no existing function within the target service file already serves the purpose of encapsulating the identified Supabase call.
    *   **If a suitable existing function is found:** Utilize that function.
    *   **If no suitable existing function is found:** A new function will be appended to the target service file. This new function will not modify any existing functions in the service file.
4.  All instances of direct `supabase.*` calls in the listed files are replaced with calls to the identified or newly appended service functions.
5.  The new or utilized service functions correctly handle potential errors from Supabase calls and return standardized responses.
6.  The refactored files correctly import and use the appropriate service functions.

## Epic 3: Batch 3 Refactoring (Files 31-45)

**Expanded Goal:** This epic will continue the refactoring process by focusing on the next 15 files from `dukancard-direct-supabase-calls.txt`. The objective is to identify all direct Supabase calls within these files, categorize them, and refactor them into the appropriate service file (`customerService.ts`, `businessService.ts`, or `sharedService.ts`), utilizing existing functions or appending new ones as needed, without interfering with existing service file functions.

#### Story 3.1: Refactor All Supabase Calls in Batch 3 Files

As a developer,
I want to identify and refactor all direct Supabase calls within files 31-45 from `dukancard-direct-supabase-calls.txt` into the appropriate centralized service files,
so that all Supabase interaction logic is centralized, consistent, and easier to maintain across these files.

**Acceptance Criteria:**
1.  For each of the following files, all direct `supabase.*` calls are identified:
    *   `app\(main)\discover\actions\combinedActions.ts`
    *   `app\(main)\discover\actions\productActions.ts`
    *   `app\(main)\email-change-success\page.tsx`
    *   `app\(main)\LandingPageClient.tsx`
    *   `app\(onboarding)\onboarding\actions.ts`
    *   `app\(onboarding)\onboarding\hooks\useUserData.ts`
    *   `app\api\admin\fix-subscription-inconsistency\route.ts`
    *   `app\api\business\likes\route.ts`
    *   `app\api\business\my-likes\route.ts`
    *   `app\api\business\my-reviews\route.ts`
    *   `app\api\business\reviews\route.ts`
    *   `app\api\check-user-type\route.ts`
    *   `app\api\customer\likes\route.ts`
    *   `app\api\customer\reviews\route.ts`
    *   `app\api\customer\reviews\update\route.ts`
2.  Each identified `supabase.*` call is categorized as belonging to the customer domain, business domain, or a shared/general domain.
3.  For each categorized call, a corresponding function is identified or appended to the appropriate service file (`customerService.ts`, `businessService.ts`, or `sharedService.ts`).
    *   **Before appending a new function:** Confirm that no existing function within the target service file already serves the purpose of encapsulating the identified Supabase call.
    *   **If a suitable existing function is found:** Utilize that function.
    *   **If no suitable existing function is found:** A new function will be appended to the target service file. This new function will not modify any existing functions in the service file.
4.  All instances of direct `supabase.*` calls in the listed files are replaced with calls to the identified or newly appended service functions.
5.  The new or utilized service functions correctly handle potential errors from Supabase calls and return standardized responses.
6.  The refactored files correctly import and use the appropriate service functions.

## Epic 4: Batch 4 Refactoring (Files 46-60)

**Expanded Goal:** This epic will continue the refactoring process by focusing on the next 15 files from `dukancard-direct-supabase-calls.txt`. The objective is to identify all direct Supabase calls within these files, categorize them, and refactor them into the appropriate service file (`customerService.ts`, `businessService.ts`, or `sharedService.ts`), utilizing existing functions or appending new ones as needed, without interfering with existing service file functions.

#### Story 4.1: Refactor All Supabase Calls in Batch 4 Files

As a developer,
I want to identify and refactor all direct Supabase calls within files 46-60 from `dukancard-direct-supabase-calls.txt` into the appropriate centralized service files,
so that all Supabase interaction logic is centralized, consistent, and easier to maintain across these files.

**Acceptance Criteria:**
1.  For each of the following files, all direct `supabase.*` calls are identified:
    *   `app\api\health\subscription\route.ts`
    *   `app\api\razorpay\key\route.ts`
    *   `app\api\subscription\[id]\payments\route.ts`
    *   `app\api\subscriptions\list\route.ts`
    *   `app\api\subscriptions\my\route.ts`
    *   `app\api\subscriptions\[id]\cancel\route.ts`
    *   `app\api\subscriptions\[id]\invoices\route.ts`
    *   `app\api\subscriptions\[id]\pause\route.ts`
    *   `app\api\subscriptions\[id]\payments\route.ts`
    *   `app\api\subscriptions\[id]\payments\[paymentId]\route.ts`
    *   `app\api\subscriptions\[id]\pending-update\route.ts`
    *   `app\api\subscriptions\[id]\refund\utils\databaseOperations.ts`
    *   `app\api\subscriptions\[id]\refund\utils\validators.ts`
    *   `app\api\subscriptions\[id]\resume\route.ts`
    *   `app\api\subscriptions\[id]\route.ts`
2.  Each identified `supabase.*` call is categorized as belonging to the customer domain, business domain, or a shared/general domain.
3.  For each categorized call, a corresponding function is identified or appended to the appropriate service file (`customerService.ts`, `businessService.ts`, or `sharedService.ts`).
    *   **Before appending a new function:** Confirm that no existing function within the target service file already serves the purpose of encapsulating the identified Supabase call.
    *   **If a suitable existing function is found:** Utilize that function.
    *   **If no suitable existing function is found:** A new function will be appended to the target service file. This new function will not modify any existing functions in the service file.
4.  All instances of direct `supabase.*` calls in the listed files are replaced with calls to the identified or newly appended service functions.
5.  The new or utilized service functions correctly handle potential errors from Supabase calls and return standardized responses.
6.  The refactored files correctly import and use the appropriate service functions.

## Epic 5: Batch 5 Refactoring (Files 61-75)

**Expanded Goal:** This epic will continue the refactoring process by focusing on the next 15 files from `dukancard-direct-supabase-calls.txt`. The objective is to identify all direct Supabase calls within these files, categorize them, and refactor them into the appropriate service file (`customerService.ts`, `businessService.ts`, or `sharedService.ts`), utilizing existing functions or appending new ones as needed, without interfering with existing service file functions.

#### Story 5.1: Refactor All Supabase Calls in Batch 5 Files

As a developer,
I want to identify and refactor all direct Supabase calls within files 61-75 from `dukancard-direct-supabase-calls.txt` into the appropriate centralized service files,
so that all Supabase interaction logic is centralized, consistent, and easier to maintain across these files.

**Acceptance Criteria:**
1.  For each of the following files, all direct `supabase.*` calls are identified:
    *   `app\api\subscriptions\[id]\scheduled-changes\route.ts`
    *   `app\api\subscriptions\[id]\switch\route.ts`
    *   `app\api\subscriptions\[id]\switch-with-new-payment\route.ts`
    *   `app\api\subscriptions\[id]\update\route.ts`
    *   `app\api\test\subscription-flow\route.ts`
    *   `app\api\test\subscription-scenarios\route.ts`
    *   `app\api\webhooks\razorpay\retry\route.ts`
    *   `app\components\BottomNav.tsx`
    *   `app\components\Header.tsx`
    *   `app\locality\actions\businessActions.ts`
    *   `app\locality\actions\combinedActions.ts`
    *   `app\locality\actions\locationActions.ts`
    *   `app\locality\actions\productActions.ts`
    *   `app\[cardSlug]\gallery\page.tsx`
    *   `app\[cardSlug]\product\actions.ts`
2.  Each identified `supabase.*` call is categorized as belonging to the customer domain, business domain, or a shared/general domain.
3.  For each categorized call, a corresponding function is identified or appended to the appropriate service file (`customerService.ts`, `businessService.ts`, or `sharedService.ts`).
    *   **Before appending a new function:** Confirm that no existing function within the target service file already serves the purpose of encapsulating the identified Supabase call.
    *   **If a suitable existing function is found:** Utilize that function.
    *   **If no suitable existing function is found:** A new function will be appended to the target service file. This new function will not modify any existing functions in the service file.
4.  All instances of direct `supabase.*` calls in the listed files are replaced with calls to the identified or newly appended service functions.
5.  The new or utilized service functions correctly handle potential errors from Supabase calls and return standardized responses.
6.  The refactored files correctly import and use the appropriate service functions.

## Epic 6: Batch 6 Refactoring (Files 76-90)

**Expanded Goal:** This epic will continue the refactoring process by focusing on the next 15 files from `dukancard-direct-supabase-calls.txt`. The objective is to identify all direct Supabase calls within these files, categorize them, and refactor them into the appropriate service file (`customerService.ts`, `businessService.ts`, or `sharedService.ts`), utilizing existing functions or appending new ones as needed, without interfering with existing service file functions.

#### Story 6.1: Refactor All Supabase Calls in Batch 6 Files

As a developer,
I want to identify and refactor all direct Supabase calls within files 76-90 from `dukancard-direct-supabase-calls.txt` into the appropriate centralized service files,
so that all Supabase interaction logic is centralized, consistent, and easier to maintain across these files.

**Acceptance Criteria:**
1.  For each of the following files, all direct `supabase.*` calls are identified:
    *   `app\[cardSlug]\product\[productSlug]\page.tsx`
    *   `components\feed\shared\forms\LocationDisplay.tsx`
    *   `components\feed\shared\hooks\usePostOwnership.ts`
    *   `components\feed\shared\ModernCustomerPostCard.tsx`
    *   `components\feed\shared\SocialMediaBusinessPostCreator.tsx`
    *   `components\feed\shared\SocialMediaPostCreator.tsx`
    *   `components\post\ConditionalPostLayout.tsx`
    *   `components\sidebar\BusinessAppSidebar.tsx`
    *   `docs\supabase-docs\custom-ads-management.md`
    *   `docs\supabase-docs\custom-ads-technical-implementation.md`
    *   `lib\actions\activities.ts`
    *   `lib\actions\blogs.ts`
    *   `lib\actions\businessProfiles\access.ts`
    *   `lib\actions\businessProfiles\discovery.ts`
    *   `lib\actions\businessProfiles\location.ts`
2.  Each identified `supabase.*` call is categorized as belonging to the customer domain, business domain, or a shared/general domain.
3.  For each categorized call, a corresponding function is identified or appended to the appropriate service file (`customerService.ts`, `businessService.ts`, or `sharedService.ts`).
    *   **Before appending a new function:** Confirm that no existing function within the target service file already serves the purpose of encapsulating the identified Supabase call.
    *   **If a suitable existing function is found:** Utilize that function.
    *   **If no suitable existing function is found:** A new function will be appended to the target service file. This new function will not modify any existing functions in the service file.
4.  All instances of direct `supabase.*` calls in the listed files are replaced with calls to the identified or newly appended service functions.
5.  The new or utilized service functions correctly handle potential errors from Supabase calls and return standardized responses.
6.  The refactored files correctly import and use the appropriate service functions.

## Epic 7: Batch 7 Refactoring (Files 91-105)

**Expanded Goal:** This epic will continue the refactoring process by focusing on the next 15 files from `dukancard-direct-supabase-calls.txt`. The objective is to identify all direct Supabase calls within these files, categorize them, and refactor them into the appropriate service file (`customerService.ts`, `businessService.ts`, or `sharedService.ts`), utilizing existing functions or appending new ones as needed, without interfering with existing service file functions.

#### Story 7.1: Refactor All Supabase Calls in Batch 7 Files

As a developer,
I want to identify and refactor all direct Supabase calls within files 91-105 from `dukancard-direct-supabase-calls.txt` into the appropriate centralized service files,
so that all Supabase interaction logic is centralized, consistent, and easier to maintain across these files.

**Acceptance Criteria:**
1.  For each of the following files, all direct `supabase.*` calls are identified:
    *   `lib\actions\businessProfiles\search.ts`
    *   `lib\actions\businessProfiles\sitemap.ts`
    *   `lib\actions\categories\locationBasedFetching.ts`
    *   `lib\actions\customerPosts\crud.ts`
    *   `lib\actions\customerProfiles\addressValidation.ts`
    *   `lib\actions\interactions.ts`
    *   `lib\actions\location\locationBySlug.ts`
    *   `lib\actions\location.ts`
    *   `lib\actions\posts\crud.ts`
    *   `lib\actions\posts\fetchSinglePost.ts`
    *   `lib\actions\products\fetchProductsByIds.ts`
    *   `lib\actions\products\sitemapHelpers.ts`
    *   `lib\actions\reviews.ts`
    *   `lib\actions\secureCustomerProfiles.ts`
    *   `lib\actions\shared\productActions.ts`
2.  Each identified `supabase.*` call is categorized as belonging to the customer domain, business domain, or a shared/general domain.
3.  For each categorized call, a corresponding function is identified or appended to the appropriate service file (`customerService.ts`, `businessService.ts`, or `sharedService.ts`).
    *   **Before appending a new function:** Confirm that no existing function within the target service file already serves the purpose of encapsulating the identified Supabase call.
    *   **If a suitable existing function is found:** Utilize that function.
    *   **If no suitable existing function is found:** A new function will be appended to the target service file. This new function will not modify any existing functions in the service file.
4.  All instances of direct `supabase.*` calls in the listed files are replaced with calls to the identified or newly appended service functions.
5.  The new or utilized service functions correctly handle potential errors from Supabase calls and return standardized responses.
6.  The refactored files correctly import and use the appropriate service functions.

## Epic 8: Batch 8 Refactoring (Files 106-120)

**Expanded Goal:** This epic will continue the refactoring process by focusing on the next 15 files from `dukancard-direct-supabase-calls.txt`. The objective is to identify all direct Supabase calls within these files, categorize them, and refactor them into the appropriate service file (`customerService.ts`, `businessService.ts`, or `sharedService.ts`), utilizing existing functions or appending new ones as needed, without interfering with existing service file functions.

#### Story 8.1: Refactor All Supabase Calls in Batch 8 Files

As a developer,
I want to identify and refactor all direct Supabase calls within files 106-120 from `dukancard-direct-supabase-calls.txt` into the appropriate centralized service files,
so that all Supabase interaction logic is centralized, consistent, and easier to maintain across these files.

**Acceptance Criteria:**
1.  For each of the following files, all direct `supabase.*` calls are identified:
    *   `lib\actions\shared\upload-business-post-media.ts`
    *   `lib\actions\shared\upload-customer-post-media.ts`
    *   `lib\actions\shared\upload-post-media.ts`
    *   `lib\actions\subscription\activateTrial.ts`
    *   `lib\actions\subscription\centralized.ts`
    *   `lib\actions\subscription\confirm.ts`
    *   `lib\actions\subscription\create.ts`
    *   `lib\actions\subscription\manage\cancel.ts`
    *   `lib\actions\subscription\manage\change.ts`
    *   `lib\actions\subscription\manage\manage.ts`
    *   `lib\actions\subscription\manage\schedule.ts`
    *   `lib\actions\subscription\manage\switch.ts`
    *   `lib\actions\subscription\status.ts`
    *   `lib\actions\subscription\utils.ts`
    *   `lib\client\locationUtils.ts`
2.  Each identified `supabase.*` call is categorized as belonging to the customer domain, business domain, or a shared/general domain.
3.  For each categorized call, a corresponding function is identified or appended to the appropriate service file (`customerService.ts`, `businessService.ts`, or `sharedService.ts`).
    *   **Before appending a new function:** Confirm that no existing function within the target service file already serves the purpose of encapsulating the identified Supabase call.
    *   **If a suitable existing function is found:** Utilize that function.
    *   **If no suitable existing function is found:** A new function will be appended to the target service file. This new function will not modify any existing functions in the service file.
4.  All instances of direct `supabase.*` calls in the listed files are replaced with calls to the identified or newly appended service functions.
5.  The new or utilized service functions correctly handle potential errors from Supabase calls and return standardized responses.
6.  The refactored files correctly import and use the appropriate service functions.

## Epic 9: Batch 9 Refactoring (Files 121-138) & Finalization

**Expanded Goal:** This epic will complete the refactoring process by focusing on the remaining files from `dukancard-direct-supabase-calls.txt`. The objective is to identify all direct Supabase calls within these files, categorize them, and refactor them into the appropriate service file (`customerService.ts`, `businessService.ts`, or `sharedService.ts`), utilizing existing functions or appending new ones as needed, without interfering with existing service file functions. This epic will also include a comprehensive review to ensure all direct Supabase calls are removed from the codebase, completing the centralization effort.

#### Story 9.1: Refactor All Supabase Calls in Batch 9 Files

As a developer,
I want to identify and refactor all direct Supabase calls within files 121-138 from `dukancard-direct-supabase-calls.txt` into the appropriate centralized service files,
so that all Supabase interaction logic is centralized, consistent, and easier to maintain across these files.

**Acceptance Criteria:**
1.  For each of the following files, all direct `supabase.*` calls are identified:
    *   `lib\razorpay\webhooks\handlers\core\subscriptionManager.ts`
    *   `lib\razorpay\webhooks\handlers\subscriptionEventHandlers\handleSubscriptionAuthenticated.ts`
    *   `lib\razorpay\webhooks\idempotency.ts`
    *   `lib\razorpay\webhooks\monitoring.ts`
    *   `lib\services\realtimeService.ts`
    *   `lib\services\subscription.ts`
    *   `lib\subscription\SubscriptionFlowTester.ts`
    *   `lib\testing\database.ts`
    *   `lib\utils\addressUtils.ts`
    *   `next.config.ts`
    *   `package.json`
    *   `README.md`
    *   `scripts\test-webhook-handlers.ts`
    *   `scripts\webhook-testing\payment-method-handling.md`
    *   `scripts\webhook-testing\README.md`
    *   `scripts\webhook-testing\subscription-statuses.md`
    *   `scripts\webhook-testing\test-webhook-events.ts`
    *   `utils\supabase\middleware.ts`
2.  Each identified `supabase.*` call is categorized as belonging to the customer domain, business domain, or a shared/general domain.
3.  For each categorized call, a corresponding function is identified or appended to the appropriate service file (`customerService.ts`, `businessService.ts`, or `sharedService.ts`).
    *   **Before appending a new function:** Confirm that no existing function within the target service file already serves the purpose of encapsulating the identified Supabase call.
    *   **If a suitable existing function is found:** Utilize that function.
    *   **If no suitable existing function is found:** A new function will be appended to the target service file. This new function will not modify any existing functions in the service file.
4.  All instances of direct `supabase.*` calls in the listed files are replaced with calls to the identified or newly appended service functions.
5.  The new or utilized service functions correctly handle potential errors from Supabase calls and return standardized responses.
6.  The refactored files correctly import and use the appropriate service functions.

#### Story 9.2: Comprehensive Review and Finalization

As a developer,
I want to perform a comprehensive review of the `dukancard` codebase,
so that all direct Supabase calls are confirmed to be refactored into the centralized service files, ensuring the completion of the centralization effort.

**Acceptance Criteria:**
1.  A final scan of the `dukancard` project confirms that no direct `supabase.*` calls remain outside of the `customerService.ts`, `businessService.ts`, and `sharedService.ts` files.
2.  All new or modified service functions are properly documented (e.g., JSDoc comments).
3.  The project's import aliases are correctly used for importing the new service files.

## Checklist Results Report

_(This section will be populated after running the `pm-checklist`.)_

## Next Steps

### UX Expert Prompt
N/A - This refactoring is primarily a backend code structure change and does not require a UX Expert prompt.

### Architect Prompt
"Architect, please review the attached PRD for the Supabase Refactoring project. Your task is to create a detailed architecture document and technical design, focusing on the implementation of the centralized Supabase service files (`customerService.ts`, `businessService.ts`, `sharedService.ts`) and the refactoring process outlined in the epics and stories. Pay close attention to the technical assumptions, especially regarding error handling, Supabase client initialization, and the removal of `supabase.auth.admin` usage. Ensure the design supports maintainability, scalability, and performance within the existing `dukancard` project architecture."