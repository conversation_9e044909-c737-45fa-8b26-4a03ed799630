import React from 'react';
import { Stack, router } from 'expo-router';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { useTheme } from '@/src/hooks/useTheme';
import { responsiveFontSize } from '@/lib/theme/colors';
import { supabase } from '@/lib/supabase';
import { signOutUser } from '@/src/config/supabase/services/sharedService';
import { LogOut } from 'lucide-react-native';
import { Text, TouchableOpacity } from 'react-native';
import { AlertProvider, useAlertDialog } from '@/src/components/providers/AlertProvider';

function OnboardingLayoutContent() {
  const theme = useTheme();
  const { logout, error } = useAlertDialog();

  const handleLogout = async () => {
    logout(
      async () => {
        try {
          await signOutUser();
          router.replace('/(auth)/login');
        } catch (err) {
          console.error('Error logging out:', err);
          error('Logout Failed', 'Failed to logout. Please try again.');
        }
      }
    );
  };

  return (
    <Stack>
      <Stack.Screen
        name="business-details"
        options={{
          headerShown: true,
          title: '',
          headerStyle: {
            backgroundColor: theme.colors.background,
          },
          headerTintColor: theme.colors.textPrimary,
          headerLeft: () => (
            <Text style={{
              fontSize: theme.typography.fontSize.xl,
              fontWeight: 'bold',
              color: theme.colors.primary,
              marginLeft: theme.spacing.md
            }}>
              Dukan<Text style={{ color: theme.colors.textPrimary }}>card</Text>
            </Text>
          ),
          headerRight: () => (
            <TouchableOpacity
              style={{
                padding: theme.spacing.xs,
                borderRadius: theme.borderRadius.md,
                borderWidth: 1,
                borderColor: theme.colors.primary + '40',
                marginRight: theme.spacing.md,
              }}
              onPress={handleLogout}
              activeOpacity={0.7}
            >
              <LogOut size={responsiveFontSize(20)} color={theme.colors.primary} />
            </TouchableOpacity>
          ),
        }}
      />
      <Stack.Screen
        name="card-information"
        options={{
          headerShown: true,
          title: '',
          headerStyle: {
            backgroundColor: theme.colors.background,
          },
          headerTintColor: theme.colors.textPrimary,
          headerLeft: () => (
            <Text style={{
              fontSize: theme.typography.fontSize.xl,
              fontWeight: 'bold',
              color: theme.colors.primary,
              marginLeft: theme.spacing.md
            }}>
              Dukan<Text style={{ color: theme.colors.textPrimary }}>card</Text>
            </Text>
          ),
          headerRight: () => (
            <TouchableOpacity
              style={{
                padding: theme.spacing.xs,
                borderRadius: theme.borderRadius.md,
                borderWidth: 1,
                borderColor: theme.colors.primary + '40',
                marginRight: theme.spacing.md,
              }}
              onPress={handleLogout}
              activeOpacity={0.7}
            >
              <LogOut size={responsiveFontSize(20)} color={theme.colors.primary} />
            </TouchableOpacity>
          ),
        }}
      />
      <Stack.Screen
        name="address-information"
        options={{
          headerShown: true,
          title: '',
          headerStyle: {
            backgroundColor: theme.colors.background,
          },
          headerTintColor: theme.colors.textPrimary,
          headerLeft: () => (
            <Text style={{
              fontSize: theme.typography.fontSize.xl,
              fontWeight: 'bold',
              color: theme.colors.primary,
              marginLeft: theme.spacing.md
            }}>
              Dukan<Text style={{ color: theme.colors.textPrimary }}>card</Text>
            </Text>
          ),
          headerRight: () => (
            <TouchableOpacity
              style={{
                padding: theme.spacing.xs,
                borderRadius: theme.borderRadius.md,
                borderWidth: 1,
                borderColor: theme.colors.primary + '40',
                marginRight: theme.spacing.md,
              }}
              onPress={handleLogout}
              activeOpacity={0.7}
            >
              <LogOut size={responsiveFontSize(20)} color={theme.colors.primary} />
            </TouchableOpacity>
          ),
        }}
      />
      <Stack.Screen
        name="plan-selection"
        options={{
          headerShown: true,
          title: '',
          headerStyle: {
            backgroundColor: theme.colors.background,
          },
          headerTintColor: theme.colors.textPrimary,
          headerLeft: () => (
            <Text style={{
              fontSize: theme.typography.fontSize.xl,
              fontWeight: 'bold',
              color: theme.colors.primary,
              marginLeft: theme.spacing.md
            }}>
              Dukan<Text style={{ color: theme.colors.textPrimary }}>card</Text>
            </Text>
          ),
          headerRight: () => (
            <TouchableOpacity
              style={{
                padding: theme.spacing.xs,
                borderRadius: theme.borderRadius.md,
                borderWidth: 1,
                borderColor: theme.colors.primary + '40',
                marginRight: theme.spacing.md,
              }}
              onPress={handleLogout}
              activeOpacity={0.7}
            >
              <LogOut size={responsiveFontSize(20)} color={theme.colors.primary} />
            </TouchableOpacity>
          ),
        }}
      />

    </Stack>
  );
}

export default function OnboardingLayout() {
  return (
    <AlertProvider>
      <OnboardingLayoutContent />
    </AlertProvider>
  );
}
