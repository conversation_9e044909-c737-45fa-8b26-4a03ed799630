/* [project]/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-ordinal: initial;
      --tw-slashed-zero: initial;
      --tw-numeric-figure: initial;
      --tw-numeric-spacing: initial;
      --tw-numeric-fraction: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-content: "";
    }
  }
}

@layer theme {
  :root, :host {
    --font-sans: var(--font-sans);
    --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    --color-red-50: oklch(97.1% .013 17.38);
    --color-red-100: oklch(93.6% .032 17.717);
    --color-red-200: oklch(88.5% .062 18.334);
    --color-red-300: oklch(80.8% .114 19.571);
    --color-red-400: oklch(70.4% .191 22.216);
    --color-red-500: oklch(63.7% .237 25.331);
    --color-red-600: oklch(57.7% .245 27.325);
    --color-red-700: oklch(50.5% .213 27.518);
    --color-red-800: oklch(44.4% .177 26.899);
    --color-red-900: oklch(39.6% .141 25.723);
    --color-red-950: oklch(25.8% .092 26.042);
    --color-orange-50: oklch(98% .016 73.684);
    --color-orange-100: oklch(95.4% .038 75.164);
    --color-orange-200: oklch(90.1% .076 70.697);
    --color-orange-300: oklch(83.7% .128 66.29);
    --color-orange-400: oklch(75% .183 55.934);
    --color-orange-500: oklch(70.5% .213 47.604);
    --color-orange-600: oklch(64.6% .222 41.116);
    --color-orange-700: oklch(55.3% .195 38.402);
    --color-orange-800: oklch(47% .157 37.304);
    --color-orange-900: oklch(40.8% .123 38.172);
    --color-orange-950: oklch(26.6% .079 36.259);
    --color-amber-50: oklch(98.7% .022 95.277);
    --color-amber-100: oklch(96.2% .059 95.617);
    --color-amber-200: oklch(92.4% .12 95.746);
    --color-amber-300: oklch(87.9% .169 91.605);
    --color-amber-400: oklch(82.8% .189 84.429);
    --color-amber-500: oklch(76.9% .188 70.08);
    --color-amber-600: oklch(66.6% .179 58.318);
    --color-amber-700: oklch(55.5% .163 48.998);
    --color-amber-800: oklch(47.3% .137 46.201);
    --color-amber-900: oklch(41.4% .112 45.904);
    --color-amber-950: oklch(27.9% .077 45.635);
    --color-yellow-50: oklch(98.7% .026 102.212);
    --color-yellow-100: oklch(97.3% .071 103.193);
    --color-yellow-200: oklch(94.5% .129 101.54);
    --color-yellow-300: oklch(90.5% .182 98.111);
    --color-yellow-400: oklch(85.2% .199 91.936);
    --color-yellow-500: oklch(79.5% .184 86.047);
    --color-yellow-600: oklch(68.1% .162 75.834);
    --color-yellow-700: oklch(55.4% .135 66.442);
    --color-yellow-800: oklch(47.6% .114 61.907);
    --color-yellow-900: oklch(42.1% .095 57.708);
    --color-yellow-950: oklch(28.6% .066 53.813);
    --color-green-50: oklch(98.2% .018 155.826);
    --color-green-100: oklch(96.2% .044 156.743);
    --color-green-200: oklch(92.5% .084 155.995);
    --color-green-300: oklch(87.1% .15 154.449);
    --color-green-400: oklch(79.2% .209 151.711);
    --color-green-500: oklch(72.3% .219 149.579);
    --color-green-600: oklch(62.7% .194 149.214);
    --color-green-700: oklch(52.7% .154 150.069);
    --color-green-800: oklch(44.8% .119 151.328);
    --color-green-900: oklch(39.3% .095 152.535);
    --color-green-950: oklch(26.6% .065 152.934);
    --color-emerald-50: oklch(97.9% .021 166.113);
    --color-emerald-100: oklch(95% .052 163.051);
    --color-emerald-400: oklch(76.5% .177 163.223);
    --color-emerald-500: oklch(69.6% .17 162.48);
    --color-emerald-600: oklch(59.6% .145 163.225);
    --color-emerald-700: oklch(50.8% .118 165.612);
    --color-emerald-900: oklch(37.8% .077 168.94);
    --color-teal-50: oklch(98.4% .014 180.72);
    --color-teal-100: oklch(95.3% .051 180.801);
    --color-teal-200: oklch(91% .096 180.426);
    --color-teal-300: oklch(85.5% .138 181.071);
    --color-teal-400: oklch(77.7% .152 181.912);
    --color-teal-600: oklch(60% .118 184.704);
    --color-teal-800: oklch(43.7% .078 188.216);
    --color-teal-900: oklch(38.6% .063 188.416);
    --color-cyan-500: oklch(71.5% .143 215.221);
    --color-blue-50: oklch(97% .014 254.604);
    --color-blue-100: oklch(93.2% .032 255.585);
    --color-blue-200: oklch(88.2% .059 254.128);
    --color-blue-300: oklch(80.9% .105 251.813);
    --color-blue-400: oklch(70.7% .165 254.624);
    --color-blue-500: oklch(62.3% .214 259.815);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-blue-700: oklch(48.8% .243 264.376);
    --color-blue-800: oklch(42.4% .199 265.638);
    --color-blue-900: oklch(37.9% .146 265.522);
    --color-blue-950: oklch(28.2% .091 267.935);
    --color-indigo-50: oklch(96.2% .018 272.314);
    --color-indigo-100: oklch(93% .034 272.788);
    --color-indigo-200: oklch(87% .065 274.039);
    --color-indigo-400: oklch(67.3% .182 276.935);
    --color-indigo-600: oklch(51.1% .262 276.966);
    --color-indigo-800: oklch(39.8% .195 277.366);
    --color-indigo-900: oklch(35.9% .144 278.697);
    --color-violet-50: oklch(96.9% .016 293.756);
    --color-violet-100: oklch(94.3% .029 294.588);
    --color-violet-300: oklch(81.1% .111 293.571);
    --color-violet-400: oklch(70.2% .183 293.541);
    --color-violet-600: oklch(54.1% .281 293.009);
    --color-violet-700: oklch(49.1% .27 292.581);
    --color-violet-800: oklch(43.2% .232 292.759);
    --color-violet-900: oklch(38% .189 293.745);
    --color-violet-950: oklch(28.3% .141 291.089);
    --color-purple-50: oklch(97.7% .014 308.299);
    --color-purple-100: oklch(94.6% .033 307.174);
    --color-purple-200: oklch(90.2% .063 306.703);
    --color-purple-300: oklch(82.7% .119 306.383);
    --color-purple-400: oklch(71.4% .203 305.504);
    --color-purple-500: oklch(62.7% .265 303.9);
    --color-purple-600: oklch(55.8% .288 302.321);
    --color-purple-700: oklch(49.6% .265 301.924);
    --color-purple-800: oklch(43.8% .218 303.724);
    --color-purple-900: oklch(38.1% .176 304.987);
    --color-purple-950: oklch(29.1% .149 302.717);
    --color-pink-100: oklch(94.8% .028 342.258);
    --color-pink-500: oklch(65.6% .241 354.308);
    --color-pink-600: oklch(59.2% .249 .584);
    --color-pink-700: oklch(52.5% .223 3.958);
    --color-pink-900: oklch(40.8% .153 2.432);
    --color-rose-50: oklch(96.9% .015 12.422);
    --color-rose-100: oklch(94.1% .03 12.58);
    --color-rose-300: oklch(81% .117 11.638);
    --color-rose-400: oklch(71.2% .194 13.428);
    --color-rose-500: oklch(64.5% .246 16.439);
    --color-rose-600: oklch(58.6% .253 17.585);
    --color-rose-700: oklch(51.4% .222 16.935);
    --color-rose-900: oklch(41% .159 10.272);
    --color-gray-50: oklch(98.5% .002 247.839);
    --color-gray-100: oklch(96.7% .003 264.542);
    --color-gray-200: oklch(92.8% .006 264.531);
    --color-gray-300: oklch(87.2% .01 258.338);
    --color-gray-400: oklch(70.7% .022 261.325);
    --color-gray-500: oklch(55.1% .027 264.364);
    --color-gray-600: oklch(44.6% .03 256.802);
    --color-gray-700: oklch(37.3% .034 259.733);
    --color-gray-800: oklch(27.8% .033 256.848);
    --color-gray-900: oklch(21% .034 264.665);
    --color-zinc-600: oklch(44.2% .017 285.786);
    --color-zinc-800: oklch(27.4% .006 286.033);
    --color-neutral-50: oklch(98.5% 0 0);
    --color-neutral-100: oklch(97% 0 0);
    --color-neutral-200: oklch(92.2% 0 0);
    --color-neutral-300: oklch(87% 0 0);
    --color-neutral-400: oklch(70.8% 0 0);
    --color-neutral-500: oklch(55.6% 0 0);
    --color-neutral-600: oklch(43.9% 0 0);
    --color-neutral-700: oklch(37.1% 0 0);
    --color-neutral-800: oklch(26.9% 0 0);
    --color-neutral-900: oklch(20.5% 0 0);
    --color-neutral-950: oklch(14.5% 0 0);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --breakpoint-xl: 80rem;
    --breakpoint-2xl: 96rem;
    --container-xs: 20rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-xl: 36rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-5xl: 64rem;
    --container-6xl: 72rem;
    --container-7xl: 80rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-tighter: -.05em;
    --tracking-tight: -.025em;
    --tracking-wide: .025em;
    --tracking-wider: .05em;
    --tracking-widest: .1em;
    --leading-tight: 1.25;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;
    --radius-xs: .125rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --drop-shadow-sm: 0 1px 2px #00000026;
    --drop-shadow-md: 0 3px 3px #0000001f;
    --ease-out: cubic-bezier(0, 0, .2, 1);
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-ping: ping 1s cubic-bezier(0, 0, .2, 1) infinite;
    --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
    --blur-sm: 8px;
    --blur-md: 12px;
    --blur-lg: 16px;
    --blur-xl: 24px;
    --blur-2xl: 40px;
    --blur-3xl: 64px;
    --aspect-video: 16 / 9;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --color-border: var(--border);
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }

  :root {
    --radius: .625rem;
    --font-sans: "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
    --background: oklch(1 0 0);
    --foreground: oklch(.09 0 0);
    --card: oklch(1 0 0);
    --card-foreground: oklch(.09 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(.09 0 0);
    --secondary: oklch(.96 0 0);
    --secondary-foreground: oklch(.09 0 0);
    --muted: oklch(.96 0 0);
    --muted-foreground: oklch(.45 0 0);
    --accent: oklch(.96 0 0);
    --accent-foreground: oklch(.09 0 0);
    --destructive: oklch(.577 .245 27.325);
    --border: oklch(.9 0 0);
    --input: oklch(.9 0 0);
    --ring: oklch(.5 0 0 / .5);
    --brand-gold: oklch(.769 .11 85);
    --brand-gold-foreground: oklch(.09 0 0);
    --brand-gold-light: oklch(.82 .1 85);
    --brand-gold-dark: oklch(.7 .12 85);
    --primary: oklch(.09 0 0);
    --primary-foreground: oklch(1 0 0);
    --chart-1: oklch(.646 .222 41.116);
    --chart-2: oklch(.6 .118 184.704);
    --chart-3: oklch(.398 .07 227.392);
    --chart-4: oklch(.828 .189 84.429);
    --chart-5: oklch(.769 .188 70.08);
    --sidebar: oklch(1 0 0);
    --sidebar-foreground: oklch(0 0 0);
    --sidebar-primary: oklch(0 0 0);
    --sidebar-primary-foreground: oklch(1 0 0);
    --sidebar-accent: oklch(.95 0 0);
    --sidebar-accent-foreground: oklch(0 0 0);
    --sidebar-border: oklch(.9 0 0);
    --sidebar-ring: oklch(.5 0 0);
  }

  .dark {
    --background: oklch(0 0 0);
    --foreground: oklch(1 0 0);
    --card: oklch(0 0 0);
    --card-foreground: oklch(1 0 0);
    --popover: oklch(.1 0 0);
    --popover-foreground: oklch(1 0 0);
    --secondary: oklch(.15 0 0);
    --secondary-foreground: oklch(1 0 0);
    --muted: oklch(.15 0 0);
    --muted-foreground: oklch(.6 0 0);
    --accent: oklch(.15 0 0);
    --accent-foreground: oklch(1 0 0);
    --destructive: oklch(.704 .191 22.216);
    --border: oklch(1 0 0 / .15);
    --input: oklch(1 0 0 / .18);
    --ring: oklch(1 0 0 / .5);
    --brand-gold: oklch(.769 .11 85);
    --brand-gold-foreground: oklch(0 0 0);
    --brand-gold-light: oklch(.82 .1 85);
    --brand-gold-dark: oklch(.7 .12 85);
    --primary: oklch(1 0 0);
    --primary-foreground: oklch(0 0 0);
    --chart-1: oklch(.488 .243 264.376);
    --chart-2: oklch(.696 .17 162.48);
    --chart-3: oklch(.769 .188 70.08);
    --chart-4: oklch(.627 .265 303.9);
    --chart-5: oklch(.645 .246 16.439);
    --sidebar: oklch(0 0 0);
    --sidebar-foreground: oklch(1 0 0);
    --sidebar-primary: oklch(1 0 0);
    --sidebar-primary-foreground: oklch(0 0 0);
    --sidebar-accent: oklch(.15 0 0);
    --sidebar-accent-foreground: oklch(1 0 0);
    --sidebar-border: oklch(1 0 0 / 10%);
    --sidebar-ring: oklch(.5 0 0);
  }

  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  * {
    border-color: var(--border);
  }

  :focus-visible {
    outline-offset: 2px;
    box-shadow: 0 0 0 2px var(--background), 0 0 0 4px var(--ring);
    outline: 2px solid #0000;
  }

  .dark .recharts-text, .dark .recharts-legend-item-text {
    fill: var(--foreground);
  }

  .dark .recharts-tooltip-wrapper .recharts-default-tooltip {
    background-color: var(--popover) !important;
    border-color: var(--border) !important;
    color: var(--popover-foreground) !important;
  }

  .dark .recharts-cartesian-axis-tick-value {
    fill: var(--muted-foreground);
  }

  .hljs {
    border-radius: .5rem;
    background: hsl(var(--muted)) !important;
    color: hsl(var(--foreground)) !important;
  }

  .dark .hljs {
    background: hsl(var(--muted)) !important;
    color: hsl(var(--foreground)) !important;
  }

  .hljs-keyword, .hljs-selector-tag, .hljs-built_in {
    color: hsl(var(--primary)) !important;
  }

  .hljs-string, .hljs-attr {
    color: #22c55e !important;
  }

  .hljs-number, .hljs-literal {
    color: #f59e0b !important;
  }

  .hljs-comment {
    font-style: italic;
    color: hsl(var(--muted-foreground)) !important;
  }

  .dark .recharts-cartesian-grid-line line {
    stroke: var(--border);
  }

  .dark .recharts-brush-texts {
    fill: var(--muted-foreground);
  }

  * {
    border-color: var(--border);
    outline-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    * {
      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  body {
    background-color: var(--background);
    color: var(--foreground);
  }
}

@layer components {
  .prose {
    --tw-prose-body: hsl(var(--foreground) / .9);
    --tw-prose-headings: hsl(var(--foreground));
    --tw-prose-lead: hsl(var(--foreground) / .8);
    --tw-prose-links: hsl(var(--primary));
    --tw-prose-bold: hsl(var(--foreground));
    --tw-prose-counters: hsl(var(--muted-foreground));
    --tw-prose-bullets: hsl(var(--muted-foreground));
    --tw-prose-hr: hsl(var(--border));
    --tw-prose-quotes: hsl(var(--foreground) / .8);
    --tw-prose-quote-borders: hsl(var(--primary));
    --tw-prose-captions: hsl(var(--muted-foreground));
    --tw-prose-code: hsl(var(--foreground));
    --tw-prose-pre-code: hsl(var(--foreground));
    --tw-prose-pre-bg: hsl(var(--muted));
    --tw-prose-th-borders: hsl(var(--border));
    --tw-prose-td-borders: hsl(var(--border));
  }
}

@layer utilities {
  .\@container\/card-header {
    container: card-header / inline-size;
  }

  .pointer-events-auto {
    pointer-events: auto;
  }

  .pointer-events-none {
    pointer-events: none;
  }

  .collapse {
    visibility: collapse;
  }

  .invisible {
    visibility: hidden;
  }

  .visible {
    visibility: visible;
  }

  .sr-only {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .static {
    position: static;
  }

  .sticky {
    position: sticky;
  }

  .-inset-0\.5 {
    inset: calc(var(--spacing) * -.5);
  }

  .-inset-1 {
    inset: calc(var(--spacing) * -1);
  }

  .-inset-2 {
    inset: calc(var(--spacing) * -2);
  }

  .-inset-4 {
    inset: calc(var(--spacing) * -4);
  }

  .-inset-8 {
    inset: calc(var(--spacing) * -8);
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .inset-x-0 {
    inset-inline: calc(var(--spacing) * 0);
  }

  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }

  .-top-1 {
    top: calc(var(--spacing) * -1);
  }

  .-top-2 {
    top: calc(var(--spacing) * -2);
  }

  .-top-5 {
    top: calc(var(--spacing) * -5);
  }

  .-top-6 {
    top: calc(var(--spacing) * -6);
  }

  .-top-10 {
    top: calc(var(--spacing) * -10);
  }

  .-top-12 {
    top: calc(var(--spacing) * -12);
  }

  .-top-20 {
    top: calc(var(--spacing) * -20);
  }

  .-top-24 {
    top: calc(var(--spacing) * -24);
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-1 {
    top: calc(var(--spacing) * 1);
  }

  .top-1\.5 {
    top: calc(var(--spacing) * 1.5);
  }

  .top-1\/2 {
    top: 50%;
  }

  .top-1\/3 {
    top: 33.3333%;
  }

  .top-1\/4 {
    top: 25%;
  }

  .top-2 {
    top: calc(var(--spacing) * 2);
  }

  .top-2\/3 {
    top: 66.6667%;
  }

  .top-3 {
    top: calc(var(--spacing) * 3);
  }

  .top-3\.5 {
    top: calc(var(--spacing) * 3.5);
  }

  .top-3\/4 {
    top: 75%;
  }

  .top-4 {
    top: calc(var(--spacing) * 4);
  }

  .top-6 {
    top: calc(var(--spacing) * 6);
  }

  .top-8 {
    top: calc(var(--spacing) * 8);
  }

  .top-10 {
    top: calc(var(--spacing) * 10);
  }

  .top-16 {
    top: calc(var(--spacing) * 16);
  }

  .top-20 {
    top: calc(var(--spacing) * 20);
  }

  .top-32 {
    top: calc(var(--spacing) * 32);
  }

  .top-\[50\%\] {
    top: 50%;
  }

  .top-\[80px\] {
    top: 80px;
  }

  .top-full {
    top: 100%;
  }

  .-right-1 {
    right: calc(var(--spacing) * -1);
  }

  .-right-2 {
    right: calc(var(--spacing) * -2);
  }

  .-right-3 {
    right: calc(var(--spacing) * -3);
  }

  .-right-6 {
    right: calc(var(--spacing) * -6);
  }

  .-right-10 {
    right: calc(var(--spacing) * -10);
  }

  .-right-12 {
    right: calc(var(--spacing) * -12);
  }

  .-right-16 {
    right: calc(var(--spacing) * -16);
  }

  .-right-20 {
    right: calc(var(--spacing) * -20);
  }

  .-right-24 {
    right: calc(var(--spacing) * -24);
  }

  .-right-32 {
    right: calc(var(--spacing) * -32);
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .right-1 {
    right: calc(var(--spacing) * 1);
  }

  .right-1\/3 {
    right: 33.3333%;
  }

  .right-1\/4 {
    right: 25%;
  }

  .right-2 {
    right: calc(var(--spacing) * 2);
  }

  .right-3 {
    right: calc(var(--spacing) * 3);
  }

  .right-4 {
    right: calc(var(--spacing) * 4);
  }

  .right-6 {
    right: calc(var(--spacing) * 6);
  }

  .right-8 {
    right: calc(var(--spacing) * 8);
  }

  .right-10 {
    right: calc(var(--spacing) * 10);
  }

  .right-\[10\%\] {
    right: 10%;
  }

  .right-\[15\%\] {
    right: 15%;
  }

  .right-\[20\%\] {
    right: 20%;
  }

  .-bottom-1 {
    bottom: calc(var(--spacing) * -1);
  }

  .-bottom-2 {
    bottom: calc(var(--spacing) * -2);
  }

  .-bottom-4 {
    bottom: calc(var(--spacing) * -4);
  }

  .-bottom-6 {
    bottom: calc(var(--spacing) * -6);
  }

  .-bottom-10 {
    bottom: calc(var(--spacing) * -10);
  }

  .-bottom-12 {
    bottom: calc(var(--spacing) * -12);
  }

  .-bottom-16 {
    bottom: calc(var(--spacing) * -16);
  }

  .-bottom-20 {
    bottom: calc(var(--spacing) * -20);
  }

  .-bottom-24 {
    bottom: calc(var(--spacing) * -24);
  }

  .-bottom-32 {
    bottom: calc(var(--spacing) * -32);
  }

  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }

  .bottom-1 {
    bottom: calc(var(--spacing) * 1);
  }

  .bottom-1\/2 {
    bottom: 50%;
  }

  .bottom-1\/3 {
    bottom: 33.3333%;
  }

  .bottom-1\/4 {
    bottom: 25%;
  }

  .bottom-2 {
    bottom: calc(var(--spacing) * 2);
  }

  .bottom-3 {
    bottom: calc(var(--spacing) * 3);
  }

  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }

  .bottom-6 {
    bottom: calc(var(--spacing) * 6);
  }

  .bottom-8 {
    bottom: calc(var(--spacing) * 8);
  }

  .bottom-20 {
    bottom: calc(var(--spacing) * 20);
  }

  .bottom-24 {
    bottom: calc(var(--spacing) * 24);
  }

  .bottom-\[70px\] {
    bottom: 70px;
  }

  .-left-1 {
    left: calc(var(--spacing) * -1);
  }

  .-left-2 {
    left: calc(var(--spacing) * -2);
  }

  .-left-6 {
    left: calc(var(--spacing) * -6);
  }

  .-left-10 {
    left: calc(var(--spacing) * -10);
  }

  .-left-12 {
    left: calc(var(--spacing) * -12);
  }

  .-left-16 {
    left: calc(var(--spacing) * -16);
  }

  .-left-20 {
    left: calc(var(--spacing) * -20);
  }

  .-left-24 {
    left: calc(var(--spacing) * -24);
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .left-1 {
    left: calc(var(--spacing) * 1);
  }

  .left-1\/2 {
    left: 50%;
  }

  .left-1\/3 {
    left: 33.3333%;
  }

  .left-1\/4 {
    left: 25%;
  }

  .left-2 {
    left: calc(var(--spacing) * 2);
  }

  .left-3 {
    left: calc(var(--spacing) * 3);
  }

  .left-4 {
    left: calc(var(--spacing) * 4);
  }

  .left-6 {
    left: calc(var(--spacing) * 6);
  }

  .left-8 {
    left: calc(var(--spacing) * 8);
  }

  .left-10 {
    left: calc(var(--spacing) * 10);
  }

  .left-\[10\%\] {
    left: 10%;
  }

  .left-\[15\%\] {
    left: 15%;
  }

  .left-\[25\%\] {
    left: 25%;
  }

  .left-\[50\%\] {
    left: 50%;
  }

  .isolate {
    isolation: isolate;
  }

  .-z-10 {
    z-index: calc(10 * -1);
  }

  .z-0 {
    z-index: 0;
  }

  .z-5 {
    z-index: 5;
  }

  .z-10 {
    z-index: 10;
  }

  .z-15 {
    z-index: 15;
  }

  .z-20 {
    z-index: 20;
  }

  .z-25 {
    z-index: 25;
  }

  .z-26 {
    z-index: 26;
  }

  .z-30 {
    z-index: 30;
  }

  .z-40 {
    z-index: 40;
  }

  .z-50 {
    z-index: 50;
  }

  .z-\[-1\] {
    z-index: -1;
  }

  .z-\[60\] {
    z-index: 60;
  }

  .z-\[70\] {
    z-index: 70;
  }

  .z-\[100\] {
    z-index: 100;
  }

  .order-1 {
    order: 1;
  }

  .order-2 {
    order: 2;
  }

  .order-first {
    order: -9999;
  }

  .col-span-2 {
    grid-column: span 2 / span 2;
  }

  .col-span-full {
    grid-column: 1 / -1;
  }

  .col-start-2 {
    grid-column-start: 2;
  }

  .row-span-2 {
    grid-row: span 2 / span 2;
  }

  .row-start-1 {
    grid-row-start: 1;
  }

  .float-left {
    float: left;
  }

  .container {
    width: 100%;
  }

  @media (width >= 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (width >= 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (width >= 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (width >= 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (width >= 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .-m-2 {
    margin: calc(var(--spacing) * -2);
  }

  .-m-px {
    margin: -1px;
  }

  .m-1 {
    margin: calc(var(--spacing) * 1);
  }

  .m-2 {
    margin: calc(var(--spacing) * 2);
  }

  .m-3 {
    margin: calc(var(--spacing) * 3);
  }

  .m-4 {
    margin: calc(var(--spacing) * 4);
  }

  .m-5 {
    margin: calc(var(--spacing) * 5);
  }

  .-mx-1 {
    margin-inline: calc(var(--spacing) * -1);
  }

  .mx-0 {
    margin-inline: calc(var(--spacing) * 0);
  }

  .mx-0\.5 {
    margin-inline: calc(var(--spacing) * .5);
  }

  .mx-1 {
    margin-inline: calc(var(--spacing) * 1);
  }

  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }

  .mx-3 {
    margin-inline: calc(var(--spacing) * 3);
  }

  .mx-3\.5 {
    margin-inline: calc(var(--spacing) * 3.5);
  }

  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }

  .mx-5 {
    margin-inline: calc(var(--spacing) * 5);
  }

  .mx-auto {
    margin-inline: auto;
  }

  .my-0\.5 {
    margin-block: calc(var(--spacing) * .5);
  }

  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }

  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }

  .my-3 {
    margin-block: calc(var(--spacing) * 3);
  }

  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }

  .my-5 {
    margin-block: calc(var(--spacing) * 5);
  }

  .my-6 {
    margin-block: calc(var(--spacing) * 6);
  }

  .my-8 {
    margin-block: calc(var(--spacing) * 8);
  }

  .my-12 {
    margin-block: calc(var(--spacing) * 12);
  }

  .my-16 {
    margin-block: calc(var(--spacing) * 16);
  }

  .-mt-1 {
    margin-top: calc(var(--spacing) * -1);
  }

  .-mt-2 {
    margin-top: calc(var(--spacing) * -2);
  }

  .-mt-4 {
    margin-top: calc(var(--spacing) * -4);
  }

  .mt-0 {
    margin-top: calc(var(--spacing) * 0);
  }

  .mt-0\.5 {
    margin-top: calc(var(--spacing) * .5);
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-1\.5 {
    margin-top: calc(var(--spacing) * 1.5);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-5 {
    margin-top: calc(var(--spacing) * 5);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mt-10 {
    margin-top: calc(var(--spacing) * 10);
  }

  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }

  .mt-16 {
    margin-top: calc(var(--spacing) * 16);
  }

  .mt-20 {
    margin-top: calc(var(--spacing) * 20);
  }

  .mt-auto {
    margin-top: auto;
  }

  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }

  .mr-1\.5 {
    margin-right: calc(var(--spacing) * 1.5);
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }

  .mr-4 {
    margin-right: calc(var(--spacing) * 4);
  }

  .mr-5 {
    margin-right: calc(var(--spacing) * 5);
  }

  .mr-6 {
    margin-right: calc(var(--spacing) * 6);
  }

  .mr-\[0\.3em\] {
    margin-right: .3em;
  }

  .mr-\[0\.25em\] {
    margin-right: .25em;
  }

  .mb-0 {
    margin-bottom: calc(var(--spacing) * 0);
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-1\.5 {
    margin-bottom: calc(var(--spacing) * 1.5);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-2\.5 {
    margin-bottom: calc(var(--spacing) * 2.5);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-5 {
    margin-bottom: calc(var(--spacing) * 5);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }

  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }

  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }

  .mb-20 {
    margin-bottom: calc(var(--spacing) * 20);
  }

  .mb-24 {
    margin-bottom: calc(var(--spacing) * 24);
  }

  .-ml-2 {
    margin-left: calc(var(--spacing) * -2);
  }

  .-ml-4 {
    margin-left: calc(var(--spacing) * -4);
  }

  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }

  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }

  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }

  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }

  .ml-5 {
    margin-left: calc(var(--spacing) * 5);
  }

  .ml-7 {
    margin-left: calc(var(--spacing) * 7);
  }

  .ml-15 {
    margin-left: calc(var(--spacing) * 15);
  }

  .ml-auto {
    margin-left: auto;
  }

  .line-clamp-1 {
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .line-clamp-2 {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .line-clamp-3 {
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .block {
    display: block;
  }

  .contents {
    display: contents;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline {
    display: inline;
  }

  .inline-block {
    display: inline-block;
  }

  .inline-flex {
    display: inline-flex;
  }

  .table {
    display: table;
  }

  .table-caption {
    display: table-caption;
  }

  .table-cell {
    display: table-cell;
  }

  .table-row {
    display: table-row;
  }

  .field-sizing-content {
    field-sizing: content;
  }

  .aspect-\[4\/3\] {
    aspect-ratio: 4 / 3;
  }

  .aspect-\[16\/9\] {
    aspect-ratio: 16 / 9;
  }

  .aspect-square {
    aspect-ratio: 1;
  }

  .aspect-video {
    aspect-ratio: var(--aspect-video);
  }

  .size-2 {
    width: calc(var(--spacing) * 2);
    height: calc(var(--spacing) * 2);
  }

  .size-2\.5 {
    width: calc(var(--spacing) * 2.5);
    height: calc(var(--spacing) * 2.5);
  }

  .size-3\.5 {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }

  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .size-7 {
    width: calc(var(--spacing) * 7);
    height: calc(var(--spacing) * 7);
  }

  .size-8 {
    width: calc(var(--spacing) * 8);
    height: calc(var(--spacing) * 8);
  }

  .size-9 {
    width: calc(var(--spacing) * 9);
    height: calc(var(--spacing) * 9);
  }

  .size-full {
    width: 100%;
    height: 100%;
  }

  .h-0\.5 {
    height: calc(var(--spacing) * .5);
  }

  .h-1 {
    height: calc(var(--spacing) * 1);
  }

  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }

  .h-1\/2 {
    height: 50%;
  }

  .h-1\/3 {
    height: 33.3333%;
  }

  .h-1\/4 {
    height: 25%;
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-2\.5 {
    height: calc(var(--spacing) * 2.5);
  }

  .h-2\/3 {
    height: 66.6667%;
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-3\.5 {
    height: calc(var(--spacing) * 3.5);
  }

  .h-3\/4 {
    height: 75%;
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-7 {
    height: calc(var(--spacing) * 7);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-9 {
    height: calc(var(--spacing) * 9);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-11 {
    height: calc(var(--spacing) * 11);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-14 {
    height: calc(var(--spacing) * 14);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-20 {
    height: calc(var(--spacing) * 20);
  }

  .h-24 {
    height: calc(var(--spacing) * 24);
  }

  .h-32 {
    height: calc(var(--spacing) * 32);
  }

  .h-40 {
    height: calc(var(--spacing) * 40);
  }

  .h-48 {
    height: calc(var(--spacing) * 48);
  }

  .h-60 {
    height: calc(var(--spacing) * 60);
  }

  .h-64 {
    height: calc(var(--spacing) * 64);
  }

  .h-80 {
    height: calc(var(--spacing) * 80);
  }

  .h-96 {
    height: calc(var(--spacing) * 96);
  }

  .h-\[1\.1rem\] {
    height: 1.1rem;
  }

  .h-\[1\.2rem\] {
    height: 1.2rem;
  }

  .h-\[1\.15rem\] {
    height: 1.15rem;
  }

  .h-\[1px\] {
    height: 1px;
  }

  .h-\[2px\] {
    height: 2px;
  }

  .h-\[3px\] {
    height: 3px;
  }

  .h-\[4px\] {
    height: 4px;
  }

  .h-\[6px\] {
    height: 6px;
  }

  .h-\[40\%\] {
    height: 40%;
  }

  .h-\[40vh\] {
    height: 40vh;
  }

  .h-\[45vh\] {
    height: 45vh;
  }

  .h-\[50\%\] {
    height: 50%;
  }

  .h-\[70\%\] {
    height: 70%;
  }

  .h-\[80\%\] {
    height: 80%;
  }

  .h-\[80vh\] {
    height: 80vh;
  }

  .h-\[90\%\] {
    height: 90%;
  }

  .h-\[120px\] {
    height: 120px;
  }

  .h-\[200px\] {
    height: 200px;
  }

  .h-\[280px\] {
    height: 280px;
  }

  .h-\[300px\] {
    height: 300px;
  }

  .h-\[320px\] {
    height: 320px;
  }

  .h-\[350px\] {
    height: 350px;
  }

  .h-\[400px\] {
    height: 400px;
  }

  .h-\[450px\] {
    height: 450px;
  }

  .h-\[500px\] {
    height: 500px;
  }

  .h-\[600px\] {
    height: 600px;
  }

  .h-\[calc\(100\%-1px\)\] {
    height: calc(100% - 1px);
  }

  .h-\[calc\(100\%-12rem\)\] {
    height: calc(100% - 12rem);
  }

  .h-\[var\(--radix-select-trigger-height\)\] {
    height: var(--radix-select-trigger-height);
  }

  .h-auto {
    height: auto;
  }

  .h-fit {
    height: fit-content;
  }

  .h-full {
    height: 100%;
  }

  .h-px {
    height: 1px;
  }

  .h-svh {
    height: 100svh;
  }

  .max-h-\(--radix-dropdown-menu-content-available-height\) {
    max-height: var(--radix-dropdown-menu-content-available-height);
  }

  .max-h-\(--radix-select-content-available-height\) {
    max-height: var(--radix-select-content-available-height);
  }

  .max-h-32 {
    max-height: calc(var(--spacing) * 32);
  }

  .max-h-48 {
    max-height: calc(var(--spacing) * 48);
  }

  .max-h-60 {
    max-height: calc(var(--spacing) * 60);
  }

  .max-h-64 {
    max-height: calc(var(--spacing) * 64);
  }

  .max-h-96 {
    max-height: calc(var(--spacing) * 96);
  }

  .max-h-\[32px\] {
    max-height: 32px;
  }

  .max-h-\[80vh\] {
    max-height: 80vh;
  }

  .max-h-\[90vh\] {
    max-height: 90vh;
  }

  .max-h-\[95vh\] {
    max-height: 95vh;
  }

  .max-h-\[200px\] {
    max-height: 200px;
  }

  .max-h-\[280px\] {
    max-height: 280px;
  }

  .max-h-\[300px\] {
    max-height: 300px;
  }

  .max-h-full {
    max-height: 100%;
  }

  .min-h-0 {
    min-height: calc(var(--spacing) * 0);
  }

  .min-h-4 {
    min-height: calc(var(--spacing) * 4);
  }

  .min-h-16 {
    min-height: calc(var(--spacing) * 16);
  }

  .min-h-\[2\.5rem\] {
    min-height: 2.5rem;
  }

  .min-h-\[40px\] {
    min-height: 40px;
  }

  .min-h-\[48px\] {
    min-height: 48px;
  }

  .min-h-\[60px\] {
    min-height: 60px;
  }

  .min-h-\[80px\] {
    min-height: 80px;
  }

  .min-h-\[80vh\] {
    min-height: 80vh;
  }

  .min-h-\[90vh\] {
    min-height: 90vh;
  }

  .min-h-\[100px\] {
    min-height: 100px;
  }

  .min-h-\[120px\] {
    min-height: 120px;
  }

  .min-h-\[280px\] {
    min-height: 280px;
  }

  .min-h-\[300px\] {
    min-height: 300px;
  }

  .min-h-\[400px\] {
    min-height: 400px;
  }

  .min-h-\[calc\(100vh-80px\)\] {
    min-height: calc(100vh - 80px);
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .min-h-svh {
    min-height: 100svh;
  }

  .w-\(--sidebar-width\) {
    width: var(--sidebar-width);
  }

  .w-0 {
    width: calc(var(--spacing) * 0);
  }

  .w-0\.5 {
    width: calc(var(--spacing) * .5);
  }

  .w-1 {
    width: calc(var(--spacing) * 1);
  }

  .w-1\.5 {
    width: calc(var(--spacing) * 1.5);
  }

  .w-1\/2 {
    width: 50%;
  }

  .w-1\/3 {
    width: 33.3333%;
  }

  .w-1\/4 {
    width: 25%;
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-2\.5 {
    width: calc(var(--spacing) * 2.5);
  }

  .w-2\/3 {
    width: 66.6667%;
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-3\.5 {
    width: calc(var(--spacing) * 3.5);
  }

  .w-3\/4 {
    width: 75%;
  }

  .w-3\/5 {
    width: 60%;
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-4\/5 {
    width: 80%;
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-5\/6 {
    width: 83.3333%;
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-7 {
    width: calc(var(--spacing) * 7);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-9 {
    width: calc(var(--spacing) * 9);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-14 {
    width: calc(var(--spacing) * 14);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-18 {
    width: calc(var(--spacing) * 18);
  }

  .w-20 {
    width: calc(var(--spacing) * 20);
  }

  .w-22 {
    width: calc(var(--spacing) * 22);
  }

  .w-24 {
    width: calc(var(--spacing) * 24);
  }

  .w-28 {
    width: calc(var(--spacing) * 28);
  }

  .w-32 {
    width: calc(var(--spacing) * 32);
  }

  .w-36 {
    width: calc(var(--spacing) * 36);
  }

  .w-40 {
    width: calc(var(--spacing) * 40);
  }

  .w-44 {
    width: calc(var(--spacing) * 44);
  }

  .w-48 {
    width: calc(var(--spacing) * 48);
  }

  .w-56 {
    width: calc(var(--spacing) * 56);
  }

  .w-60 {
    width: calc(var(--spacing) * 60);
  }

  .w-64 {
    width: calc(var(--spacing) * 64);
  }

  .w-72 {
    width: calc(var(--spacing) * 72);
  }

  .w-80 {
    width: calc(var(--spacing) * 80);
  }

  .w-96 {
    width: calc(var(--spacing) * 96);
  }

  .w-\[--radix-dropdown-menu-trigger-width\] {
    width: --radix-dropdown-menu-trigger-width;
  }

  .w-\[--radix-popover-trigger-width\] {
    width: --radix-popover-trigger-width;
  }

  .w-\[1\.1rem\] {
    width: 1.1rem;
  }

  .w-\[1\.2rem\] {
    width: 1.2rem;
  }

  .w-\[3px\] {
    width: 3px;
  }

  .w-\[4px\] {
    width: 4px;
  }

  .w-\[30px\] {
    width: 30px;
  }

  .w-\[40\%\] {
    width: 40%;
  }

  .w-\[40px\] {
    width: 40px;
  }

  .w-\[50\%\] {
    width: 50%;
  }

  .w-\[50px\] {
    width: 50px;
  }

  .w-\[70\%\] {
    width: 70%;
  }

  .w-\[80\%\] {
    width: 80%;
  }

  .w-\[90\%\] {
    width: 90%;
  }

  .w-\[95\%\] {
    width: 95%;
  }

  .w-\[95vw\] {
    width: 95vw;
  }

  .w-\[120px\] {
    width: 120px;
  }

  .w-\[160px\] {
    width: 160px;
  }

  .w-\[180px\] {
    width: 180px;
  }

  .w-\[200\%\] {
    width: 200%;
  }

  .w-\[300px\] {
    width: 300px;
  }

  .w-\[350px\] {
    width: 350px;
  }

  .w-\[400px\] {
    width: 400px;
  }

  .w-\[500px\] {
    width: 500px;
  }

  .w-\[600px\] {
    width: 600px;
  }

  .w-\[calc\(100\%-2rem\)\] {
    width: calc(100% - 2rem);
  }

  .w-\[var\(--radix-popover-trigger-width\)\] {
    width: var(--radix-popover-trigger-width);
  }

  .w-auto {
    width: auto;
  }

  .w-fit {
    width: fit-content;
  }

  .w-full {
    width: 100%;
  }

  .w-px {
    width: 1px;
  }

  .max-w-\(--skeleton-width\) {
    max-width: var(--skeleton-width);
  }

  .max-w-2xl {
    max-width: var(--container-2xl);
  }

  .max-w-3xl {
    max-width: var(--container-3xl);
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-5xl {
    max-width: var(--container-5xl);
  }

  .max-w-6xl {
    max-width: var(--container-6xl);
  }

  .max-w-7xl {
    max-width: var(--container-7xl);
  }

  .max-w-24 {
    max-width: calc(var(--spacing) * 24);
  }

  .max-w-32 {
    max-width: calc(var(--spacing) * 32);
  }

  .max-w-\[85\%\] {
    max-width: 85%;
  }

  .max-w-\[90\%\] {
    max-width: 90%;
  }

  .max-w-\[120px\] {
    max-width: 120px;
  }

  .max-w-\[150px\] {
    max-width: 150px;
  }

  .max-w-\[180px\] {
    max-width: 180px;
  }

  .max-w-\[200px\] {
    max-width: 200px;
  }

  .max-w-\[250px\] {
    max-width: 250px;
  }

  .max-w-\[350px\] {
    max-width: 350px;
  }

  .max-w-\[900px\] {
    max-width: 900px;
  }

  .max-w-\[1920px\] {
    max-width: 1920px;
  }

  .max-w-\[calc\(100\%-2rem\)\] {
    max-width: calc(100% - 2rem);
  }

  .max-w-\[calc\(100vw-2rem\)\] {
    max-width: calc(100vw - 2rem);
  }

  .max-w-full {
    max-width: 100%;
  }

  .max-w-lg {
    max-width: var(--container-lg);
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .max-w-none {
    max-width: none;
  }

  .max-w-screen-2xl {
    max-width: var(--breakpoint-2xl);
  }

  .max-w-screen-xl {
    max-width: var(--breakpoint-xl);
  }

  .max-w-sm {
    max-width: var(--container-sm);
  }

  .max-w-xl {
    max-width: var(--container-xl);
  }

  .max-w-xs {
    max-width: var(--container-xs);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .min-w-5 {
    min-width: calc(var(--spacing) * 5);
  }

  .min-w-48 {
    min-width: calc(var(--spacing) * 48);
  }

  .min-w-56 {
    min-width: calc(var(--spacing) * 56);
  }

  .min-w-\[3rem\] {
    min-width: 3rem;
  }

  .min-w-\[8rem\] {
    min-width: 8rem;
  }

  .min-w-\[120px\] {
    min-width: 120px;
  }

  .min-w-\[160px\] {
    min-width: 160px;
  }

  .min-w-\[180px\] {
    min-width: 180px;
  }

  .min-w-\[200px\] {
    min-width: 200px;
  }

  .min-w-\[var\(--radix-select-trigger-width\)\] {
    min-width: var(--radix-select-trigger-width);
  }

  .flex-1, .flex-\[1\] {
    flex: 1;
  }

  .flex-\[2\] {
    flex: 2;
  }

  .flex-shrink-0, .shrink-0 {
    flex-shrink: 0;
  }

  .flex-grow, .grow {
    flex-grow: 1;
  }

  .grow-0 {
    flex-grow: 0;
  }

  .basis-1\/3 {
    flex-basis: 33.3333%;
  }

  .basis-1\/4 {
    flex-basis: 25%;
  }

  .basis-full {
    flex-basis: 100%;
  }

  .caption-bottom {
    caption-side: bottom;
  }

  .border-collapse {
    border-collapse: collapse;
  }

  .origin-\(--radix-dropdown-menu-content-transform-origin\) {
    transform-origin: var(--radix-dropdown-menu-content-transform-origin);
  }

  .origin-\(--radix-popover-content-transform-origin\) {
    transform-origin: var(--radix-popover-content-transform-origin);
  }

  .origin-\(--radix-select-content-transform-origin\) {
    transform-origin: var(--radix-select-content-transform-origin);
  }

  .origin-\(--radix-tooltip-content-transform-origin\) {
    transform-origin: var(--radix-tooltip-content-transform-origin);
  }

  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-full {
    --tw-translate-x: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-px {
    --tw-translate-x: -1px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-\[-50\%\] {
    --tw-translate-x: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-\[-100\%\] {
    --tw-translate-x: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-px {
    --tw-translate-x: 1px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-1 {
    --tw-translate-y: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-full {
    --tw-translate-y: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-0 {
    --tw-translate-y: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-0\.5 {
    --tw-translate-y: calc(var(--spacing) * .5);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[-50\%\] {
    --tw-translate-y: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[calc\(-50\%_-_2px\)\] {
    --tw-translate-y: calc(-50% - 2px);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .scale-0 {
    --tw-scale-x: 0%;
    --tw-scale-y: 0%;
    --tw-scale-z: 0%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-100 {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-105 {
    --tw-scale-x: 105%;
    --tw-scale-y: 105%;
    --tw-scale-z: 105%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-110 {
    --tw-scale-x: 110%;
    --tw-scale-y: 110%;
    --tw-scale-z: 110%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-150 {
    --tw-scale-x: 150%;
    --tw-scale-y: 150%;
    --tw-scale-z: 150%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .-rotate-3 {
    rotate: -3deg;
  }

  .rotate-0 {
    rotate: none;
  }

  .rotate-3 {
    rotate: 3deg;
  }

  .rotate-6 {
    rotate: 6deg;
  }

  .rotate-12 {
    rotate: 12deg;
  }

  .rotate-45 {
    rotate: 45deg;
  }

  .rotate-90 {
    rotate: 90deg;
  }

  .rotate-180 {
    rotate: 180deg;
  }

  .transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .transform-gpu {
    transform: translateZ(0) var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .animate-ping {
    animation: var(--animate-ping);
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .cursor-default {
    cursor: default;
  }

  .cursor-grab {
    cursor: grab;
  }

  .cursor-help {
    cursor: help;
  }

  .cursor-not-allowed {
    cursor: not-allowed;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .cursor-wait {
    cursor: wait;
  }

  .touch-none {
    touch-action: none;
  }

  .resize {
    resize: both;
  }

  .resize-none {
    resize: none;
  }

  .scroll-my-1 {
    scroll-margin-block: calc(var(--spacing) * 1);
  }

  .scroll-mt-24 {
    scroll-margin-top: calc(var(--spacing) * 24);
  }

  .scroll-py-1 {
    scroll-padding-block: calc(var(--spacing) * 1);
  }

  .list-inside {
    list-style-position: inside;
  }

  .list-decimal {
    list-style-type: decimal;
  }

  .list-disc {
    list-style-type: disc;
  }

  .appearance-none {
    appearance: none;
  }

  .auto-rows-min {
    grid-auto-rows: min-content;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
  }

  .grid-cols-15 {
    grid-template-columns: repeat(15, minmax(0, 1fr));
  }

  .grid-cols-\[0_1fr\] {
    grid-template-columns: 0 1fr;
  }

  .grid-rows-\[auto_auto\] {
    grid-template-rows: auto auto;
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-col-reverse {
    flex-direction: column-reverse;
  }

  .flex-row {
    flex-direction: row;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .items-baseline {
    align-items: baseline;
  }

  .items-center {
    align-items: center;
  }

  .items-end {
    align-items: flex-end;
  }

  .items-start {
    align-items: flex-start;
  }

  .items-stretch {
    align-items: stretch;
  }

  .justify-around {
    justify-content: space-around;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .justify-start {
    justify-content: flex-start;
  }

  .justify-items-start {
    justify-items: start;
  }

  .justify-items-stretch {
    justify-items: stretch;
  }

  .gap-0 {
    gap: calc(var(--spacing) * 0);
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-5 {
    gap: calc(var(--spacing) * 5);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }

  .gap-16 {
    gap: calc(var(--spacing) * 16);
  }

  :where(.space-y-0 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-0\.5 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * .5) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * .5) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-1\.5 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1.5) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 1.5) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-5 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 5) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 5) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-8 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-10 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 10) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 10) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-12 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 12) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 12) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-16 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 16) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 16) * calc(1 - var(--tw-space-y-reverse)));
  }

  .gap-x-3 {
    column-gap: calc(var(--spacing) * 3);
  }

  .gap-x-4 {
    column-gap: calc(var(--spacing) * 4);
  }

  :where(.space-x-0 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 0) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  .gap-y-0\.5 {
    row-gap: calc(var(--spacing) * .5);
  }

  .gap-y-1\.5 {
    row-gap: calc(var(--spacing) * 1.5);
  }

  .gap-y-2 {
    row-gap: calc(var(--spacing) * 2);
  }

  :where(.divide-y > :not(:last-child)) {
    --tw-divide-y-reverse: 0;
    border-bottom-style: var(--tw-border-style);
    border-top-style: var(--tw-border-style);
    border-top-width: calc(1px * var(--tw-divide-y-reverse));
    border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  }

  :where(.divide-neutral-100\/60 > :not(:last-child)) {
    border-color: #f5f5f599;
  }

  @supports (color: color-mix(in lab, red, red)) {
    :where(.divide-neutral-100\/60 > :not(:last-child)) {
      border-color: color-mix(in oklab, var(--color-neutral-100) 60%, transparent);
    }
  }

  .self-start {
    align-self: flex-start;
  }

  .justify-self-end {
    justify-self: flex-end;
  }

  .truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .overflow-auto {
    overflow: auto;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-visible {
    overflow: visible;
  }

  .overflow-x-auto {
    overflow-x: auto;
  }

  .overflow-x-hidden {
    overflow-x: hidden;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }

  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }

  .rounded-\[2px\] {
    border-radius: 2px;
  }

  .rounded-\[4px\] {
    border-radius: 4px;
  }

  .rounded-\[24px\] {
    border-radius: 24px;
  }

  .rounded-\[40px\] {
    border-radius: 40px;
  }

  .rounded-\[inherit\] {
    border-radius: inherit;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius);
  }

  .rounded-md {
    border-radius: calc(var(--radius)  - 2px);
  }

  .rounded-none {
    border-radius: 0;
  }

  .rounded-sm {
    border-radius: calc(var(--radius)  - 4px);
  }

  .rounded-xl {
    border-radius: calc(var(--radius)  + 4px);
  }

  .rounded-xs {
    border-radius: var(--radius-xs);
  }

  .rounded-t-2xl {
    border-top-left-radius: var(--radius-2xl);
    border-top-right-radius: var(--radius-2xl);
  }

  .rounded-t-md {
    border-top-left-radius: calc(var(--radius)  - 2px);
    border-top-right-radius: calc(var(--radius)  - 2px);
  }

  .rounded-t-xl {
    border-top-left-radius: calc(var(--radius)  + 4px);
    border-top-right-radius: calc(var(--radius)  + 4px);
  }

  .rounded-l-lg {
    border-top-left-radius: var(--radius);
    border-bottom-left-radius: var(--radius);
  }

  .rounded-tl-full {
    border-top-left-radius: 3.40282e38px;
  }

  .rounded-r-lg {
    border-top-right-radius: var(--radius);
    border-bottom-right-radius: var(--radius);
  }

  .rounded-r-xl {
    border-top-right-radius: calc(var(--radius)  + 4px);
    border-bottom-right-radius: calc(var(--radius)  + 4px);
  }

  .rounded-tr-full {
    border-top-right-radius: 3.40282e38px;
  }

  .rounded-b-md {
    border-bottom-right-radius: calc(var(--radius)  - 2px);
    border-bottom-left-radius: calc(var(--radius)  - 2px);
  }

  .rounded-b-xl {
    border-bottom-right-radius: calc(var(--radius)  + 4px);
    border-bottom-left-radius: calc(var(--radius)  + 4px);
  }

  .rounded-br-full {
    border-bottom-right-radius: 3.40282e38px;
  }

  .rounded-bl-full {
    border-bottom-left-radius: 3.40282e38px;
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-3 {
    border-style: var(--tw-border-style);
    border-width: 3px;
  }

  .border-4 {
    border-style: var(--tw-border-style);
    border-width: 4px;
  }

  .border-8 {
    border-style: var(--tw-border-style);
    border-width: 8px;
  }

  .border-\[1\.5px\] {
    border-style: var(--tw-border-style);
    border-width: 1.5px;
  }

  .border-y {
    border-block-style: var(--tw-border-style);
    border-block-width: 1px;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-t-2 {
    border-top-style: var(--tw-border-style);
    border-top-width: 2px;
  }

  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .border-r-2 {
    border-right-style: var(--tw-border-style);
    border-right-width: 2px;
  }

  .border-r-4 {
    border-right-style: var(--tw-border-style);
    border-right-width: 4px;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }

  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .border-l-0 {
    border-left-style: var(--tw-border-style);
    border-left-width: 0;
  }

  .border-l-2 {
    border-left-style: var(--tw-border-style);
    border-left-width: 2px;
  }

  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }

  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }

  .border-none {
    --tw-border-style: none;
    border-style: none;
  }

  .border-\(--color-border\) {
    border-color: var(--color-border);
  }

  .border-\[\#C29D5B\]\/20 {
    border-color: oklab(71.5721% .0152636 .093965 / .2);
  }

  .border-\[\#C29D5B\]\/30 {
    border-color: oklab(71.5721% .0152636 .093965 / .3);
  }

  .border-\[\#D4AF37\]\/20 {
    border-color: oklab(76.6528% -.00256401 .138654 / .2);
  }

  .border-\[--theme-color-50\] {
    border-color: --theme-color-50;
  }

  .border-\[--theme-color\] {
    border-color: --theme-color;
  }

  .border-\[var\(--brand-gold\)\] {
    border-color: var(--brand-gold);
  }

  .border-\[var\(--brand-gold\)\]\/10 {
    border-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-\[var\(--brand-gold\)\]\/10 {
      border-color: color-mix(in oklab, var(--brand-gold) 10%, transparent);
    }
  }

  .border-\[var\(--brand-gold\)\]\/20 {
    border-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-\[var\(--brand-gold\)\]\/20 {
      border-color: color-mix(in oklab, var(--brand-gold) 20%, transparent);
    }
  }

  .border-\[var\(--brand-gold\)\]\/30 {
    border-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-\[var\(--brand-gold\)\]\/30 {
      border-color: color-mix(in oklab, var(--brand-gold) 30%, transparent);
    }
  }

  .border-\[var\(--brand-gold\)\]\/50 {
    border-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-\[var\(--brand-gold\)\]\/50 {
      border-color: color-mix(in oklab, var(--brand-gold) 50%, transparent);
    }
  }

  .border-\[var\(--theme-color\)\] {
    border-color: var(--theme-color);
  }

  .border-amber-100 {
    border-color: var(--color-amber-100);
  }

  .border-amber-200 {
    border-color: var(--color-amber-200);
  }

  .border-amber-200\/50 {
    border-color: #fee68580;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-amber-200\/50 {
      border-color: color-mix(in oklab, var(--color-amber-200) 50%, transparent);
    }
  }

  .border-amber-300 {
    border-color: var(--color-amber-300);
  }

  .border-amber-300\/70 {
    border-color: #ffd236b3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-amber-300\/70 {
      border-color: color-mix(in oklab, var(--color-amber-300) 70%, transparent);
    }
  }

  .border-amber-500 {
    border-color: var(--color-amber-500);
  }

  .border-amber-500\/20 {
    border-color: #f99c0033;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-amber-500\/20 {
      border-color: color-mix(in oklab, var(--color-amber-500) 20%, transparent);
    }
  }

  .border-amber-500\/30 {
    border-color: #f99c004d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-amber-500\/30 {
      border-color: color-mix(in oklab, var(--color-amber-500) 30%, transparent);
    }
  }

  .border-background {
    border-color: var(--background);
  }

  .border-blue-100 {
    border-color: var(--color-blue-100);
  }

  .border-blue-200 {
    border-color: var(--color-blue-200);
  }

  .border-blue-200\/50 {
    border-color: #bedbff80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-blue-200\/50 {
      border-color: color-mix(in oklab, var(--color-blue-200) 50%, transparent);
    }
  }

  .border-blue-300 {
    border-color: var(--color-blue-300);
  }

  .border-blue-300\/70 {
    border-color: #90c5ffb3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-blue-300\/70 {
      border-color: color-mix(in oklab, var(--color-blue-300) 70%, transparent);
    }
  }

  .border-blue-500 {
    border-color: var(--color-blue-500);
  }

  .border-blue-500\/20 {
    border-color: #3080ff33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-blue-500\/20 {
      border-color: color-mix(in oklab, var(--color-blue-500) 20%, transparent);
    }
  }

  .border-blue-500\/50 {
    border-color: #3080ff80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-blue-500\/50 {
      border-color: color-mix(in oklab, var(--color-blue-500) 50%, transparent);
    }
  }

  .border-border {
    border-color: var(--border);
  }

  .border-border\/20 {
    border-color: var(--border);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-border\/20 {
      border-color: color-mix(in oklab, var(--border) 20%, transparent);
    }
  }

  .border-border\/30 {
    border-color: var(--border);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-border\/30 {
      border-color: color-mix(in oklab, var(--border) 30%, transparent);
    }
  }

  .border-border\/40 {
    border-color: var(--border);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-border\/40 {
      border-color: color-mix(in oklab, var(--border) 40%, transparent);
    }
  }

  .border-border\/50 {
    border-color: var(--border);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-border\/50 {
      border-color: color-mix(in oklab, var(--border) 50%, transparent);
    }
  }

  .border-border\/80 {
    border-color: var(--border);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-border\/80 {
      border-color: color-mix(in oklab, var(--border) 80%, transparent);
    }
  }

  .border-current {
    border-color: currentColor;
  }

  .border-current\/10 {
    border-color: currentColor;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-current\/10 {
      border-color: color-mix(in oklab, currentcolor 10%, transparent);
    }
  }

  .border-current\/20 {
    border-color: currentColor;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-current\/20 {
      border-color: color-mix(in oklab, currentcolor 20%, transparent);
    }
  }

  .border-destructive {
    border-color: var(--destructive);
  }

  .border-destructive\/20 {
    border-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-destructive\/20 {
      border-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .border-emerald-500\/20 {
    border-color: #00bb7f33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-emerald-500\/20 {
      border-color: color-mix(in oklab, var(--color-emerald-500) 20%, transparent);
    }
  }

  .border-gray-200 {
    border-color: var(--color-gray-200);
  }

  .border-gray-300 {
    border-color: var(--color-gray-300);
  }

  .border-gray-300\/70 {
    border-color: #d1d5dcb3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-gray-300\/70 {
      border-color: color-mix(in oklab, var(--color-gray-300) 70%, transparent);
    }
  }

  .border-green-100 {
    border-color: var(--color-green-100);
  }

  .border-green-200 {
    border-color: var(--color-green-200);
  }

  .border-green-300 {
    border-color: var(--color-green-300);
  }

  .border-green-300\/70 {
    border-color: #7bf1a8b3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-green-300\/70 {
      border-color: color-mix(in oklab, var(--color-green-300) 70%, transparent);
    }
  }

  .border-green-500 {
    border-color: var(--color-green-500);
  }

  .border-green-500\/20 {
    border-color: #00c75833;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-green-500\/20 {
      border-color: color-mix(in oklab, var(--color-green-500) 20%, transparent);
    }
  }

  .border-green-500\/30 {
    border-color: #00c7584d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-green-500\/30 {
      border-color: color-mix(in oklab, var(--color-green-500) 30%, transparent);
    }
  }

  .border-green-500\/50 {
    border-color: #00c75880;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-green-500\/50 {
      border-color: color-mix(in oklab, var(--color-green-500) 50%, transparent);
    }
  }

  .border-indigo-200 {
    border-color: var(--color-indigo-200);
  }

  .border-input {
    border-color: var(--input);
  }

  .border-muted-foreground\/25 {
    border-color: var(--muted-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-muted-foreground\/25 {
      border-color: color-mix(in oklab, var(--muted-foreground) 25%, transparent);
    }
  }

  .border-neutral-100 {
    border-color: var(--color-neutral-100);
  }

  .border-neutral-100\/60 {
    border-color: #f5f5f599;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-neutral-100\/60 {
      border-color: color-mix(in oklab, var(--color-neutral-100) 60%, transparent);
    }
  }

  .border-neutral-200 {
    border-color: var(--color-neutral-200);
  }

  .border-neutral-200\/30 {
    border-color: #e5e5e54d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-neutral-200\/30 {
      border-color: color-mix(in oklab, var(--color-neutral-200) 30%, transparent);
    }
  }

  .border-neutral-200\/50 {
    border-color: #e5e5e580;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-neutral-200\/50 {
      border-color: color-mix(in oklab, var(--color-neutral-200) 50%, transparent);
    }
  }

  .border-neutral-200\/60 {
    border-color: #e5e5e599;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-neutral-200\/60 {
      border-color: color-mix(in oklab, var(--color-neutral-200) 60%, transparent);
    }
  }

  .border-neutral-200\/70 {
    border-color: #e5e5e5b3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-neutral-200\/70 {
      border-color: color-mix(in oklab, var(--color-neutral-200) 70%, transparent);
    }
  }

  .border-neutral-200\/80 {
    border-color: #e5e5e5cc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-neutral-200\/80 {
      border-color: color-mix(in oklab, var(--color-neutral-200) 80%, transparent);
    }
  }

  .border-neutral-300 {
    border-color: var(--color-neutral-300);
  }

  .border-neutral-300\/50 {
    border-color: #d4d4d480;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-neutral-300\/50 {
      border-color: color-mix(in oklab, var(--color-neutral-300) 50%, transparent);
    }
  }

  .border-neutral-300\/60 {
    border-color: #d4d4d499;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-neutral-300\/60 {
      border-color: color-mix(in oklab, var(--color-neutral-300) 60%, transparent);
    }
  }

  .border-neutral-400 {
    border-color: var(--color-neutral-400);
  }

  .border-neutral-500\/10 {
    border-color: #7373731a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-neutral-500\/10 {
      border-color: color-mix(in oklab, var(--color-neutral-500) 10%, transparent);
    }
  }

  .border-neutral-500\/30 {
    border-color: #7373734d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-neutral-500\/30 {
      border-color: color-mix(in oklab, var(--color-neutral-500) 30%, transparent);
    }
  }

  .border-neutral-500\/50 {
    border-color: #73737380;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-neutral-500\/50 {
      border-color: color-mix(in oklab, var(--color-neutral-500) 50%, transparent);
    }
  }

  .border-neutral-700 {
    border-color: var(--color-neutral-700);
  }

  .border-neutral-700\/50 {
    border-color: #40404080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-neutral-700\/50 {
      border-color: color-mix(in oklab, var(--color-neutral-700) 50%, transparent);
    }
  }

  .border-neutral-800 {
    border-color: var(--color-neutral-800);
  }

  .border-orange-200 {
    border-color: var(--color-orange-200);
  }

  .border-primary {
    border-color: var(--primary);
  }

  .border-primary\/20 {
    border-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-primary\/20 {
      border-color: color-mix(in oklab, var(--primary) 20%, transparent);
    }
  }

  .border-primary\/30 {
    border-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-primary\/30 {
      border-color: color-mix(in oklab, var(--primary) 30%, transparent);
    }
  }

  .border-primary\/40 {
    border-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-primary\/40 {
      border-color: color-mix(in oklab, var(--primary) 40%, transparent);
    }
  }

  .border-purple-100 {
    border-color: var(--color-purple-100);
  }

  .border-purple-100\/30 {
    border-color: #f3e8ff4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-purple-100\/30 {
      border-color: color-mix(in oklab, var(--color-purple-100) 30%, transparent);
    }
  }

  .border-purple-200 {
    border-color: var(--color-purple-200);
  }

  .border-purple-200\/50 {
    border-color: #e9d5ff80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-purple-200\/50 {
      border-color: color-mix(in oklab, var(--color-purple-200) 50%, transparent);
    }
  }

  .border-purple-300\/70 {
    border-color: #d9b3ffb3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-purple-300\/70 {
      border-color: color-mix(in oklab, var(--color-purple-300) 70%, transparent);
    }
  }

  .border-purple-500 {
    border-color: var(--color-purple-500);
  }

  .border-purple-500\/50 {
    border-color: #ac4bff80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-purple-500\/50 {
      border-color: color-mix(in oklab, var(--color-purple-500) 50%, transparent);
    }
  }

  .border-red-100 {
    border-color: var(--color-red-100);
  }

  .border-red-200 {
    border-color: var(--color-red-200);
  }

  .border-red-200\/50 {
    border-color: #ffcaca80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-red-200\/50 {
      border-color: color-mix(in oklab, var(--color-red-200) 50%, transparent);
    }
  }

  .border-red-200\/60 {
    border-color: #ffcaca99;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-red-200\/60 {
      border-color: color-mix(in oklab, var(--color-red-200) 60%, transparent);
    }
  }

  .border-red-300\/70 {
    border-color: #ffa3a3b3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-red-300\/70 {
      border-color: color-mix(in oklab, var(--color-red-300) 70%, transparent);
    }
  }

  .border-red-500 {
    border-color: var(--color-red-500);
  }

  .border-red-500\/20 {
    border-color: #fb2c3633;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-red-500\/20 {
      border-color: color-mix(in oklab, var(--color-red-500) 20%, transparent);
    }
  }

  .border-red-500\/50 {
    border-color: #fb2c3680;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-red-500\/50 {
      border-color: color-mix(in oklab, var(--color-red-500) 50%, transparent);
    }
  }

  .border-sidebar-border {
    border-color: var(--sidebar-border);
  }

  .border-teal-200 {
    border-color: var(--color-teal-200);
  }

  .border-transparent {
    border-color: #0000;
  }

  .border-violet-100 {
    border-color: var(--color-violet-100);
  }

  .border-white {
    border-color: var(--color-white);
  }

  .border-white\/10 {
    border-color: #ffffff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-white\/10 {
      border-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .border-white\/20 {
    border-color: #fff3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-white\/20 {
      border-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  .border-yellow-200 {
    border-color: var(--color-yellow-200);
  }

  .border-yellow-200\/50 {
    border-color: #fff08580;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-yellow-200\/50 {
      border-color: color-mix(in oklab, var(--color-yellow-200) 50%, transparent);
    }
  }

  .border-yellow-300\/70 {
    border-color: #ffe02ab3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-yellow-300\/70 {
      border-color: color-mix(in oklab, var(--color-yellow-300) 70%, transparent);
    }
  }

  .border-t-\[var\(--brand-gold\)\] {
    border-top-color: var(--brand-gold);
  }

  .border-t-transparent {
    border-top-color: #0000;
  }

  .border-r-primary\/50 {
    border-right-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-r-primary\/50 {
      border-right-color: color-mix(in oklab, var(--primary) 50%, transparent);
    }
  }

  .border-l-primary {
    border-left-color: var(--primary);
  }

  .border-l-primary\/50 {
    border-left-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-l-primary\/50 {
      border-left-color: color-mix(in oklab, var(--primary) 50%, transparent);
    }
  }

  .border-l-transparent {
    border-left-color: #0000;
  }

  .bg-\(--color-bg\) {
    background-color: var(--color-bg);
  }

  .bg-\[\#C29D5B\]\/10 {
    background-color: oklab(71.5721% .0152636 .093965 / .1);
  }

  .bg-\[\#D4AF37\] {
    background-color: #d4af37;
  }

  .bg-\[\#D4AF37\]\/10 {
    background-color: oklab(76.6528% -.00256401 .138654 / .1);
  }

  .bg-\[--theme-color\] {
    background-color: --theme-color;
  }

  .bg-\[--theme-color\]\/5 {
    background-color: color-mix(in oklab, --theme-color 5%, transparent);
  }

  .bg-\[--theme-color\]\/20 {
    background-color: color-mix(in oklab, --theme-color 20%, transparent);
  }

  .bg-\[var\(--brand-gold\)\] {
    background-color: var(--brand-gold);
  }

  .bg-\[var\(--brand-gold\)\]\/5 {
    background-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\[var\(--brand-gold\)\]\/5 {
      background-color: color-mix(in oklab, var(--brand-gold) 5%, transparent);
    }
  }

  .bg-\[var\(--brand-gold\)\]\/8 {
    background-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\[var\(--brand-gold\)\]\/8 {
      background-color: color-mix(in oklab, var(--brand-gold) 8%, transparent);
    }
  }

  .bg-\[var\(--brand-gold\)\]\/10 {
    background-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\[var\(--brand-gold\)\]\/10 {
      background-color: color-mix(in oklab, var(--brand-gold) 10%, transparent);
    }
  }

  .bg-\[var\(--brand-gold\)\]\/15 {
    background-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\[var\(--brand-gold\)\]\/15 {
      background-color: color-mix(in oklab, var(--brand-gold) 15%, transparent);
    }
  }

  .bg-\[var\(--brand-gold\)\]\/20 {
    background-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\[var\(--brand-gold\)\]\/20 {
      background-color: color-mix(in oklab, var(--brand-gold) 20%, transparent);
    }
  }

  .bg-\[var\(--brand-gold\)\]\/30 {
    background-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\[var\(--brand-gold\)\]\/30 {
      background-color: color-mix(in oklab, var(--brand-gold) 30%, transparent);
    }
  }

  .bg-\[var\(--brand-gold\)\]\/40 {
    background-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\[var\(--brand-gold\)\]\/40 {
      background-color: color-mix(in oklab, var(--brand-gold) 40%, transparent);
    }
  }

  .bg-\[var\(--brand-gold\)\]\/50 {
    background-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\[var\(--brand-gold\)\]\/50 {
      background-color: color-mix(in oklab, var(--brand-gold) 50%, transparent);
    }
  }

  .bg-\[var\(--brand-gold\)\]\/70 {
    background-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\[var\(--brand-gold\)\]\/70 {
      background-color: color-mix(in oklab, var(--brand-gold) 70%, transparent);
    }
  }

  .bg-\[var\(--brand-gold\)\]\/80 {
    background-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\[var\(--brand-gold\)\]\/80 {
      background-color: color-mix(in oklab, var(--brand-gold) 80%, transparent);
    }
  }

  .bg-\[var\(--brand-gold-rgb\)\]\/5 {
    background-color: var(--brand-gold-rgb);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\[var\(--brand-gold-rgb\)\]\/5 {
      background-color: color-mix(in oklab, var(--brand-gold-rgb) 5%, transparent);
    }
  }

  .bg-\[var\(--theme-color-5\)\] {
    background-color: var(--theme-color-5);
  }

  .bg-\[var\(--theme-color-10\)\] {
    background-color: var(--theme-color-10);
  }

  .bg-\[var\(--theme-color-30\)\] {
    background-color: var(--theme-color-30);
  }

  .bg-\[var\(--theme-color-50\)\] {
    background-color: var(--theme-color-50);
  }

  .bg-accent {
    background-color: var(--accent);
  }

  .bg-amber-50 {
    background-color: var(--color-amber-50);
  }

  .bg-amber-100 {
    background-color: var(--color-amber-100);
  }

  .bg-amber-100\/50 {
    background-color: #fef3c680;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-amber-100\/50 {
      background-color: color-mix(in oklab, var(--color-amber-100) 50%, transparent);
    }
  }

  .bg-amber-400 {
    background-color: var(--color-amber-400);
  }

  .bg-amber-400\/5 {
    background-color: #fcbb000d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-amber-400\/5 {
      background-color: color-mix(in oklab, var(--color-amber-400) 5%, transparent);
    }
  }

  .bg-amber-500 {
    background-color: var(--color-amber-500);
  }

  .bg-amber-500\/10 {
    background-color: #f99c001a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-amber-500\/10 {
      background-color: color-mix(in oklab, var(--color-amber-500) 10%, transparent);
    }
  }

  .bg-amber-500\/20 {
    background-color: #f99c0033;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-amber-500\/20 {
      background-color: color-mix(in oklab, var(--color-amber-500) 20%, transparent);
    }
  }

  .bg-background {
    background-color: var(--background);
  }

  .bg-background\/50 {
    background-color: var(--background);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background\/50 {
      background-color: color-mix(in oklab, var(--background) 50%, transparent);
    }
  }

  .bg-background\/80 {
    background-color: var(--background);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background\/80 {
      background-color: color-mix(in oklab, var(--background) 80%, transparent);
    }
  }

  .bg-background\/90 {
    background-color: var(--background);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background\/90 {
      background-color: color-mix(in oklab, var(--background) 90%, transparent);
    }
  }

  .bg-background\/95 {
    background-color: var(--background);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background\/95 {
      background-color: color-mix(in oklab, var(--background) 95%, transparent);
    }
  }

  .bg-black {
    background-color: var(--color-black);
  }

  .bg-black\/0 {
    background-color: #0000;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/0 {
      background-color: color-mix(in oklab, var(--color-black) 0%, transparent);
    }
  }

  .bg-black\/5 {
    background-color: #0000000d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/5 {
      background-color: color-mix(in oklab, var(--color-black) 5%, transparent);
    }
  }

  .bg-black\/10 {
    background-color: #0000001a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/10 {
      background-color: color-mix(in oklab, var(--color-black) 10%, transparent);
    }
  }

  .bg-black\/50 {
    background-color: #00000080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/50 {
      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }

  .bg-black\/60 {
    background-color: #0009;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/60 {
      background-color: color-mix(in oklab, var(--color-black) 60%, transparent);
    }
  }

  .bg-black\/70 {
    background-color: #000000b3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/70 {
      background-color: color-mix(in oklab, var(--color-black) 70%, transparent);
    }
  }

  .bg-black\/90 {
    background-color: #000000e6;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/90 {
      background-color: color-mix(in oklab, var(--color-black) 90%, transparent);
    }
  }

  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }

  .bg-blue-50\/20 {
    background-color: #eff6ff33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-blue-50\/20 {
      background-color: color-mix(in oklab, var(--color-blue-50) 20%, transparent);
    }
  }

  .bg-blue-50\/50 {
    background-color: #eff6ff80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-blue-50\/50 {
      background-color: color-mix(in oklab, var(--color-blue-50) 50%, transparent);
    }
  }

  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }

  .bg-blue-100\/20 {
    background-color: #dbeafe33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-blue-100\/20 {
      background-color: color-mix(in oklab, var(--color-blue-100) 20%, transparent);
    }
  }

  .bg-blue-100\/30 {
    background-color: #dbeafe4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-blue-100\/30 {
      background-color: color-mix(in oklab, var(--color-blue-100) 30%, transparent);
    }
  }

  .bg-blue-100\/50 {
    background-color: #dbeafe80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-blue-100\/50 {
      background-color: color-mix(in oklab, var(--color-blue-100) 50%, transparent);
    }
  }

  .bg-blue-300\/5 {
    background-color: #90c5ff0d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-blue-300\/5 {
      background-color: color-mix(in oklab, var(--color-blue-300) 5%, transparent);
    }
  }

  .bg-blue-400\/5 {
    background-color: #54a2ff0d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-blue-400\/5 {
      background-color: color-mix(in oklab, var(--color-blue-400) 5%, transparent);
    }
  }

  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }

  .bg-blue-500\/5 {
    background-color: #3080ff0d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-blue-500\/5 {
      background-color: color-mix(in oklab, var(--color-blue-500) 5%, transparent);
    }
  }

  .bg-blue-500\/10 {
    background-color: #3080ff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-blue-500\/10 {
      background-color: color-mix(in oklab, var(--color-blue-500) 10%, transparent);
    }
  }

  .bg-blue-500\/20 {
    background-color: #3080ff33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-blue-500\/20 {
      background-color: color-mix(in oklab, var(--color-blue-500) 20%, transparent);
    }
  }

  .bg-blue-500\/30 {
    background-color: #3080ff4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-blue-500\/30 {
      background-color: color-mix(in oklab, var(--color-blue-500) 30%, transparent);
    }
  }

  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }

  .bg-blue-800 {
    background-color: var(--color-blue-800);
  }

  .bg-border {
    background-color: var(--border);
  }

  .bg-border\/50 {
    background-color: var(--border);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-border\/50 {
      background-color: color-mix(in oklab, var(--border) 50%, transparent);
    }
  }

  .bg-card {
    background-color: var(--card);
  }

  .bg-card\/30 {
    background-color: var(--card);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-card\/30 {
      background-color: color-mix(in oklab, var(--card) 30%, transparent);
    }
  }

  .bg-card\/50 {
    background-color: var(--card);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-card\/50 {
      background-color: color-mix(in oklab, var(--card) 50%, transparent);
    }
  }

  .bg-current {
    background-color: currentColor;
  }

  .bg-current\/5 {
    background-color: currentColor;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-current\/5 {
      background-color: color-mix(in oklab, currentcolor 5%, transparent);
    }
  }

  .bg-current\/10 {
    background-color: currentColor;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-current\/10 {
      background-color: color-mix(in oklab, currentcolor 10%, transparent);
    }
  }

  .bg-destructive {
    background-color: var(--destructive);
  }

  .bg-destructive\/5 {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-destructive\/5 {
      background-color: color-mix(in oklab, var(--destructive) 5%, transparent);
    }
  }

  .bg-destructive\/10 {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-destructive\/10 {
      background-color: color-mix(in oklab, var(--destructive) 10%, transparent);
    }
  }

  .bg-emerald-50 {
    background-color: var(--color-emerald-50);
  }

  .bg-emerald-100 {
    background-color: var(--color-emerald-100);
  }

  .bg-emerald-500 {
    background-color: var(--color-emerald-500);
  }

  .bg-foreground {
    background-color: var(--foreground);
  }

  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }

  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }

  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }

  .bg-gray-400 {
    background-color: var(--color-gray-400);
  }

  .bg-gray-500 {
    background-color: var(--color-gray-500);
  }

  .bg-gray-600 {
    background-color: var(--color-gray-600);
  }

  .bg-green-50 {
    background-color: var(--color-green-50);
  }

  .bg-green-100 {
    background-color: var(--color-green-100);
  }

  .bg-green-500 {
    background-color: var(--color-green-500);
  }

  .bg-green-500\/10 {
    background-color: #00c7581a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-green-500\/10 {
      background-color: color-mix(in oklab, var(--color-green-500) 10%, transparent);
    }
  }

  .bg-green-600 {
    background-color: var(--color-green-600);
  }

  .bg-indigo-50 {
    background-color: var(--color-indigo-50);
  }

  .bg-muted {
    background-color: var(--muted);
  }

  .bg-muted\/20 {
    background-color: var(--muted);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted\/20 {
      background-color: color-mix(in oklab, var(--muted) 20%, transparent);
    }
  }

  .bg-muted\/30 {
    background-color: var(--muted);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted\/30 {
      background-color: color-mix(in oklab, var(--muted) 30%, transparent);
    }
  }

  .bg-muted\/50 {
    background-color: var(--muted);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted\/50 {
      background-color: color-mix(in oklab, var(--muted) 50%, transparent);
    }
  }

  .bg-muted\/80 {
    background-color: var(--muted);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted\/80 {
      background-color: color-mix(in oklab, var(--muted) 80%, transparent);
    }
  }

  .bg-neutral-50 {
    background-color: var(--color-neutral-50);
  }

  .bg-neutral-50\/50 {
    background-color: #fafafa80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-neutral-50\/50 {
      background-color: color-mix(in oklab, var(--color-neutral-50) 50%, transparent);
    }
  }

  .bg-neutral-100 {
    background-color: var(--color-neutral-100);
  }

  .bg-neutral-100\/50 {
    background-color: #f5f5f580;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-neutral-100\/50 {
      background-color: color-mix(in oklab, var(--color-neutral-100) 50%, transparent);
    }
  }

  .bg-neutral-100\/80 {
    background-color: #f5f5f5cc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-neutral-100\/80 {
      background-color: color-mix(in oklab, var(--color-neutral-100) 80%, transparent);
    }
  }

  .bg-neutral-200 {
    background-color: var(--color-neutral-200);
  }

  .bg-neutral-200\/50 {
    background-color: #e5e5e580;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-neutral-200\/50 {
      background-color: color-mix(in oklab, var(--color-neutral-200) 50%, transparent);
    }
  }

  .bg-neutral-300 {
    background-color: var(--color-neutral-300);
  }

  .bg-neutral-300\/5 {
    background-color: #d4d4d40d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-neutral-300\/5 {
      background-color: color-mix(in oklab, var(--color-neutral-300) 5%, transparent);
    }
  }

  .bg-neutral-400 {
    background-color: var(--color-neutral-400);
  }

  .bg-neutral-500\/10 {
    background-color: #7373731a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-neutral-500\/10 {
      background-color: color-mix(in oklab, var(--color-neutral-500) 10%, transparent);
    }
  }

  .bg-neutral-700 {
    background-color: var(--color-neutral-700);
  }

  .bg-neutral-800 {
    background-color: var(--color-neutral-800);
  }

  .bg-neutral-800\/5 {
    background-color: #2626260d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-neutral-800\/5 {
      background-color: color-mix(in oklab, var(--color-neutral-800) 5%, transparent);
    }
  }

  .bg-neutral-800\/95 {
    background-color: #262626f2;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-neutral-800\/95 {
      background-color: color-mix(in oklab, var(--color-neutral-800) 95%, transparent);
    }
  }

  .bg-orange-50 {
    background-color: var(--color-orange-50);
  }

  .bg-orange-100 {
    background-color: var(--color-orange-100);
  }

  .bg-orange-500 {
    background-color: var(--color-orange-500);
  }

  .bg-pink-600 {
    background-color: var(--color-pink-600);
  }

  .bg-popover {
    background-color: var(--popover);
  }

  .bg-primary {
    background-color: var(--primary);
  }

  .bg-primary\/5 {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-primary\/5 {
      background-color: color-mix(in oklab, var(--primary) 5%, transparent);
    }
  }

  .bg-primary\/10 {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-primary\/10 {
      background-color: color-mix(in oklab, var(--primary) 10%, transparent);
    }
  }

  .bg-primary\/20 {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-primary\/20 {
      background-color: color-mix(in oklab, var(--primary) 20%, transparent);
    }
  }

  .bg-primary\/30 {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-primary\/30 {
      background-color: color-mix(in oklab, var(--primary) 30%, transparent);
    }
  }

  .bg-primary\/90 {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-primary\/90 {
      background-color: color-mix(in oklab, var(--primary) 90%, transparent);
    }
  }

  .bg-purple-50 {
    background-color: var(--color-purple-50);
  }

  .bg-purple-50\/30 {
    background-color: #faf5ff4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-purple-50\/30 {
      background-color: color-mix(in oklab, var(--color-purple-50) 30%, transparent);
    }
  }

  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }

  .bg-purple-500 {
    background-color: var(--color-purple-500);
  }

  .bg-purple-500\/5 {
    background-color: #ac4bff0d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-purple-500\/5 {
      background-color: color-mix(in oklab, var(--color-purple-500) 5%, transparent);
    }
  }

  .bg-purple-500\/10 {
    background-color: #ac4bff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-purple-500\/10 {
      background-color: color-mix(in oklab, var(--color-purple-500) 10%, transparent);
    }
  }

  .bg-purple-500\/15 {
    background-color: #ac4bff26;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-purple-500\/15 {
      background-color: color-mix(in oklab, var(--color-purple-500) 15%, transparent);
    }
  }

  .bg-purple-500\/30 {
    background-color: #ac4bff4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-purple-500\/30 {
      background-color: color-mix(in oklab, var(--color-purple-500) 30%, transparent);
    }
  }

  .bg-purple-600 {
    background-color: var(--color-purple-600);
  }

  .bg-red-50 {
    background-color: var(--color-red-50);
  }

  .bg-red-50\/50 {
    background-color: #fef2f280;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-red-50\/50 {
      background-color: color-mix(in oklab, var(--color-red-50) 50%, transparent);
    }
  }

  .bg-red-100 {
    background-color: var(--color-red-100);
  }

  .bg-red-500 {
    background-color: var(--color-red-500);
  }

  .bg-red-500\/5 {
    background-color: #fb2c360d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-red-500\/5 {
      background-color: color-mix(in oklab, var(--color-red-500) 5%, transparent);
    }
  }

  .bg-red-500\/15 {
    background-color: #fb2c3626;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-red-500\/15 {
      background-color: color-mix(in oklab, var(--color-red-500) 15%, transparent);
    }
  }

  .bg-red-500\/30 {
    background-color: #fb2c364d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-red-500\/30 {
      background-color: color-mix(in oklab, var(--color-red-500) 30%, transparent);
    }
  }

  .bg-red-500\/80 {
    background-color: #fb2c36cc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-red-500\/80 {
      background-color: color-mix(in oklab, var(--color-red-500) 80%, transparent);
    }
  }

  .bg-red-600 {
    background-color: var(--color-red-600);
  }

  .bg-rose-50 {
    background-color: var(--color-rose-50);
  }

  .bg-rose-100 {
    background-color: var(--color-rose-100);
  }

  .bg-secondary {
    background-color: var(--secondary);
  }

  .bg-sidebar {
    background-color: var(--sidebar);
  }

  .bg-sidebar-border {
    background-color: var(--sidebar-border);
  }

  .bg-teal-50 {
    background-color: var(--color-teal-50);
  }

  .bg-teal-100 {
    background-color: var(--color-teal-100);
  }

  .bg-transparent {
    background-color: #0000;
  }

  .bg-violet-100 {
    background-color: var(--color-violet-100);
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-white\/5 {
    background-color: #ffffff0d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/5 {
      background-color: color-mix(in oklab, var(--color-white) 5%, transparent);
    }
  }

  .bg-white\/20 {
    background-color: #fff3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/20 {
      background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  .bg-white\/30 {
    background-color: #ffffff4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/30 {
      background-color: color-mix(in oklab, var(--color-white) 30%, transparent);
    }
  }

  .bg-white\/40 {
    background-color: #fff6;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/40 {
      background-color: color-mix(in oklab, var(--color-white) 40%, transparent);
    }
  }

  .bg-white\/50 {
    background-color: #ffffff80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/50 {
      background-color: color-mix(in oklab, var(--color-white) 50%, transparent);
    }
  }

  .bg-white\/80 {
    background-color: #fffc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/80 {
      background-color: color-mix(in oklab, var(--color-white) 80%, transparent);
    }
  }

  .bg-white\/90 {
    background-color: #ffffffe6;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/90 {
      background-color: color-mix(in oklab, var(--color-white) 90%, transparent);
    }
  }

  .bg-white\/95 {
    background-color: #fffffff2;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/95 {
      background-color: color-mix(in oklab, var(--color-white) 95%, transparent);
    }
  }

  .bg-yellow-50 {
    background-color: var(--color-yellow-50);
  }

  .bg-yellow-100 {
    background-color: var(--color-yellow-100);
  }

  .bg-yellow-300\/20 {
    background-color: #ffe02a33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-yellow-300\/20 {
      background-color: color-mix(in oklab, var(--color-yellow-300) 20%, transparent);
    }
  }

  .bg-yellow-500 {
    background-color: var(--color-yellow-500);
  }

  .bg-yellow-500\/5 {
    background-color: #edb2000d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-yellow-500\/5 {
      background-color: color-mix(in oklab, var(--color-yellow-500) 5%, transparent);
    }
  }

  .bg-yellow-500\/30 {
    background-color: #edb2004d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-yellow-500\/30 {
      background-color: color-mix(in oklab, var(--color-yellow-500) 30%, transparent);
    }
  }

  .bg-gradient-to-b {
    --tw-gradient-position: to bottom in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-l {
    --tw-gradient-position: to left in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-t {
    --tw-gradient-position: to top in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-tr {
    --tw-gradient-position: to top right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-\[url\(\'\/decorative\/subtle-pattern\.svg\'\)\] {
    background-image: url("/decorative/subtle-pattern.svg");
  }

  .bg-\[url\(\'\/grid-pattern\.svg\'\)\] {
    background-image: url("/grid-pattern.svg");
  }

  .bg-\[url\(\'\/map-grid\.svg\'\)\] {
    background-image: url("/map-grid.svg");
  }

  .bg-\[url\(\'\/noise\.svg\'\)\] {
    background-image: url("/noise.svg");
  }

  .from-\[\#C29D5B\] {
    --tw-gradient-from: #c29d5b;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[var\(--brand-gold\)\] {
    --tw-gradient-from: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[var\(--brand-gold\)\]\/5 {
    --tw-gradient-from: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-\[var\(--brand-gold\)\]\/5 {
      --tw-gradient-from: color-mix(in oklab, var(--brand-gold) 5%, transparent);
    }
  }

  .from-\[var\(--brand-gold\)\]\/10 {
    --tw-gradient-from: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-\[var\(--brand-gold\)\]\/10 {
      --tw-gradient-from: color-mix(in oklab, var(--brand-gold) 10%, transparent);
    }
  }

  .from-\[var\(--brand-gold\)\]\/20 {
    --tw-gradient-from: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-\[var\(--brand-gold\)\]\/20 {
      --tw-gradient-from: color-mix(in oklab, var(--brand-gold) 20%, transparent);
    }
  }

  .from-\[var\(--brand-gold\)\]\/30 {
    --tw-gradient-from: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-\[var\(--brand-gold\)\]\/30 {
      --tw-gradient-from: color-mix(in oklab, var(--brand-gold) 30%, transparent);
    }
  }

  .from-\[var\(--brand-gold\)\]\/40 {
    --tw-gradient-from: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-\[var\(--brand-gold\)\]\/40 {
      --tw-gradient-from: color-mix(in oklab, var(--brand-gold) 40%, transparent);
    }
  }

  .from-\[var\(--brand-gold\)\]\/50 {
    --tw-gradient-from: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-\[var\(--brand-gold\)\]\/50 {
      --tw-gradient-from: color-mix(in oklab, var(--brand-gold) 50%, transparent);
    }
  }

  .from-\[var\(--brand-gold\)\]\/60 {
    --tw-gradient-from: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-\[var\(--brand-gold\)\]\/60 {
      --tw-gradient-from: color-mix(in oklab, var(--brand-gold) 60%, transparent);
    }
  }

  .from-\[var\(--theme-color-10\)\] {
    --tw-gradient-from: var(--theme-color-10);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[var\(--theme-color-30\)\] {
    --tw-gradient-from: var(--theme-color-30);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-amber-50 {
    --tw-gradient-from: var(--color-amber-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-amber-100 {
    --tw-gradient-from: var(--color-amber-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-amber-500 {
    --tw-gradient-from: var(--color-amber-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-background {
    --tw-gradient-from: var(--background);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-black\/20 {
    --tw-gradient-from: #0003;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-black\/20 {
      --tw-gradient-from: color-mix(in oklab, var(--color-black) 20%, transparent);
    }
  }

  .from-black\/40 {
    --tw-gradient-from: #0006;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-black\/40 {
      --tw-gradient-from: color-mix(in oklab, var(--color-black) 40%, transparent);
    }
  }

  .from-black\/60 {
    --tw-gradient-from: #0009;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-black\/60 {
      --tw-gradient-from: color-mix(in oklab, var(--color-black) 60%, transparent);
    }
  }

  .from-black\/70 {
    --tw-gradient-from: #000000b3;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-black\/70 {
      --tw-gradient-from: color-mix(in oklab, var(--color-black) 70%, transparent);
    }
  }

  .from-blue-50 {
    --tw-gradient-from: var(--color-blue-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-blue-50\/50 {
    --tw-gradient-from: #eff6ff80;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-blue-50\/50 {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-50) 50%, transparent);
    }
  }

  .from-blue-100 {
    --tw-gradient-from: var(--color-blue-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-blue-500 {
    --tw-gradient-from: var(--color-blue-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-blue-500\/5 {
    --tw-gradient-from: #3080ff0d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-blue-500\/5 {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-500) 5%, transparent);
    }
  }

  .from-blue-500\/10 {
    --tw-gradient-from: #3080ff1a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-blue-500\/10 {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-500) 10%, transparent);
    }
  }

  .from-blue-500\/20 {
    --tw-gradient-from: #3080ff33;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-blue-500\/20 {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-500) 20%, transparent);
    }
  }

  .from-blue-500\/30 {
    --tw-gradient-from: #3080ff4d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-blue-500\/30 {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-500) 30%, transparent);
    }
  }

  .from-card {
    --tw-gradient-from: var(--card);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-emerald-500 {
    --tw-gradient-from: var(--color-emerald-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-emerald-500\/10 {
    --tw-gradient-from: #00bb7f1a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-emerald-500\/10 {
      --tw-gradient-from: color-mix(in oklab, var(--color-emerald-500) 10%, transparent);
    }
  }

  .from-foreground {
    --tw-gradient-from: var(--foreground);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-gray-100 {
    --tw-gradient-from: var(--color-gray-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-green-50 {
    --tw-gradient-from: var(--color-green-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-green-100 {
    --tw-gradient-from: var(--color-green-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-green-500 {
    --tw-gradient-from: var(--color-green-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-green-500\/10 {
    --tw-gradient-from: #00c7581a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-green-500\/10 {
      --tw-gradient-from: color-mix(in oklab, var(--color-green-500) 10%, transparent);
    }
  }

  .from-green-500\/30 {
    --tw-gradient-from: #00c7584d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-green-500\/30 {
      --tw-gradient-from: color-mix(in oklab, var(--color-green-500) 30%, transparent);
    }
  }

  .from-green-600 {
    --tw-gradient-from: var(--color-green-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-muted\/50 {
    --tw-gradient-from: var(--muted);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-muted\/50 {
      --tw-gradient-from: color-mix(in oklab, var(--muted) 50%, transparent);
    }
  }

  .from-neutral-50 {
    --tw-gradient-from: var(--color-neutral-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-neutral-50\/80 {
    --tw-gradient-from: #fafafacc;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-neutral-50\/80 {
      --tw-gradient-from: color-mix(in oklab, var(--color-neutral-50) 80%, transparent);
    }
  }

  .from-neutral-100 {
    --tw-gradient-from: var(--color-neutral-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-neutral-200 {
    --tw-gradient-from: var(--color-neutral-200);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-neutral-400\/20 {
    --tw-gradient-from: #a1a1a133;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-neutral-400\/20 {
      --tw-gradient-from: color-mix(in oklab, var(--color-neutral-400) 20%, transparent);
    }
  }

  .from-neutral-500 {
    --tw-gradient-from: var(--color-neutral-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-neutral-500\/20 {
    --tw-gradient-from: #73737333;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-neutral-500\/20 {
      --tw-gradient-from: color-mix(in oklab, var(--color-neutral-500) 20%, transparent);
    }
  }

  .from-neutral-900 {
    --tw-gradient-from: var(--color-neutral-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-primary {
    --tw-gradient-from: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-primary\/0 {
    --tw-gradient-from: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-primary\/0 {
      --tw-gradient-from: color-mix(in oklab, var(--primary) 0%, transparent);
    }
  }

  .from-primary\/5 {
    --tw-gradient-from: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-primary\/5 {
      --tw-gradient-from: color-mix(in oklab, var(--primary) 5%, transparent);
    }
  }

  .from-primary\/10 {
    --tw-gradient-from: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-primary\/10 {
      --tw-gradient-from: color-mix(in oklab, var(--primary) 10%, transparent);
    }
  }

  .from-primary\/20 {
    --tw-gradient-from: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-primary\/20 {
      --tw-gradient-from: color-mix(in oklab, var(--primary) 20%, transparent);
    }
  }

  .from-primary\/30 {
    --tw-gradient-from: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-primary\/30 {
      --tw-gradient-from: color-mix(in oklab, var(--primary) 30%, transparent);
    }
  }

  .from-purple-100 {
    --tw-gradient-from: var(--color-purple-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-purple-500\/5 {
    --tw-gradient-from: #ac4bff0d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-purple-500\/5 {
      --tw-gradient-from: color-mix(in oklab, var(--color-purple-500) 5%, transparent);
    }
  }

  .from-purple-600 {
    --tw-gradient-from: var(--color-purple-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-red-50 {
    --tw-gradient-from: var(--color-red-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-red-50\/80 {
    --tw-gradient-from: #fef2f2cc;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-red-50\/80 {
      --tw-gradient-from: color-mix(in oklab, var(--color-red-50) 80%, transparent);
    }
  }

  .from-red-100 {
    --tw-gradient-from: var(--color-red-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-red-500 {
    --tw-gradient-from: var(--color-red-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-red-500\/10 {
    --tw-gradient-from: #fb2c361a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-red-500\/10 {
      --tw-gradient-from: color-mix(in oklab, var(--color-red-500) 10%, transparent);
    }
  }

  .from-rose-500 {
    --tw-gradient-from: var(--color-rose-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-transparent {
    --tw-gradient-from: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-violet-50 {
    --tw-gradient-from: var(--color-violet-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-white {
    --tw-gradient-from: var(--color-white);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-white\/0 {
    --tw-gradient-from: #0000;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-white\/0 {
      --tw-gradient-from: color-mix(in oklab, var(--color-white) 0%, transparent);
    }
  }

  .from-white\/5 {
    --tw-gradient-from: #ffffff0d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-white\/5 {
      --tw-gradient-from: color-mix(in oklab, var(--color-white) 5%, transparent);
    }
  }

  .from-white\/20 {
    --tw-gradient-from: #fff3;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-white\/20 {
      --tw-gradient-from: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  .from-white\/60 {
    --tw-gradient-from: #fff9;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-white\/60 {
      --tw-gradient-from: color-mix(in oklab, var(--color-white) 60%, transparent);
    }
  }

  .from-white\/80 {
    --tw-gradient-from: #fffc;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-white\/80 {
      --tw-gradient-from: color-mix(in oklab, var(--color-white) 80%, transparent);
    }
  }

  .from-white\/90 {
    --tw-gradient-from: #ffffffe6;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-white\/90 {
      --tw-gradient-from: color-mix(in oklab, var(--color-white) 90%, transparent);
    }
  }

  .from-yellow-100 {
    --tw-gradient-from: var(--color-yellow-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .via-\[var\(--brand-gold\)\] {
    --tw-gradient-via: var(--brand-gold);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-\[var\(--brand-gold\)\]\/5 {
    --tw-gradient-via: var(--brand-gold);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .via-\[var\(--brand-gold\)\]\/5 {
      --tw-gradient-via: color-mix(in oklab, var(--brand-gold) 5%, transparent);
    }
  }

  .via-\[var\(--brand-gold\)\]\/20 {
    --tw-gradient-via: var(--brand-gold);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .via-\[var\(--brand-gold\)\]\/20 {
      --tw-gradient-via: color-mix(in oklab, var(--brand-gold) 20%, transparent);
    }
  }

  .via-\[var\(--brand-gold\)\]\/30 {
    --tw-gradient-via: var(--brand-gold);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .via-\[var\(--brand-gold\)\]\/30 {
      --tw-gradient-via: color-mix(in oklab, var(--brand-gold) 30%, transparent);
    }
  }

  .via-\[var\(--brand-gold\)\]\/50 {
    --tw-gradient-via: var(--brand-gold);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .via-\[var\(--brand-gold\)\]\/50 {
      --tw-gradient-via: color-mix(in oklab, var(--brand-gold) 50%, transparent);
    }
  }

  .via-\[var\(--brand-gold\)\]\/70 {
    --tw-gradient-via: var(--brand-gold);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .via-\[var\(--brand-gold\)\]\/70 {
      --tw-gradient-via: color-mix(in oklab, var(--brand-gold) 70%, transparent);
    }
  }

  .via-amber-500 {
    --tw-gradient-via: var(--color-amber-500);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-background {
    --tw-gradient-via: var(--background);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-black\/0 {
    --tw-gradient-via: #0000;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .via-black\/0 {
      --tw-gradient-via: color-mix(in oklab, var(--color-black) 0%, transparent);
    }
  }

  .via-neutral-300 {
    --tw-gradient-via: var(--color-neutral-300);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-neutral-400 {
    --tw-gradient-via: var(--color-neutral-400);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-primary {
    --tw-gradient-via: var(--primary);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-purple-600 {
    --tw-gradient-via: var(--color-purple-600);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-transparent {
    --tw-gradient-via: transparent;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-white\/10 {
    --tw-gradient-via: #ffffff1a;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .via-white\/10 {
      --tw-gradient-via: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .via-white\/20 {
    --tw-gradient-via: #fff3;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .via-white\/20 {
      --tw-gradient-via: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  .to-\[\#B08A4A\] {
    --tw-gradient-to: #b08a4a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[var\(--brand-gold\)\] {
    --tw-gradient-to: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[var\(--brand-gold\)\]\/5 {
    --tw-gradient-to: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-\[var\(--brand-gold\)\]\/5 {
      --tw-gradient-to: color-mix(in oklab, var(--brand-gold) 5%, transparent);
    }
  }

  .to-\[var\(--brand-gold\)\]\/10 {
    --tw-gradient-to: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-\[var\(--brand-gold\)\]\/10 {
      --tw-gradient-to: color-mix(in oklab, var(--brand-gold) 10%, transparent);
    }
  }

  .to-\[var\(--brand-gold\)\]\/20 {
    --tw-gradient-to: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-\[var\(--brand-gold\)\]\/20 {
      --tw-gradient-to: color-mix(in oklab, var(--brand-gold) 20%, transparent);
    }
  }

  .to-\[var\(--brand-gold\)\]\/30 {
    --tw-gradient-to: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-\[var\(--brand-gold\)\]\/30 {
      --tw-gradient-to: color-mix(in oklab, var(--brand-gold) 30%, transparent);
    }
  }

  .to-\[var\(--brand-gold\)\]\/50 {
    --tw-gradient-to: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-\[var\(--brand-gold\)\]\/50 {
      --tw-gradient-to: color-mix(in oklab, var(--brand-gold) 50%, transparent);
    }
  }

  .to-\[var\(--brand-gold\)\]\/60 {
    --tw-gradient-to: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-\[var\(--brand-gold\)\]\/60 {
      --tw-gradient-to: color-mix(in oklab, var(--brand-gold) 60%, transparent);
    }
  }

  .to-\[var\(--brand-gold\)\]\/80 {
    --tw-gradient-to: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-\[var\(--brand-gold\)\]\/80 {
      --tw-gradient-to: color-mix(in oklab, var(--brand-gold) 80%, transparent);
    }
  }

  .to-\[var\(--brand-gold-dark\)\] {
    --tw-gradient-to: var(--brand-gold-dark);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[var\(--brand-gold-light\)\] {
    --tw-gradient-to: var(--brand-gold-light);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[var\(--theme-color-10\)\] {
    --tw-gradient-to: var(--theme-color-10);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-amber-50 {
    --tw-gradient-to: var(--color-amber-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-amber-100\/70 {
    --tw-gradient-to: #fef3c6b3;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-amber-100\/70 {
      --tw-gradient-to: color-mix(in oklab, var(--color-amber-100) 70%, transparent);
    }
  }

  .to-amber-200 {
    --tw-gradient-to: var(--color-amber-200);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-amber-500 {
    --tw-gradient-to: var(--color-amber-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-amber-500\/5 {
    --tw-gradient-to: #f99c000d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-amber-500\/5 {
      --tw-gradient-to: color-mix(in oklab, var(--color-amber-500) 5%, transparent);
    }
  }

  .to-amber-500\/10 {
    --tw-gradient-to: #f99c001a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-amber-500\/10 {
      --tw-gradient-to: color-mix(in oklab, var(--color-amber-500) 10%, transparent);
    }
  }

  .to-amber-500\/20 {
    --tw-gradient-to: #f99c0033;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-amber-500\/20 {
      --tw-gradient-to: color-mix(in oklab, var(--color-amber-500) 20%, transparent);
    }
  }

  .to-amber-600 {
    --tw-gradient-to: var(--color-amber-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-background {
    --tw-gradient-to: var(--background);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-black\/0 {
    --tw-gradient-to: #0000;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-black\/0 {
      --tw-gradient-to: color-mix(in oklab, var(--color-black) 0%, transparent);
    }
  }

  .to-black\/10 {
    --tw-gradient-to: #0000001a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-black\/10 {
      --tw-gradient-to: color-mix(in oklab, var(--color-black) 10%, transparent);
    }
  }

  .to-black\/20 {
    --tw-gradient-to: #0003;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-black\/20 {
      --tw-gradient-to: color-mix(in oklab, var(--color-black) 20%, transparent);
    }
  }

  .to-black\/40 {
    --tw-gradient-to: #0006;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-black\/40 {
      --tw-gradient-to: color-mix(in oklab, var(--color-black) 40%, transparent);
    }
  }

  .to-blue-50 {
    --tw-gradient-to: var(--color-blue-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-blue-50\/50 {
    --tw-gradient-to: #eff6ff80;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-blue-50\/50 {
      --tw-gradient-to: color-mix(in oklab, var(--color-blue-50) 50%, transparent);
    }
  }

  .to-blue-100\/70 {
    --tw-gradient-to: #dbeafeb3;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-blue-100\/70 {
      --tw-gradient-to: color-mix(in oklab, var(--color-blue-100) 70%, transparent);
    }
  }

  .to-blue-200 {
    --tw-gradient-to: var(--color-blue-200);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-blue-500\/5 {
    --tw-gradient-to: #3080ff0d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-blue-500\/5 {
      --tw-gradient-to: color-mix(in oklab, var(--color-blue-500) 5%, transparent);
    }
  }

  .to-blue-500\/20 {
    --tw-gradient-to: #3080ff33;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-blue-500\/20 {
      --tw-gradient-to: color-mix(in oklab, var(--color-blue-500) 20%, transparent);
    }
  }

  .to-blue-600 {
    --tw-gradient-to: var(--color-blue-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-blue-600\/5 {
    --tw-gradient-to: #155dfc0d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-blue-600\/5 {
      --tw-gradient-to: color-mix(in oklab, var(--color-blue-600) 5%, transparent);
    }
  }

  .to-blue-600\/30 {
    --tw-gradient-to: #155dfc4d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-blue-600\/30 {
      --tw-gradient-to: color-mix(in oklab, var(--color-blue-600) 30%, transparent);
    }
  }

  .to-blue-700 {
    --tw-gradient-to: var(--color-blue-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-card\/50 {
    --tw-gradient-to: var(--card);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-card\/50 {
      --tw-gradient-to: color-mix(in oklab, var(--card) 50%, transparent);
    }
  }

  .to-emerald-100 {
    --tw-gradient-to: var(--color-emerald-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-emerald-400 {
    --tw-gradient-to: var(--color-emerald-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-emerald-500\/5 {
    --tw-gradient-to: #00bb7f0d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-emerald-500\/5 {
      --tw-gradient-to: color-mix(in oklab, var(--color-emerald-500) 5%, transparent);
    }
  }

  .to-foreground\/80 {
    --tw-gradient-to: var(--foreground);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-foreground\/80 {
      --tw-gradient-to: color-mix(in oklab, var(--foreground) 80%, transparent);
    }
  }

  .to-gray-50 {
    --tw-gradient-to: var(--color-gray-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-green-50 {
    --tw-gradient-to: var(--color-green-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-green-500 {
    --tw-gradient-to: var(--color-green-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-green-500\/5 {
    --tw-gradient-to: #00c7580d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-green-500\/5 {
      --tw-gradient-to: color-mix(in oklab, var(--color-green-500) 5%, transparent);
    }
  }

  .to-green-600 {
    --tw-gradient-to: var(--color-green-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-green-600\/30 {
    --tw-gradient-to: #00a5444d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-green-600\/30 {
      --tw-gradient-to: color-mix(in oklab, var(--color-green-600) 30%, transparent);
    }
  }

  .to-indigo-100 {
    --tw-gradient-to: var(--color-indigo-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-muted\/20 {
    --tw-gradient-to: var(--muted);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-muted\/20 {
      --tw-gradient-to: color-mix(in oklab, var(--muted) 20%, transparent);
    }
  }

  .to-neutral-50 {
    --tw-gradient-to: var(--color-neutral-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-neutral-50\/50 {
    --tw-gradient-to: #fafafa80;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-neutral-50\/50 {
      --tw-gradient-to: color-mix(in oklab, var(--color-neutral-50) 50%, transparent);
    }
  }

  .to-neutral-100 {
    --tw-gradient-to: var(--color-neutral-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-neutral-100\/40 {
    --tw-gradient-to: #f5f5f566;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-neutral-100\/40 {
      --tw-gradient-to: color-mix(in oklab, var(--color-neutral-100) 40%, transparent);
    }
  }

  .to-neutral-100\/50 {
    --tw-gradient-to: #f5f5f580;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-neutral-100\/50 {
      --tw-gradient-to: color-mix(in oklab, var(--color-neutral-100) 50%, transparent);
    }
  }

  .to-neutral-200 {
    --tw-gradient-to: var(--color-neutral-200);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-neutral-300 {
    --tw-gradient-to: var(--color-neutral-300);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-neutral-600 {
    --tw-gradient-to: var(--color-neutral-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-neutral-600\/20 {
    --tw-gradient-to: #52525233;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-neutral-600\/20 {
      --tw-gradient-to: color-mix(in oklab, var(--color-neutral-600) 20%, transparent);
    }
  }

  .to-pink-100 {
    --tw-gradient-to: var(--color-pink-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-primary\/0 {
    --tw-gradient-to: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-primary\/0 {
      --tw-gradient-to: color-mix(in oklab, var(--primary) 0%, transparent);
    }
  }

  .to-primary\/2 {
    --tw-gradient-to: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-primary\/2 {
      --tw-gradient-to: color-mix(in oklab, var(--primary) 2%, transparent);
    }
  }

  .to-primary\/5 {
    --tw-gradient-to: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-primary\/5 {
      --tw-gradient-to: color-mix(in oklab, var(--primary) 5%, transparent);
    }
  }

  .to-primary\/10 {
    --tw-gradient-to: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-primary\/10 {
      --tw-gradient-to: color-mix(in oklab, var(--primary) 10%, transparent);
    }
  }

  .to-primary\/20 {
    --tw-gradient-to: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-primary\/20 {
      --tw-gradient-to: color-mix(in oklab, var(--primary) 20%, transparent);
    }
  }

  .to-primary\/30 {
    --tw-gradient-to: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-primary\/30 {
      --tw-gradient-to: color-mix(in oklab, var(--primary) 30%, transparent);
    }
  }

  .to-primary\/80 {
    --tw-gradient-to: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-primary\/80 {
      --tw-gradient-to: color-mix(in oklab, var(--primary) 80%, transparent);
    }
  }

  .to-purple-50 {
    --tw-gradient-to: var(--color-purple-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-purple-50\/50 {
    --tw-gradient-to: #faf5ff80;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-purple-50\/50 {
      --tw-gradient-to: color-mix(in oklab, var(--color-purple-50) 50%, transparent);
    }
  }

  .to-purple-500 {
    --tw-gradient-to: var(--color-purple-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-purple-500\/5 {
    --tw-gradient-to: #ac4bff0d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-purple-500\/5 {
      --tw-gradient-to: color-mix(in oklab, var(--color-purple-500) 5%, transparent);
    }
  }

  .to-purple-500\/10 {
    --tw-gradient-to: #ac4bff1a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-purple-500\/10 {
      --tw-gradient-to: color-mix(in oklab, var(--color-purple-500) 10%, transparent);
    }
  }

  .to-red-50 {
    --tw-gradient-to: var(--color-red-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-red-50\/40 {
    --tw-gradient-to: #fef2f266;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-red-50\/40 {
      --tw-gradient-to: color-mix(in oklab, var(--color-red-50) 40%, transparent);
    }
  }

  .to-red-500\/5 {
    --tw-gradient-to: #fb2c360d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-red-500\/5 {
      --tw-gradient-to: color-mix(in oklab, var(--color-red-500) 5%, transparent);
    }
  }

  .to-red-600 {
    --tw-gradient-to: var(--color-red-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-rose-400 {
    --tw-gradient-to: var(--color-rose-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-transparent {
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-white {
    --tw-gradient-to: var(--color-white);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-white\/5 {
    --tw-gradient-to: #ffffff0d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-white\/5 {
      --tw-gradient-to: color-mix(in oklab, var(--color-white) 5%, transparent);
    }
  }

  .to-white\/40 {
    --tw-gradient-to: #fff6;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-white\/40 {
      --tw-gradient-to: color-mix(in oklab, var(--color-white) 40%, transparent);
    }
  }

  .to-white\/70 {
    --tw-gradient-to: #ffffffb3;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-white\/70 {
      --tw-gradient-to: color-mix(in oklab, var(--color-white) 70%, transparent);
    }
  }

  .to-yellow-50 {
    --tw-gradient-to: var(--color-yellow-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .bg-clip-text {
    background-clip: text;
  }

  .bg-repeat {
    background-repeat: repeat;
  }

  .fill-\[var\(--brand-gold\)\] {
    fill: var(--brand-gold);
  }

  .fill-amber-500 {
    fill: var(--color-amber-500);
  }

  .fill-current {
    fill: currentColor;
  }

  .fill-primary {
    fill: var(--primary);
  }

  .fill-yellow-400 {
    fill: var(--color-yellow-400);
  }

  .object-contain {
    object-fit: contain;
  }

  .object-cover {
    object-fit: cover;
  }

  .p-0 {
    padding: calc(var(--spacing) * 0);
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-2\.5 {
    padding: calc(var(--spacing) * 2.5);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-5 {
    padding: calc(var(--spacing) * 5);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .p-12 {
    padding: calc(var(--spacing) * 12);
  }

  .p-\[3px\] {
    padding: 3px;
  }

  .p-px {
    padding: 1px;
  }

  .px-0 {
    padding-inline: calc(var(--spacing) * 0);
  }

  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }

  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }

  .py-0 {
    padding-block: calc(var(--spacing) * 0);
  }

  .py-0\.5 {
    padding-block: calc(var(--spacing) * .5);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-5 {
    padding-block: calc(var(--spacing) * 5);
  }

  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .py-10 {
    padding-block: calc(var(--spacing) * 10);
  }

  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }

  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }

  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }

  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }

  .pt-0\.5 {
    padding-top: calc(var(--spacing) * .5);
  }

  .pt-1 {
    padding-top: calc(var(--spacing) * 1);
  }

  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }

  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }

  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }

  .pt-5 {
    padding-top: calc(var(--spacing) * 5);
  }

  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }

  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }

  .pt-16 {
    padding-top: calc(var(--spacing) * 16);
  }

  .pt-20 {
    padding-top: calc(var(--spacing) * 20);
  }

  .pt-24 {
    padding-top: calc(var(--spacing) * 24);
  }

  .pt-28 {
    padding-top: calc(var(--spacing) * 28);
  }

  .pr-1 {
    padding-right: calc(var(--spacing) * 1);
  }

  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }

  .pr-2\.5 {
    padding-right: calc(var(--spacing) * 2.5);
  }

  .pr-3 {
    padding-right: calc(var(--spacing) * 3);
  }

  .pr-4 {
    padding-right: calc(var(--spacing) * 4);
  }

  .pr-5 {
    padding-right: calc(var(--spacing) * 5);
  }

  .pr-8 {
    padding-right: calc(var(--spacing) * 8);
  }

  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }

  .pb-0 {
    padding-bottom: calc(var(--spacing) * 0);
  }

  .pb-1 {
    padding-bottom: calc(var(--spacing) * 1);
  }

  .pb-1\.5 {
    padding-bottom: calc(var(--spacing) * 1.5);
  }

  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }

  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }

  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }

  .pb-5 {
    padding-bottom: calc(var(--spacing) * 5);
  }

  .pb-6 {
    padding-bottom: calc(var(--spacing) * 6);
  }

  .pb-8 {
    padding-bottom: calc(var(--spacing) * 8);
  }

  .pb-10 {
    padding-bottom: calc(var(--spacing) * 10);
  }

  .pb-12 {
    padding-bottom: calc(var(--spacing) * 12);
  }

  .pb-14 {
    padding-bottom: calc(var(--spacing) * 14);
  }

  .pb-16 {
    padding-bottom: calc(var(--spacing) * 16);
  }

  .pb-20 {
    padding-bottom: calc(var(--spacing) * 20);
  }

  .pl-1 {
    padding-left: calc(var(--spacing) * 1);
  }

  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }

  .pl-2\.5 {
    padding-left: calc(var(--spacing) * 2.5);
  }

  .pl-3 {
    padding-left: calc(var(--spacing) * 3);
  }

  .pl-4 {
    padding-left: calc(var(--spacing) * 4);
  }

  .pl-5 {
    padding-left: calc(var(--spacing) * 5);
  }

  .pl-6 {
    padding-left: calc(var(--spacing) * 6);
  }

  .pl-8 {
    padding-left: calc(var(--spacing) * 8);
  }

  .pl-10 {
    padding-left: calc(var(--spacing) * 10);
  }

  .pl-12 {
    padding-left: calc(var(--spacing) * 12);
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .text-right {
    text-align: right;
  }

  .text-start {
    text-align: start;
  }

  .align-middle {
    vertical-align: middle;
  }

  .font-mono {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
  }

  .font-serif {
    font-family: var(--font-serif);
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-6xl {
    font-size: var(--text-6xl);
    line-height: var(--tw-leading, var(--text-6xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .text-\[0\.8rem\] {
    font-size: .8rem;
  }

  .text-\[2\.5rem\] {
    font-size: 2.5rem;
  }

  .text-\[7px\] {
    font-size: 7px;
  }

  .text-\[8px\] {
    font-size: 8px;
  }

  .text-\[9px\] {
    font-size: 9px;
  }

  .text-\[10px\] {
    font-size: 10px;
  }

  .leading-loose {
    --tw-leading: var(--leading-loose);
    line-height: var(--leading-loose);
  }

  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }

  .leading-normal {
    --tw-leading: var(--leading-normal);
    line-height: var(--leading-normal);
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }

  .tracking-tighter {
    --tw-tracking: var(--tracking-tighter);
    letter-spacing: var(--tracking-tighter);
  }

  .tracking-wide {
    --tw-tracking: var(--tracking-wide);
    letter-spacing: var(--tracking-wide);
  }

  .tracking-wider {
    --tw-tracking: var(--tracking-wider);
    letter-spacing: var(--tracking-wider);
  }

  .tracking-widest {
    --tw-tracking: var(--tracking-widest);
    letter-spacing: var(--tracking-widest);
  }

  .text-balance {
    text-wrap: balance;
  }

  .break-words {
    overflow-wrap: break-word;
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .whitespace-pre-line {
    white-space: pre-line;
  }

  .whitespace-pre-wrap {
    white-space: pre-wrap;
  }

  .text-\[\#C29D5B\] {
    color: #c29d5b;
  }

  .text-\[\#D4AF37\] {
    color: #d4af37;
  }

  .text-\[--theme-color\] {
    color: --theme-color;
  }

  .text-\[var\(--brand-gold\)\] {
    color: var(--brand-gold);
  }

  .text-\[var\(--brand-gold\)\]\/10 {
    color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-\[var\(--brand-gold\)\]\/10 {
      color: color-mix(in oklab, var(--brand-gold) 10%, transparent);
    }
  }

  .text-\[var\(--brand-gold\)\]\/20 {
    color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-\[var\(--brand-gold\)\]\/20 {
      color: color-mix(in oklab, var(--brand-gold) 20%, transparent);
    }
  }

  .text-\[var\(--brand-gold\)\]\/80 {
    color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-\[var\(--brand-gold\)\]\/80 {
      color: color-mix(in oklab, var(--brand-gold) 80%, transparent);
    }
  }

  .text-\[var\(--brand-gold-foreground\)\] {
    color: var(--brand-gold-foreground);
  }

  .text-\[var\(--primary\)\] {
    color: var(--primary);
  }

  .text-\[var\(--theme-color\)\] {
    color: var(--theme-color);
  }

  .text-accent-foreground {
    color: var(--accent-foreground);
  }

  .text-amber-500 {
    color: var(--color-amber-500);
  }

  .text-amber-600 {
    color: var(--color-amber-600);
  }

  .text-amber-700 {
    color: var(--color-amber-700);
  }

  .text-amber-800 {
    color: var(--color-amber-800);
  }

  .text-black {
    color: var(--color-black);
  }

  .text-black\/40 {
    color: #0006;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-black\/40 {
      color: color-mix(in oklab, var(--color-black) 40%, transparent);
    }
  }

  .text-blue-500 {
    color: var(--color-blue-500);
  }

  .text-blue-500\/20 {
    color: #3080ff33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-blue-500\/20 {
      color: color-mix(in oklab, var(--color-blue-500) 20%, transparent);
    }
  }

  .text-blue-600 {
    color: var(--color-blue-600);
  }

  .text-blue-600\/80 {
    color: #155dfccc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-blue-600\/80 {
      color: color-mix(in oklab, var(--color-blue-600) 80%, transparent);
    }
  }

  .text-blue-700 {
    color: var(--color-blue-700);
  }

  .text-blue-800 {
    color: var(--color-blue-800);
  }

  .text-blue-900 {
    color: var(--color-blue-900);
  }

  .text-card-foreground {
    color: var(--card-foreground);
  }

  .text-current {
    color: currentColor;
  }

  .text-cyan-500 {
    color: var(--color-cyan-500);
  }

  .text-destructive {
    color: var(--destructive);
  }

  .text-emerald-500 {
    color: var(--color-emerald-500);
  }

  .text-emerald-600 {
    color: var(--color-emerald-600);
  }

  .text-emerald-700 {
    color: var(--color-emerald-700);
  }

  .text-foreground {
    color: var(--foreground);
  }

  .text-foreground\/50 {
    color: var(--foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-foreground\/50 {
      color: color-mix(in oklab, var(--foreground) 50%, transparent);
    }
  }

  .text-foreground\/80 {
    color: var(--foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-foreground\/80 {
      color: color-mix(in oklab, var(--foreground) 80%, transparent);
    }
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-700 {
    color: var(--color-gray-700);
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-green-500 {
    color: var(--color-green-500);
  }

  .text-green-600 {
    color: var(--color-green-600);
  }

  .text-green-700 {
    color: var(--color-green-700);
  }

  .text-green-800 {
    color: var(--color-green-800);
  }

  .text-green-900 {
    color: var(--color-green-900);
  }

  .text-indigo-600 {
    color: var(--color-indigo-600);
  }

  .text-muted-foreground {
    color: var(--muted-foreground);
  }

  .text-muted-foreground\/50 {
    color: var(--muted-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-muted-foreground\/50 {
      color: color-mix(in oklab, var(--muted-foreground) 50%, transparent);
    }
  }

  .text-muted-foreground\/70 {
    color: var(--muted-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-muted-foreground\/70 {
      color: color-mix(in oklab, var(--muted-foreground) 70%, transparent);
    }
  }

  .text-neutral-300 {
    color: var(--color-neutral-300);
  }

  .text-neutral-400 {
    color: var(--color-neutral-400);
  }

  .text-neutral-500 {
    color: var(--color-neutral-500);
  }

  .text-neutral-600 {
    color: var(--color-neutral-600);
  }

  .text-neutral-700 {
    color: var(--color-neutral-700);
  }

  .text-neutral-800 {
    color: var(--color-neutral-800);
  }

  .text-neutral-900 {
    color: var(--color-neutral-900);
  }

  .text-orange-500 {
    color: var(--color-orange-500);
  }

  .text-orange-600 {
    color: var(--color-orange-600);
  }

  .text-orange-700 {
    color: var(--color-orange-700);
  }

  .text-orange-800 {
    color: var(--color-orange-800);
  }

  .text-pink-500 {
    color: var(--color-pink-500);
  }

  .text-popover-foreground {
    color: var(--popover-foreground);
  }

  .text-primary {
    color: var(--primary);
  }

  .text-primary-foreground {
    color: var(--primary-foreground);
  }

  .text-primary\/40 {
    color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-primary\/40 {
      color: color-mix(in oklab, var(--primary) 40%, transparent);
    }
  }

  .text-primary\/50 {
    color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-primary\/50 {
      color: color-mix(in oklab, var(--primary) 50%, transparent);
    }
  }

  .text-primary\/80 {
    color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-primary\/80 {
      color: color-mix(in oklab, var(--primary) 80%, transparent);
    }
  }

  .text-purple-200 {
    color: var(--color-purple-200);
  }

  .text-purple-500 {
    color: var(--color-purple-500);
  }

  .text-purple-600 {
    color: var(--color-purple-600);
  }

  .text-purple-600\/80 {
    color: #9810facc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-purple-600\/80 {
      color: color-mix(in oklab, var(--color-purple-600) 80%, transparent);
    }
  }

  .text-purple-700 {
    color: var(--color-purple-700);
  }

  .text-red-500 {
    color: var(--color-red-500);
  }

  .text-red-600 {
    color: var(--color-red-600);
  }

  .text-red-600\/80 {
    color: #e40014cc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-red-600\/80 {
      color: color-mix(in oklab, var(--color-red-600) 80%, transparent);
    }
  }

  .text-red-700 {
    color: var(--color-red-700);
  }

  .text-red-800 {
    color: var(--color-red-800);
  }

  .text-red-900 {
    color: var(--color-red-900);
  }

  .text-rose-500 {
    color: var(--color-rose-500);
  }

  .text-rose-600 {
    color: var(--color-rose-600);
  }

  .text-rose-700 {
    color: var(--color-rose-700);
  }

  .text-secondary-foreground {
    color: var(--secondary-foreground);
  }

  .text-sidebar-foreground {
    color: var(--sidebar-foreground);
  }

  .text-sidebar-foreground\/70 {
    color: var(--sidebar-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-sidebar-foreground\/70 {
      color: color-mix(in oklab, var(--sidebar-foreground) 70%, transparent);
    }
  }

  .text-teal-600 {
    color: var(--color-teal-600);
  }

  .text-teal-800 {
    color: var(--color-teal-800);
  }

  .text-transparent {
    color: #0000;
  }

  .text-violet-600 {
    color: var(--color-violet-600);
  }

  .text-violet-700 {
    color: var(--color-violet-700);
  }

  .text-violet-800 {
    color: var(--color-violet-800);
  }

  .text-white {
    color: var(--color-white);
  }

  .text-white\/80 {
    color: #fffc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-white\/80 {
      color: color-mix(in oklab, var(--color-white) 80%, transparent);
    }
  }

  .text-yellow-400 {
    color: var(--color-yellow-400);
  }

  .text-yellow-500 {
    color: var(--color-yellow-500);
  }

  .text-yellow-600 {
    color: var(--color-yellow-600);
  }

  .text-yellow-700 {
    color: var(--color-yellow-700);
  }

  .text-yellow-800 {
    color: var(--color-yellow-800);
  }

  .text-zinc-600 {
    color: var(--color-zinc-600);
  }

  .capitalize {
    text-transform: capitalize;
  }

  .lowercase {
    text-transform: lowercase;
  }

  .uppercase {
    text-transform: uppercase;
  }

  .italic {
    font-style: italic;
  }

  .not-italic {
    font-style: normal;
  }

  .tabular-nums {
    --tw-numeric-spacing: tabular-nums;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
  }

  .line-through {
    text-decoration-line: line-through;
  }

  .overline {
    text-decoration-line: overline;
  }

  .underline {
    text-decoration-line: underline;
  }

  .underline-offset-4 {
    text-underline-offset: 4px;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .placeholder-gray-500::placeholder {
    color: var(--color-gray-500);
  }

  .opacity-0 {
    opacity: 0;
  }

  .opacity-3 {
    opacity: .03;
  }

  .opacity-5 {
    opacity: .05;
  }

  .opacity-10 {
    opacity: .1;
  }

  .opacity-15 {
    opacity: .15;
  }

  .opacity-20 {
    opacity: .2;
  }

  .opacity-30 {
    opacity: .3;
  }

  .opacity-40 {
    opacity: .4;
  }

  .opacity-50 {
    opacity: .5;
  }

  .opacity-60 {
    opacity: .6;
  }

  .opacity-70 {
    opacity: .7;
  }

  .opacity-75 {
    opacity: .75;
  }

  .opacity-80 {
    opacity: .8;
  }

  .opacity-90 {
    opacity: .9;
  }

  .opacity-100 {
    opacity: 1;
  }

  .opacity-\[0\.015\] {
    opacity: .015;
  }

  .opacity-\[var\(--light-opacity\)\] {
    opacity: var(--light-opacity);
  }

  .mix-blend-overlay {
    mix-blend-mode: overlay;
  }

  .shadow-\[--theme-color\]\/10 {
    --tw-shadow-alpha: 10%;
    --tw-shadow: --theme-color;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[var\(--brand-gold\)\]\/10 {
    --tw-shadow-alpha: 10%;
    --tw-shadow: var(--brand-gold);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, #00000040);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\] {
    --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-border)));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_0_15px_rgba\(var\(--brand-gold-rgb\)\,0\.5\)\] {
    --tw-shadow: 0 0 15px var(--tw-shadow-color, rgba(var(--brand-gold-rgb), .5));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_0_24px_rgba\(34\,_42\,_53\,_0\.06\)\,_0_1px_1px_rgba\(0\,_0\,_0\,_0\.05\)\,_0_0_0_1px_rgba\(34\,_42\,_53\,_0\.04\)\,_0_0_4px_rgba\(34\,_42\,_53\,_0\.08\)\,_0_16px_68px_rgba\(47\,_48\,_55\,_0\.05\)\,_0_1px_0_rgba\(255\,_255\,_255\,_0\.1\)_inset\] {
    --tw-shadow: 0 0 24px var(--tw-shadow-color, #222a350f), 0 1px 1px var(--tw-shadow-color, #0000000d), 0 0 0 1px var(--tw-shadow-color, #222a350a), 0 0 4px var(--tw-shadow-color, #222a3514), 0 16px 68px var(--tw-shadow-color, #2f30370d), 0 1px 0 var(--tw-shadow-color, #ffffff1a) inset;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_15px_60px_rgba\(0\,0\,0\,0\.3\)\,0_5px_20px_var\(--theme-color-20\)\] {
    --tw-shadow: 0 15px 60px var(--tw-shadow-color, #0000004d), 0 5px 20px var(--tw-shadow-color, var(--theme-color-20));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0px_2px_0px_0px_rgba\(255\,255\,255\,0\.3\)_inset\] {
    --tw-shadow: 0px 2px 0px 0px var(--tw-shadow-color, #ffffff4d) inset;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[inset_0_0_20px_rgba\(0\,0\,0\,0\.1\)\,inset_0_0_5px_var\(--theme-color-20\)\] {
    --tw-shadow: inset 0 0 20px var(--tw-shadow-color, #0000001a), inset 0 0 5px var(--tw-shadow-color, var(--theme-color-20));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-inner {
    --tw-shadow: inset 0 2px 4px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-none {
    --tw-shadow: 0 0 #0000;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xs {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-0 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-1 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-2 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-4 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-amber-200\/20 {
    --tw-shadow-color: #fee68533;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-amber-200\/20 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-amber-200) 20%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-blue-200\/20 {
    --tw-shadow-color: #bedbff33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-blue-200\/20 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-blue-200) 20%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-blue-500\/40 {
    --tw-shadow-color: #3080ff66;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-blue-500\/40 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-blue-500) 40%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-blue-500\/60 {
    --tw-shadow-color: #3080ff99;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-blue-500\/60 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-blue-500) 60%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-purple-500\/40 {
    --tw-shadow-color: #ac4bff66;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-purple-500\/40 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-purple-500) 40%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-purple-500\/60 {
    --tw-shadow-color: #ac4bff99;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-purple-500\/60 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-purple-500) 60%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-red-500\/40 {
    --tw-shadow-color: #fb2c3666;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-red-500\/40 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-red-500) 40%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-red-500\/60 {
    --tw-shadow-color: #fb2c3699;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-red-500\/60 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-red-500) 60%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-yellow-500\/40 {
    --tw-shadow-color: #edb20066;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-yellow-500\/40 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-yellow-500) 40%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-yellow-500\/60 {
    --tw-shadow-color: #edb20099;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-yellow-500\/60 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-yellow-500) 60%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .ring-\[\#D4AF37\]\/30 {
    --tw-ring-color: oklab(76.6528% -.00256401 .138654 / .3);
  }

  .ring-\[var\(--brand-gold\)\] {
    --tw-ring-color: var(--brand-gold);
  }

  .ring-\[var\(--brand-gold\)\]\/30 {
    --tw-ring-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .ring-\[var\(--brand-gold\)\]\/30 {
      --tw-ring-color: color-mix(in oklab, var(--brand-gold) 30%, transparent);
    }
  }

  .ring-amber-400 {
    --tw-ring-color: var(--color-amber-400);
  }

  .ring-black\/5 {
    --tw-ring-color: #0000000d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .ring-black\/5 {
      --tw-ring-color: color-mix(in oklab, var(--color-black) 5%, transparent);
    }
  }

  .ring-primary\/10 {
    --tw-ring-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .ring-primary\/10 {
      --tw-ring-color: color-mix(in oklab, var(--primary) 10%, transparent);
    }
  }

  .ring-primary\/20 {
    --tw-ring-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .ring-primary\/20 {
      --tw-ring-color: color-mix(in oklab, var(--primary) 20%, transparent);
    }
  }

  .ring-ring\/50 {
    --tw-ring-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .ring-ring\/50 {
      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  .ring-sidebar-ring {
    --tw-ring-color: var(--sidebar-ring);
  }

  .ring-offset-2 {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .ring-offset-background {
    --tw-ring-offset-color: var(--background);
  }

  .ring-offset-white {
    --tw-ring-offset-color: var(--color-white);
  }

  .outline-hidden {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .outline-hidden {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-2xl {
    --tw-blur: blur(var(--blur-2xl));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-3xl {
    --tw-blur: blur(var(--blur-3xl));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-\[1px\] {
    --tw-blur: blur(1px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-\[2px\] {
    --tw-blur: blur(2px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-\[8px\] {
    --tw-blur: blur(8px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-\[30px\] {
    --tw-blur: blur(30px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-\[40px\] {
    --tw-blur: blur(40px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-\[50px\] {
    --tw-blur: blur(50px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-\[60px\] {
    --tw-blur: blur(60px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-\[80px\] {
    --tw-blur: blur(80px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-\[100px\] {
    --tw-blur: blur(100px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-\[120px\] {
    --tw-blur: blur(120px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-lg {
    --tw-blur: blur(var(--blur-lg));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-md {
    --tw-blur: blur(var(--blur-md));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-sm {
    --tw-blur: blur(var(--blur-sm));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-xl {
    --tw-blur: blur(var(--blur-xl));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .drop-shadow-\[0_0_3px_rgba\(var\(--brand-gold-rgb\)\,0\.5\)\] {
    --tw-drop-shadow-size: drop-shadow(0 0 3px var(--tw-drop-shadow-color, rgba(var(--brand-gold-rgb), .5)));
    --tw-drop-shadow: var(--tw-drop-shadow-size);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .drop-shadow-md {
    --tw-drop-shadow-size: drop-shadow(0 3px 3px var(--tw-drop-shadow-color, #0000001f));
    --tw-drop-shadow: drop-shadow(var(--drop-shadow-md));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .drop-shadow-sm {
    --tw-drop-shadow-size: drop-shadow(0 1px 2px var(--tw-drop-shadow-color, #00000026));
    --tw-drop-shadow: drop-shadow(var(--drop-shadow-sm));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .grayscale {
    --tw-grayscale: grayscale(100%);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .backdrop-blur-\[1px\] {
    --tw-backdrop-blur: blur(1px);
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-lg {
    --tw-backdrop-blur: blur(var(--blur-lg));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-md {
    --tw-backdrop-blur: blur(var(--blur-md));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-xl {
    --tw-backdrop-blur: blur(var(--blur-xl));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition\! {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter !important;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function)) !important;
    transition-duration: var(--tw-duration, var(--default-transition-duration)) !important;
  }

  .transition-\[color\,box-shadow\] {
    transition-property: color, box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[left\,right\,width\] {
    transition-property: left, right, width;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[margin\,opacity\] {
    transition-property: margin, opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[width\,height\,padding\] {
    transition-property: width, height, padding;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[width\] {
    transition-property: width;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-none {
    transition-property: none;
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .duration-500 {
    --tw-duration: .5s;
    transition-duration: .5s;
  }

  .duration-1000 {
    --tw-duration: 1s;
    transition-duration: 1s;
  }

  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }

  .ease-linear {
    --tw-ease: linear;
    transition-timing-function: linear;
  }

  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }

  .will-change-transform {
    will-change: transform;
  }

  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }

  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }

  .group-focus-within\:text-primary:is(:where(.group):focus-within *) {
    color: var(--primary);
  }

  .group-focus-within\/menu-item\:opacity-100:is(:where(.group\/menu-item):focus-within *) {
    opacity: 1;
  }

  @media (hover: hover) {
    .group-hover\:-translate-x-1:is(:where(.group):hover *) {
      --tw-translate-x: calc(var(--spacing) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:translate-x-1:is(:where(.group):hover *) {
      --tw-translate-x: calc(var(--spacing) * 1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:translate-x-\[100\%\]:is(:where(.group):hover *) {
      --tw-translate-x: 100%;
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:translate-x-full:is(:where(.group):hover *) {
      --tw-translate-x: 100%;
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:scale-105:is(:where(.group):hover *) {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:scale-110:is(:where(.group):hover *) {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:rotate-12:is(:where(.group):hover *) {
      rotate: 12deg;
    }
  }

  @media (hover: hover) {
    .group-hover\:border-\[\#C29D5B\]:is(:where(.group):hover *) {
      border-color: #c29d5b;
    }
  }

  @media (hover: hover) {
    .group-hover\:border-\[var\(--brand-gold\)\]:is(:where(.group):hover *) {
      border-color: var(--brand-gold);
    }
  }

  @media (hover: hover) {
    .group-hover\:bg-\[var\(--brand-gold\)\]\/20:is(:where(.group):hover *) {
      background-color: var(--brand-gold);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .group-hover\:bg-\[var\(--brand-gold\)\]\/20:is(:where(.group):hover *) {
        background-color: color-mix(in oklab, var(--brand-gold) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .group-hover\:bg-amber-100:is(:where(.group):hover *) {
      background-color: var(--color-amber-100);
    }
  }

  @media (hover: hover) {
    .group-hover\:bg-blue-100:is(:where(.group):hover *) {
      background-color: var(--color-blue-100);
    }
  }

  @media (hover: hover) {
    .group-hover\:bg-emerald-100:is(:where(.group):hover *) {
      background-color: var(--color-emerald-100);
    }
  }

  @media (hover: hover) {
    .group-hover\:bg-primary\/15:is(:where(.group):hover *) {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .group-hover\:bg-primary\/15:is(:where(.group):hover *) {
        background-color: color-mix(in oklab, var(--primary) 15%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .group-hover\:bg-red-100:is(:where(.group):hover *) {
      background-color: var(--color-red-100);
    }
  }

  @media (hover: hover) {
    .group-hover\:bg-rose-100:is(:where(.group):hover *) {
      background-color: var(--color-rose-100);
    }
  }

  @media (hover: hover) {
    .group-hover\:bg-yellow-100:is(:where(.group):hover *) {
      background-color: var(--color-yellow-100);
    }
  }

  @media (hover: hover) {
    .group-hover\:text-\[\#D4AF37\]:is(:where(.group):hover *) {
      color: #d4af37;
    }
  }

  @media (hover: hover) {
    .group-hover\:text-\[var\(--brand-gold\)\]:is(:where(.group):hover *) {
      color: var(--brand-gold);
    }
  }

  @media (hover: hover) {
    .group-hover\:text-blue-500:is(:where(.group):hover *) {
      color: var(--color-blue-500);
    }
  }

  @media (hover: hover) {
    .group-hover\:text-blue-600:is(:where(.group):hover *) {
      color: var(--color-blue-600);
    }
  }

  @media (hover: hover) {
    .group-hover\:text-primary:is(:where(.group):hover *) {
      color: var(--primary);
    }
  }

  @media (hover: hover) {
    .group-hover\:opacity-70:is(:where(.group):hover *) {
      opacity: .7;
    }
  }

  @media (hover: hover) {
    .group-hover\:opacity-80:is(:where(.group):hover *) {
      opacity: .8;
    }
  }

  @media (hover: hover) {
    .group-hover\:opacity-90:is(:where(.group):hover *) {
      opacity: .9;
    }
  }

  @media (hover: hover) {
    .group-hover\:opacity-100:is(:where(.group):hover *) {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .group-hover\:shadow-lg:is(:where(.group):hover *) {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .group-hover\:shadow-md:is(:where(.group):hover *) {
      --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .group-hover\:duration-200:is(:where(.group):hover *) {
      --tw-duration: .2s;
      transition-duration: .2s;
    }
  }

  @media (hover: hover) {
    .group-hover\/menu-item\:opacity-100:is(:where(.group\/menu-item):hover *) {
      opacity: 1;
    }
  }

  .group-has-data-\[sidebar\=menu-action\]\/menu-item\:pr-8:is(:where(.group\/menu-item):has([data-sidebar="menu-action"]) *) {
    padding-right: calc(var(--spacing) * 8);
  }

  .group-data-\[collapsible\=icon\]\:-mt-8:is(:where(.group)[data-collapsible="icon"] *) {
    margin-top: calc(var(--spacing) * -8);
  }

  .group-data-\[collapsible\=icon\]\:hidden:is(:where(.group)[data-collapsible="icon"] *) {
    display: none;
  }

  .group-data-\[collapsible\=icon\]\:size-8\!:is(:where(.group)[data-collapsible="icon"] *) {
    width: calc(var(--spacing) * 8) !important;
    height: calc(var(--spacing) * 8) !important;
  }

  .group-data-\[collapsible\=icon\]\:w-\(--sidebar-width-icon\):is(:where(.group)[data-collapsible="icon"] *) {
    width: var(--sidebar-width-icon);
  }

  .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)\+\(--spacing\(4\)\)\)\]:is(:where(.group)[data-collapsible="icon"] *) {
    width: calc(var(--sidebar-width-icon)  + (calc(var(--spacing) * 4)));
  }

  .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)\+\(--spacing\(4\)\)\+2px\)\]:is(:where(.group)[data-collapsible="icon"] *) {
    width: calc(var(--sidebar-width-icon)  + (calc(var(--spacing) * 4))  + 2px);
  }

  .group-data-\[collapsible\=icon\]\:overflow-hidden:is(:where(.group)[data-collapsible="icon"] *) {
    overflow: hidden;
  }

  .group-data-\[collapsible\=icon\]\:p-0\!:is(:where(.group)[data-collapsible="icon"] *) {
    padding: calc(var(--spacing) * 0) !important;
  }

  .group-data-\[collapsible\=icon\]\:p-2\!:is(:where(.group)[data-collapsible="icon"] *) {
    padding: calc(var(--spacing) * 2) !important;
  }

  .group-data-\[collapsible\=icon\]\:opacity-0:is(:where(.group)[data-collapsible="icon"] *) {
    opacity: 0;
  }

  .group-data-\[collapsible\=offcanvas\]\:right-\[calc\(var\(--sidebar-width\)\*-1\)\]:is(:where(.group)[data-collapsible="offcanvas"] *) {
    right: calc(var(--sidebar-width) * -1);
  }

  .group-data-\[collapsible\=offcanvas\]\:left-\[calc\(var\(--sidebar-width\)\*-1\)\]:is(:where(.group)[data-collapsible="offcanvas"] *) {
    left: calc(var(--sidebar-width) * -1);
  }

  .group-data-\[collapsible\=offcanvas\]\:w-0:is(:where(.group)[data-collapsible="offcanvas"] *) {
    width: calc(var(--spacing) * 0);
  }

  .group-data-\[collapsible\=offcanvas\]\:translate-x-0:is(:where(.group)[data-collapsible="offcanvas"] *) {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .group-data-\[disabled\=true\]\:pointer-events-none:is(:where(.group)[data-disabled="true"] *) {
    pointer-events: none;
  }

  .group-data-\[disabled\=true\]\:opacity-50:is(:where(.group)[data-disabled="true"] *) {
    opacity: .5;
  }

  .group-data-\[side\=left\]\:-right-4:is(:where(.group)[data-side="left"] *) {
    right: calc(var(--spacing) * -4);
  }

  .group-data-\[side\=left\]\:border-r:is(:where(.group)[data-side="left"] *) {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .group-data-\[side\=right\]\:left-0:is(:where(.group)[data-side="right"] *) {
    left: calc(var(--spacing) * 0);
  }

  .group-data-\[side\=right\]\:rotate-180:is(:where(.group)[data-side="right"] *) {
    rotate: 180deg;
  }

  .group-data-\[side\=right\]\:border-l:is(:where(.group)[data-side="right"] *) {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .group-data-\[state\=open\]\/collapsible\:rotate-90:is(:where(.group\/collapsible)[data-state="open"] *) {
    rotate: 90deg;
  }

  .group-data-\[variant\=floating\]\:rounded-lg:is(:where(.group)[data-variant="floating"] *) {
    border-radius: var(--radius);
  }

  .group-data-\[variant\=floating\]\:border:is(:where(.group)[data-variant="floating"] *) {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .group-data-\[variant\=floating\]\:border-sidebar-border:is(:where(.group)[data-variant="floating"] *) {
    border-color: var(--sidebar-border);
  }

  .group-data-\[variant\=floating\]\:shadow-sm:is(:where(.group)[data-variant="floating"] *) {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  @media (hover: hover) {
    .peer-hover\/menu-button\:text-sidebar-accent-foreground:is(:where(.peer\/menu-button):hover ~ *) {
      color: var(--sidebar-accent-foreground);
    }
  }

  .peer-disabled\:cursor-not-allowed:is(:where(.peer):disabled ~ *) {
    cursor: not-allowed;
  }

  .peer-disabled\:opacity-50:is(:where(.peer):disabled ~ *) {
    opacity: .5;
  }

  .peer-data-\[active\=true\]\/menu-button\:text-sidebar-accent-foreground:is(:where(.peer\/menu-button)[data-active="true"] ~ *) {
    color: var(--sidebar-accent-foreground);
  }

  .peer-data-\[size\=default\]\/menu-button\:top-1\.5:is(:where(.peer\/menu-button)[data-size="default"] ~ *) {
    top: calc(var(--spacing) * 1.5);
  }

  .peer-data-\[size\=lg\]\/menu-button\:top-2\.5:is(:where(.peer\/menu-button)[data-size="lg"] ~ *) {
    top: calc(var(--spacing) * 2.5);
  }

  .peer-data-\[size\=sm\]\/menu-button\:top-1:is(:where(.peer\/menu-button)[data-size="sm"] ~ *) {
    top: calc(var(--spacing) * 1);
  }

  .selection\:bg-primary ::selection, .selection\:bg-primary::selection {
    background-color: var(--primary);
  }

  .selection\:text-primary-foreground ::selection, .selection\:text-primary-foreground::selection {
    color: var(--primary-foreground);
  }

  .file\:inline-flex::file-selector-button {
    display: inline-flex;
  }

  .file\:h-7::file-selector-button {
    height: calc(var(--spacing) * 7);
  }

  .file\:border-0::file-selector-button {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .file\:bg-transparent::file-selector-button {
    background-color: #0000;
  }

  .file\:text-sm::file-selector-button {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .file\:font-medium::file-selector-button {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .file\:text-foreground::file-selector-button {
    color: var(--foreground);
  }

  .placeholder\:text-muted-foreground::placeholder {
    color: var(--muted-foreground);
  }

  .placeholder\:text-neutral-400::placeholder {
    color: var(--color-neutral-400);
  }

  .before\:absolute:before {
    content: var(--tw-content);
    position: absolute;
  }

  .before\:inset-0:before {
    content: var(--tw-content);
    inset: calc(var(--spacing) * 0);
  }

  .before\:bg-gradient-to-r:before {
    content: var(--tw-content);
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .before\:from-blue-400:before {
    content: var(--tw-content);
    --tw-gradient-from: var(--color-blue-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .before\:from-red-400:before {
    content: var(--tw-content);
    --tw-gradient-from: var(--color-red-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .before\:to-blue-500:before {
    content: var(--tw-content);
    --tw-gradient-to: var(--color-blue-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .before\:to-red-500:before {
    content: var(--tw-content);
    --tw-gradient-to: var(--color-red-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .before\:opacity-0:before {
    content: var(--tw-content);
    opacity: 0;
  }

  .before\:transition-opacity:before {
    content: var(--tw-content);
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .before\:duration-300:before {
    content: var(--tw-content);
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .after\:absolute:after {
    content: var(--tw-content);
    position: absolute;
  }

  .after\:-inset-2:after {
    content: var(--tw-content);
    inset: calc(var(--spacing) * -2);
  }

  .after\:inset-0:after {
    content: var(--tw-content);
    inset: calc(var(--spacing) * 0);
  }

  .after\:inset-y-0:after {
    content: var(--tw-content);
    inset-block: calc(var(--spacing) * 0);
  }

  .after\:left-1\/2:after {
    content: var(--tw-content);
    left: 50%;
  }

  .after\:h-\[1px\]:after {
    content: var(--tw-content);
    height: 1px;
  }

  .after\:w-\[2px\]:after {
    content: var(--tw-content);
    width: 2px;
  }

  .after\:flex-grow:after {
    content: var(--tw-content);
    flex-grow: 1;
  }

  .after\:rounded-md:after {
    content: var(--tw-content);
    border-radius: calc(var(--radius)  - 2px);
  }

  .after\:bg-\[var\(--brand-gold\)\]\/10:after {
    content: var(--tw-content);
    background-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .after\:bg-\[var\(--brand-gold\)\]\/10:after {
      background-color: color-mix(in oklab, var(--brand-gold) 10%, transparent);
    }
  }

  .after\:bg-amber-500\/10:after {
    content: var(--tw-content);
    background-color: #f99c001a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .after\:bg-amber-500\/10:after {
      background-color: color-mix(in oklab, var(--color-amber-500) 10%, transparent);
    }
  }

  .after\:bg-blue-500\/10:after {
    content: var(--tw-content);
    background-color: #3080ff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .after\:bg-blue-500\/10:after {
      background-color: color-mix(in oklab, var(--color-blue-500) 10%, transparent);
    }
  }

  .after\:bg-neutral-200:after {
    content: var(--tw-content);
    background-color: var(--color-neutral-200);
  }

  .after\:opacity-30:after {
    content: var(--tw-content);
    opacity: .3;
  }

  .after\:blur-xl:after {
    content: var(--tw-content);
    --tw-blur: blur(var(--blur-xl));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .after\:content-\[\'\'\]:after {
    content: var(--tw-content);
    --tw-content: "";
    content: var(--tw-content);
  }

  .group-data-\[collapsible\=offcanvas\]\:after\:left-full:is(:where(.group)[data-collapsible="offcanvas"] *):after {
    content: var(--tw-content);
    left: 100%;
  }

  .first\:rounded-l-md:first-child {
    border-top-left-radius: calc(var(--radius)  - 2px);
    border-bottom-left-radius: calc(var(--radius)  - 2px);
  }

  .first\:border-l:first-child {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .last\:mb-0:last-child {
    margin-bottom: calc(var(--spacing) * 0);
  }

  .last\:rounded-r-md:last-child {
    border-top-right-radius: calc(var(--radius)  - 2px);
    border-bottom-right-radius: calc(var(--radius)  - 2px);
  }

  .last\:border-b-0:last-child {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0;
  }

  .focus-within\:relative:focus-within {
    position: relative;
  }

  .focus-within\:z-20:focus-within {
    z-index: 20;
  }

  .focus-within\:border-primary\/70:focus-within {
    border-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-within\:border-primary\/70:focus-within {
      border-color: color-mix(in oklab, var(--primary) 70%, transparent);
    }
  }

  .focus-within\:ring-2:focus-within {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-within\:ring-primary\/20:focus-within {
    --tw-ring-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-within\:ring-primary\/20:focus-within {
      --tw-ring-color: color-mix(in oklab, var(--primary) 20%, transparent);
    }
  }

  @media (hover: hover) {
    .hover\:-translate-y-0\.5:hover {
      --tw-translate-y: calc(var(--spacing) * -.5);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .hover\:-translate-y-1:hover {
      --tw-translate-y: calc(var(--spacing) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .hover\:scale-105:hover {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:scale-110:hover {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:scale-\[1\.02\]:hover {
      scale: 1.02;
    }
  }

  @media (hover: hover) {
    .hover\:border-\[\#D4AF37\]\/50:hover {
      border-color: oklab(76.6528% -.00256401 .138654 / .5);
    }
  }

  @media (hover: hover) {
    .hover\:border-\[--theme-color\]:hover {
      border-color: --theme-color;
    }
  }

  @media (hover: hover) {
    .hover\:border-\[var\(--brand-gold\)\]:hover {
      border-color: var(--brand-gold);
    }
  }

  @media (hover: hover) {
    .hover\:border-\[var\(--brand-gold\)\]\/30:hover {
      border-color: var(--brand-gold);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-\[var\(--brand-gold\)\]\/30:hover {
        border-color: color-mix(in oklab, var(--brand-gold) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:border-\[var\(--brand-gold\)\]\/50:hover {
      border-color: var(--brand-gold);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-\[var\(--brand-gold\)\]\/50:hover {
        border-color: color-mix(in oklab, var(--brand-gold) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:border-\[var\(--brand-gold\)\]\/70:hover {
      border-color: var(--brand-gold);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-\[var\(--brand-gold\)\]\/70:hover {
        border-color: color-mix(in oklab, var(--brand-gold) 70%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:border-amber-400:hover {
      border-color: var(--color-amber-400);
    }
  }

  @media (hover: hover) {
    .hover\:border-amber-500:hover {
      border-color: var(--color-amber-500);
    }
  }

  @media (hover: hover) {
    .hover\:border-amber-600:hover {
      border-color: var(--color-amber-600);
    }
  }

  @media (hover: hover) {
    .hover\:border-blue-300:hover {
      border-color: var(--color-blue-300);
    }
  }

  @media (hover: hover) {
    .hover\:border-blue-600:hover {
      border-color: var(--color-blue-600);
    }
  }

  @media (hover: hover) {
    .hover\:border-border:hover {
      border-color: var(--border);
    }
  }

  @media (hover: hover) {
    .hover\:border-current\/30:hover {
      border-color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-current\/30:hover {
        border-color: color-mix(in oklab, currentcolor 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:border-gray-400:hover {
      border-color: var(--color-gray-400);
    }
  }

  @media (hover: hover) {
    .hover\:border-green-600:hover {
      border-color: var(--color-green-600);
    }
  }

  @media (hover: hover) {
    .hover\:border-neutral-300:hover {
      border-color: var(--color-neutral-300);
    }
  }

  @media (hover: hover) {
    .hover\:border-neutral-300\/60:hover {
      border-color: #d4d4d499;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-neutral-300\/60:hover {
        border-color: color-mix(in oklab, var(--color-neutral-300) 60%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:border-neutral-400:hover {
      border-color: var(--color-neutral-400);
    }
  }

  @media (hover: hover) {
    .hover\:border-primary:hover {
      border-color: var(--primary);
    }
  }

  @media (hover: hover) {
    .hover\:border-primary\/50:hover {
      border-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-primary\/50:hover {
        border-color: color-mix(in oklab, var(--primary) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:border-red-200:hover {
      border-color: var(--color-red-200);
    }
  }

  @media (hover: hover) {
    .hover\:border-red-300:hover {
      border-color: var(--color-red-300);
    }
  }

  @media (hover: hover) {
    .hover\:border-red-600:hover {
      border-color: var(--color-red-600);
    }
  }

  @media (hover: hover) {
    .hover\:border-r-primary:hover {
      border-right-color: var(--primary);
    }
  }

  @media (hover: hover) {
    .hover\:border-l-primary:hover {
      border-left-color: var(--primary);
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[\#B8941F\]:hover {
      background-color: #b8941f;
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[--theme-color-5\]:hover {
      background-color: --theme-color-5;
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[var\(--brand-gold\)\]\/5:hover {
      background-color: var(--brand-gold);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-\[var\(--brand-gold\)\]\/5:hover {
        background-color: color-mix(in oklab, var(--brand-gold) 5%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[var\(--brand-gold\)\]\/10:hover {
      background-color: var(--brand-gold);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-\[var\(--brand-gold\)\]\/10:hover {
        background-color: color-mix(in oklab, var(--brand-gold) 10%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[var\(--brand-gold\)\]\/80:hover {
      background-color: var(--brand-gold);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-\[var\(--brand-gold\)\]\/80:hover {
        background-color: color-mix(in oklab, var(--brand-gold) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[var\(--brand-gold\)\]\/90:hover {
      background-color: var(--brand-gold);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-\[var\(--brand-gold\)\]\/90:hover {
        background-color: color-mix(in oklab, var(--brand-gold) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[var\(--brand-gold-light\)\]:hover {
      background-color: var(--brand-gold-light);
    }
  }

  @media (hover: hover) {
    .hover\:bg-accent:hover {
      background-color: var(--accent);
    }
  }

  @media (hover: hover) {
    .hover\:bg-amber-100:hover {
      background-color: var(--color-amber-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-amber-500\/10:hover {
      background-color: #f99c001a;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-amber-500\/10:hover {
        background-color: color-mix(in oklab, var(--color-amber-500) 10%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-amber-600:hover {
      background-color: var(--color-amber-600);
    }
  }

  @media (hover: hover) {
    .hover\:bg-background\/80:hover {
      background-color: var(--background);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-background\/80:hover {
        background-color: color-mix(in oklab, var(--background) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-black\/10:hover {
      background-color: #0000001a;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-black\/10:hover {
        background-color: color-mix(in oklab, var(--color-black) 10%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-black\/70:hover {
      background-color: #000000b3;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-black\/70:hover {
        background-color: color-mix(in oklab, var(--color-black) 70%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-black\/80:hover {
      background-color: #000c;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-black\/80:hover {
        background-color: color-mix(in oklab, var(--color-black) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-50:hover {
      background-color: var(--color-blue-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-50\/50:hover {
      background-color: #eff6ff80;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-blue-50\/50:hover {
        background-color: color-mix(in oklab, var(--color-blue-50) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-500\/10:hover {
      background-color: #3080ff1a;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-blue-500\/10:hover {
        background-color: color-mix(in oklab, var(--color-blue-500) 10%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-600:hover {
      background-color: var(--color-blue-600);
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-700:hover {
      background-color: var(--color-blue-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-900:hover {
      background-color: var(--color-blue-900);
    }
  }

  @media (hover: hover) {
    .hover\:bg-current\/15:hover {
      background-color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-current\/15:hover {
        background-color: color-mix(in oklab, currentcolor 15%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-current\/20:hover {
      background-color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-current\/20:hover {
        background-color: color-mix(in oklab, currentcolor 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-destructive\/10:hover {
      background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-destructive\/10:hover {
        background-color: color-mix(in oklab, var(--destructive) 10%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-destructive\/90:hover {
      background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-destructive\/90:hover {
        background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-50:hover {
      background-color: var(--color-gray-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-100:hover {
      background-color: var(--color-gray-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-600:hover {
      background-color: var(--color-gray-600);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-700:hover {
      background-color: var(--color-gray-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-50:hover {
      background-color: var(--color-green-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-600:hover {
      background-color: var(--color-green-600);
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-700:hover {
      background-color: var(--color-green-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted:hover {
      background-color: var(--muted);
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted\/20:hover {
      background-color: var(--muted);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-muted\/20:hover {
        background-color: color-mix(in oklab, var(--muted) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted\/30:hover {
      background-color: var(--muted);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-muted\/30:hover {
        background-color: color-mix(in oklab, var(--muted) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted\/50:hover {
      background-color: var(--muted);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-muted\/50:hover {
        background-color: color-mix(in oklab, var(--muted) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-neutral-50:hover {
      background-color: var(--color-neutral-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-neutral-100:hover {
      background-color: var(--color-neutral-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-neutral-100\/50:hover {
      background-color: #f5f5f580;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-neutral-100\/50:hover {
        background-color: color-mix(in oklab, var(--color-neutral-100) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-neutral-200:hover {
      background-color: var(--color-neutral-200);
    }
  }

  @media (hover: hover) {
    .hover\:bg-pink-700:hover {
      background-color: var(--color-pink-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary:hover {
      background-color: var(--primary);
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/5:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/5:hover {
        background-color: color-mix(in oklab, var(--primary) 5%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/10:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/10:hover {
        background-color: color-mix(in oklab, var(--primary) 10%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/20:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/20:hover {
        background-color: color-mix(in oklab, var(--primary) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/90:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, var(--primary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-purple-700:hover {
      background-color: var(--color-purple-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-50:hover {
      background-color: var(--color-red-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-100:hover {
      background-color: var(--color-red-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-600:hover {
      background-color: var(--color-red-600);
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-600\/90:hover {
      background-color: #e40014e6;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-red-600\/90:hover {
        background-color: color-mix(in oklab, var(--color-red-600) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-700:hover {
      background-color: var(--color-red-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-rose-50:hover {
      background-color: var(--color-rose-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-secondary\/80:hover {
      background-color: var(--secondary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-secondary\/80:hover {
        background-color: color-mix(in oklab, var(--secondary) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-sidebar-accent:hover {
      background-color: var(--sidebar-accent);
    }
  }

  @media (hover: hover) {
    .hover\:bg-transparent:hover {
      background-color: #0000;
    }
  }

  @media (hover: hover) {
    .hover\:bg-white:hover {
      background-color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .hover\:bg-white\/30:hover {
      background-color: #ffffff4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-white\/30:hover {
        background-color: color-mix(in oklab, var(--color-white) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-white\/50:hover {
      background-color: #ffffff80;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-white\/50:hover {
        background-color: color-mix(in oklab, var(--color-white) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-white\/80:hover {
      background-color: #fffc;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-white\/80:hover {
        background-color: color-mix(in oklab, var(--color-white) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-yellow-600:hover {
      background-color: var(--color-yellow-600);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gradient-to-r:hover {
      --tw-gradient-position: to right in oklab;
      background-image: linear-gradient(var(--tw-gradient-stops));
    }
  }

  @media (hover: hover) {
    .hover\:from-\[\#B08A4A\]:hover {
      --tw-gradient-from: #b08a4a;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:from-\[var\(--brand-gold-dark\)\]:hover {
      --tw-gradient-from: var(--brand-gold-dark);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:from-\[var\(--brand-gold-light\)\]:hover {
      --tw-gradient-from: var(--brand-gold-light);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:from-amber-600:hover {
      --tw-gradient-from: var(--color-amber-600);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:from-blue-600:hover {
      --tw-gradient-from: var(--color-blue-600);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:from-green-600:hover {
      --tw-gradient-from: var(--color-green-600);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:from-neutral-50\/80:hover {
      --tw-gradient-from: #fafafacc;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:from-neutral-50\/80:hover {
        --tw-gradient-from: color-mix(in oklab, var(--color-neutral-50) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:from-neutral-100\/80:hover {
      --tw-gradient-from: #f5f5f5cc;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:from-neutral-100\/80:hover {
        --tw-gradient-from: color-mix(in oklab, var(--color-neutral-100) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:from-purple-700:hover {
      --tw-gradient-from: var(--color-purple-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:from-red-600:hover {
      --tw-gradient-from: var(--color-red-600);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:to-\[\#9A7A3A\]:hover {
      --tw-gradient-to: #9a7a3a;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:to-\[var\(--brand-gold\)\]:hover {
      --tw-gradient-to: var(--brand-gold);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:to-amber-600:hover {
      --tw-gradient-to: var(--color-amber-600);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:to-amber-700:hover {
      --tw-gradient-to: var(--color-amber-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:to-blue-700:hover {
      --tw-gradient-to: var(--color-blue-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:to-green-700:hover {
      --tw-gradient-to: var(--color-green-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:to-neutral-100\/40:hover {
      --tw-gradient-to: #f5f5f566;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:to-neutral-100\/40:hover {
        --tw-gradient-to: color-mix(in oklab, var(--color-neutral-100) 40%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:to-red-700:hover {
      --tw-gradient-to: var(--color-red-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:text-\[\#D4AF37\]:hover {
      color: #d4af37;
    }
  }

  @media (hover: hover) {
    .hover\:text-\[var\(--brand-gold\)\]:hover {
      color: var(--brand-gold);
    }
  }

  @media (hover: hover) {
    .hover\:text-accent-foreground:hover {
      color: var(--accent-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-amber-400:hover {
      color: var(--color-amber-400);
    }
  }

  @media (hover: hover) {
    .hover\:text-amber-600:hover {
      color: var(--color-amber-600);
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-600:hover {
      color: var(--color-blue-600);
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-700:hover {
      color: var(--color-blue-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-destructive:hover {
      color: var(--destructive);
    }
  }

  @media (hover: hover) {
    .hover\:text-foreground:hover {
      color: var(--foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-700:hover {
      color: var(--color-gray-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-900:hover {
      color: var(--color-gray-900);
    }
  }

  @media (hover: hover) {
    .hover\:text-green-600:hover {
      color: var(--color-green-600);
    }
  }

  @media (hover: hover) {
    .hover\:text-green-800:hover {
      color: var(--color-green-800);
    }
  }

  @media (hover: hover) {
    .hover\:text-neutral-400:hover {
      color: var(--color-neutral-400);
    }
  }

  @media (hover: hover) {
    .hover\:text-neutral-600:hover {
      color: var(--color-neutral-600);
    }
  }

  @media (hover: hover) {
    .hover\:text-neutral-700:hover {
      color: var(--color-neutral-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-neutral-800:hover {
      color: var(--color-neutral-800);
    }
  }

  @media (hover: hover) {
    .hover\:text-neutral-900:hover {
      color: var(--color-neutral-900);
    }
  }

  @media (hover: hover) {
    .hover\:text-primary:hover {
      color: var(--primary);
    }
  }

  @media (hover: hover) {
    .hover\:text-primary-foreground:hover {
      color: var(--primary-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-primary\/80:hover {
      color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:text-primary\/80:hover {
        color: color-mix(in oklab, var(--primary) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:text-purple-500:hover {
      color: var(--color-purple-500);
    }
  }

  @media (hover: hover) {
    .hover\:text-red-500:hover {
      color: var(--color-red-500);
    }
  }

  @media (hover: hover) {
    .hover\:text-red-600:hover {
      color: var(--color-red-600);
    }
  }

  @media (hover: hover) {
    .hover\:text-red-700:hover {
      color: var(--color-red-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-red-800:hover {
      color: var(--color-red-800);
    }
  }

  @media (hover: hover) {
    .hover\:text-rose-600:hover {
      color: var(--color-rose-600);
    }
  }

  @media (hover: hover) {
    .hover\:text-sidebar-accent-foreground:hover {
      color: var(--sidebar-accent-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-yellow-300:hover {
      color: var(--color-yellow-300);
    }
  }

  @media (hover: hover) {
    .hover\:text-zinc-800:hover {
      color: var(--color-zinc-800);
    }
  }

  @media (hover: hover) {
    .hover\:underline:hover {
      text-decoration-line: underline;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-80:hover {
      opacity: .8;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-95:hover {
      opacity: .95;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-100:hover {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .hover\:shadow:hover {
      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\]:hover {
      --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-accent)));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-\[0_0_20px_rgba\(var\(--brand-gold-rgb\)\,0\.7\)\]:hover {
      --tw-shadow: 0 0 20px var(--tw-shadow-color, rgba(var(--brand-gold-rgb), .7));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-lg:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-md:hover {
      --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-xl:hover {
      --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:ring-4:hover {
      --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-neutral-200\/20:hover {
      --tw-shadow-color: #e5e5e533;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:shadow-neutral-200\/20:hover {
        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-neutral-200) 20%, transparent) var(--tw-shadow-alpha), transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:ring-primary\/20:hover {
      --tw-ring-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:ring-primary\/20:hover {
        --tw-ring-color: color-mix(in oklab, var(--primary) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:group-data-\[collapsible\=offcanvas\]\:bg-sidebar:hover:is(:where(.group)[data-collapsible="offcanvas"] *) {
      background-color: var(--sidebar);
    }
  }

  @media (hover: hover) {
    .hover\:before\:opacity-20:hover:before {
      content: var(--tw-content);
      opacity: .2;
    }
  }

  @media (hover: hover) {
    .hover\:after\:bg-sidebar-border:hover:after {
      content: var(--tw-content);
      background-color: var(--sidebar-border);
    }
  }

  .focus\:border-\[var\(--brand-gold\)\]:focus {
    border-color: var(--brand-gold);
  }

  .focus\:border-amber-400:focus {
    border-color: var(--color-amber-400);
  }

  .focus\:border-destructive:focus {
    border-color: var(--destructive);
  }

  .focus\:border-primary:focus {
    border-color: var(--primary);
  }

  .focus\:border-red-500:focus {
    border-color: var(--color-red-500);
  }

  .focus\:border-transparent:focus {
    border-color: #0000;
  }

  .focus\:bg-accent:focus {
    background-color: var(--accent);
  }

  .focus\:bg-primary:focus {
    background-color: var(--primary);
  }

  .focus\:bg-primary\/10:focus {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus\:bg-primary\/10:focus {
      background-color: color-mix(in oklab, var(--primary) 10%, transparent);
    }
  }

  .focus\:text-accent-foreground:focus {
    color: var(--accent-foreground);
  }

  .focus\:text-destructive:focus {
    color: var(--destructive);
  }

  .focus\:text-primary:focus {
    color: var(--primary);
  }

  .focus\:text-primary-foreground:focus {
    color: var(--primary-foreground);
  }

  .focus\:opacity-100:focus {
    opacity: 1;
  }

  .focus\:ring-0:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-\[var\(--brand-gold\)\]:focus {
    --tw-ring-color: var(--brand-gold);
  }

  .focus\:ring-amber-400:focus {
    --tw-ring-color: var(--color-amber-400);
  }

  .focus\:ring-blue-500:focus {
    --tw-ring-color: var(--color-blue-500);
  }

  .focus\:ring-primary\/20:focus {
    --tw-ring-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus\:ring-primary\/20:focus {
      --tw-ring-color: color-mix(in oklab, var(--primary) 20%, transparent);
    }
  }

  .focus\:ring-ring:focus {
    --tw-ring-color: var(--ring);
  }

  .focus\:ring-rose-500:focus {
    --tw-ring-color: var(--color-rose-500);
  }

  .focus\:ring-offset-0:focus {
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus\:outline-hidden:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .focus\:outline-hidden:focus {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  .focus-visible\:border-\[var\(--brand-gold\)\]:focus-visible {
    border-color: var(--brand-gold);
  }

  .focus-visible\:border-\[var\(--brand-gold\)\]\/50:focus-visible {
    border-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:border-\[var\(--brand-gold\)\]\/50:focus-visible {
      border-color: color-mix(in oklab, var(--brand-gold) 50%, transparent);
    }
  }

  .focus-visible\:border-ring:focus-visible {
    border-color: var(--ring);
  }

  .focus-visible\:ring-0:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-1:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-2:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-4:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-\[3px\]:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-\[var\(--brand-gold\)\]:focus-visible {
    --tw-ring-color: var(--brand-gold);
  }

  .focus-visible\:ring-\[var\(--brand-gold\)\]\/50:focus-visible {
    --tw-ring-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-\[var\(--brand-gold\)\]\/50:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--brand-gold) 50%, transparent);
    }
  }

  .focus-visible\:ring-destructive\/20:focus-visible {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-destructive\/20:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .focus-visible\:ring-primary\/20:focus-visible {
    --tw-ring-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-primary\/20:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--primary) 20%, transparent);
    }
  }

  .focus-visible\:ring-primary\/50:focus-visible {
    --tw-ring-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-primary\/50:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--primary) 50%, transparent);
    }
  }

  .focus-visible\:ring-red-500\/30:focus-visible {
    --tw-ring-color: #fb2c364d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-red-500\/30:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--color-red-500) 30%, transparent);
    }
  }

  .focus-visible\:ring-ring\/50:focus-visible {
    --tw-ring-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-ring\/50:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  .focus-visible\:ring-offset-0:focus-visible {
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus-visible\:outline-hidden:focus-visible {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .focus-visible\:outline-hidden:focus-visible {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .focus-visible\:outline-1:focus-visible {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .focus-visible\:outline-ring:focus-visible {
    outline-color: var(--ring);
  }

  .active\:scale-\[0\.98\]:active {
    scale: .98;
  }

  .active\:cursor-grabbing:active {
    cursor: grabbing;
  }

  .active\:bg-sidebar-accent:active {
    background-color: var(--sidebar-accent);
  }

  .active\:text-sidebar-accent-foreground:active {
    color: var(--sidebar-accent-foreground);
  }

  .disabled\:pointer-events-none:disabled {
    pointer-events: none;
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:opacity-50:disabled {
    opacity: .5;
  }

  .disabled\:opacity-70:disabled {
    opacity: .7;
  }

  :where([data-side="left"]) .in-data-\[side\=left\]\:cursor-w-resize {
    cursor: w-resize;
  }

  :where([data-side="right"]) .in-data-\[side\=right\]\:cursor-e-resize {
    cursor: e-resize;
  }

  .has-disabled\:opacity-50:has(:disabled) {
    opacity: .5;
  }

  .has-data-\[slot\=card-action\]\:grid-cols-\[1fr_auto\]:has([data-slot="card-action"]) {
    grid-template-columns: 1fr auto;
  }

  .has-data-\[variant\=inset\]\:bg-sidebar:has([data-variant="inset"]) {
    background-color: var(--sidebar);
  }

  .has-\[\>svg\]\:grid-cols-\[calc\(var\(--spacing\)\*4\)_1fr\]:has( > svg) {
    grid-template-columns: calc(var(--spacing) * 4) 1fr;
  }

  .has-\[\>svg\]\:gap-x-3:has( > svg) {
    column-gap: calc(var(--spacing) * 3);
  }

  .has-\[\>svg\]\:px-2\.5:has( > svg) {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .has-\[\>svg\]\:px-3:has( > svg) {
    padding-inline: calc(var(--spacing) * 3);
  }

  .has-\[\>svg\]\:px-4:has( > svg) {
    padding-inline: calc(var(--spacing) * 4);
  }

  .aria-disabled\:pointer-events-none[aria-disabled="true"] {
    pointer-events: none;
  }

  .aria-disabled\:opacity-50[aria-disabled="true"] {
    opacity: .5;
  }

  .aria-invalid\:border-destructive[aria-invalid="true"] {
    border-color: var(--destructive);
  }

  .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .aria-selected\:bg-accent[aria-selected="true"] {
    background-color: var(--accent);
  }

  .aria-selected\:bg-primary[aria-selected="true"] {
    background-color: var(--primary);
  }

  .aria-selected\:text-accent-foreground[aria-selected="true"] {
    color: var(--accent-foreground);
  }

  .aria-selected\:text-muted-foreground[aria-selected="true"] {
    color: var(--muted-foreground);
  }

  .aria-selected\:text-primary-foreground[aria-selected="true"] {
    color: var(--primary-foreground);
  }

  .aria-selected\:opacity-100[aria-selected="true"] {
    opacity: 1;
  }

  .data-\[active\=true\]\:z-10[data-active="true"] {
    z-index: 10;
  }

  .data-\[active\=true\]\:border-ring[data-active="true"] {
    border-color: var(--ring);
  }

  .data-\[active\=true\]\:bg-sidebar-accent[data-active="true"] {
    background-color: var(--sidebar-accent);
  }

  .data-\[active\=true\]\:font-medium[data-active="true"] {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .data-\[active\=true\]\:text-sidebar-accent-foreground[data-active="true"] {
    color: var(--sidebar-accent-foreground);
  }

  .data-\[active\=true\]\:ring-\[3px\][data-active="true"] {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[active\=true\]\:ring-ring\/50[data-active="true"] {
    --tw-ring-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[active\=true\]\:ring-ring\/50[data-active="true"] {
      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  .data-\[active\=true\]\:aria-invalid\:border-destructive[data-active="true"][aria-invalid="true"] {
    border-color: var(--destructive);
  }

  .data-\[active\=true\]\:aria-invalid\:ring-destructive\/20[data-active="true"][aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[active\=true\]\:aria-invalid\:ring-destructive\/20[data-active="true"][aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .data-\[disabled\]\:pointer-events-none[data-disabled] {
    pointer-events: none;
  }

  .data-\[disabled\]\:opacity-50[data-disabled] {
    opacity: .5;
  }

  .data-\[disabled\=true\]\:pointer-events-none[data-disabled="true"] {
    pointer-events: none;
  }

  .data-\[disabled\=true\]\:opacity-50[data-disabled="true"] {
    opacity: .5;
  }

  .data-\[error\=true\]\:text-destructive[data-error="true"] {
    color: var(--destructive);
  }

  .data-\[inset\]\:pl-8[data-inset] {
    padding-left: calc(var(--spacing) * 8);
  }

  .data-\[orientation\=horizontal\]\:h-1\.5[data-orientation="horizontal"] {
    height: calc(var(--spacing) * 1.5);
  }

  .data-\[orientation\=horizontal\]\:h-full[data-orientation="horizontal"] {
    height: 100%;
  }

  .data-\[orientation\=horizontal\]\:h-px[data-orientation="horizontal"] {
    height: 1px;
  }

  .data-\[orientation\=horizontal\]\:w-full[data-orientation="horizontal"] {
    width: 100%;
  }

  .data-\[orientation\=vertical\]\:h-full[data-orientation="vertical"] {
    height: 100%;
  }

  .data-\[orientation\=vertical\]\:min-h-44[data-orientation="vertical"] {
    min-height: calc(var(--spacing) * 44);
  }

  .data-\[orientation\=vertical\]\:w-1\.5[data-orientation="vertical"] {
    width: calc(var(--spacing) * 1.5);
  }

  .data-\[orientation\=vertical\]\:w-auto[data-orientation="vertical"] {
    width: auto;
  }

  .data-\[orientation\=vertical\]\:w-full[data-orientation="vertical"] {
    width: 100%;
  }

  .data-\[orientation\=vertical\]\:w-px[data-orientation="vertical"] {
    width: 1px;
  }

  .data-\[orientation\=vertical\]\:flex-col[data-orientation="vertical"] {
    flex-direction: column;
  }

  .data-\[placeholder\]\:text-muted-foreground[data-placeholder] {
    color: var(--muted-foreground);
  }

  .data-\[selected\=true\]\:bg-accent[data-selected="true"] {
    background-color: var(--accent);
  }

  .data-\[selected\=true\]\:text-accent-foreground[data-selected="true"] {
    color: var(--accent-foreground);
  }

  .data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
    --tw-translate-y: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=left\]\:-translate-x-1[data-side="left"] {
    --tw-translate-x: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=right\]\:translate-x-1[data-side="right"] {
    --tw-translate-x: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=top\]\:-translate-y-1[data-side="top"] {
    --tw-translate-y: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[size\=default\]\:h-9[data-size="default"] {
    height: calc(var(--spacing) * 9);
  }

  .data-\[size\=sm\]\:h-8[data-size="sm"] {
    height: calc(var(--spacing) * 8);
  }

  :is(.\*\:data-\[slot\=alert-description\]\:text-destructive\/90 > *)[data-slot="alert-description"] {
    color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    :is(.\*\:data-\[slot\=alert-description\]\:text-destructive\/90 > *)[data-slot="alert-description"] {
      color: color-mix(in oklab, var(--destructive) 90%, transparent);
    }
  }

  :is(.\*\*\:data-\[slot\=command-input-wrapper\]\:h-12 *)[data-slot="command-input-wrapper"] {
    height: calc(var(--spacing) * 12);
  }

  :is(.\*\:data-\[slot\=select-value\]\:line-clamp-1 > *)[data-slot="select-value"] {
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  :is(.\*\:data-\[slot\=select-value\]\:flex > *)[data-slot="select-value"] {
    display: flex;
  }

  :is(.\*\:data-\[slot\=select-value\]\:items-center > *)[data-slot="select-value"] {
    align-items: center;
  }

  :is(.\*\:data-\[slot\=select-value\]\:gap-2 > *)[data-slot="select-value"] {
    gap: calc(var(--spacing) * 2);
  }

  .data-\[state\=active\]\:border-primary\/20[data-state="active"] {
    border-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[state\=active\]\:border-primary\/20[data-state="active"] {
      border-color: color-mix(in oklab, var(--primary) 20%, transparent);
    }
  }

  .data-\[state\=active\]\:bg-background[data-state="active"] {
    background-color: var(--background);
  }

  .data-\[state\=active\]\:bg-primary[data-state="active"] {
    background-color: var(--primary);
  }

  .data-\[state\=active\]\:bg-white[data-state="active"] {
    background-color: var(--color-white);
  }

  .data-\[state\=active\]\:text-\[var\(--brand-gold\)\][data-state="active"] {
    color: var(--brand-gold);
  }

  .data-\[state\=active\]\:text-neutral-900[data-state="active"] {
    color: var(--color-neutral-900);
  }

  .data-\[state\=active\]\:text-primary-foreground[data-state="active"] {
    color: var(--primary-foreground);
  }

  .data-\[state\=active\]\:shadow-lg[data-state="active"] {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[state\=active\]\:shadow-sm[data-state="active"] {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[state\=checked\]\:translate-x-\[calc\(100\%-2px\)\][data-state="checked"] {
    --tw-translate-x: calc(100% - 2px);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[state\=checked\]\:border-primary[data-state="checked"] {
    border-color: var(--primary);
  }

  .data-\[state\=checked\]\:bg-primary[data-state="checked"] {
    background-color: var(--primary);
  }

  .data-\[state\=checked\]\:text-primary-foreground[data-state="checked"] {
    color: var(--primary-foreground);
  }

  .data-\[state\=closed\]\:duration-300[data-state="closed"] {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .data-\[state\=open\]\:bg-accent[data-state="open"] {
    background-color: var(--accent);
  }

  .data-\[state\=open\]\:bg-secondary[data-state="open"] {
    background-color: var(--secondary);
  }

  .data-\[state\=open\]\:bg-sidebar-accent[data-state="open"] {
    background-color: var(--sidebar-accent);
  }

  .data-\[state\=open\]\:text-accent-foreground[data-state="open"] {
    color: var(--accent-foreground);
  }

  .data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
    color: var(--muted-foreground);
  }

  .data-\[state\=open\]\:text-sidebar-accent-foreground[data-state="open"] {
    color: var(--sidebar-accent-foreground);
  }

  .data-\[state\=open\]\:opacity-100[data-state="open"] {
    opacity: 1;
  }

  .data-\[state\=open\]\:duration-500[data-state="open"] {
    --tw-duration: .5s;
    transition-duration: .5s;
  }

  @media (hover: hover) {
    .data-\[state\=open\]\:hover\:bg-sidebar-accent[data-state="open"]:hover {
      background-color: var(--sidebar-accent);
    }
  }

  @media (hover: hover) {
    .data-\[state\=open\]\:hover\:text-sidebar-accent-foreground[data-state="open"]:hover {
      color: var(--sidebar-accent-foreground);
    }
  }

  .data-\[state\=selected\]\:bg-muted[data-state="selected"] {
    background-color: var(--muted);
  }

  .data-\[state\=unchecked\]\:translate-x-0[data-state="unchecked"] {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[state\=unchecked\]\:bg-input[data-state="unchecked"] {
    background-color: var(--input);
  }

  .data-\[swipe\=cancel\]\:translate-x-0[data-swipe="cancel"] {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[swipe\=end\]\:translate-x-\[var\(--radix-toast-swipe-end-x\)\][data-swipe="end"] {
    --tw-translate-x: var(--radix-toast-swipe-end-x);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[swipe\=move\]\:translate-x-\[var\(--radix-toast-swipe-move-x\)\][data-swipe="move"] {
    --tw-translate-x: var(--radix-toast-swipe-move-x);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[swipe\=move\]\:transition-none[data-swipe="move"] {
    transition-property: none;
  }

  .data-\[variant\=destructive\]\:text-destructive[data-variant="destructive"] {
    color: var(--destructive);
  }

  .data-\[variant\=destructive\]\:focus\:bg-destructive\/10[data-variant="destructive"]:focus {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[variant\=destructive\]\:focus\:bg-destructive\/10[data-variant="destructive"]:focus {
      background-color: color-mix(in oklab, var(--destructive) 10%, transparent);
    }
  }

  .data-\[variant\=destructive\]\:focus\:text-destructive[data-variant="destructive"]:focus {
    color: var(--destructive);
  }

  @supports (backdrop-filter: var(--tw)) {
    .supports-\[backdrop-filter\]\:bg-background\/80 {
      background-color: var(--background);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .supports-\[backdrop-filter\]\:bg-background\/80 {
        background-color: color-mix(in oklab, var(--background) 80%, transparent);
      }
    }
  }

  @media (width >= 40rem) {
    .sm\:not-sr-only {
      clip: auto;
      white-space: normal;
      width: auto;
      height: auto;
      margin: 0;
      padding: 0;
      position: static;
      overflow: visible;
    }
  }

  @media (width >= 40rem) {
    .sm\:top-3 {
      top: calc(var(--spacing) * 3);
    }
  }

  @media (width >= 40rem) {
    .sm\:right-3 {
      right: calc(var(--spacing) * 3);
    }
  }

  @media (width >= 40rem) {
    .sm\:order-1 {
      order: 1;
    }
  }

  @media (width >= 40rem) {
    .sm\:order-2 {
      order: 2;
    }
  }

  @media (width >= 40rem) {
    .sm\:col-span-1 {
      grid-column: span 1 / span 1;
    }
  }

  @media (width >= 40rem) {
    .sm\:mx-2 {
      margin-inline: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 40rem) {
    .sm\:my-8 {
      margin-block: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:mt-1 {
      margin-top: calc(var(--spacing) * 1);
    }
  }

  @media (width >= 40rem) {
    .sm\:mt-3 {
      margin-top: calc(var(--spacing) * 3);
    }
  }

  @media (width >= 40rem) {
    .sm\:mt-6 {
      margin-top: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:mt-8 {
      margin-top: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:mt-10 {
      margin-top: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 40rem) {
    .sm\:mt-12 {
      margin-top: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 40rem) {
    .sm\:mr-1 {
      margin-right: calc(var(--spacing) * 1);
    }
  }

  @media (width >= 40rem) {
    .sm\:mr-1\.5 {
      margin-right: calc(var(--spacing) * 1.5);
    }
  }

  @media (width >= 40rem) {
    .sm\:mr-2 {
      margin-right: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 40rem) {
    .sm\:mb-0\.5 {
      margin-bottom: calc(var(--spacing) * .5);
    }
  }

  @media (width >= 40rem) {
    .sm\:mb-2 {
      margin-bottom: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 40rem) {
    .sm\:mb-3 {
      margin-bottom: calc(var(--spacing) * 3);
    }
  }

  @media (width >= 40rem) {
    .sm\:mb-4 {
      margin-bottom: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 40rem) {
    .sm\:mb-6 {
      margin-bottom: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:mb-8 {
      margin-bottom: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:mb-10 {
      margin-bottom: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 40rem) {
    .sm\:ml-2 {
      margin-left: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 40rem) {
    .sm\:block {
      display: block;
    }
  }

  @media (width >= 40rem) {
    .sm\:flex {
      display: flex;
    }
  }

  @media (width >= 40rem) {
    .sm\:hidden {
      display: none;
    }
  }

  @media (width >= 40rem) {
    .sm\:inline {
      display: inline;
    }
  }

  @media (width >= 40rem) {
    .sm\:inline-block {
      display: inline-block;
    }
  }

  @media (width >= 40rem) {
    .sm\:h-3\.5 {
      height: calc(var(--spacing) * 3.5);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-4 {
      height: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-5 {
      height: calc(var(--spacing) * 5);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-6 {
      height: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-8 {
      height: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-9 {
      height: calc(var(--spacing) * 9);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-10 {
      height: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-11 {
      height: calc(var(--spacing) * 11);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-12 {
      height: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-16 {
      height: calc(var(--spacing) * 16);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-24 {
      height: calc(var(--spacing) * 24);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-64 {
      height: calc(var(--spacing) * 64);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-\[50vh\] {
      height: 50vh;
    }
  }

  @media (width >= 40rem) {
    .sm\:min-h-\[68px\] {
      min-height: 68px;
    }
  }

  @media (width >= 40rem) {
    .sm\:min-h-\[100px\] {
      min-height: 100px;
    }
  }

  @media (width >= 40rem) {
    .sm\:w-3\.5 {
      width: calc(var(--spacing) * 3.5);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-4 {
      width: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-5 {
      width: calc(var(--spacing) * 5);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-6 {
      width: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-8 {
      width: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-10 {
      width: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-12 {
      width: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-16 {
      width: calc(var(--spacing) * 16);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-20 {
      width: calc(var(--spacing) * 20);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-24 {
      width: calc(var(--spacing) * 24);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-64 {
      width: calc(var(--spacing) * 64);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-80 {
      width: calc(var(--spacing) * 80);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-\[140px\] {
      width: 140px;
    }
  }

  @media (width >= 40rem) {
    .sm\:w-\[180px\] {
      width: 180px;
    }
  }

  @media (width >= 40rem) {
    .sm\:w-auto {
      width: auto;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-\[425px\] {
      max-width: 425px;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-\[600px\] {
      max-width: 600px;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-\[700px\] {
      max-width: 700px;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-\[800px\] {
      max-width: 800px;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-lg {
      max-width: var(--container-lg);
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-md {
      max-width: var(--container-md);
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-none {
      max-width: none;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-sm {
      max-width: var(--container-sm);
    }
  }

  @media (width >= 40rem) {
    .sm\:min-w-\[140px\] {
      min-width: 140px;
    }
  }

  @media (width >= 40rem) {
    .sm\:min-w-\[160px\] {
      min-width: 160px;
    }
  }

  @media (width >= 40rem) {
    .sm\:flex-1 {
      flex: 1;
    }
  }

  @media (width >= 40rem) {
    .sm\:flex-none {
      flex: none;
    }
  }

  @media (width >= 40rem) {
    .sm\:flex-shrink-0 {
      flex-shrink: 0;
    }
  }

  @media (width >= 40rem) {
    .sm\:basis-1\/4 {
      flex-basis: 25%;
    }
  }

  @media (width >= 40rem) {
    .sm\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 40rem) {
    .sm\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 40rem) {
    .sm\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 40rem) {
    .sm\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 40rem) {
    .sm\:items-center {
      align-items: center;
    }
  }

  @media (width >= 40rem) {
    .sm\:justify-between {
      justify-content: space-between;
    }
  }

  @media (width >= 40rem) {
    .sm\:justify-end {
      justify-content: flex-end;
    }
  }

  @media (width >= 40rem) {
    .sm\:gap-2 {
      gap: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 40rem) {
    .sm\:gap-2\.5 {
      gap: calc(var(--spacing) * 2.5);
    }
  }

  @media (width >= 40rem) {
    .sm\:gap-3 {
      gap: calc(var(--spacing) * 3);
    }
  }

  @media (width >= 40rem) {
    .sm\:gap-4 {
      gap: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 40rem) {
    .sm\:gap-5 {
      gap: calc(var(--spacing) * 5);
    }
  }

  @media (width >= 40rem) {
    .sm\:gap-6 {
      gap: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:gap-12 {
      gap: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 40rem) {
    :where(.sm\:space-y-1 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
  }

  @media (width >= 40rem) {
    :where(.sm\:space-y-2 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }

  @media (width >= 40rem) {
    :where(.sm\:space-y-3 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
    }
  }

  @media (width >= 40rem) {
    :where(.sm\:space-y-4 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }

  @media (width >= 40rem) {
    :where(.sm\:space-y-6 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }

  @media (width >= 40rem) {
    :where(.sm\:space-y-12 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 12) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 12) * calc(1 - var(--tw-space-y-reverse)));
    }
  }

  @media (width >= 40rem) {
    :where(.sm\:space-x-2 > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }

  @media (width >= 40rem) {
    :where(.sm\:space-x-3 > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
    }
  }

  @media (width >= 40rem) {
    :where(.sm\:space-x-4 > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }

  @media (width >= 40rem) {
    .sm\:rounded-2xl {
      border-radius: var(--radius-2xl);
    }
  }

  @media (width >= 40rem) {
    .sm\:rounded-lg {
      border-radius: var(--radius);
    }
  }

  @media (width >= 40rem) {
    .sm\:rounded-xl {
      border-radius: calc(var(--radius)  + 4px);
    }
  }

  @media (width >= 40rem) {
    .sm\:p-1\.5 {
      padding: calc(var(--spacing) * 1.5);
    }
  }

  @media (width >= 40rem) {
    .sm\:p-2 {
      padding: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 40rem) {
    .sm\:p-2\.5 {
      padding: calc(var(--spacing) * 2.5);
    }
  }

  @media (width >= 40rem) {
    .sm\:p-3 {
      padding: calc(var(--spacing) * 3);
    }
  }

  @media (width >= 40rem) {
    .sm\:p-4 {
      padding: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 40rem) {
    .sm\:p-5 {
      padding: calc(var(--spacing) * 5);
    }
  }

  @media (width >= 40rem) {
    .sm\:p-6 {
      padding: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:p-8 {
      padding: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:p-10 {
      padding: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-2 {
      padding-inline: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-3 {
      padding-inline: calc(var(--spacing) * 3);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-4 {
      padding-inline: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:py-1\.5 {
      padding-block: calc(var(--spacing) * 1.5);
    }
  }

  @media (width >= 40rem) {
    .sm\:py-2 {
      padding-block: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 40rem) {
    .sm\:py-3 {
      padding-block: calc(var(--spacing) * 3);
    }
  }

  @media (width >= 40rem) {
    .sm\:py-5 {
      padding-block: calc(var(--spacing) * 5);
    }
  }

  @media (width >= 40rem) {
    .sm\:py-6 {
      padding-block: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:py-16 {
      padding-block: calc(var(--spacing) * 16);
    }
  }

  @media (width >= 40rem) {
    .sm\:pt-1 {
      padding-top: calc(var(--spacing) * 1);
    }
  }

  @media (width >= 40rem) {
    .sm\:pt-1\.5 {
      padding-top: calc(var(--spacing) * 1.5);
    }
  }

  @media (width >= 40rem) {
    .sm\:pt-2 {
      padding-top: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 40rem) {
    .sm\:pt-4 {
      padding-top: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 40rem) {
    .sm\:pt-6 {
      padding-top: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:pt-10 {
      padding-top: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 40rem) {
    .sm\:pr-2 {
      padding-right: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 40rem) {
    .sm\:pb-2 {
      padding-bottom: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 40rem) {
    .sm\:pb-4 {
      padding-bottom: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 40rem) {
    .sm\:pb-6 {
      padding-bottom: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:pb-18 {
      padding-bottom: calc(var(--spacing) * 18);
    }
  }

  @media (width >= 40rem) {
    .sm\:text-left {
      text-align: left;
    }
  }

  @media (width >= 40rem) {
    .sm\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-4xl {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-lg {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-xl {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-xs {
      font-size: var(--text-xs);
      line-height: var(--tw-leading, var(--text-xs--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-\[0\.6rem\] {
      font-size: .6rem;
    }
  }

  @media (width >= 40rem) {
    .sm\:text-\[9px\] {
      font-size: 9px;
    }
  }

  @media (width >= 40rem) {
    .sm\:shadow-none {
      --tw-shadow: 0 0 #0000;
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (width >= 48rem) {
    .md\:sticky {
      position: sticky;
    }
  }

  @media (width >= 48rem) {
    .md\:top-4 {
      top: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 48rem) {
    .md\:top-8 {
      top: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 48rem) {
    .md\:top-20 {
      top: calc(var(--spacing) * 20);
    }
  }

  @media (width >= 48rem) {
    .md\:right-4 {
      right: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 48rem) {
    .md\:bottom-\[60px\] {
      bottom: 60px;
    }
  }

  @media (width >= 48rem) {
    .md\:col-span-1 {
      grid-column: span 1 / span 1;
    }
  }

  @media (width >= 48rem) {
    .md\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @media (width >= 48rem) {
    .md\:col-span-3 {
      grid-column: span 3 / span 3;
    }
  }

  @media (width >= 48rem) {
    .md\:mt-0 {
      margin-top: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:mt-4 {
      margin-top: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 48rem) {
    .md\:mt-10 {
      margin-top: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 48rem) {
    .md\:mt-12 {
      margin-top: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 48rem) {
    .md\:mt-16 {
      margin-top: calc(var(--spacing) * 16);
    }
  }

  @media (width >= 48rem) {
    .md\:mr-2 {
      margin-right: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 48rem) {
    .md\:mb-0 {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:mb-1 {
      margin-bottom: calc(var(--spacing) * 1);
    }
  }

  @media (width >= 48rem) {
    .md\:mb-5 {
      margin-bottom: calc(var(--spacing) * 5);
    }
  }

  @media (width >= 48rem) {
    .md\:mb-6 {
      margin-bottom: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 48rem) {
    .md\:mb-10 {
      margin-bottom: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 48rem) {
    .md\:mb-12 {
      margin-bottom: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 48rem) {
    .md\:mb-16 {
      margin-bottom: calc(var(--spacing) * 16);
    }
  }

  @media (width >= 48rem) {
    .md\:-ml-4 {
      margin-left: calc(var(--spacing) * -4);
    }
  }

  @media (width >= 48rem) {
    .md\:ml-0 {
      margin-left: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:ml-4 {
      margin-left: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 48rem) {
    .md\:block {
      display: block;
    }
  }

  @media (width >= 48rem) {
    .md\:flex {
      display: flex;
    }
  }

  @media (width >= 48rem) {
    .md\:grid {
      display: grid;
    }
  }

  @media (width >= 48rem) {
    .md\:hidden {
      display: none;
    }
  }

  @media (width >= 48rem) {
    .md\:inline {
      display: inline;
    }
  }

  @media (width >= 48rem) {
    .md\:h-4 {
      height: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 48rem) {
    .md\:h-4\.5 {
      height: calc(var(--spacing) * 4.5);
    }
  }

  @media (width >= 48rem) {
    .md\:h-5 {
      height: calc(var(--spacing) * 5);
    }
  }

  @media (width >= 48rem) {
    .md\:h-6 {
      height: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 48rem) {
    .md\:h-7 {
      height: calc(var(--spacing) * 7);
    }
  }

  @media (width >= 48rem) {
    .md\:h-8 {
      height: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 48rem) {
    .md\:h-10 {
      height: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 48rem) {
    .md\:h-11 {
      height: calc(var(--spacing) * 11);
    }
  }

  @media (width >= 48rem) {
    .md\:h-12 {
      height: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 48rem) {
    .md\:h-16 {
      height: calc(var(--spacing) * 16);
    }
  }

  @media (width >= 48rem) {
    .md\:h-20 {
      height: calc(var(--spacing) * 20);
    }
  }

  @media (width >= 48rem) {
    .md\:h-24 {
      height: calc(var(--spacing) * 24);
    }
  }

  @media (width >= 48rem) {
    .md\:h-48 {
      height: calc(var(--spacing) * 48);
    }
  }

  @media (width >= 48rem) {
    .md\:h-96 {
      height: calc(var(--spacing) * 96);
    }
  }

  @media (width >= 48rem) {
    .md\:h-\[50vh\] {
      height: 50vh;
    }
  }

  @media (width >= 48rem) {
    .md\:h-\[55vh\] {
      height: 55vh;
    }
  }

  @media (width >= 48rem) {
    .md\:h-\[140px\] {
      height: 140px;
    }
  }

  @media (width >= 48rem) {
    .md\:h-\[400px\] {
      height: 400px;
    }
  }

  @media (width >= 48rem) {
    .md\:h-auto {
      height: auto;
    }
  }

  @media (width >= 48rem) {
    .md\:min-h-\[calc\(100vh-64px\)\] {
      min-height: calc(100vh - 64px);
    }
  }

  @media (width >= 48rem) {
    .md\:w-1\/2 {
      width: 50%;
    }
  }

  @media (width >= 48rem) {
    .md\:w-4 {
      width: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 48rem) {
    .md\:w-4\.5 {
      width: calc(var(--spacing) * 4.5);
    }
  }

  @media (width >= 48rem) {
    .md\:w-5 {
      width: calc(var(--spacing) * 5);
    }
  }

  @media (width >= 48rem) {
    .md\:w-6 {
      width: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 48rem) {
    .md\:w-7 {
      width: calc(var(--spacing) * 7);
    }
  }

  @media (width >= 48rem) {
    .md\:w-8 {
      width: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 48rem) {
    .md\:w-10 {
      width: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 48rem) {
    .md\:w-11 {
      width: calc(var(--spacing) * 11);
    }
  }

  @media (width >= 48rem) {
    .md\:w-12 {
      width: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 48rem) {
    .md\:w-20 {
      width: calc(var(--spacing) * 20);
    }
  }

  @media (width >= 48rem) {
    .md\:w-40 {
      width: calc(var(--spacing) * 40);
    }
  }

  @media (width >= 48rem) {
    .md\:w-96 {
      width: calc(var(--spacing) * 96);
    }
  }

  @media (width >= 48rem) {
    .md\:w-\[120px\] {
      width: 120px;
    }
  }

  @media (width >= 48rem) {
    .md\:w-\[140px\] {
      width: 140px;
    }
  }

  @media (width >= 48rem) {
    .md\:w-\[200px\] {
      width: 200px;
    }
  }

  @media (width >= 48rem) {
    .md\:w-auto {
      width: auto;
    }
  }

  @media (width >= 48rem) {
    .md\:max-w-\[420px\] {
      max-width: 420px;
    }
  }

  @media (width >= 48rem) {
    .md\:max-w-lg {
      max-width: var(--container-lg);
    }
  }

  @media (width >= 48rem) {
    .md\:min-w-\[150px\] {
      min-width: 150px;
    }
  }

  @media (width >= 48rem) {
    .md\:flex-1 {
      flex: 1;
    }
  }

  @media (width >= 48rem) {
    .md\:flex-none {
      flex: none;
    }
  }

  @media (width >= 48rem) {
    .md\:basis-1\/5 {
      flex-basis: 20%;
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-5 {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 48rem) {
    .md\:flex-row-reverse {
      flex-direction: row-reverse;
    }
  }

  @media (width >= 48rem) {
    .md\:flex-wrap {
      flex-wrap: wrap;
    }
  }

  @media (width >= 48rem) {
    .md\:items-center {
      align-items: center;
    }
  }

  @media (width >= 48rem) {
    .md\:justify-between {
      justify-content: space-between;
    }
  }

  @media (width >= 48rem) {
    .md\:justify-center {
      justify-content: center;
    }
  }

  @media (width >= 48rem) {
    .md\:gap-2 {
      gap: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 48rem) {
    .md\:gap-4 {
      gap: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 48rem) {
    .md\:gap-5 {
      gap: calc(var(--spacing) * 5);
    }
  }

  @media (width >= 48rem) {
    .md\:gap-6 {
      gap: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 48rem) {
    .md\:gap-8 {
      gap: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 48rem) {
    .md\:gap-12 {
      gap: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 48rem) {
    :where(.md\:space-y-6 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }

  @media (width >= 48rem) {
    :where(.md\:space-y-16 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 16) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 16) * calc(1 - var(--tw-space-y-reverse)));
    }
  }

  @media (width >= 48rem) {
    :where(.md\:space-x-4 > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }

  @media (width >= 48rem) {
    .md\:rounded-xl {
      border-radius: calc(var(--radius)  + 4px);
    }
  }

  @media (width >= 48rem) {
    .md\:border {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }

  @media (width >= 48rem) {
    .md\:border-gray-200 {
      border-color: var(--color-gray-200);
    }
  }

  @media (width >= 48rem) {
    .md\:border-neutral-200 {
      border-color: var(--color-neutral-200);
    }
  }

  @media (width >= 48rem) {
    .md\:p-2 {
      padding: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 48rem) {
    .md\:p-3 {
      padding: calc(var(--spacing) * 3);
    }
  }

  @media (width >= 48rem) {
    .md\:p-4 {
      padding: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 48rem) {
    .md\:p-5 {
      padding: calc(var(--spacing) * 5);
    }
  }

  @media (width >= 48rem) {
    .md\:p-6 {
      padding: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 48rem) {
    .md\:p-8 {
      padding: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 48rem) {
    .md\:p-12 {
      padding: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 48rem) {
    .md\:px-2 {
      padding-inline: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 48rem) {
    .md\:px-4 {
      padding-inline: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 48rem) {
    .md\:px-5 {
      padding-inline: calc(var(--spacing) * 5);
    }
  }

  @media (width >= 48rem) {
    .md\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 48rem) {
    .md\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 48rem) {
    .md\:py-0 {
      padding-block: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:py-2\.5 {
      padding-block: calc(var(--spacing) * 2.5);
    }
  }

  @media (width >= 48rem) {
    .md\:py-6 {
      padding-block: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 48rem) {
    .md\:py-12 {
      padding-block: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 48rem) {
    .md\:py-16 {
      padding-block: calc(var(--spacing) * 16);
    }
  }

  @media (width >= 48rem) {
    .md\:py-20 {
      padding-block: calc(var(--spacing) * 20);
    }
  }

  @media (width >= 48rem) {
    .md\:py-24 {
      padding-block: calc(var(--spacing) * 24);
    }
  }

  @media (width >= 48rem) {
    .md\:pt-4 {
      padding-top: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 48rem) {
    .md\:pt-8 {
      padding-top: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 48rem) {
    .md\:pt-12 {
      padding-top: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 48rem) {
    .md\:pt-28 {
      padding-top: calc(var(--spacing) * 28);
    }
  }

  @media (width >= 48rem) {
    .md\:pt-32 {
      padding-top: calc(var(--spacing) * 32);
    }
  }

  @media (width >= 48rem) {
    .md\:pt-36 {
      padding-top: calc(var(--spacing) * 36);
    }
  }

  @media (width >= 48rem) {
    .md\:pr-12 {
      padding-right: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 48rem) {
    .md\:pb-0 {
      padding-bottom: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:pb-6 {
      padding-bottom: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 48rem) {
    .md\:pb-8 {
      padding-bottom: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 48rem) {
    .md\:pb-12 {
      padding-bottom: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 48rem) {
    .md\:pb-24 {
      padding-bottom: calc(var(--spacing) * 24);
    }
  }

  @media (width >= 48rem) {
    .md\:pl-4 {
      padding-left: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 48rem) {
    .md\:pl-12 {
      padding-left: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 48rem) {
    .md\:text-left {
      text-align: left;
    }
  }

  @media (width >= 48rem) {
    .md\:text-right {
      text-align: right;
    }
  }

  @media (width >= 48rem) {
    .md\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-4xl {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-6xl {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-lg {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-xl {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-xl\/relaxed {
      font-size: var(--text-xl);
      line-height: var(--leading-relaxed);
    }
  }

  @media (width >= 48rem) {
    .md\:text-\[10px\] {
      font-size: 10px;
    }
  }

  @media (width >= 48rem) {
    .md\:opacity-0 {
      opacity: 0;
    }
  }

  @media (width >= 48rem) {
    .md\:shadow-sm {
      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (width >= 48rem) {
    .md\:transition-all {
      transition-property: all;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
    }
  }

  @media (width >= 48rem) {
    .md\:duration-300 {
      --tw-duration: .3s;
      transition-duration: .3s;
    }
  }

  @media (width >= 48rem) {
    @media (hover: hover) {
      .md\:group-hover\:opacity-100:is(:where(.group):hover *) {
        opacity: 1;
      }
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:m-2:is(:where(.peer)[data-variant="inset"] ~ *) {
      margin: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:ml-0:is(:where(.peer)[data-variant="inset"] ~ *) {
      margin-left: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:rounded-xl:is(:where(.peer)[data-variant="inset"] ~ *) {
      border-radius: calc(var(--radius)  + 4px);
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:shadow-sm:is(:where(.peer)[data-variant="inset"] ~ *) {
      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:peer-data-\[state\=collapsed\]\:ml-2:is(:where(.peer)[data-variant="inset"] ~ *):is(:where(.peer)[data-state="collapsed"] ~ *) {
      margin-left: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 48rem) {
    .md\:after\:hidden:after {
      content: var(--tw-content);
      display: none;
    }
  }

  @media (width >= 48rem) {
    @media (hover: hover) {
      .md\:hover\:shadow-md:hover {
        --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }

  @media (width >= 64rem) {
    .lg\:sticky {
      position: sticky;
    }
  }

  @media (width >= 64rem) {
    .lg\:top-24 {
      top: calc(var(--spacing) * 24);
    }
  }

  @media (width >= 64rem) {
    .lg\:bottom-8 {
      bottom: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 64rem) {
    .lg\:col-span-1 {
      grid-column: span 1 / span 1;
    }
  }

  @media (width >= 64rem) {
    .lg\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @media (width >= 64rem) {
    .lg\:col-span-4 {
      grid-column: span 4 / span 4;
    }
  }

  @media (width >= 64rem) {
    .lg\:col-span-8 {
      grid-column: span 8 / span 8;
    }
  }

  @media (width >= 64rem) {
    .lg\:mx-0 {
      margin-inline: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 64rem) {
    .lg\:mt-0 {
      margin-top: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 64rem) {
    .lg\:block {
      display: block;
    }
  }

  @media (width >= 64rem) {
    .lg\:flex {
      display: flex;
    }
  }

  @media (width >= 64rem) {
    .lg\:grid {
      display: grid;
    }
  }

  @media (width >= 64rem) {
    .lg\:hidden {
      display: none;
    }
  }

  @media (width >= 64rem) {
    .lg\:h-9 {
      height: calc(var(--spacing) * 9);
    }
  }

  @media (width >= 64rem) {
    .lg\:h-20 {
      height: calc(var(--spacing) * 20);
    }
  }

  @media (width >= 64rem) {
    .lg\:h-64 {
      height: calc(var(--spacing) * 64);
    }
  }

  @media (width >= 64rem) {
    .lg\:w-1\/2 {
      width: 50%;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-1\/4 {
      width: 25%;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-2\/5 {
      width: 40%;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-3\/4 {
      width: 75%;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-3\/5 {
      width: 60%;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-9 {
      width: calc(var(--spacing) * 9);
    }
  }

  @media (width >= 64rem) {
    .lg\:w-20 {
      width: calc(var(--spacing) * 20);
    }
  }

  @media (width >= 64rem) {
    .lg\:basis-1\/6 {
      flex-basis: 16.6667%;
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-5 {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-6 {
      grid-template-columns: repeat(6, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-12 {
      grid-template-columns: repeat(12, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 64rem) {
    .lg\:items-center {
      align-items: center;
    }
  }

  @media (width >= 64rem) {
    .lg\:justify-start {
      justify-content: flex-start;
    }
  }

  @media (width >= 64rem) {
    .lg\:gap-6 {
      gap: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 64rem) {
    .lg\:gap-12 {
      gap: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 64rem) {
    .lg\:gap-16 {
      gap: calc(var(--spacing) * 16);
    }
  }

  @media (width >= 64rem) {
    :where(.lg\:space-y-8 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
    }
  }

  @media (width >= 64rem) {
    :where(.lg\:space-x-2 > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }

  @media (width >= 64rem) {
    .lg\:p-10 {
      padding: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 64rem) {
    .lg\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 64rem) {
    .lg\:px-12 {
      padding-inline: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 64rem) {
    .lg\:text-left {
      text-align: left;
    }
  }

  @media (width >= 64rem) {
    .lg\:text-4xl {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-6xl {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-base\/relaxed {
      font-size: var(--text-base);
      line-height: var(--leading-relaxed);
    }
  }

  @media (width >= 64rem) {
    .lg\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }

  @media (width >= 80rem) {
    .xl\:col-span-3 {
      grid-column: span 3 / span 3;
    }
  }

  @media (width >= 80rem) {
    .xl\:col-span-9 {
      grid-column: span 9 / span 9;
    }
  }

  @media (width >= 80rem) {
    .xl\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 80rem) {
    .xl\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 80rem) {
    .xl\:grid-cols-5 {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }

  @media (width >= 80rem) {
    .xl\:gap-16 {
      gap: calc(var(--spacing) * 16);
    }
  }

  @media (width >= 80rem) {
    .xl\:px-16 {
      padding-inline: calc(var(--spacing) * 16);
    }
  }

  @media (width >= 80rem) {
    .xl\:text-xl\/relaxed {
      font-size: var(--text-xl);
      line-height: var(--leading-relaxed);
    }
  }

  .dark\:scale-0:is(.dark *) {
    --tw-scale-x: 0%;
    --tw-scale-y: 0%;
    --tw-scale-z: 0%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .dark\:scale-100:is(.dark *) {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .dark\:-rotate-90:is(.dark *) {
    rotate: -90deg;
  }

  .dark\:rotate-0:is(.dark *) {
    rotate: none;
  }

  :where(.dark\:divide-neutral-800\/60:is(.dark *) > :not(:last-child)) {
    border-color: #26262699;
  }

  @supports (color: color-mix(in lab, red, red)) {
    :where(.dark\:divide-neutral-800\/60:is(.dark *) > :not(:last-child)) {
      border-color: color-mix(in oklab, var(--color-neutral-800) 60%, transparent);
    }
  }

  .dark\:border-\[\#D4AF37\]\/30:is(.dark *) {
    border-color: oklab(76.6528% -.00256401 .138654 / .3);
  }

  .dark\:border-\[var\(--brand-gold\)\]:is(.dark *) {
    border-color: var(--brand-gold);
  }

  .dark\:border-\[var\(--brand-gold\)\]\/10:is(.dark *) {
    border-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-\[var\(--brand-gold\)\]\/10:is(.dark *) {
      border-color: color-mix(in oklab, var(--brand-gold) 10%, transparent);
    }
  }

  .dark\:border-\[var\(--brand-gold\)\]\/20:is(.dark *) {
    border-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-\[var\(--brand-gold\)\]\/20:is(.dark *) {
      border-color: color-mix(in oklab, var(--brand-gold) 20%, transparent);
    }
  }

  .dark\:border-\[var\(--brand-gold\)\]\/30:is(.dark *) {
    border-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-\[var\(--brand-gold\)\]\/30:is(.dark *) {
      border-color: color-mix(in oklab, var(--brand-gold) 30%, transparent);
    }
  }

  .dark\:border-amber-400:is(.dark *) {
    border-color: var(--color-amber-400);
  }

  .dark\:border-amber-600\/70:is(.dark *) {
    border-color: #dd7400b3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-amber-600\/70:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-amber-600) 70%, transparent);
    }
  }

  .dark\:border-amber-700:is(.dark *) {
    border-color: var(--color-amber-700);
  }

  .dark\:border-amber-700\/70:is(.dark *) {
    border-color: #b75000b3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-amber-700\/70:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-amber-700) 70%, transparent);
    }
  }

  .dark\:border-amber-800:is(.dark *) {
    border-color: var(--color-amber-800);
  }

  .dark\:border-amber-800\/30:is(.dark *) {
    border-color: #953d004d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-amber-800\/30:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-amber-800) 30%, transparent);
    }
  }

  .dark\:border-amber-800\/50:is(.dark *) {
    border-color: #953d0080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-amber-800\/50:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-amber-800) 50%, transparent);
    }
  }

  .dark\:border-amber-900\/30:is(.dark *) {
    border-color: #7b33064d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-amber-900\/30:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-amber-900) 30%, transparent);
    }
  }

  .dark\:border-black:is(.dark *) {
    border-color: var(--color-black);
  }

  .dark\:border-blue-400\/50:is(.dark *) {
    border-color: #54a2ff80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-blue-400\/50:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-blue-400) 50%, transparent);
    }
  }

  .dark\:border-blue-500\/30:is(.dark *) {
    border-color: #3080ff4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-blue-500\/30:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-blue-500) 30%, transparent);
    }
  }

  .dark\:border-blue-700:is(.dark *) {
    border-color: var(--color-blue-700);
  }

  .dark\:border-blue-700\/50:is(.dark *) {
    border-color: #1447e680;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-blue-700\/50:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-blue-700) 50%, transparent);
    }
  }

  .dark\:border-blue-700\/70:is(.dark *) {
    border-color: #1447e6b3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-blue-700\/70:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-blue-700) 70%, transparent);
    }
  }

  .dark\:border-blue-800:is(.dark *) {
    border-color: var(--color-blue-800);
  }

  .dark\:border-blue-800\/30:is(.dark *) {
    border-color: #193cb84d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-blue-800\/30:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-blue-800) 30%, transparent);
    }
  }

  .dark\:border-blue-800\/50:is(.dark *) {
    border-color: #193cb880;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-blue-800\/50:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-blue-800) 50%, transparent);
    }
  }

  .dark\:border-blue-900:is(.dark *) {
    border-color: var(--color-blue-900);
  }

  .dark\:border-blue-900\/30:is(.dark *) {
    border-color: #1c398e4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-blue-900\/30:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-blue-900) 30%, transparent);
    }
  }

  .dark\:border-border:is(.dark *) {
    border-color: var(--border);
  }

  .dark\:border-gray-600:is(.dark *) {
    border-color: var(--color-gray-600);
  }

  .dark\:border-gray-700:is(.dark *) {
    border-color: var(--color-gray-700);
  }

  .dark\:border-gray-700\/70:is(.dark *) {
    border-color: #364153b3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-gray-700\/70:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-gray-700) 70%, transparent);
    }
  }

  .dark\:border-gray-800:is(.dark *) {
    border-color: var(--color-gray-800);
  }

  .dark\:border-green-400\/50:is(.dark *) {
    border-color: #05df7280;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-green-400\/50:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-green-400) 50%, transparent);
    }
  }

  .dark\:border-green-700:is(.dark *) {
    border-color: var(--color-green-700);
  }

  .dark\:border-green-700\/70:is(.dark *) {
    border-color: #008138b3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-green-700\/70:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-green-700) 70%, transparent);
    }
  }

  .dark\:border-green-800:is(.dark *) {
    border-color: var(--color-green-800);
  }

  .dark\:border-green-800\/30:is(.dark *) {
    border-color: #0166304d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-green-800\/30:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-green-800) 30%, transparent);
    }
  }

  .dark\:border-green-800\/50:is(.dark *) {
    border-color: #01663080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-green-800\/50:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-green-800) 50%, transparent);
    }
  }

  .dark\:border-green-900\/30:is(.dark *) {
    border-color: #0d542b4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-green-900\/30:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-green-900) 30%, transparent);
    }
  }

  .dark\:border-indigo-800:is(.dark *) {
    border-color: var(--color-indigo-800);
  }

  .dark\:border-input:is(.dark *) {
    border-color: var(--input);
  }

  .dark\:border-neutral-400\/50:is(.dark *) {
    border-color: #a1a1a180;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-neutral-400\/50:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-neutral-400) 50%, transparent);
    }
  }

  .dark\:border-neutral-600:is(.dark *) {
    border-color: var(--color-neutral-600);
  }

  .dark\:border-neutral-700:is(.dark *) {
    border-color: var(--color-neutral-700);
  }

  .dark\:border-neutral-700\/30:is(.dark *) {
    border-color: #4040404d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-neutral-700\/30:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-neutral-700) 30%, transparent);
    }
  }

  .dark\:border-neutral-700\/50:is(.dark *) {
    border-color: #40404080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-neutral-700\/50:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-neutral-700) 50%, transparent);
    }
  }

  .dark\:border-neutral-700\/60:is(.dark *) {
    border-color: #40404099;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-neutral-700\/60:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-neutral-700) 60%, transparent);
    }
  }

  .dark\:border-neutral-700\/70:is(.dark *) {
    border-color: #404040b3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-neutral-700\/70:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-neutral-700) 70%, transparent);
    }
  }

  .dark\:border-neutral-700\/80:is(.dark *) {
    border-color: #404040cc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-neutral-700\/80:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-neutral-700) 80%, transparent);
    }
  }

  .dark\:border-neutral-800:is(.dark *) {
    border-color: var(--color-neutral-800);
  }

  .dark\:border-neutral-800\/30:is(.dark *) {
    border-color: #2626264d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-neutral-800\/30:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-neutral-800) 30%, transparent);
    }
  }

  .dark\:border-neutral-800\/50:is(.dark *) {
    border-color: #26262680;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-neutral-800\/50:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-neutral-800) 50%, transparent);
    }
  }

  .dark\:border-neutral-800\/60:is(.dark *) {
    border-color: #26262699;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-neutral-800\/60:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-neutral-800) 60%, transparent);
    }
  }

  .dark\:border-neutral-800\/80:is(.dark *) {
    border-color: #262626cc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-neutral-800\/80:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-neutral-800) 80%, transparent);
    }
  }

  .dark\:border-orange-800:is(.dark *) {
    border-color: var(--color-orange-800);
  }

  .dark\:border-orange-800\/50:is(.dark *) {
    border-color: #9f2d0080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-orange-800\/50:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-orange-800) 50%, transparent);
    }
  }

  .dark\:border-primary\/40:is(.dark *) {
    border-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-primary\/40:is(.dark *) {
      border-color: color-mix(in oklab, var(--primary) 40%, transparent);
    }
  }

  .dark\:border-purple-400\/50:is(.dark *) {
    border-color: #c07eff80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-purple-400\/50:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-purple-400) 50%, transparent);
    }
  }

  .dark\:border-purple-700\/50:is(.dark *) {
    border-color: #8200da80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-purple-700\/50:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-purple-700) 50%, transparent);
    }
  }

  .dark\:border-purple-700\/70:is(.dark *) {
    border-color: #8200dab3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-purple-700\/70:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-purple-700) 70%, transparent);
    }
  }

  .dark\:border-purple-800:is(.dark *) {
    border-color: var(--color-purple-800);
  }

  .dark\:border-purple-800\/30:is(.dark *) {
    border-color: #6e11b04d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-purple-800\/30:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-purple-800) 30%, transparent);
    }
  }

  .dark\:border-red-700\/50:is(.dark *) {
    border-color: #bf000f80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-red-700\/50:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-red-700) 50%, transparent);
    }
  }

  .dark\:border-red-700\/60:is(.dark *) {
    border-color: #bf000f99;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-red-700\/60:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-red-700) 60%, transparent);
    }
  }

  .dark\:border-red-700\/70:is(.dark *) {
    border-color: #bf000fb3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-red-700\/70:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-red-700) 70%, transparent);
    }
  }

  .dark\:border-red-800:is(.dark *) {
    border-color: var(--color-red-800);
  }

  .dark\:border-red-800\/20:is(.dark *) {
    border-color: #9f071233;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-red-800\/20:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-red-800) 20%, transparent);
    }
  }

  .dark\:border-red-800\/30:is(.dark *) {
    border-color: #9f07124d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-red-800\/30:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-red-800) 30%, transparent);
    }
  }

  .dark\:border-red-800\/50:is(.dark *) {
    border-color: #9f071280;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-red-800\/50:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-red-800) 50%, transparent);
    }
  }

  .dark\:border-red-800\/60:is(.dark *) {
    border-color: #9f071299;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-red-800\/60:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-red-800) 60%, transparent);
    }
  }

  .dark\:border-red-900:is(.dark *) {
    border-color: var(--color-red-900);
  }

  .dark\:border-red-900\/30:is(.dark *) {
    border-color: #82181a4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-red-900\/30:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-red-900) 30%, transparent);
    }
  }

  .dark\:border-red-900\/50:is(.dark *) {
    border-color: #82181a80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-red-900\/50:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-red-900) 50%, transparent);
    }
  }

  .dark\:border-teal-800:is(.dark *) {
    border-color: var(--color-teal-800);
  }

  .dark\:border-violet-900\/30:is(.dark *) {
    border-color: #4d179a4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-violet-900\/30:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-violet-900) 30%, transparent);
    }
  }

  .dark\:border-yellow-700\/50:is(.dark *) {
    border-color: #a3610080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-yellow-700\/50:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-yellow-700) 50%, transparent);
    }
  }

  .dark\:border-yellow-700\/70:is(.dark *) {
    border-color: #a36100b3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-yellow-700\/70:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-yellow-700) 70%, transparent);
    }
  }

  .dark\:border-yellow-800:is(.dark *) {
    border-color: var(--color-yellow-800);
  }

  .dark\:border-yellow-800\/50:is(.dark *) {
    border-color: #874b0080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-yellow-800\/50:is(.dark *) {
      border-color: color-mix(in oklab, var(--color-yellow-800) 50%, transparent);
    }
  }

  .dark\:bg-\[\#D4AF37\]\/20:is(.dark *) {
    background-color: oklab(76.6528% -.00256401 .138654 / .2);
  }

  .dark\:bg-\[var\(--brand-gold\)\]:is(.dark *) {
    background-color: var(--brand-gold);
  }

  .dark\:bg-\[var\(--brand-gold\)\]\/3:is(.dark *) {
    background-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-\[var\(--brand-gold\)\]\/3:is(.dark *) {
      background-color: color-mix(in oklab, var(--brand-gold) 3%, transparent);
    }
  }

  .dark\:bg-\[var\(--brand-gold\)\]\/5:is(.dark *) {
    background-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-\[var\(--brand-gold\)\]\/5:is(.dark *) {
      background-color: color-mix(in oklab, var(--brand-gold) 5%, transparent);
    }
  }

  .dark\:bg-\[var\(--brand-gold\)\]\/8:is(.dark *) {
    background-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-\[var\(--brand-gold\)\]\/8:is(.dark *) {
      background-color: color-mix(in oklab, var(--brand-gold) 8%, transparent);
    }
  }

  .dark\:bg-\[var\(--brand-gold\)\]\/10:is(.dark *) {
    background-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-\[var\(--brand-gold\)\]\/10:is(.dark *) {
      background-color: color-mix(in oklab, var(--brand-gold) 10%, transparent);
    }
  }

  .dark\:bg-\[var\(--brand-gold\)\]\/12:is(.dark *) {
    background-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-\[var\(--brand-gold\)\]\/12:is(.dark *) {
      background-color: color-mix(in oklab, var(--brand-gold) 12%, transparent);
    }
  }

  .dark\:bg-\[var\(--brand-gold\)\]\/15:is(.dark *) {
    background-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-\[var\(--brand-gold\)\]\/15:is(.dark *) {
      background-color: color-mix(in oklab, var(--brand-gold) 15%, transparent);
    }
  }

  .dark\:bg-\[var\(--brand-gold\)\]\/20:is(.dark *) {
    background-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-\[var\(--brand-gold\)\]\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--brand-gold) 20%, transparent);
    }
  }

  .dark\:bg-\[var\(--brand-gold\)\]\/25:is(.dark *) {
    background-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-\[var\(--brand-gold\)\]\/25:is(.dark *) {
      background-color: color-mix(in oklab, var(--brand-gold) 25%, transparent);
    }
  }

  .dark\:bg-\[var\(--brand-gold\)\]\/30:is(.dark *) {
    background-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-\[var\(--brand-gold\)\]\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--brand-gold) 30%, transparent);
    }
  }

  .dark\:bg-\[var\(--brand-gold-rgb\)\]\/10:is(.dark *) {
    background-color: var(--brand-gold-rgb);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-\[var\(--brand-gold-rgb\)\]\/10:is(.dark *) {
      background-color: color-mix(in oklab, var(--brand-gold-rgb) 10%, transparent);
    }
  }

  .dark\:bg-\[var\(--theme-color-20\)\]:is(.dark *) {
    background-color: var(--theme-color-20);
  }

  .dark\:bg-amber-400\/10:is(.dark *) {
    background-color: #fcbb001a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-amber-400\/10:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-amber-400) 10%, transparent);
    }
  }

  .dark\:bg-amber-900:is(.dark *) {
    background-color: var(--color-amber-900);
  }

  .dark\:bg-amber-900\/20:is(.dark *) {
    background-color: #7b330633;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-amber-900\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-amber-900) 20%, transparent);
    }
  }

  .dark\:bg-amber-900\/30:is(.dark *) {
    background-color: #7b33064d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-amber-900\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-amber-900) 30%, transparent);
    }
  }

  .dark\:bg-amber-900\/50:is(.dark *) {
    background-color: #7b330680;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-amber-900\/50:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-amber-900) 50%, transparent);
    }
  }

  .dark\:bg-amber-950:is(.dark *) {
    background-color: var(--color-amber-950);
  }

  .dark\:bg-amber-950\/20:is(.dark *) {
    background-color: #46190133;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-amber-950\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-amber-950) 20%, transparent);
    }
  }

  .dark\:bg-amber-950\/30:is(.dark *) {
    background-color: #4619014d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-amber-950\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-amber-950) 30%, transparent);
    }
  }

  .dark\:bg-amber-950\/50:is(.dark *) {
    background-color: #46190180;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-amber-950\/50:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-amber-950) 50%, transparent);
    }
  }

  .dark\:bg-background\/90:is(.dark *) {
    background-color: var(--background);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-background\/90:is(.dark *) {
      background-color: color-mix(in oklab, var(--background) 90%, transparent);
    }
  }

  .dark\:bg-black:is(.dark *) {
    background-color: var(--color-black);
  }

  .dark\:bg-black\/20:is(.dark *) {
    background-color: #0003;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-black\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-black) 20%, transparent);
    }
  }

  .dark\:bg-black\/30:is(.dark *) {
    background-color: #0000004d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-black\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-black) 30%, transparent);
    }
  }

  .dark\:bg-black\/40:is(.dark *) {
    background-color: #0006;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-black\/40:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-black) 40%, transparent);
    }
  }

  .dark\:bg-black\/50:is(.dark *) {
    background-color: #00000080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-black\/50:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }

  .dark\:bg-black\/60:is(.dark *) {
    background-color: #0009;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-black\/60:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-black) 60%, transparent);
    }
  }

  .dark\:bg-black\/70:is(.dark *) {
    background-color: #000000b3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-black\/70:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-black) 70%, transparent);
    }
  }

  .dark\:bg-black\/80:is(.dark *) {
    background-color: #000c;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-black\/80:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-black) 80%, transparent);
    }
  }

  .dark\:bg-black\/90:is(.dark *) {
    background-color: #000000e6;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-black\/90:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-black) 90%, transparent);
    }
  }

  .dark\:bg-blue-400\/10:is(.dark *) {
    background-color: #54a2ff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-blue-400\/10:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-blue-400) 10%, transparent);
    }
  }

  .dark\:bg-blue-500\/5:is(.dark *) {
    background-color: #3080ff0d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-blue-500\/5:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-blue-500) 5%, transparent);
    }
  }

  .dark\:bg-blue-500\/10:is(.dark *) {
    background-color: #3080ff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-blue-500\/10:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-blue-500) 10%, transparent);
    }
  }

  .dark\:bg-blue-500\/30:is(.dark *) {
    background-color: #3080ff4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-blue-500\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-blue-500) 30%, transparent);
    }
  }

  .dark\:bg-blue-600:is(.dark *) {
    background-color: var(--color-blue-600);
  }

  .dark\:bg-blue-600\/10:is(.dark *) {
    background-color: #155dfc1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-blue-600\/10:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-blue-600) 10%, transparent);
    }
  }

  .dark\:bg-blue-900:is(.dark *) {
    background-color: var(--color-blue-900);
  }

  .dark\:bg-blue-900\/5:is(.dark *) {
    background-color: #1c398e0d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-blue-900\/5:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-blue-900) 5%, transparent);
    }
  }

  .dark\:bg-blue-900\/10:is(.dark *) {
    background-color: #1c398e1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-blue-900\/10:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-blue-900) 10%, transparent);
    }
  }

  .dark\:bg-blue-900\/20:is(.dark *) {
    background-color: #1c398e33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-blue-900\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-blue-900) 20%, transparent);
    }
  }

  .dark\:bg-blue-900\/30:is(.dark *) {
    background-color: #1c398e4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-blue-900\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-blue-900) 30%, transparent);
    }
  }

  .dark\:bg-blue-900\/40:is(.dark *) {
    background-color: #1c398e66;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-blue-900\/40:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-blue-900) 40%, transparent);
    }
  }

  .dark\:bg-blue-900\/50:is(.dark *) {
    background-color: #1c398e80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-blue-900\/50:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-blue-900) 50%, transparent);
    }
  }

  .dark\:bg-blue-950:is(.dark *) {
    background-color: var(--color-blue-950);
  }

  .dark\:bg-blue-950\/20:is(.dark *) {
    background-color: #16245633;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-blue-950\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-blue-950) 20%, transparent);
    }
  }

  .dark\:bg-blue-950\/50:is(.dark *) {
    background-color: #16245680;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-blue-950\/50:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-blue-950) 50%, transparent);
    }
  }

  .dark\:bg-destructive\/60:is(.dark *) {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-destructive\/60:is(.dark *) {
      background-color: color-mix(in oklab, var(--destructive) 60%, transparent);
    }
  }

  .dark\:bg-destructive\/70:is(.dark *) {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-destructive\/70:is(.dark *) {
      background-color: color-mix(in oklab, var(--destructive) 70%, transparent);
    }
  }

  .dark\:bg-emerald-900\/20:is(.dark *) {
    background-color: #004e3b33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-emerald-900\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-emerald-900) 20%, transparent);
    }
  }

  .dark\:bg-emerald-900\/30:is(.dark *) {
    background-color: #004e3b4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-emerald-900\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-emerald-900) 30%, transparent);
    }
  }

  .dark\:bg-gray-700:is(.dark *) {
    background-color: var(--color-gray-700);
  }

  .dark\:bg-gray-800:is(.dark *) {
    background-color: var(--color-gray-800);
  }

  .dark\:bg-gray-800\/50:is(.dark *) {
    background-color: #1e293980;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-gray-800\/50:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-gray-800) 50%, transparent);
    }
  }

  .dark\:bg-gray-900:is(.dark *) {
    background-color: var(--color-gray-900);
  }

  .dark\:bg-gray-900\/20:is(.dark *) {
    background-color: #10182833;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-gray-900\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-gray-900) 20%, transparent);
    }
  }

  .dark\:bg-gray-900\/50:is(.dark *) {
    background-color: #10182880;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-gray-900\/50:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-gray-900) 50%, transparent);
    }
  }

  .dark\:bg-green-500\/20:is(.dark *) {
    background-color: #00c75833;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-green-500\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-green-500) 20%, transparent);
    }
  }

  .dark\:bg-green-600:is(.dark *) {
    background-color: var(--color-green-600);
  }

  .dark\:bg-green-900:is(.dark *) {
    background-color: var(--color-green-900);
  }

  .dark\:bg-green-900\/20:is(.dark *) {
    background-color: #0d542b33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-green-900\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-green-900) 20%, transparent);
    }
  }

  .dark\:bg-green-900\/30:is(.dark *) {
    background-color: #0d542b4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-green-900\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-green-900) 30%, transparent);
    }
  }

  .dark\:bg-green-900\/50:is(.dark *) {
    background-color: #0d542b80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-green-900\/50:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-green-900) 50%, transparent);
    }
  }

  .dark\:bg-green-950:is(.dark *) {
    background-color: var(--color-green-950);
  }

  .dark\:bg-green-950\/20:is(.dark *) {
    background-color: #032e1533;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-green-950\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-green-950) 20%, transparent);
    }
  }

  .dark\:bg-indigo-900\/20:is(.dark *) {
    background-color: #312c8533;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-indigo-900\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-indigo-900) 20%, transparent);
    }
  }

  .dark\:bg-indigo-900\/50:is(.dark *) {
    background-color: #312c8580;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-indigo-900\/50:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-indigo-900) 50%, transparent);
    }
  }

  .dark\:bg-input\/30:is(.dark *) {
    background-color: var(--input);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-input\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--input) 30%, transparent);
    }
  }

  .dark\:bg-muted\/10:is(.dark *) {
    background-color: var(--muted);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-muted\/10:is(.dark *) {
      background-color: color-mix(in oklab, var(--muted) 10%, transparent);
    }
  }

  .dark\:bg-muted\/80:is(.dark *) {
    background-color: var(--muted);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-muted\/80:is(.dark *) {
      background-color: color-mix(in oklab, var(--muted) 80%, transparent);
    }
  }

  .dark\:bg-neutral-300\/5:is(.dark *) {
    background-color: #d4d4d40d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-neutral-300\/5:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-neutral-300) 5%, transparent);
    }
  }

  .dark\:bg-neutral-300\/10:is(.dark *) {
    background-color: #d4d4d41a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-neutral-300\/10:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-neutral-300) 10%, transparent);
    }
  }

  .dark\:bg-neutral-500\/20:is(.dark *) {
    background-color: #73737333;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-neutral-500\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-neutral-500) 20%, transparent);
    }
  }

  .dark\:bg-neutral-600:is(.dark *) {
    background-color: var(--color-neutral-600);
  }

  .dark\:bg-neutral-600\/10:is(.dark *) {
    background-color: #5252521a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-neutral-600\/10:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-neutral-600) 10%, transparent);
    }
  }

  .dark\:bg-neutral-700:is(.dark *) {
    background-color: var(--color-neutral-700);
  }

  .dark\:bg-neutral-700\/50:is(.dark *) {
    background-color: #40404080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-neutral-700\/50:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-neutral-700) 50%, transparent);
    }
  }

  .dark\:bg-neutral-800:is(.dark *) {
    background-color: var(--color-neutral-800);
  }

  .dark\:bg-neutral-800\/20:is(.dark *) {
    background-color: #26262633;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-neutral-800\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-neutral-800) 20%, transparent);
    }
  }

  .dark\:bg-neutral-800\/30:is(.dark *) {
    background-color: #2626264d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-neutral-800\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-neutral-800) 30%, transparent);
    }
  }

  .dark\:bg-neutral-800\/40:is(.dark *) {
    background-color: #26262666;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-neutral-800\/40:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-neutral-800) 40%, transparent);
    }
  }

  .dark\:bg-neutral-800\/50:is(.dark *) {
    background-color: #26262680;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-neutral-800\/50:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-neutral-800) 50%, transparent);
    }
  }

  .dark\:bg-neutral-800\/70:is(.dark *) {
    background-color: #262626b3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-neutral-800\/70:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-neutral-800) 70%, transparent);
    }
  }

  .dark\:bg-neutral-800\/80:is(.dark *) {
    background-color: #262626cc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-neutral-800\/80:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-neutral-800) 80%, transparent);
    }
  }

  .dark\:bg-neutral-800\/90:is(.dark *) {
    background-color: #262626e6;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-neutral-800\/90:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-neutral-800) 90%, transparent);
    }
  }

  .dark\:bg-neutral-800\/95:is(.dark *) {
    background-color: #262626f2;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-neutral-800\/95:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-neutral-800) 95%, transparent);
    }
  }

  .dark\:bg-neutral-900:is(.dark *) {
    background-color: var(--color-neutral-900);
  }

  .dark\:bg-neutral-900\/20:is(.dark *) {
    background-color: #17171733;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-neutral-900\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-neutral-900) 20%, transparent);
    }
  }

  .dark\:bg-neutral-900\/50:is(.dark *) {
    background-color: #17171780;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-neutral-900\/50:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-neutral-900) 50%, transparent);
    }
  }

  .dark\:bg-neutral-900\/80:is(.dark *) {
    background-color: #171717cc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-neutral-900\/80:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-neutral-900) 80%, transparent);
    }
  }

  .dark\:bg-neutral-900\/90:is(.dark *) {
    background-color: #171717e6;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-neutral-900\/90:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-neutral-900) 90%, transparent);
    }
  }

  .dark\:bg-neutral-900\/95:is(.dark *) {
    background-color: #171717f2;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-neutral-900\/95:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-neutral-900) 95%, transparent);
    }
  }

  .dark\:bg-neutral-950:is(.dark *) {
    background-color: var(--color-neutral-950);
  }

  .dark\:bg-neutral-950\/80:is(.dark *) {
    background-color: #0a0a0acc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-neutral-950\/80:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-neutral-950) 80%, transparent);
    }
  }

  .dark\:bg-neutral-950\/95:is(.dark *) {
    background-color: #0a0a0af2;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-neutral-950\/95:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-neutral-950) 95%, transparent);
    }
  }

  .dark\:bg-orange-900\/20:is(.dark *) {
    background-color: #7e2a0c33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-orange-900\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-orange-900) 20%, transparent);
    }
  }

  .dark\:bg-orange-900\/30:is(.dark *) {
    background-color: #7e2a0c4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-orange-900\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-orange-900) 30%, transparent);
    }
  }

  .dark\:bg-orange-950\/50:is(.dark *) {
    background-color: #44130680;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-orange-950\/50:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-orange-950) 50%, transparent);
    }
  }

  .dark\:bg-primary\/5:is(.dark *) {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-primary\/5:is(.dark *) {
      background-color: color-mix(in oklab, var(--primary) 5%, transparent);
    }
  }

  .dark\:bg-purple-500\/10:is(.dark *) {
    background-color: #ac4bff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-purple-500\/10:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-purple-500) 10%, transparent);
    }
  }

  .dark\:bg-purple-900\/20:is(.dark *) {
    background-color: #59168b33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-purple-900\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-purple-900) 20%, transparent);
    }
  }

  .dark\:bg-purple-900\/30:is(.dark *) {
    background-color: #59168b4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-purple-900\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-purple-900) 30%, transparent);
    }
  }

  .dark\:bg-purple-900\/50:is(.dark *) {
    background-color: #59168b80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-purple-900\/50:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-purple-900) 50%, transparent);
    }
  }

  .dark\:bg-red-500\/10:is(.dark *) {
    background-color: #fb2c361a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-red-500\/10:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-red-500) 10%, transparent);
    }
  }

  .dark\:bg-red-900:is(.dark *) {
    background-color: var(--color-red-900);
  }

  .dark\:bg-red-900\/10:is(.dark *) {
    background-color: #82181a1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-red-900\/10:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-red-900) 10%, transparent);
    }
  }

  .dark\:bg-red-900\/20:is(.dark *) {
    background-color: #82181a33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-red-900\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-red-900) 20%, transparent);
    }
  }

  .dark\:bg-red-900\/30:is(.dark *) {
    background-color: #82181a4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-red-900\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-red-900) 30%, transparent);
    }
  }

  .dark\:bg-red-900\/50:is(.dark *) {
    background-color: #82181a80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-red-900\/50:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-red-900) 50%, transparent);
    }
  }

  .dark\:bg-red-950\/20:is(.dark *) {
    background-color: #46080933;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-red-950\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-red-950) 20%, transparent);
    }
  }

  .dark\:bg-red-950\/30:is(.dark *) {
    background-color: #4608094d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-red-950\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-red-950) 30%, transparent);
    }
  }

  .dark\:bg-red-950\/50:is(.dark *) {
    background-color: #46080980;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-red-950\/50:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-red-950) 50%, transparent);
    }
  }

  .dark\:bg-rose-900\/20:is(.dark *) {
    background-color: #8b083633;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-rose-900\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-rose-900) 20%, transparent);
    }
  }

  .dark\:bg-rose-900\/30:is(.dark *) {
    background-color: #8b08364d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-rose-900\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-rose-900) 30%, transparent);
    }
  }

  .dark\:bg-teal-900\/30:is(.dark *) {
    background-color: #0b4f4a4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-teal-900\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-teal-900) 30%, transparent);
    }
  }

  .dark\:bg-teal-900\/50:is(.dark *) {
    background-color: #0b4f4a80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-teal-900\/50:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-teal-900) 50%, transparent);
    }
  }

  .dark\:bg-transparent:is(.dark *) {
    background-color: #0000;
  }

  .dark\:bg-violet-900\/60:is(.dark *) {
    background-color: #4d179a99;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-violet-900\/60:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-violet-900) 60%, transparent);
    }
  }

  .dark\:bg-white\/3:is(.dark *) {
    background-color: #ffffff08;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-white\/3:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-white) 3%, transparent);
    }
  }

  .dark\:bg-white\/5:is(.dark *) {
    background-color: #ffffff0d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-white\/5:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-white) 5%, transparent);
    }
  }

  .dark\:bg-white\/10:is(.dark *) {
    background-color: #ffffff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-white\/10:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .dark\:bg-white\/90:is(.dark *) {
    background-color: #ffffffe6;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-white\/90:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-white) 90%, transparent);
    }
  }

  .dark\:bg-yellow-500\/10:is(.dark *) {
    background-color: #edb2001a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-yellow-500\/10:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-yellow-500) 10%, transparent);
    }
  }

  .dark\:bg-yellow-500\/30:is(.dark *) {
    background-color: #edb2004d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-yellow-500\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-yellow-500) 30%, transparent);
    }
  }

  .dark\:bg-yellow-900\/20:is(.dark *) {
    background-color: #733e0a33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-yellow-900\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-yellow-900) 20%, transparent);
    }
  }

  .dark\:bg-yellow-900\/30:is(.dark *) {
    background-color: #733e0a4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-yellow-900\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-yellow-900) 30%, transparent);
    }
  }

  .dark\:bg-yellow-900\/50:is(.dark *) {
    background-color: #733e0a80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-yellow-900\/50:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-yellow-900) 50%, transparent);
    }
  }

  .dark\:bg-yellow-950:is(.dark *) {
    background-color: var(--color-yellow-950);
  }

  .dark\:bg-yellow-950\/20:is(.dark *) {
    background-color: #43200433;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-yellow-950\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-yellow-950) 20%, transparent);
    }
  }

  .dark\:bg-yellow-950\/50:is(.dark *) {
    background-color: #43200480;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-yellow-950\/50:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-yellow-950) 50%, transparent);
    }
  }

  .dark\:bg-gradient-to-b:is(.dark *) {
    --tw-gradient-position: to bottom in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .dark\:bg-gradient-to-br:is(.dark *) {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .dark\:from-\[\#1a1a1a\]:is(.dark *) {
    --tw-gradient-from: #1a1a1a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:from-\[var\(--brand-gold\)\]\/0:is(.dark *) {
    --tw-gradient-from: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-\[var\(--brand-gold\)\]\/0:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--brand-gold) 0%, transparent);
    }
  }

  .dark\:from-\[var\(--brand-gold\)\]\/10:is(.dark *) {
    --tw-gradient-from: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-\[var\(--brand-gold\)\]\/10:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--brand-gold) 10%, transparent);
    }
  }

  .dark\:from-\[var\(--brand-gold\)\]\/15:is(.dark *) {
    --tw-gradient-from: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-\[var\(--brand-gold\)\]\/15:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--brand-gold) 15%, transparent);
    }
  }

  .dark\:from-\[var\(--brand-gold\)\]\/20:is(.dark *) {
    --tw-gradient-from: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-\[var\(--brand-gold\)\]\/20:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--brand-gold) 20%, transparent);
    }
  }

  .dark\:from-\[var\(--brand-gold\)\]\/30:is(.dark *) {
    --tw-gradient-from: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-\[var\(--brand-gold\)\]\/30:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--brand-gold) 30%, transparent);
    }
  }

  .dark\:from-\[var\(--theme-color-20\)\]:is(.dark *) {
    --tw-gradient-from: var(--theme-color-20);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:from-amber-900\/40:is(.dark *) {
    --tw-gradient-from: #7b330666;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-amber-900\/40:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-amber-900) 40%, transparent);
    }
  }

  .dark\:from-amber-950\/40:is(.dark *) {
    --tw-gradient-from: #46190166;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-amber-950\/40:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-amber-950) 40%, transparent);
    }
  }

  .dark\:from-background:is(.dark *) {
    --tw-gradient-from: var(--background);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:from-black\/40:is(.dark *) {
    --tw-gradient-from: #0006;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-black\/40:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-black) 40%, transparent);
    }
  }

  .dark\:from-black\/70:is(.dark *) {
    --tw-gradient-from: #000000b3;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-black\/70:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-black) 70%, transparent);
    }
  }

  .dark\:from-black\/90:is(.dark *) {
    --tw-gradient-from: #000000e6;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-black\/90:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-black) 90%, transparent);
    }
  }

  .dark\:from-blue-500\/10:is(.dark *) {
    --tw-gradient-from: #3080ff1a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-blue-500\/10:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-500) 10%, transparent);
    }
  }

  .dark\:from-blue-500\/20:is(.dark *) {
    --tw-gradient-from: #3080ff33;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-blue-500\/20:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-500) 20%, transparent);
    }
  }

  .dark\:from-blue-500\/30:is(.dark *) {
    --tw-gradient-from: #3080ff4d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-blue-500\/30:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-500) 30%, transparent);
    }
  }

  .dark\:from-blue-800:is(.dark *) {
    --tw-gradient-from: var(--color-blue-800);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:from-blue-900:is(.dark *) {
    --tw-gradient-from: var(--color-blue-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:from-blue-900\/30:is(.dark *) {
    --tw-gradient-from: #1c398e4d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-blue-900\/30:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-900) 30%, transparent);
    }
  }

  .dark\:from-blue-900\/40:is(.dark *) {
    --tw-gradient-from: #1c398e66;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-blue-900\/40:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-900) 40%, transparent);
    }
  }

  .dark\:from-blue-950\/30:is(.dark *) {
    --tw-gradient-from: #1624564d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-blue-950\/30:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-950) 30%, transparent);
    }
  }

  .dark\:from-blue-950\/40:is(.dark *) {
    --tw-gradient-from: #16245666;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-blue-950\/40:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-950) 40%, transparent);
    }
  }

  .dark\:from-gray-800\/40:is(.dark *) {
    --tw-gradient-from: #1e293966;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-gray-800\/40:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-gray-800) 40%, transparent);
    }
  }

  .dark\:from-gray-900:is(.dark *) {
    --tw-gradient-from: var(--color-gray-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:from-green-900\/40:is(.dark *) {
    --tw-gradient-from: #0d542b66;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-green-900\/40:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-green-900) 40%, transparent);
    }
  }

  .dark\:from-neutral-100:is(.dark *) {
    --tw-gradient-from: var(--color-neutral-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:from-neutral-700:is(.dark *) {
    --tw-gradient-from: var(--color-neutral-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:from-neutral-800:is(.dark *) {
    --tw-gradient-from: var(--color-neutral-800);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:from-neutral-800\/5:is(.dark *) {
    --tw-gradient-from: #2626260d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-neutral-800\/5:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-neutral-800) 5%, transparent);
    }
  }

  .dark\:from-neutral-800\/50:is(.dark *) {
    --tw-gradient-from: #26262680;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-neutral-800\/50:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-neutral-800) 50%, transparent);
    }
  }

  .dark\:from-neutral-800\/90:is(.dark *) {
    --tw-gradient-from: #262626e6;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-neutral-800\/90:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-neutral-800) 90%, transparent);
    }
  }

  .dark\:from-neutral-900:is(.dark *) {
    --tw-gradient-from: var(--color-neutral-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:from-neutral-900\/5:is(.dark *) {
    --tw-gradient-from: #1717170d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-neutral-900\/5:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-neutral-900) 5%, transparent);
    }
  }

  .dark\:from-neutral-900\/50:is(.dark *) {
    --tw-gradient-from: #17171780;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-neutral-900\/50:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-neutral-900) 50%, transparent);
    }
  }

  .dark\:from-neutral-900\/80:is(.dark *) {
    --tw-gradient-from: #171717cc;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-neutral-900\/80:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-neutral-900) 80%, transparent);
    }
  }

  .dark\:from-primary\/10:is(.dark *) {
    --tw-gradient-from: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-primary\/10:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--primary) 10%, transparent);
    }
  }

  .dark\:from-primary\/20:is(.dark *) {
    --tw-gradient-from: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-primary\/20:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--primary) 20%, transparent);
    }
  }

  .dark\:from-purple-500\/10:is(.dark *) {
    --tw-gradient-from: #ac4bff1a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-purple-500\/10:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-purple-500) 10%, transparent);
    }
  }

  .dark\:from-purple-900\/30:is(.dark *) {
    --tw-gradient-from: #59168b4d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-purple-900\/30:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-purple-900) 30%, transparent);
    }
  }

  .dark\:from-purple-900\/40:is(.dark *) {
    --tw-gradient-from: #59168b66;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-purple-900\/40:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-purple-900) 40%, transparent);
    }
  }

  .dark\:from-red-900\/40:is(.dark *) {
    --tw-gradient-from: #82181a66;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-red-900\/40:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-red-900) 40%, transparent);
    }
  }

  .dark\:from-red-900\/50:is(.dark *) {
    --tw-gradient-from: #82181a80;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-red-900\/50:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-red-900) 50%, transparent);
    }
  }

  .dark\:from-red-950\/80:is(.dark *) {
    --tw-gradient-from: #460809cc;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-red-950\/80:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-red-950) 80%, transparent);
    }
  }

  .dark\:from-transparent:is(.dark *) {
    --tw-gradient-from: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:from-violet-950\/30:is(.dark *) {
    --tw-gradient-from: #2f0d684d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-violet-950\/30:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-violet-950) 30%, transparent);
    }
  }

  .dark\:from-white\/8:is(.dark *) {
    --tw-gradient-from: #ffffff14;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-white\/8:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-white) 8%, transparent);
    }
  }

  .dark\:from-yellow-800\/80:is(.dark *) {
    --tw-gradient-from: #874b00cc;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-yellow-800\/80:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-yellow-800) 80%, transparent);
    }
  }

  .dark\:from-yellow-900\/40:is(.dark *) {
    --tw-gradient-from: #733e0a66;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-yellow-900\/40:is(.dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-yellow-900) 40%, transparent);
    }
  }

  .dark\:via-\[var\(--brand-gold\)\]\/10:is(.dark *) {
    --tw-gradient-via: var(--brand-gold);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:via-\[var\(--brand-gold\)\]\/10:is(.dark *) {
      --tw-gradient-via: color-mix(in oklab, var(--brand-gold) 10%, transparent);
    }
  }

  .dark\:via-neutral-600:is(.dark *) {
    --tw-gradient-via: var(--color-neutral-600);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .dark\:via-neutral-700:is(.dark *) {
    --tw-gradient-via: var(--color-neutral-700);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .dark\:via-white\/5:is(.dark *) {
    --tw-gradient-via: #ffffff0d;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:via-white\/5:is(.dark *) {
      --tw-gradient-via: color-mix(in oklab, var(--color-white) 5%, transparent);
    }
  }

  .dark\:via-white\/20:is(.dark *) {
    --tw-gradient-via: #fff3;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:via-white\/20:is(.dark *) {
      --tw-gradient-via: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  .dark\:to-\[\#0d0d0d\]:is(.dark *) {
    --tw-gradient-to: #0d0d0d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:to-\[var\(--brand-gold\)\]\/0:is(.dark *) {
    --tw-gradient-to: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-\[var\(--brand-gold\)\]\/0:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--brand-gold) 0%, transparent);
    }
  }

  .dark\:to-\[var\(--brand-gold\)\]\/10:is(.dark *) {
    --tw-gradient-to: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-\[var\(--brand-gold\)\]\/10:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--brand-gold) 10%, transparent);
    }
  }

  .dark\:to-\[var\(--brand-gold\)\]\/25:is(.dark *) {
    --tw-gradient-to: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-\[var\(--brand-gold\)\]\/25:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--brand-gold) 25%, transparent);
    }
  }

  .dark\:to-\[var\(--brand-gold\)\]\/30:is(.dark *) {
    --tw-gradient-to: var(--brand-gold);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-\[var\(--brand-gold\)\]\/30:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--brand-gold) 30%, transparent);
    }
  }

  .dark\:to-\[var\(--theme-color-10\)\]:is(.dark *) {
    --tw-gradient-to: var(--theme-color-10);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:to-amber-500\/10:is(.dark *) {
    --tw-gradient-to: #f99c001a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-amber-500\/10:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-amber-500) 10%, transparent);
    }
  }

  .dark\:to-amber-500\/20:is(.dark *) {
    --tw-gradient-to: #f99c0033;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-amber-500\/20:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-amber-500) 20%, transparent);
    }
  }

  .dark\:to-amber-900\/20:is(.dark *) {
    --tw-gradient-to: #7b330633;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-amber-900\/20:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-amber-900) 20%, transparent);
    }
  }

  .dark\:to-amber-900\/30:is(.dark *) {
    --tw-gradient-to: #7b33064d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-amber-900\/30:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-amber-900) 30%, transparent);
    }
  }

  .dark\:to-amber-900\/80:is(.dark *) {
    --tw-gradient-to: #7b3306cc;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-amber-900\/80:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-amber-900) 80%, transparent);
    }
  }

  .dark\:to-amber-950\/20:is(.dark *) {
    --tw-gradient-to: #46190133;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-amber-950\/20:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-amber-950) 20%, transparent);
    }
  }

  .dark\:to-black:is(.dark *) {
    --tw-gradient-to: var(--color-black);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:to-black\/15:is(.dark *) {
    --tw-gradient-to: #00000026;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-black\/15:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-black) 15%, transparent);
    }
  }

  .dark\:to-black\/20:is(.dark *) {
    --tw-gradient-to: #0003;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-black\/20:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-black) 20%, transparent);
    }
  }

  .dark\:to-black\/30:is(.dark *) {
    --tw-gradient-to: #0000004d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-black\/30:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-black) 30%, transparent);
    }
  }

  .dark\:to-black\/50:is(.dark *) {
    --tw-gradient-to: #00000080;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-black\/50:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }

  .dark\:to-black\/70:is(.dark *) {
    --tw-gradient-to: #000000b3;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-black\/70:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-black) 70%, transparent);
    }
  }

  .dark\:to-blue-500\/10:is(.dark *) {
    --tw-gradient-to: #3080ff1a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-blue-500\/10:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-blue-500) 10%, transparent);
    }
  }

  .dark\:to-blue-800:is(.dark *) {
    --tw-gradient-to: var(--color-blue-800);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:to-blue-900:is(.dark *) {
    --tw-gradient-to: var(--color-blue-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:to-blue-900\/10:is(.dark *) {
    --tw-gradient-to: #1c398e1a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-blue-900\/10:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-blue-900) 10%, transparent);
    }
  }

  .dark\:to-blue-900\/20:is(.dark *) {
    --tw-gradient-to: #1c398e33;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-blue-900\/20:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-blue-900) 20%, transparent);
    }
  }

  .dark\:to-blue-900\/30:is(.dark *) {
    --tw-gradient-to: #1c398e4d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-blue-900\/30:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-blue-900) 30%, transparent);
    }
  }

  .dark\:to-gray-800:is(.dark *) {
    --tw-gradient-to: var(--color-gray-800);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:to-gray-800\/20:is(.dark *) {
    --tw-gradient-to: #1e293933;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-gray-800\/20:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-gray-800) 20%, transparent);
    }
  }

  .dark\:to-green-900\/20:is(.dark *) {
    --tw-gradient-to: #0d542b33;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-green-900\/20:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-green-900) 20%, transparent);
    }
  }

  .dark\:to-neutral-400:is(.dark *) {
    --tw-gradient-to: var(--color-neutral-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:to-neutral-700:is(.dark *) {
    --tw-gradient-to: var(--color-neutral-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:to-neutral-800:is(.dark *) {
    --tw-gradient-to: var(--color-neutral-800);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:to-neutral-800\/30:is(.dark *) {
    --tw-gradient-to: #2626264d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-neutral-800\/30:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-neutral-800) 30%, transparent);
    }
  }

  .dark\:to-neutral-800\/50:is(.dark *) {
    --tw-gradient-to: #26262680;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-neutral-800\/50:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-neutral-800) 50%, transparent);
    }
  }

  .dark\:to-neutral-900:is(.dark *) {
    --tw-gradient-to: var(--color-neutral-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:to-neutral-900\/20:is(.dark *) {
    --tw-gradient-to: #17171733;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-neutral-900\/20:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-neutral-900) 20%, transparent);
    }
  }

  .dark\:to-neutral-900\/30:is(.dark *) {
    --tw-gradient-to: #1717174d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-neutral-900\/30:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-neutral-900) 30%, transparent);
    }
  }

  .dark\:to-neutral-900\/40:is(.dark *) {
    --tw-gradient-to: #17171766;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-neutral-900\/40:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-neutral-900) 40%, transparent);
    }
  }

  .dark\:to-neutral-900\/50:is(.dark *) {
    --tw-gradient-to: #17171780;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-neutral-900\/50:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-neutral-900) 50%, transparent);
    }
  }

  .dark\:to-neutral-900\/90:is(.dark *) {
    --tw-gradient-to: #171717e6;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-neutral-900\/90:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-neutral-900) 90%, transparent);
    }
  }

  .dark\:to-neutral-950:is(.dark *) {
    --tw-gradient-to: var(--color-neutral-950);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:to-pink-900\/30:is(.dark *) {
    --tw-gradient-to: #8610434d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-pink-900\/30:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-pink-900) 30%, transparent);
    }
  }

  .dark\:to-primary\/5:is(.dark *) {
    --tw-gradient-to: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-primary\/5:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--primary) 5%, transparent);
    }
  }

  .dark\:to-primary\/30:is(.dark *) {
    --tw-gradient-to: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-primary\/30:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--primary) 30%, transparent);
    }
  }

  .dark\:to-purple-500\/10:is(.dark *) {
    --tw-gradient-to: #ac4bff1a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-purple-500\/10:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-purple-500) 10%, transparent);
    }
  }

  .dark\:to-purple-500\/20:is(.dark *) {
    --tw-gradient-to: #ac4bff33;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-purple-500\/20:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-purple-500) 20%, transparent);
    }
  }

  .dark\:to-purple-900\/20:is(.dark *) {
    --tw-gradient-to: #59168b33;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-purple-900\/20:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-purple-900) 20%, transparent);
    }
  }

  .dark\:to-purple-950\/20:is(.dark *) {
    --tw-gradient-to: #3c036633;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-purple-950\/20:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-purple-950) 20%, transparent);
    }
  }

  .dark\:to-purple-950\/30:is(.dark *) {
    --tw-gradient-to: #3c03664d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-purple-950\/30:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-purple-950) 30%, transparent);
    }
  }

  .dark\:to-red-900\/20:is(.dark *) {
    --tw-gradient-to: #82181a33;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-red-900\/20:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-red-900) 20%, transparent);
    }
  }

  .dark\:to-red-950\/40:is(.dark *) {
    --tw-gradient-to: #46080966;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-red-950\/40:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-red-950) 40%, transparent);
    }
  }

  .dark\:to-red-950\/50:is(.dark *) {
    --tw-gradient-to: #46080980;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-red-950\/50:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-red-950) 50%, transparent);
    }
  }

  .dark\:to-transparent:is(.dark *) {
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:to-white\/8:is(.dark *) {
    --tw-gradient-to: #ffffff14;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-white\/8:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-white) 8%, transparent);
    }
  }

  .dark\:to-yellow-900\/20:is(.dark *) {
    --tw-gradient-to: #733e0a33;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-yellow-900\/20:is(.dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-yellow-900) 20%, transparent);
    }
  }

  .dark\:text-\[var\(--brand-gold\)\]:is(.dark *) {
    color: var(--brand-gold);
  }

  .dark\:text-\[var\(--brand-gold\)\]\/5:is(.dark *) {
    color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:text-\[var\(--brand-gold\)\]\/5:is(.dark *) {
      color: color-mix(in oklab, var(--brand-gold) 5%, transparent);
    }
  }

  .dark\:text-\[var\(--brand-gold\)\]\/30:is(.dark *) {
    color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:text-\[var\(--brand-gold\)\]\/30:is(.dark *) {
      color: color-mix(in oklab, var(--brand-gold) 30%, transparent);
    }
  }

  .dark\:text-amber-200:is(.dark *) {
    color: var(--color-amber-200);
  }

  .dark\:text-amber-300:is(.dark *) {
    color: var(--color-amber-300);
  }

  .dark\:text-amber-400:is(.dark *) {
    color: var(--color-amber-400);
  }

  .dark\:text-amber-500:is(.dark *) {
    color: var(--color-amber-500);
  }

  .dark\:text-black:is(.dark *) {
    color: var(--color-black);
  }

  .dark\:text-blue-100:is(.dark *) {
    color: var(--color-blue-100);
  }

  .dark\:text-blue-200:is(.dark *) {
    color: var(--color-blue-200);
  }

  .dark\:text-blue-300:is(.dark *) {
    color: var(--color-blue-300);
  }

  .dark\:text-blue-400:is(.dark *) {
    color: var(--color-blue-400);
  }

  .dark\:text-blue-400\/80:is(.dark *) {
    color: #54a2ffcc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:text-blue-400\/80:is(.dark *) {
      color: color-mix(in oklab, var(--color-blue-400) 80%, transparent);
    }
  }

  .dark\:text-blue-500:is(.dark *) {
    color: var(--color-blue-500);
  }

  .dark\:text-blue-500\/30:is(.dark *) {
    color: #3080ff4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:text-blue-500\/30:is(.dark *) {
      color: color-mix(in oklab, var(--color-blue-500) 30%, transparent);
    }
  }

  .dark\:text-emerald-400:is(.dark *) {
    color: var(--color-emerald-400);
  }

  .dark\:text-gray-100:is(.dark *) {
    color: var(--color-gray-100);
  }

  .dark\:text-gray-300:is(.dark *) {
    color: var(--color-gray-300);
  }

  .dark\:text-gray-400:is(.dark *) {
    color: var(--color-gray-400);
  }

  .dark\:text-gray-600:is(.dark *) {
    color: var(--color-gray-600);
  }

  .dark\:text-green-100:is(.dark *) {
    color: var(--color-green-100);
  }

  .dark\:text-green-200:is(.dark *) {
    color: var(--color-green-200);
  }

  .dark\:text-green-300:is(.dark *) {
    color: var(--color-green-300);
  }

  .dark\:text-green-400:is(.dark *) {
    color: var(--color-green-400);
  }

  .dark\:text-indigo-400:is(.dark *) {
    color: var(--color-indigo-400);
  }

  .dark\:text-muted-foreground:is(.dark *) {
    color: var(--muted-foreground);
  }

  .dark\:text-neutral-50:is(.dark *) {
    color: var(--color-neutral-50);
  }

  .dark\:text-neutral-100:is(.dark *) {
    color: var(--color-neutral-100);
  }

  .dark\:text-neutral-200:is(.dark *) {
    color: var(--color-neutral-200);
  }

  .dark\:text-neutral-300:is(.dark *) {
    color: var(--color-neutral-300);
  }

  .dark\:text-neutral-400:is(.dark *) {
    color: var(--color-neutral-400);
  }

  .dark\:text-neutral-500:is(.dark *) {
    color: var(--color-neutral-500);
  }

  .dark\:text-neutral-600:is(.dark *) {
    color: var(--color-neutral-600);
  }

  .dark\:text-neutral-700:is(.dark *) {
    color: var(--color-neutral-700);
  }

  .dark\:text-neutral-900:is(.dark *) {
    color: var(--color-neutral-900);
  }

  .dark\:text-orange-300:is(.dark *) {
    color: var(--color-orange-300);
  }

  .dark\:text-orange-400:is(.dark *) {
    color: var(--color-orange-400);
  }

  .dark\:text-primary:is(.dark *) {
    color: var(--primary);
  }

  .dark\:text-purple-300:is(.dark *) {
    color: var(--color-purple-300);
  }

  .dark\:text-purple-400:is(.dark *) {
    color: var(--color-purple-400);
  }

  .dark\:text-purple-400\/80:is(.dark *) {
    color: #c07effcc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:text-purple-400\/80:is(.dark *) {
      color: color-mix(in oklab, var(--color-purple-400) 80%, transparent);
    }
  }

  .dark\:text-purple-900:is(.dark *) {
    color: var(--color-purple-900);
  }

  .dark\:text-red-100:is(.dark *) {
    color: var(--color-red-100);
  }

  .dark\:text-red-200:is(.dark *) {
    color: var(--color-red-200);
  }

  .dark\:text-red-300:is(.dark *) {
    color: var(--color-red-300);
  }

  .dark\:text-red-400:is(.dark *) {
    color: var(--color-red-400);
  }

  .dark\:text-red-400\/80:is(.dark *) {
    color: #ff6568cc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:text-red-400\/80:is(.dark *) {
      color: color-mix(in oklab, var(--color-red-400) 80%, transparent);
    }
  }

  .dark\:text-red-500:is(.dark *) {
    color: var(--color-red-500);
  }

  .dark\:text-rose-300:is(.dark *) {
    color: var(--color-rose-300);
  }

  .dark\:text-rose-400:is(.dark *) {
    color: var(--color-rose-400);
  }

  .dark\:text-teal-300:is(.dark *) {
    color: var(--color-teal-300);
  }

  .dark\:text-teal-400:is(.dark *) {
    color: var(--color-teal-400);
  }

  .dark\:text-violet-300:is(.dark *) {
    color: var(--color-violet-300);
  }

  .dark\:text-violet-400:is(.dark *) {
    color: var(--color-violet-400);
  }

  .dark\:text-white:is(.dark *) {
    color: var(--color-white);
  }

  .dark\:text-white\/40:is(.dark *) {
    color: #fff6;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:text-white\/40:is(.dark *) {
      color: color-mix(in oklab, var(--color-white) 40%, transparent);
    }
  }

  .dark\:text-yellow-200:is(.dark *) {
    color: var(--color-yellow-200);
  }

  .dark\:text-yellow-300:is(.dark *) {
    color: var(--color-yellow-300);
  }

  .dark\:text-yellow-400:is(.dark *) {
    color: var(--color-yellow-400);
  }

  .dark\:placeholder-gray-400:is(.dark *)::placeholder {
    color: var(--color-gray-400);
  }

  .dark\:opacity-5:is(.dark *) {
    opacity: .05;
  }

  .dark\:opacity-10:is(.dark *) {
    opacity: .1;
  }

  .dark\:opacity-15:is(.dark *) {
    opacity: .15;
  }

  .dark\:opacity-20:is(.dark *) {
    opacity: .2;
  }

  .dark\:opacity-30:is(.dark *) {
    opacity: .3;
  }

  .dark\:opacity-\[0\.03\]:is(.dark *) {
    opacity: .03;
  }

  .dark\:opacity-\[var\(--dark-opacity\)\]:is(.dark *) {
    opacity: var(--dark-opacity);
  }

  .dark\:shadow-\[var\(--brand-gold\)\]\/10:is(.dark *) {
    --tw-shadow-alpha: 10%;
    --tw-shadow: var(--brand-gold);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .dark\:shadow-\[var\(--brand-gold\)\]\/15:is(.dark *) {
    --tw-shadow-alpha: 15%;
    --tw-shadow: var(--brand-gold);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .dark\:shadow-2xl:is(.dark *) {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, #00000040);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .dark\:shadow-\[0_15px_60px_rgba\(0\,0\,0\,0\.5\)\,0_5px_20px_var\(--theme-color-30\)\]:is(.dark *) {
    --tw-shadow: 0 15px 60px var(--tw-shadow-color, #00000080), 0 5px 20px var(--tw-shadow-color, var(--theme-color-30));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .dark\:shadow-\[inset_0_0_20px_rgba\(255\,255\,255\,0\.03\)\,inset_0_0_5px_var\(--theme-color-30\)\]:is(.dark *) {
    --tw-shadow: inset 0 0 20px var(--tw-shadow-color, #ffffff08), inset 0 0 5px var(--tw-shadow-color, var(--theme-color-30));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .dark\:shadow-amber-900\/20:is(.dark *) {
    --tw-shadow-color: #7b330633;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:shadow-amber-900\/20:is(.dark *) {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-amber-900) 20%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .dark\:shadow-blue-900\/20:is(.dark *) {
    --tw-shadow-color: #1c398e33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:shadow-blue-900\/20:is(.dark *) {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-blue-900) 20%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .dark\:shadow-neutral-900\/20:is(.dark *) {
    --tw-shadow-color: #17171733;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:shadow-neutral-900\/20:is(.dark *) {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-neutral-900) 20%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .dark\:shadow-purple-500\/15:is(.dark *) {
    --tw-shadow-color: #ac4bff26;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:shadow-purple-500\/15:is(.dark *) {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-purple-500) 15%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .dark\:ring-amber-600:is(.dark *) {
    --tw-ring-color: var(--color-amber-600);
  }

  .dark\:ring-white\/10:is(.dark *) {
    --tw-ring-color: #ffffff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:ring-white\/10:is(.dark *) {
      --tw-ring-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .dark\:ring-offset-neutral-900:is(.dark *) {
    --tw-ring-offset-color: var(--color-neutral-900);
  }

  .dark\:blur-2xl:is(.dark *) {
    --tw-blur: blur(var(--blur-2xl));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  @media (hover: hover) {
    .dark\:group-hover\:bg-\[var\(--brand-gold\)\]\/30:is(.dark *):is(:where(.group):hover *) {
      background-color: var(--brand-gold);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:group-hover\:bg-\[var\(--brand-gold\)\]\/30:is(.dark *):is(:where(.group):hover *) {
        background-color: color-mix(in oklab, var(--brand-gold) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:group-hover\:bg-amber-900\/30:is(.dark *):is(:where(.group):hover *) {
      background-color: #7b33064d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:group-hover\:bg-amber-900\/30:is(.dark *):is(:where(.group):hover *) {
        background-color: color-mix(in oklab, var(--color-amber-900) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:group-hover\:bg-blue-900\/30:is(.dark *):is(:where(.group):hover *) {
      background-color: #1c398e4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:group-hover\:bg-blue-900\/30:is(.dark *):is(:where(.group):hover *) {
        background-color: color-mix(in oklab, var(--color-blue-900) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:group-hover\:bg-emerald-900\/30:is(.dark *):is(:where(.group):hover *) {
      background-color: #004e3b4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:group-hover\:bg-emerald-900\/30:is(.dark *):is(:where(.group):hover *) {
        background-color: color-mix(in oklab, var(--color-emerald-900) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:group-hover\:bg-red-900\/30:is(.dark *):is(:where(.group):hover *) {
      background-color: #82181a4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:group-hover\:bg-red-900\/30:is(.dark *):is(:where(.group):hover *) {
        background-color: color-mix(in oklab, var(--color-red-900) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:group-hover\:bg-rose-900\/30:is(.dark *):is(:where(.group):hover *) {
      background-color: #8b08364d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:group-hover\:bg-rose-900\/30:is(.dark *):is(:where(.group):hover *) {
        background-color: color-mix(in oklab, var(--color-rose-900) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:group-hover\:bg-yellow-900\/30:is(.dark *):is(:where(.group):hover *) {
      background-color: #733e0a4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:group-hover\:bg-yellow-900\/30:is(.dark *):is(:where(.group):hover *) {
        background-color: color-mix(in oklab, var(--color-yellow-900) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:group-hover\:text-blue-400:is(.dark *):is(:where(.group):hover *) {
      color: var(--color-blue-400);
    }
  }

  .dark\:placeholder\:text-neutral-500:is(.dark *)::placeholder {
    color: var(--color-neutral-500);
  }

  .dark\:after\:bg-\[var\(--brand-gold\)\]\/10:is(.dark *):after {
    content: var(--tw-content);
    background-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:after\:bg-\[var\(--brand-gold\)\]\/10:is(.dark *):after {
      background-color: color-mix(in oklab, var(--brand-gold) 10%, transparent);
    }
  }

  .dark\:after\:bg-amber-400\/10:is(.dark *):after {
    content: var(--tw-content);
    background-color: #fcbb001a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:after\:bg-amber-400\/10:is(.dark *):after {
      background-color: color-mix(in oklab, var(--color-amber-400) 10%, transparent);
    }
  }

  .dark\:after\:bg-blue-400\/10:is(.dark *):after {
    content: var(--tw-content);
    background-color: #54a2ff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:after\:bg-blue-400\/10:is(.dark *):after {
      background-color: color-mix(in oklab, var(--color-blue-400) 10%, transparent);
    }
  }

  .dark\:after\:bg-neutral-800:is(.dark *):after {
    content: var(--tw-content);
    background-color: var(--color-neutral-800);
  }

  @media (hover: hover) {
    .dark\:hover\:border-\[\#D4AF37\]\/50:is(.dark *):hover {
      border-color: oklab(76.6528% -.00256401 .138654 / .5);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:border-\[var\(--brand-gold\)\]:is(.dark *):hover {
      border-color: var(--brand-gold);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:border-\[var\(--brand-gold\)\]\/30:is(.dark *):hover {
      border-color: var(--brand-gold);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:border-\[var\(--brand-gold\)\]\/30:is(.dark *):hover {
        border-color: color-mix(in oklab, var(--brand-gold) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:border-\[var\(--brand-gold\)\]\/50:is(.dark *):hover {
      border-color: var(--brand-gold);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:border-\[var\(--brand-gold\)\]\/50:is(.dark *):hover {
        border-color: color-mix(in oklab, var(--brand-gold) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:border-amber-500:is(.dark *):hover {
      border-color: var(--color-amber-500);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:border-gray-600:is(.dark *):hover {
      border-color: var(--color-gray-600);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:border-neutral-500:is(.dark *):hover {
      border-color: var(--color-neutral-500);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:border-neutral-600:is(.dark *):hover {
      border-color: var(--color-neutral-600);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:border-neutral-700\/60:is(.dark *):hover {
      border-color: #40404099;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:border-neutral-700\/60:is(.dark *):hover {
        border-color: color-mix(in oklab, var(--color-neutral-700) 60%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:border-primary:is(.dark *):hover {
      border-color: var(--primary);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:border-red-800:is(.dark *):hover {
      border-color: var(--color-red-800);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:border-red-900\/50:is(.dark *):hover {
      border-color: #82181a80;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:border-red-900\/50:is(.dark *):hover {
        border-color: color-mix(in oklab, var(--color-red-900) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-\[var\(--brand-gold\)\]\/5:is(.dark *):hover {
      background-color: var(--brand-gold);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-\[var\(--brand-gold\)\]\/5:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--brand-gold) 5%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-\[var\(--brand-gold\)\]\/10:is(.dark *):hover {
      background-color: var(--brand-gold);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-\[var\(--brand-gold\)\]\/10:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--brand-gold) 10%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-\[var\(--brand-gold\)\]\/20:is(.dark *):hover {
      background-color: var(--brand-gold);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-\[var\(--brand-gold\)\]\/20:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--brand-gold) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-\[var\(--brand-gold\)\]\/90:is(.dark *):hover {
      background-color: var(--brand-gold);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-\[var\(--brand-gold\)\]\/90:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--brand-gold) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-accent\/50:is(.dark *):hover {
      background-color: var(--accent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-accent\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--accent) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-amber-900\/20:is(.dark *):hover {
      background-color: #7b330633;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-amber-900\/20:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--color-amber-900) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-blue-700:is(.dark *):hover {
      background-color: var(--color-blue-700);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-blue-900\/10:is(.dark *):hover {
      background-color: #1c398e1a;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-blue-900\/10:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--color-blue-900) 10%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-blue-900\/20:is(.dark *):hover {
      background-color: #1c398e33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-blue-900\/20:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--color-blue-900) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-blue-950\/30:is(.dark *):hover {
      background-color: #1624564d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-blue-950\/30:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--color-blue-950) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-gray-700:is(.dark *):hover {
      background-color: var(--color-gray-700);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-gray-800:is(.dark *):hover {
      background-color: var(--color-gray-800);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-green-700:is(.dark *):hover {
      background-color: var(--color-green-700);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-green-900\/20:is(.dark *):hover {
      background-color: #0d542b33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-green-900\/20:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--color-green-900) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-input\/50:is(.dark *):hover {
      background-color: var(--input);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-input\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--input) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-neutral-700:is(.dark *):hover {
      background-color: var(--color-neutral-700);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-neutral-800:is(.dark *):hover {
      background-color: var(--color-neutral-800);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-neutral-800\/50:is(.dark *):hover {
      background-color: #26262680;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-neutral-800\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--color-neutral-800) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-neutral-800\/80:is(.dark *):hover {
      background-color: #262626cc;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-neutral-800\/80:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--color-neutral-800) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-neutral-900:is(.dark *):hover {
      background-color: var(--color-neutral-900);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-neutral-900\/80:is(.dark *):hover {
      background-color: #171717cc;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-neutral-900\/80:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--color-neutral-900) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-primary\/10:is(.dark *):hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-primary\/10:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--primary) 10%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-red-900\/20:is(.dark *):hover {
      background-color: #82181a33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-red-900\/20:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--color-red-900) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-red-900\/30:is(.dark *):hover {
      background-color: #82181a4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-red-900\/30:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--color-red-900) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-red-950:is(.dark *):hover {
      background-color: var(--color-red-950);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-red-950\/20:is(.dark *):hover {
      background-color: #46080933;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-red-950\/20:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--color-red-950) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-red-950\/30:is(.dark *):hover {
      background-color: #4608094d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-red-950\/30:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--color-red-950) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-rose-900\/20:is(.dark *):hover {
      background-color: #8b083633;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-rose-900\/20:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--color-rose-900) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-white\/10:is(.dark *):hover {
      background-color: #ffffff1a;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-white\/10:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:from-neutral-700\/50:is(.dark *):hover {
      --tw-gradient-from: #40404080;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:from-neutral-700\/50:is(.dark *):hover {
        --tw-gradient-from: color-mix(in oklab, var(--color-neutral-700) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:from-neutral-800\/50:is(.dark *):hover {
      --tw-gradient-from: #26262680;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:from-neutral-800\/50:is(.dark *):hover {
        --tw-gradient-from: color-mix(in oklab, var(--color-neutral-800) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:to-neutral-800\/30:is(.dark *):hover {
      --tw-gradient-to: #2626264d;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:to-neutral-800\/30:is(.dark *):hover {
        --tw-gradient-to: color-mix(in oklab, var(--color-neutral-800) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:to-neutral-900\/30:is(.dark *):hover {
      --tw-gradient-to: #1717174d;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:to-neutral-900\/30:is(.dark *):hover {
        --tw-gradient-to: color-mix(in oklab, var(--color-neutral-900) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-\[\#D4AF37\]:is(.dark *):hover {
      color: #d4af37;
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-\[var\(--brand-gold\)\]:is(.dark *):hover {
      color: var(--brand-gold);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-blue-200:is(.dark *):hover {
      color: var(--color-blue-200);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-gray-100:is(.dark *):hover {
      color: var(--color-gray-100);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-gray-200:is(.dark *):hover {
      color: var(--color-gray-200);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-green-300:is(.dark *):hover {
      color: var(--color-green-300);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-neutral-200:is(.dark *):hover {
      color: var(--color-neutral-200);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-neutral-300:is(.dark *):hover {
      color: var(--color-neutral-300);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-neutral-500:is(.dark *):hover {
      color: var(--color-neutral-500);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-primary:is(.dark *):hover {
      color: var(--primary);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-purple-400:is(.dark *):hover {
      color: var(--color-purple-400);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-red-300:is(.dark *):hover {
      color: var(--color-red-300);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-red-400:is(.dark *):hover {
      color: var(--color-red-400);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:shadow-\[var\(--brand-gold\)\]\/10:is(.dark *):hover {
      --tw-shadow-alpha: 10%;
      --tw-shadow: var(--brand-gold);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:shadow-lg:is(.dark *):hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:shadow-neutral-900\/20:is(.dark *):hover {
      --tw-shadow-color: #17171733;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:shadow-neutral-900\/20:is(.dark *):hover {
        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-neutral-900) 20%, transparent) var(--tw-shadow-alpha), transparent);
      }
    }
  }

  .dark\:focus\:border-\[var\(--brand-gold\)\]:is(.dark *):focus {
    border-color: var(--brand-gold);
  }

  .dark\:focus\:bg-primary\/20:is(.dark *):focus {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:focus\:bg-primary\/20:is(.dark *):focus {
      background-color: color-mix(in oklab, var(--primary) 20%, transparent);
    }
  }

  .dark\:focus\:ring-blue-600:is(.dark *):focus {
    --tw-ring-color: var(--color-blue-600);
  }

  .dark\:focus\:ring-rose-600:is(.dark *):focus {
    --tw-ring-color: var(--color-rose-600);
  }

  .dark\:focus\:ring-offset-black:is(.dark *):focus {
    --tw-ring-offset-color: var(--color-black);
  }

  .dark\:focus-visible\:border-\[var\(--brand-gold\)\]\/60:is(.dark *):focus-visible {
    border-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:focus-visible\:border-\[var\(--brand-gold\)\]\/60:is(.dark *):focus-visible {
      border-color: color-mix(in oklab, var(--brand-gold) 60%, transparent);
    }
  }

  .dark\:focus-visible\:ring-\[var\(--brand-gold\)\]\/20:is(.dark *):focus-visible {
    --tw-ring-color: var(--brand-gold);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:focus-visible\:ring-\[var\(--brand-gold\)\]\/20:is(.dark *):focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--brand-gold) 20%, transparent);
    }
  }

  .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
    }
  }

  .dark\:focus-visible\:ring-primary\/50:is(.dark *):focus-visible {
    --tw-ring-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:focus-visible\:ring-primary\/50:is(.dark *):focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--primary) 50%, transparent);
    }
  }

  .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
    }
  }

  .dark\:data-\[active\=true\]\:aria-invalid\:ring-destructive\/40:is(.dark *)[data-active="true"][aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[active\=true\]\:aria-invalid\:ring-destructive\/40:is(.dark *)[data-active="true"][aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
    }
  }

  .dark\:data-\[state\=active\]\:border-input:is(.dark *)[data-state="active"] {
    border-color: var(--input);
  }

  .dark\:data-\[state\=active\]\:bg-input\/30:is(.dark *)[data-state="active"] {
    background-color: var(--input);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[state\=active\]\:bg-input\/30:is(.dark *)[data-state="active"] {
      background-color: color-mix(in oklab, var(--input) 30%, transparent);
    }
  }

  .dark\:data-\[state\=active\]\:bg-neutral-700:is(.dark *)[data-state="active"] {
    background-color: var(--color-neutral-700);
  }

  .dark\:data-\[state\=active\]\:bg-neutral-900:is(.dark *)[data-state="active"] {
    background-color: var(--color-neutral-900);
  }

  .dark\:data-\[state\=active\]\:text-foreground:is(.dark *)[data-state="active"] {
    color: var(--foreground);
  }

  .dark\:data-\[state\=active\]\:text-neutral-50:is(.dark *)[data-state="active"] {
    color: var(--color-neutral-50);
  }

  .dark\:data-\[state\=checked\]\:bg-primary:is(.dark *)[data-state="checked"] {
    background-color: var(--primary);
  }

  .dark\:data-\[state\=checked\]\:bg-primary-foreground:is(.dark *)[data-state="checked"] {
    background-color: var(--primary-foreground);
  }

  .dark\:data-\[state\=unchecked\]\:bg-foreground:is(.dark *)[data-state="unchecked"] {
    background-color: var(--foreground);
  }

  .dark\:data-\[state\=unchecked\]\:bg-input\/80:is(.dark *)[data-state="unchecked"] {
    background-color: var(--input);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[state\=unchecked\]\:bg-input\/80:is(.dark *)[data-state="unchecked"] {
      background-color: color-mix(in oklab, var(--input) 80%, transparent);
    }
  }

  .dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:is(.dark *)[data-variant="destructive"]:focus {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:is(.dark *)[data-variant="destructive"]:focus {
      background-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  @media (width >= 48rem) {
    .md\:dark\:border-gray-700:is(.dark *) {
      border-color: var(--color-gray-700);
    }
  }

  @media (width >= 48rem) {
    .md\:dark\:border-neutral-800:is(.dark *) {
      border-color: var(--color-neutral-800);
    }
  }

  .\[\&_\.recharts-cartesian-axis-tick_text\]\:fill-muted-foreground .recharts-cartesian-axis-tick text {
    fill: var(--muted-foreground);
  }

  .\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 .recharts-cartesian-grid line[stroke="#ccc"] {
    stroke: var(--border);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 .recharts-cartesian-grid line[stroke="#ccc"] {
      stroke: color-mix(in oklab, var(--border) 50%, transparent);
    }
  }

  .\[\&_\.recharts-curve\.recharts-tooltip-cursor\]\:stroke-border .recharts-curve.recharts-tooltip-cursor {
    stroke: var(--border);
  }

  .\[\&_\.recharts-dot\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-dot[stroke="#fff"] {
    stroke: #0000;
  }

  .\[\&_\.recharts-layer\]\:outline-hidden .recharts-layer {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .\[\&_\.recharts-layer\]\:outline-hidden .recharts-layer {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .\[\&_\.recharts-polar-grid_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-polar-grid [stroke="#ccc"] {
    stroke: var(--border);
  }

  .\[\&_\.recharts-radial-bar-background-sector\]\:fill-muted .recharts-radial-bar-background-sector {
    fill: var(--muted);
  }

  .\[\&_\.recharts-rectangle\.recharts-tooltip-cursor\]\:fill-muted .recharts-rectangle.recharts-tooltip-cursor {
    fill: var(--muted);
  }

  .\[\&_\.recharts-reference-line_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-reference-line [stroke="#ccc"] {
    stroke: var(--border);
  }

  .\[\&_\.recharts-sector\]\:outline-hidden .recharts-sector {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .\[\&_\.recharts-sector\]\:outline-hidden .recharts-sector {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .\[\&_\.recharts-sector\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-sector[stroke="#fff"] {
    stroke: #0000;
  }

  .\[\&_\.recharts-surface\]\:outline-hidden .recharts-surface {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .\[\&_\.recharts-surface\]\:outline-hidden .recharts-surface {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading] {
    padding-inline: calc(var(--spacing) * 2);
  }

  .\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading] {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading] {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading] {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading] {
    color: var(--muted-foreground);
  }

  .\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group] {
    padding-inline: calc(var(--spacing) * 2);
  }

  .\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-group]:not([hidden]) ~ [cmdk-group] {
    padding-top: calc(var(--spacing) * 0);
  }

  .\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg {
    height: calc(var(--spacing) * 5);
  }

  .\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg {
    width: calc(var(--spacing) * 5);
  }

  .\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input] {
    height: calc(var(--spacing) * 12);
  }

  .\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item] {
    padding-inline: calc(var(--spacing) * 2);
  }

  .\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item] {
    padding-block: calc(var(--spacing) * 3);
  }

  .\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg {
    height: calc(var(--spacing) * 5);
  }

  .\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg {
    width: calc(var(--spacing) * 5);
  }

  .\[\&_p\]\:leading-relaxed p {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .\[\&_svg\]\:pointer-events-none svg {
    pointer-events: none;
  }

  .\[\&_svg\]\:shrink-0 svg {
    flex-shrink: 0;
  }

  .\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*="size-"]) {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\&_svg\:not\(\[class\*\=\'text-\'\]\)\]\:text-muted-foreground svg:not([class*="text-"]) {
    color: var(--muted-foreground);
  }

  .\[\&_tr\]\:border-b tr {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .\[\&_tr\:last-child\]\:border-0 tr:last-child {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .\[\&\:has\(\>\.day-range-end\)\]\:rounded-r-md:has( > .day-range-end) {
    border-top-right-radius: calc(var(--radius)  - 2px);
    border-bottom-right-radius: calc(var(--radius)  - 2px);
  }

  .\[\&\:has\(\>\.day-range-start\)\]\:rounded-l-md:has( > .day-range-start) {
    border-top-left-radius: calc(var(--radius)  - 2px);
    border-bottom-left-radius: calc(var(--radius)  - 2px);
  }

  .\[\&\:has\(\[aria-selected\]\)\]\:rounded-md:has([aria-selected]) {
    border-radius: calc(var(--radius)  - 2px);
  }

  .\[\&\:has\(\[aria-selected\]\)\]\:bg-accent:has([aria-selected]) {
    background-color: var(--accent);
  }

  .first\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-l-md:first-child:has([aria-selected]) {
    border-top-left-radius: calc(var(--radius)  - 2px);
    border-bottom-left-radius: calc(var(--radius)  - 2px);
  }

  .last\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-r-md:last-child:has([aria-selected]) {
    border-top-right-radius: calc(var(--radius)  - 2px);
    border-bottom-right-radius: calc(var(--radius)  - 2px);
  }

  .\[\&\:has\(\[aria-selected\]\.day-range-end\)\]\:rounded-r-md:has([aria-selected].day-range-end) {
    border-top-right-radius: calc(var(--radius)  - 2px);
    border-bottom-right-radius: calc(var(--radius)  - 2px);
  }

  .\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role="checkbox"]) {
    padding-right: calc(var(--spacing) * 0);
  }

  .\[\.border-b\]\:pb-6.border-b {
    padding-bottom: calc(var(--spacing) * 6);
  }

  .\[\.border-t\]\:pt-6.border-t {
    padding-top: calc(var(--spacing) * 6);
  }

  :is(.\*\:\[span\]\:last\:flex > *):is(span):last-child {
    display: flex;
  }

  :is(.\*\:\[span\]\:last\:items-center > *):is(span):last-child {
    align-items: center;
  }

  :is(.\*\:\[span\]\:last\:gap-2 > *):is(span):last-child {
    gap: calc(var(--spacing) * 2);
  }

  :is(.data-\[variant\=destructive\]\:\*\:\[svg\]\:\!text-destructive[data-variant="destructive"] > *):is(svg) {
    color: var(--destructive) !important;
  }

  .\[\&\>\[role\=checkbox\]\]\:translate-y-\[2px\] > [role="checkbox"] {
    --tw-translate-y: 2px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .\[\&\>button\]\:hidden > button {
    display: none;
  }

  .\[\&\>span\:last-child\]\:truncate > span:last-child {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .\[\&\>svg\]\:pointer-events-none > svg {
    pointer-events: none;
  }

  .\[\&\>svg\]\:size-3 > svg {
    width: calc(var(--spacing) * 3);
    height: calc(var(--spacing) * 3);
  }

  .\[\&\>svg\]\:size-3\.5 > svg {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }

  .\[\&\>svg\]\:size-4 > svg {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\&\>svg\]\:h-2\.5 > svg {
    height: calc(var(--spacing) * 2.5);
  }

  .\[\&\>svg\]\:h-3 > svg {
    height: calc(var(--spacing) * 3);
  }

  .\[\&\>svg\]\:w-2\.5 > svg {
    width: calc(var(--spacing) * 2.5);
  }

  .\[\&\>svg\]\:w-3 > svg {
    width: calc(var(--spacing) * 3);
  }

  .\[\&\>svg\]\:shrink-0 > svg {
    flex-shrink: 0;
  }

  .\[\&\>svg\]\:translate-y-0\.5 > svg {
    --tw-translate-y: calc(var(--spacing) * .5);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .\[\&\>svg\]\:text-current > svg {
    color: currentColor;
  }

  .\[\&\>svg\]\:text-muted-foreground > svg {
    color: var(--muted-foreground);
  }

  .\[\&\>svg\]\:text-sidebar-accent-foreground > svg {
    color: var(--sidebar-accent-foreground);
  }

  .\[\&\>tr\]\:last\:border-b-0 > tr:last-child {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0;
  }

  .\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state="open"] > svg {
    rotate: 180deg;
  }

  [data-side="left"][data-collapsible="offcanvas"] .\[\[data-side\=left\]\[data-collapsible\=offcanvas\]_\&\]\:-right-2 {
    right: calc(var(--spacing) * -2);
  }

  [data-side="left"][data-state="collapsed"] .\[\[data-side\=left\]\[data-state\=collapsed\]_\&\]\:cursor-e-resize {
    cursor: e-resize;
  }

  [data-side="right"][data-collapsible="offcanvas"] .\[\[data-side\=right\]\[data-collapsible\=offcanvas\]_\&\]\:-left-2 {
    left: calc(var(--spacing) * -2);
  }

  [data-side="right"][data-state="collapsed"] .\[\[data-side\=right\]\[data-state\=collapsed\]_\&\]\:cursor-w-resize {
    cursor: w-resize;
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-accent:hover {
      background-color: var(--accent);
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-destructive\/90:hover {
      background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-destructive\/90:hover {
        background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-primary\/90:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, var(--primary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-secondary\/90:hover {
      background-color: var(--secondary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-secondary\/90:hover {
        background-color: color-mix(in oklab, var(--secondary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:text-accent-foreground:hover {
      color: var(--accent-foreground);
    }
  }
}

:root {
  --sidebar: #fff;
  --sidebar-foreground: #000;
  --sidebar-primary: #000;
  --sidebar-primary-foreground: #fff;
  --sidebar-accent: #f2f2f2;
  --sidebar-accent-foreground: #000;
  --sidebar-border: #e6e6e6;
  --sidebar-ring: gray;
}

.dark {
  --sidebar: #000;
  --sidebar-foreground: #fff;
  --sidebar-primary: #fff;
  --sidebar-primary-foreground: #000;
  --sidebar-accent: #262626;
  --sidebar-accent-foreground: #fff;
  --sidebar-border: #262626;
  --sidebar-ring: gray;
}

:root {
  --brand-gold-rgb: 194, 157, 91;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

@keyframes gradient-x {
  0% {
    background-position: 0%;
  }

  50% {
    background-position: 100%;
  }

  100% {
    background-position: 0%;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

.animate-gradient-x {
  background-size: 200% 200%;
  animation: 15s infinite gradient-x;
}

input[type="range"]::-webkit-slider-thumb {
  appearance: none;
  cursor: pointer;
  background: #3b82f6;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  transition: all .2s;
  box-shadow: 0 2px 6px #3b82f666;
}

input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px #3b82f699;
}

input[type="range"]::-moz-range-thumb {
  cursor: pointer;
  background: #3b82f6;
  border: none;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  transition: all .2s;
  box-shadow: 0 2px 6px #3b82f666;
}

input[type="range"]::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px #3b82f699;
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-ordinal {
  syntax: "*";
  inherits: false
}

@property --tw-slashed-zero {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-figure {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-spacing {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-fraction {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@property --tw-content {
  syntax: "*";
  inherits: false;
  initial-value: "";
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes ping {
  75%, 100% {
    opacity: 0;
    transform: scale(2);
  }
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

/*# sourceMappingURL=app_globals_css_f9ee138c._.single.css.map*/