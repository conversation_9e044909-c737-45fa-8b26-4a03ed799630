{"node": {"4039928e7548d26b67a82d94754953987e64086b42": {"workers": {"app/(main)/login/page": {"moduleId": "[project]/.next-internal/server/app/(main)/login/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/login/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(main)/login/page": "action-browser"}}, "408a3fc0038eb1b725d89d6b69dcc55b2b1cd06dd3": {"workers": {"app/(main)/login/page": {"moduleId": "[project]/.next-internal/server/app/(main)/login/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/login/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(main)/login/page": "action-browser"}}, "404769c266f0c071880e95e6a0dfe29621565ed871": {"workers": {"app/(main)/login/page": {"moduleId": "[project]/.next-internal/server/app/(main)/login/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/login/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(main)/login/page": "action-browser"}}, "00c2d9624329cded09d08193b88cb56d91c74b75b9": {"workers": {"app/(main)/login/page": {"moduleId": "[project]/.next-internal/server/app/(main)/login/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/login/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(main)/login/page": "action-browser"}}}, "edge": {}}