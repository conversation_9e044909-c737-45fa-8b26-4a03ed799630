[{"C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\actions.ts": "1", "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\ChooseRoleClient.tsx": "2", "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\page.tsx": "3", "C:\\web-app\\dukancard\\app\\(auth)\\layout.tsx": "4", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\activities\\components\\ActivitiesPageClient.tsx": "5", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\activities\\components\\ActivityItem.tsx": "6", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\activities\\page.tsx": "7", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\actions.ts": "8", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\DailyVisitTrendChart.tsx": "9", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedAnalyticsPageClient.tsx": "10", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedChartCard.tsx": "11", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedEngagementMetricsSection.tsx": "12", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedMetricCard.tsx": "13", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedVisitMetricsSection.tsx": "14", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\HourlyVisitTrendChart.tsx": "15", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\MonthlyVisitTrendChart.tsx": "16", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\PremiumFeatureLock.tsx": "17", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\page.tsx": "18", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\customAdUpload.ts": "19", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\customHeaderUpload.ts": "20", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\themeHeaderActions.ts": "21", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\themeSpecificHeaderUpload.ts": "22", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions.ts": "23", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\business-card\\getBusinessCardData.ts": "24", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\business-card\\updateBusinessCard.ts": "25", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\CardEditorClient.tsx": "26", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\BusinessCardPreview.tsx": "27", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardBackgroundEffects.tsx": "28", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardBusinessInfo.tsx": "29", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardCornerDecorations.tsx": "30", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardDivider.tsx": "31", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\AppearanceSection.tsx": "32", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BasicInfoSection.tsx": "33", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BusinessDetailsSection.tsx": "34", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BusinessHoursEditor.tsx": "35", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\CardEditFormContent.tsx": "36", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\ContactLocationSection.tsx": "37", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\CustomAdUpload.tsx": "38", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\CustomBrandingSection.tsx": "39", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\formStyles.ts": "40", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\FormSubmitButton.tsx": "41", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\LinksSection.tsx": "42", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\StatusSlugSection.tsx": "43", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\ThemeSpecificHeaderUpload.tsx": "44", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardGlowEffects.tsx": "45", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardHeader.tsx": "46", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\DownloadButton.tsx": "47", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\index.tsx": "48", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\ShareButtons.tsx": "49", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardProfile.tsx": "50", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardTextures.ts": "51", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CustomAdCropDialog.tsx": "52", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\EnhancedInteractionButtons.tsx": "53", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\useLogoUpload.ts": "54", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\usePincodeDetails.ts": "55", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\useSlugCheck.ts": "56", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\ImageCropDialog.tsx": "57", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\LogoDeleteDialog.tsx": "58", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\UnsavedChangesReminder.tsx": "59", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\utils\\cardUtils.ts": "60", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\data\\businessCardMapper.ts": "61", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\data\\subscriptionChecker.ts": "62", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\logo\\logoActions.ts": "63", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\page.tsx": "64", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\public\\publicCardActions.ts": "65", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\schema.ts": "66", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\slug\\slugUtils.ts": "67", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\businessHoursProcessor.ts": "68", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\constants.ts": "69", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\scrollToError.ts": "70", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\slugGenerator.ts": "71", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\validation\\businessCardValidation.ts": "72", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\AnimatedMetricCard.tsx": "73", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\AnimatedSubscriptionStatus.tsx": "74", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClient.tsx": "75", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClientLayout.tsx": "76", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessStatusAlert.tsx": "77", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\DashboardOverviewClient.tsx": "78", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\EnhancedQuickActions.tsx": "79", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\EnhancedSubscriptionStatus.tsx": "80", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\FlipTimer.tsx": "81", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\RecentActivities.tsx": "82", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\actions.ts": "83", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\DeleteDialog.tsx": "84", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\EmptyGallery.tsx": "85", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\GalleryGrid.tsx": "86", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\GalleryHeader.tsx": "87", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\Lightbox.tsx": "88", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\ReorderControls.tsx": "89", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\SortableImageItem.tsx": "90", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\StaticImageItem.tsx": "91", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\UploadBox.tsx": "92", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\UploadDialog.tsx": "93", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\GalleryPageClient.tsx": "94", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useDragAndDrop.ts": "95", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useGalleryState.ts": "96", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useReordering.ts": "97", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\page.tsx": "98", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\types\\galleryTypes.ts": "99", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\types.ts": "100", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\utils\\fileValidation.ts": "101", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\utils.ts": "102", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\layout.tsx": "103", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\actions.ts": "104", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessLikesPageClient.tsx": "105", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessLikesReceivedList.tsx": "106", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessMyLikesList.tsx": "107", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\LikeCardClient.tsx": "108", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\LikeListClient.tsx": "109", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\page.tsx": "110", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\overview\\page.tsx": "111", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\page.tsx": "112", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\animations.ts": "113", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\BillingToggle.tsx": "114", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\BusinessPlanSkeleton.tsx": "115", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\ConfirmationDialog.tsx": "116", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\DialogBackground.tsx": "117", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\DialogManager.tsx": "118", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedActionButtons.tsx": "119", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedCurrentPlanSection.tsx": "120", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedGlowButton.tsx": "121", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedInvoiceHistoryCard.tsx": "122", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedPlanPageWithManager.tsx": "123", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedPlanSelectionSection.tsx": "124", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedSubscriptionDetailsCard.tsx": "125", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedTrialAlert.tsx": "126", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\FirstTimePaidPlanDialog.tsx": "127", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\PaymentMethodLimitationsDialog.tsx": "128", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\PlanBackground.tsx": "129", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\PlanPageContainer.tsx": "130", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\RealtimePlanPageClient.tsx": "131", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SimplifiedPlanActionDialog.tsx": "132", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\DialogComponents.tsx": "133", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\EligiblePaymentMethodsCard.tsx": "134", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\EnhancedPaymentHistoryCard.tsx": "135", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\EnhancedSubscriptionActionCard.tsx": "136", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\hooks.ts": "137", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\index.ts": "138", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernCancellationDialog.tsx": "139", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernRefundDialog.tsx": "140", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernSubscriptionStatusCard.tsx": "141", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernTabs.tsx": "142", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionActions.ts": "143", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionButton.tsx": "144", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionManager.tsx": "145", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionProcessingIndicator.tsx": "146", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionTabsToggle.tsx": "147", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\types.ts": "148", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SubscriptionStatusBadge.tsx": "149", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SubscriptionStatusIndicator.tsx": "150", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SubscriptionTabContent.tsx": "151", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\TrialManagement.tsx": "152", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\TrialSubscriptionWarningDialog.tsx": "153", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\UpiPaymentMethodWarning.tsx": "154", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\WebhookWaitingIndicator.tsx": "155", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\context\\SubscriptionProcessingContext.tsx": "156", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\hooks\\useSubscriptionHandler.ts": "157", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\hooks\\useSubscriptionLogic.ts": "158", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\hooks\\useUrlParameterHandler.ts": "159", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\page.tsx": "160", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\PlanPageWrapper.tsx": "161", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\addProduct.ts": "162", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\addVariant.ts": "163", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\bulkVariantOperations.ts": "164", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\deleteProduct.ts": "165", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\deleteVariant.ts": "166", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\getProducts.ts": "167", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\getProductWithVariants.ts": "168", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\image-library.ts": "169", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\imageHandlers.ts": "170", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\index.ts": "171", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\schemas.ts": "172", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\types.ts": "173", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\updateProduct.ts": "174", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\updateVariant.ts": "175", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions.ts": "176", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\AddProductClient.tsx": "177", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\page.tsx": "178", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\BulkVariantOperations.tsx": "179", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\hooks\\useProductImageUpload.ts": "180", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\hooks\\useProductMultiImageUpload.ts": "181", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ImageLibraryDialog.tsx": "182", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\index.ts": "183", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductDeleteDialog.tsx": "184", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductEmptyState.tsx": "185", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductFilters.tsx": "186", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductGrid.tsx": "187", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductHeader.tsx": "188", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductItemsPerPageSelector.tsx": "189", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductLoadingState.tsx": "190", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductPagination.tsx": "191", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductStats.tsx": "192", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductTable.tsx": "193", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductViewToggle.tsx": "194", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductImageCropDialog.tsx": "195", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductMultiImageUpload.tsx": "196", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductsPageClient.tsx": "197", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\StandaloneProductForm.tsx": "198", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantCombinationGenerator.tsx": "199", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantForm.tsx": "200", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantTable.tsx": "201", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantTypeSelector.tsx": "202", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\context\\ProductsContext.tsx": "203", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\EditProductClient.tsx": "204", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\page.tsx": "205", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\page.tsx": "206", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\ProductsClient.tsx": "207", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\types.ts": "208", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessMyReviewListClient.tsx": "209", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessReviewListClient.tsx": "210", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessReviewsPageClient.tsx": "211", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\page.tsx": "212", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\actions.ts": "213", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\AccountDeletionSection.tsx": "214", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\CardEditorLinkSection.tsx": "215", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\EmailUpdateDialog.tsx": "216", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\LinkEmailSection.tsx": "217", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\LinkPhoneSection.tsx": "218", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\SettingsPageClient.tsx": "219", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\page.tsx": "220", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\schema.ts": "221", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\actions.ts": "222", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\components\\BusinessSubscriptionsPageClient.tsx": "223", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\page.tsx": "224", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerAnimatedMetricCard.tsx": "225", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerDashboardClient.tsx": "226", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerMetricsOverview.tsx": "227", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\CustomerDashboardClientLayout.tsx": "228", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\layout.tsx": "229", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\actions.ts": "230", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\components\\LikesPageClient.tsx": "231", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\LikeListClient.tsx": "232", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\page.tsx": "233", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\overview\\page.tsx": "234", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\page.tsx": "235", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\actions.ts": "236", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\avatar-actions.ts": "237", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AddressForm.tsx": "238", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AvatarDeleteDialog.tsx": "239", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AvatarUpload.tsx": "240", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\EmailForm.tsx": "241", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\hooks\\usePincodeDetails.ts": "242", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\MobileForm.tsx": "243", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\PhoneForm.tsx": "244", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\ProfilePageClient.tsx": "245", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\ProfileRequirementDialog.tsx": "246", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\hooks\\useAvatarUpload.ts": "247", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\page.tsx": "248", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\ProfileForm.tsx": "249", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\EnhancedReviewListClient.tsx": "250", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\ReviewsBackground.tsx": "251", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\ReviewsPageClient.tsx": "252", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\page.tsx": "253", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\ReviewListClient.tsx": "254", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\actions.ts": "255", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\EmailUpdateDialog.tsx": "256", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\LinkEmailSection.tsx": "257", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\LinkPhoneSection.tsx": "258", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\SettingsPageClient.tsx": "259", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\CustomerSettingsForm.tsx": "260", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\DeleteAccountSection.tsx": "261", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\page.tsx": "262", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\schema.ts": "263", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\UpdateEmailForm.tsx": "264", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\actions.ts": "265", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionCardSkeleton.tsx": "266", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionPagination.tsx": "267", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionsBackground.tsx": "268", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionSearch.tsx": "269", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionsPageClient.tsx": "270", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\page.tsx": "271", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\SubscriptionCardClient.tsx": "272", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\SubscriptionListClient.tsx": "273", "C:\\web-app\\dukancard\\app\\(main)\\about\\AboutUsClient.tsx": "274", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\AboutCTASection.tsx": "275", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\AboutHeroSection.tsx": "276", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\animations\\AboutAnimatedBackground.tsx": "277", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\CoreValuesSection.tsx": "278", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\MilestonesSection.tsx": "279", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\MissionVisionSection.tsx": "280", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\StorySection.tsx": "281", "C:\\web-app\\dukancard\\app\\(main)\\about\\page.tsx": "282", "C:\\web-app\\dukancard\\app\\(main)\\actions\\getHomepageBusinessCard.ts": "283", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\AdvertisePageClient.tsx": "284", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\BenefitsSection.tsx": "285", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\ContactSection.tsx": "286", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\FAQSection.tsx": "287", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\HeroSection.tsx": "288", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\HubSpotForm.tsx": "289", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\page.tsx": "290", "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\AuthCallbackClient.tsx": "291", "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\AuthCallbackWrapper.tsx": "292", "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\metadata.ts": "293", "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\page.tsx": "294", "C:\\web-app\\dukancard\\app\\(main)\\blog\\components\\BlogListingClient.tsx": "295", "C:\\web-app\\dukancard\\app\\(main)\\blog\\page.tsx": "296", "C:\\web-app\\dukancard\\app\\(main)\\blog\\sitemap.ts": "297", "C:\\web-app\\dukancard\\app\\(main)\\blog\\[blogSlug]\\components\\BlogPostClient.tsx": "298", "C:\\web-app\\dukancard\\app\\(main)\\blog\\[blogSlug]\\page.tsx": "299", "C:\\web-app\\dukancard\\app\\(main)\\components\\AnimatedBackground.tsx": "300", "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedAuthCard.tsx": "301", "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedButton.tsx": "302", "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedIcon.tsx": "303", "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AuthPageBackground.tsx": "304", "C:\\web-app\\dukancard\\app\\(main)\\components\\CardShowcase.tsx": "305", "C:\\web-app\\dukancard\\app\\(main)\\components\\DeviceFrame.tsx": "306", "C:\\web-app\\dukancard\\app\\(main)\\components\\FeatureElements.tsx": "307", "C:\\web-app\\dukancard\\app\\(main)\\components\\FuturisticScanner.tsx": "308", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ActionButtonsSection.tsx": "309", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\animations.ts": "310", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\BillingToggle.tsx": "311", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\CardBackground.tsx": "312", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\CTASection.tsx": "313", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\DigitalCardFeature.tsx": "314", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedCardShowcase.tsx": "315", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedFeatureElements.tsx": "316", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedMetricsContainer.tsx": "317", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedMetricsDisplay.tsx": "318", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ErrorDialog.tsx": "319", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\exampleCardData.ts": "320", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FeatureCard.tsx": "321", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FeaturesSection.tsx": "322", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FloatingParticlesBackground.tsx": "323", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FloatingPricingElements.tsx": "324", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HeroActionButtons.tsx": "325", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HeroSearchSection.tsx": "326", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HomeCategoriesSection.tsx": "327", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\MobileFeatureCarousel.tsx": "328", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ModernSearchSection.tsx": "329", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\NewArrivalsSection.tsx": "330", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\PopularBusinessesSection.tsx": "331", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\PricingSection.tsx": "332", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\SectionDivider.tsx": "333", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\StickyHeroSectionClient.tsx": "334", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialCard.tsx": "335", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialCarousel.tsx": "336", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialsSection.tsx": "337", "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\index.ts": "338", "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\MetricsContainer.tsx": "339", "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\MetricsParticles.tsx": "340", "C:\\web-app\\dukancard\\app\\(main)\\components\\MetricsDisplay.tsx": "341", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyBackground.tsx": "342", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyCTASection.tsx": "343", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyHeroSection.tsx": "344", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyNavigation.tsx": "345", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicySection.tsx": "346", "C:\\web-app\\dukancard\\app\\(main)\\components\\SectionBackground.tsx": "347", "C:\\web-app\\dukancard\\app\\(main)\\components\\shared\\FeatureComparisonTable.tsx": "348", "C:\\web-app\\dukancard\\app\\(main)\\components\\shared\\PopularCategoriesSection.tsx": "349", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\animations\\ContactAnimatedBackground.tsx": "350", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactCTASection.tsx": "351", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactHeroSection.tsx": "352", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactInfoSection.tsx": "353", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactMapSection.tsx": "354", "C:\\web-app\\dukancard\\app\\(main)\\contact\\ModernContactClient.tsx": "355", "C:\\web-app\\dukancard\\app\\(main)\\contact\\page.tsx": "356", "C:\\web-app\\dukancard\\app\\(main)\\cookies\\ModernCookiePolicyClient.tsx": "357", "C:\\web-app\\dukancard\\app\\(main)\\cookies\\page.tsx": "358", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\businessActions.ts": "359", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\combinedActions.ts": "360", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\locationActions.ts": "361", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\productActions.ts": "362", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\types.ts": "363", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions.ts": "364", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBadge.tsx": "365", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessCard.tsx": "366", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessGrid.tsx": "367", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessGridSkeleton.tsx": "368", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedButton.tsx": "369", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedDivider.tsx": "370", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessCardTable.tsx": "371", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessCardTableSkeleton.tsx": "372", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessResultsGrid.tsx": "373", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessSortControls.tsx": "374", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CategoryCarousel.tsx": "375", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CategoryFilter.tsx": "376", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CitySearchSkeleton.tsx": "377", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ErrorSection.tsx": "378", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\FloatingCard.tsx": "379", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ImprovedSearchSection.tsx": "380", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\LoadingSpinner.tsx": "381", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\LocationIndicator.tsx": "382", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernBusinessFilterGrid.tsx": "383", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernBusinessResults.tsx": "384", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernResultsSection.tsx": "385", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernSearchSection.tsx": "386", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\NoResultsMessage.tsx": "387", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductGrid.tsx": "388", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductGridSkeleton.tsx": "389", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductResults.tsx": "390", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductResultsGrid.tsx": "391", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductSortControls.tsx": "392", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ResultsSkeleton.tsx": "393", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\RevealTitle.tsx": "394", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SearchResultsHeader.tsx": "395", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SectionTitle.tsx": "396", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SortingControls.tsx": "397", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ViewToggle.tsx": "398", "C:\\web-app\\dukancard\\app\\(main)\\discover\\constants\\paginationConstants.ts": "399", "C:\\web-app\\dukancard\\app\\(main)\\discover\\constants\\urlParamConstants.ts": "400", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\businessContextFunctions.ts": "401", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\commonContextFunctions.ts": "402", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\DiscoverContext.tsx": "403", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\productContextFunctions.ts": "404", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\types.ts": "405", "C:\\web-app\\dukancard\\app\\(main)\\discover\\ModernDiscoverClient.tsx": "406", "C:\\web-app\\dukancard\\app\\(main)\\discover\\ModernResultsSkeleton.tsx": "407", "C:\\web-app\\dukancard\\app\\(main)\\discover\\page.tsx": "408", "C:\\web-app\\dukancard\\app\\(main)\\discover\\utils\\sortMappings.ts": "409", "C:\\web-app\\dukancard\\app\\(main)\\email-change-success\\page.tsx": "410", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\BusinessUseCasesSection.tsx": "411", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureAnimation.tsx": "412", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureBackground.tsx": "413", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureCard.tsx": "414", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeaturesCTASection.tsx": "415", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\HeroFeatureSection.tsx": "416", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\PlanComparisonSection.tsx": "417", "C:\\web-app\\dukancard\\app\\(main)\\features\\ModernFeaturesClient.tsx": "418", "C:\\web-app\\dukancard\\app\\(main)\\features\\page.tsx": "419", "C:\\web-app\\dukancard\\app\\(main)\\hooks\\useIntersectionAnimation.ts": "420", "C:\\web-app\\dukancard\\app\\(main)\\LandingPageClient.tsx": "421", "C:\\web-app\\dukancard\\app\\(main)\\layout.tsx": "422", "C:\\web-app\\dukancard\\app\\(main)\\login\\actions.ts": "423", "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\AuthMethodToggle.tsx": "424", "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\EmailOTPForm.tsx": "425", "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\MobilePasswordForm.tsx": "426", "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\SocialLoginButton.tsx": "427", "C:\\web-app\\dukancard\\app\\(main)\\login\\LoginForm.tsx": "428", "C:\\web-app\\dukancard\\app\\(main)\\login\\page.tsx": "429", "C:\\web-app\\dukancard\\app\\(main)\\page.tsx": "430", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\animations\\AnimatedTitle.tsx": "431", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\animations\\PricingAnimatedBackground.tsx": "432", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingCards.tsx": "433", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingCTA.tsx": "434", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingFAQ.tsx": "435", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingHero.tsx": "436", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingToggle.tsx": "437", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\FeatureComparisonTable.tsx": "438", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\EnhancedPricingPageClient.tsx": "439", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\page.tsx": "440", "C:\\web-app\\dukancard\\app\\(main)\\privacy\\ModernPrivacyPolicyClient.tsx": "441", "C:\\web-app\\dukancard\\app\\(main)\\privacy\\page.tsx": "442", "C:\\web-app\\dukancard\\app\\(main)\\refund\\ModernRefundPolicyClient.tsx": "443", "C:\\web-app\\dukancard\\app\\(main)\\refund\\page.tsx": "444", "C:\\web-app\\dukancard\\app\\(main)\\support\\account-billing\\AccountBillingClient.tsx": "445", "C:\\web-app\\dukancard\\app\\(main)\\support\\account-billing\\page.tsx": "446", "C:\\web-app\\dukancard\\app\\(main)\\support\\analytics\\AnalyticsClient.tsx": "447", "C:\\web-app\\dukancard\\app\\(main)\\support\\analytics\\page.tsx": "448", "C:\\web-app\\dukancard\\app\\(main)\\support\\business-card-setup\\BusinessCardSetupClient.tsx": "449", "C:\\web-app\\dukancard\\app\\(main)\\support\\business-card-setup\\page.tsx": "450", "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\AnimatedTitle.tsx": "451", "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedFAQSection.tsx": "452", "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedSupportFAQ.tsx": "453", "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedSupportSubPage.tsx": "454", "C:\\web-app\\dukancard\\app\\(main)\\support\\page.tsx": "455", "C:\\web-app\\dukancard\\app\\(main)\\support\\product-management\\page.tsx": "456", "C:\\web-app\\dukancard\\app\\(main)\\support\\product-management\\ProductManagementClient.tsx": "457", "C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\page.tsx": "458", "C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\SettingsClient.tsx": "459", "C:\\web-app\\dukancard\\app\\(main)\\support\\SupportPageClient.tsx": "460", "C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\page.tsx": "461", "C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\TechnicalIssuesClient.tsx": "462", "C:\\web-app\\dukancard\\app\\(main)\\terms\\ModernTermsOfServiceClient.tsx": "463", "C:\\web-app\\dukancard\\app\\(main)\\terms\\page.tsx": "464", "C:\\web-app\\dukancard\\app\\(onboarding)\\layout.tsx": "465", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\actions.ts": "466", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\LoadingOverlay.tsx": "467", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\NavigationButtons.tsx": "468", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\StepProgress.tsx": "469", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\AddressStep.tsx": "470", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\BusinessDetailsStep.tsx": "471", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\CardInformationStep.tsx": "472", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\PlanSelectionStep.tsx": "473", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\constants\\onboardingSteps.ts": "474", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useExistingData.ts": "475", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useOnboardingForm.ts": "476", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\usePincodeDetails.ts": "477", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useSlugAvailability.ts": "478", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useUserData.ts": "479", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\layout.tsx": "480", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\OnboardingClient.tsx": "481", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\page.tsx": "482", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\types\\onboarding.ts": "483", "C:\\web-app\\dukancard\\app\\api\\admin\\fix-subscription-inconsistency\\route.ts": "484", "C:\\web-app\\dukancard\\app\\api\\business\\likes\\route.ts": "485", "C:\\web-app\\dukancard\\app\\api\\business\\my-likes\\route.ts": "486", "C:\\web-app\\dukancard\\app\\api\\business\\my-reviews\\route.ts": "487", "C:\\web-app\\dukancard\\app\\api\\business\\reviews\\route.ts": "488", "C:\\web-app\\dukancard\\app\\api\\check-user-type\\route.ts": "489", "C:\\web-app\\dukancard\\app\\api\\customer\\likes\\route.ts": "490", "C:\\web-app\\dukancard\\app\\api\\customer\\reviews\\route.ts": "491", "C:\\web-app\\dukancard\\app\\api\\customer\\reviews\\update\\route.ts": "492", "C:\\web-app\\dukancard\\app\\api\\health\\subscription\\route.ts": "493", "C:\\web-app\\dukancard\\app\\api\\razorpay\\key\\route.ts": "494", "C:\\web-app\\dukancard\\app\\api\\subscription\\[id]\\payments\\route.ts": "495", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\centralized\\route.ts": "496", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\list\\route.ts": "497", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\my\\route.ts": "498", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\cancel\\route.ts": "499", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\invoices\\route.ts": "500", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\pause\\route.ts": "501", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\payments\\route.ts": "502", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\payments\\[paymentId]\\route.ts": "503", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\pending-update\\route.ts": "504", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\databaseOperations.ts": "505", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\errorHandlers.ts": "506", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\index.ts": "507", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\paymentHelpers.ts": "508", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\razorpayApi.ts": "509", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\types.ts": "510", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\validators.ts": "511", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\resume\\route.ts": "512", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\route.ts": "513", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\scheduled-changes\\route.ts": "514", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\switch\\route.ts": "515", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\switch-with-new-payment\\route.ts": "516", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\update\\route.ts": "517", "C:\\web-app\\dukancard\\app\\api\\test\\subscription-flow\\route.ts": "518", "C:\\web-app\\dukancard\\app\\api\\test\\subscription-scenarios\\route.ts": "519", "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\create\\route.ts": "520", "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\delete\\route.ts": "521", "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\get\\route.ts": "522", "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\list\\route.ts": "523", "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\retry\\route.ts": "524", "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\route.ts": "525", "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\update\\route.ts": "526", "C:\\web-app\\dukancard\\app\\auth\\actions.ts": "527", "C:\\web-app\\dukancard\\app\\cards\\sitemap.ts": "528", "C:\\web-app\\dukancard\\app\\components\\AdSlot.tsx": "529", "C:\\web-app\\dukancard\\app\\components\\AdvertiseButton.tsx": "530", "C:\\web-app\\dukancard\\app\\components\\BottomNav.tsx": "531", "C:\\web-app\\dukancard\\app\\components\\EnhancedPublicCardActions.tsx": "532", "C:\\web-app\\dukancard\\app\\components\\FloatingAIButton.tsx": "533", "C:\\web-app\\dukancard\\app\\components\\FloatingInteractionButtons.tsx": "534", "C:\\web-app\\dukancard\\app\\components\\Footer.tsx": "535", "C:\\web-app\\dukancard\\app\\components\\GoogleAnalytics.tsx": "536", "C:\\web-app\\dukancard\\app\\components\\Header.tsx": "537", "C:\\web-app\\dukancard\\app\\components\\icons\\FacebookIcon.tsx": "538", "C:\\web-app\\dukancard\\app\\components\\icons\\InstagramIcon.tsx": "539", "C:\\web-app\\dukancard\\app\\components\\icons\\LinkedInIcon.tsx": "540", "C:\\web-app\\dukancard\\app\\components\\icons\\PinterestIcon.tsx": "541", "C:\\web-app\\dukancard\\app\\components\\icons\\TelegramIcon.tsx": "542", "C:\\web-app\\dukancard\\app\\components\\icons\\TwitterIcon.tsx": "543", "C:\\web-app\\dukancard\\app\\components\\icons\\WhatsAppIcon.tsx": "544", "C:\\web-app\\dukancard\\app\\components\\icons\\YouTubeIcon.tsx": "545", "C:\\web-app\\dukancard\\app\\components\\LoadingOverlay.tsx": "546", "C:\\web-app\\dukancard\\app\\components\\MetaPixel.tsx": "547", "C:\\web-app\\dukancard\\app\\components\\MinimalFooter.tsx": "548", "C:\\web-app\\dukancard\\app\\components\\MinimalHeader.tsx": "549", "C:\\web-app\\dukancard\\app\\components\\MobileFooter.tsx": "550", "C:\\web-app\\dukancard\\app\\components\\PricingCard.tsx": "551", "C:\\web-app\\dukancard\\app\\components\\PricingCardContext.tsx": "552", "C:\\web-app\\dukancard\\app\\components\\ProductListItem.tsx": "553", "C:\\web-app\\dukancard\\app\\components\\PublicCardView.tsx": "554", "C:\\web-app\\dukancard\\app\\components\\reviews\\AuthMessage.tsx": "555", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewForm.tsx": "556", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewSignInPrompt.tsx": "557", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsList.tsx": "558", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsSummary.tsx": "559", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsTab.tsx": "560", "C:\\web-app\\dukancard\\app\\components\\ReviewsTab.tsx": "561", "C:\\web-app\\dukancard\\app\\components\\shared\\EnhancedCardActions.tsx": "562", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\index.ts": "563", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCard.tsx": "564", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCardSkeleton.tsx": "565", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeList.tsx": "566", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikePagination.tsx": "567", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeSearch.tsx": "568", "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewCard.tsx": "569", "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewCardSkeleton.tsx": "570", "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewSortDropdown.tsx": "571", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\index.ts": "572", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCard.tsx": "573", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCardSkeleton.tsx": "574", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionList.tsx": "575", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionPagination.tsx": "576", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionSearch.tsx": "577", "C:\\web-app\\dukancard\\app\\components\\ThemeToggle.tsx": "578", "C:\\web-app\\dukancard\\app\\components\\ui\\container.tsx": "579", "C:\\web-app\\dukancard\\app\\context\\PaymentMethodLimitationsContext.tsx": "580", "C:\\web-app\\dukancard\\app\\layout.tsx": "581", "C:\\web-app\\dukancard\\app\\locality\\actions\\businessActions.ts": "582", "C:\\web-app\\dukancard\\app\\locality\\actions\\combinedActions.ts": "583", "C:\\web-app\\dukancard\\app\\locality\\actions\\locationActions.ts": "584", "C:\\web-app\\dukancard\\app\\locality\\actions\\productActions.ts": "585", "C:\\web-app\\dukancard\\app\\locality\\actions.ts": "586", "C:\\web-app\\dukancard\\app\\locality\\components\\BreadcrumbNav.tsx": "587", "C:\\web-app\\dukancard\\app\\locality\\components\\ErrorSection.tsx": "588", "C:\\web-app\\dukancard\\app\\locality\\components\\ImprovedSearchSection.tsx": "589", "C:\\web-app\\dukancard\\app\\locality\\components\\LocationIndicator.tsx": "590", "C:\\web-app\\dukancard\\app\\locality\\components\\ModernResultsSection.tsx": "591", "C:\\web-app\\dukancard\\app\\locality\\constants\\paginationConstants.ts": "592", "C:\\web-app\\dukancard\\app\\locality\\context\\businessContextFunctions.ts": "593", "C:\\web-app\\dukancard\\app\\locality\\context\\commonContextFunctions.ts": "594", "C:\\web-app\\dukancard\\app\\locality\\context\\LocalityContext.tsx": "595", "C:\\web-app\\dukancard\\app\\locality\\context\\productContextFunctions.ts": "596", "C:\\web-app\\dukancard\\app\\locality\\context\\types.ts": "597", "C:\\web-app\\dukancard\\app\\locality\\layout.tsx": "598", "C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\LocalityPageClient.tsx": "599", "C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\page.tsx": "600", "C:\\web-app\\dukancard\\app\\post\\[postId]\\error.tsx": "601", "C:\\web-app\\dukancard\\app\\post\\[postId]\\loading.tsx": "602", "C:\\web-app\\dukancard\\app\\post\\[postId]\\not-found.tsx": "603", "C:\\web-app\\dukancard\\app\\post\\[postId]\\page.tsx": "604", "C:\\web-app\\dukancard\\app\\products\\sitemap.ts": "605", "C:\\web-app\\dukancard\\app\\robots.ts": "606", "C:\\web-app\\dukancard\\app\\sitemap.ts": "607", "C:\\web-app\\dukancard\\app\\[cardSlug]\\actions.ts": "608", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\animations.ts": "609", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\BusinessDetails\\EnhancedMetricsCards.tsx": "610", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\BusinessDetails\\ProfessionalBusinessTable.tsx": "611", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedAdSection.tsx": "612", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessCardSection.tsx": "613", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessDetails.tsx": "614", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessGallery.tsx": "615", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedPublicCardPageWrapper.tsx": "616", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedTabsToggle.tsx": "617", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\GalleryTab.tsx": "618", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\OfflineBusinessMessage.tsx": "619", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\ProductGridSkeleton.tsx": "620", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\ProductsTab.tsx": "621", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\VisitTracker.tsx": "622", "C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\GalleryPageClient.tsx": "623", "C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\page.tsx": "624", "C:\\web-app\\dukancard\\app\\[cardSlug]\\layout.tsx": "625", "C:\\web-app\\dukancard\\app\\[cardSlug]\\loading.tsx": "626", "C:\\web-app\\dukancard\\app\\[cardSlug]\\page.tsx": "627", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\actions.ts": "628", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\BuyNowButton.tsx": "629", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ImageZoomModal.tsx": "630", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\OfflineProductMessage.tsx": "631", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\PhoneButton.tsx": "632", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductAdSection.tsx": "633", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductDetail.tsx": "634", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductRecommendations.tsx": "635", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\VariantSelector.tsx": "636", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\WhatsAppButton.tsx": "637", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\hooks\\usePinchZoom.ts": "638", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\page.tsx": "639", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\ProductDetailClient.tsx": "640", "C:\\web-app\\dukancard\\app\\[cardSlug]\\PublicCardPageClient.tsx": "641", "C:\\web-app\\dukancard\\components\\blog\\BlogCard.tsx": "642", "C:\\web-app\\dukancard\\components\\blog\\BlogContent.tsx": "643", "C:\\web-app\\dukancard\\components\\blog\\BlogListingSkeleton.tsx": "644", "C:\\web-app\\dukancard\\components\\blog\\BlogNavigation.tsx": "645", "C:\\web-app\\dukancard\\components\\blog\\BlogPagination.tsx": "646", "C:\\web-app\\dukancard\\components\\blog\\BlogPostSkeleton.tsx": "647", "C:\\web-app\\dukancard\\components\\blog\\BlogSearch.tsx": "648", "C:\\web-app\\dukancard\\components\\feed\\ModernBusinessFeedList.tsx": "649", "C:\\web-app\\dukancard\\components\\feed\\ModernCustomerFeedList.tsx": "650", "C:\\web-app\\dukancard\\components\\feed\\shared\\dialogs\\PostDeleteDialog.tsx": "651", "C:\\web-app\\dukancard\\components\\feed\\shared\\editors\\InlinePostAndProductEditor.tsx": "652", "C:\\web-app\\dukancard\\components\\feed\\shared\\editors\\InlinePostEditor.tsx": "653", "C:\\web-app\\dukancard\\components\\feed\\shared\\FilterPills.tsx": "654", "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\ImageCropper.tsx": "655", "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\LocationDisplay.tsx": "656", "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\ProductSelector.tsx": "657", "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\useCustomerPostMediaUpload.ts": "658", "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\usePostMediaUpload.ts": "659", "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\usePostOwnership.ts": "660", "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernCustomerPostCard.tsx": "661", "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernFeedContainer.tsx": "662", "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernFeedHeader.tsx": "663", "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernPostCard.tsx": "664", "C:\\web-app\\dukancard\\components\\feed\\shared\\PostActions.tsx": "665", "C:\\web-app\\dukancard\\components\\feed\\shared\\PostCardSkeleton.tsx": "666", "C:\\web-app\\dukancard\\components\\feed\\shared\\SocialMediaBusinessPostCreator.tsx": "667", "C:\\web-app\\dukancard\\components\\feed\\shared\\SocialMediaPostCreator.tsx": "668", "C:\\web-app\\dukancard\\components\\feed\\shared\\UnifiedPostCard.tsx": "669", "C:\\web-app\\dukancard\\components\\post\\BackNavigation.tsx": "670", "C:\\web-app\\dukancard\\components\\post\\ConditionalPostLayout.tsx": "671", "C:\\web-app\\dukancard\\components\\post\\PostShareButton.tsx": "672", "C:\\web-app\\dukancard\\components\\post\\SinglePostView.tsx": "673", "C:\\web-app\\dukancard\\components\\qr\\QRScanner.tsx": "674", "C:\\web-app\\dukancard\\components\\qr\\QRScannerModal.tsx": "675", "C:\\web-app\\dukancard\\components\\sidebar\\BusinessAppSidebar.tsx": "676", "C:\\web-app\\dukancard\\components\\sidebar\\CustomerAppSidebar.tsx": "677", "C:\\web-app\\dukancard\\components\\sidebar\\NavBusinessMain.tsx": "678", "C:\\web-app\\dukancard\\components\\sidebar\\NavBusinessUser.tsx": "679", "C:\\web-app\\dukancard\\components\\sidebar\\NavCustomerMain.tsx": "680", "C:\\web-app\\dukancard\\components\\sidebar\\NavCustomerUser.tsx": "681", "C:\\web-app\\dukancard\\components\\sidebar\\SidebarLink.tsx": "682", "C:\\web-app\\dukancard\\components\\ui\\accordion.tsx": "683", "C:\\web-app\\dukancard\\components\\ui\\alert-dialog.tsx": "684", "C:\\web-app\\dukancard\\components\\ui\\alert.tsx": "685", "C:\\web-app\\dukancard\\components\\ui\\avatar.tsx": "686", "C:\\web-app\\dukancard\\components\\ui\\badge.tsx": "687", "C:\\web-app\\dukancard\\components\\ui\\breadcrumb.tsx": "688", "C:\\web-app\\dukancard\\components\\ui\\button.tsx": "689", "C:\\web-app\\dukancard\\components\\ui\\calendar.tsx": "690", "C:\\web-app\\dukancard\\components\\ui\\card.tsx": "691", "C:\\web-app\\dukancard\\components\\ui\\carousel.tsx": "692", "C:\\web-app\\dukancard\\components\\ui\\category-combobox.tsx": "693", "C:\\web-app\\dukancard\\components\\ui\\chart.tsx": "694", "C:\\web-app\\dukancard\\components\\ui\\checkbox.tsx": "695", "C:\\web-app\\dukancard\\components\\ui\\collapsible.tsx": "696", "C:\\web-app\\dukancard\\components\\ui\\command.tsx": "697", "C:\\web-app\\dukancard\\components\\ui\\dialog.tsx": "698", "C:\\web-app\\dukancard\\components\\ui\\dropdown-menu.tsx": "699", "C:\\web-app\\dukancard\\components\\ui\\form.tsx": "700", "C:\\web-app\\dukancard\\components\\ui\\input-otp.tsx": "701", "C:\\web-app\\dukancard\\components\\ui\\input.tsx": "702", "C:\\web-app\\dukancard\\components\\ui\\label.tsx": "703", "C:\\web-app\\dukancard\\components\\ui\\multi-select-combobox.tsx": "704", "C:\\web-app\\dukancard\\components\\ui\\pagination.tsx": "705", "C:\\web-app\\dukancard\\components\\ui\\popover.tsx": "706", "C:\\web-app\\dukancard\\components\\ui\\progress.tsx": "707", "C:\\web-app\\dukancard\\components\\ui\\radio-group.tsx": "708", "C:\\web-app\\dukancard\\components\\ui\\resizable-navbar.tsx": "709", "C:\\web-app\\dukancard\\components\\ui\\scroll-area.tsx": "710", "C:\\web-app\\dukancard\\components\\ui\\scroll-to-top-button.tsx": "711", "C:\\web-app\\dukancard\\components\\ui\\select.tsx": "712", "C:\\web-app\\dukancard\\components\\ui\\separator.tsx": "713", "C:\\web-app\\dukancard\\components\\ui\\sheet.tsx": "714", "C:\\web-app\\dukancard\\components\\ui\\sidebar.tsx": "715", "C:\\web-app\\dukancard\\components\\ui\\skeleton.tsx": "716", "C:\\web-app\\dukancard\\components\\ui\\slider.tsx": "717", "C:\\web-app\\dukancard\\components\\ui\\sonner.tsx": "718", "C:\\web-app\\dukancard\\components\\ui\\switch.tsx": "719", "C:\\web-app\\dukancard\\components\\ui\\table.tsx": "720", "C:\\web-app\\dukancard\\components\\ui\\tabs.tsx": "721", "C:\\web-app\\dukancard\\components\\ui\\textarea.tsx": "722", "C:\\web-app\\dukancard\\components\\ui\\toast.tsx": "723", "C:\\web-app\\dukancard\\components\\ui\\tooltip.tsx": "724", "C:\\web-app\\dukancard\\components\\ui\\use-toast.ts": "725", "C:\\web-app\\dukancard\\components\\ui\\visually-hidden.tsx": "726", "C:\\web-app\\dukancard\\lib\\actions\\activities.ts": "727", "C:\\web-app\\dukancard\\lib\\actions\\blogs.ts": "728", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\access.ts": "729", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\discovery.ts": "730", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\index.ts": "731", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\location.ts": "732", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\profileRetrieval.ts": "733", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\search.ts": "734", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\sitemap.ts": "735", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\types.ts": "736", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\utils.ts": "737", "C:\\web-app\\dukancard\\lib\\actions\\categories\\locationBasedFetching.ts": "738", "C:\\web-app\\dukancard\\lib\\actions\\categories\\pincodeTypes.ts": "739", "C:\\web-app\\dukancard\\lib\\actions\\customerPosts\\crud.ts": "740", "C:\\web-app\\dukancard\\lib\\actions\\customerPosts\\index.ts": "741", "C:\\web-app\\dukancard\\lib\\actions\\customerProfiles\\addressValidation.ts": "742", "C:\\web-app\\dukancard\\lib\\actions\\gallery.ts": "743", "C:\\web-app\\dukancard\\lib\\actions\\interactions.ts": "744", "C:\\web-app\\dukancard\\lib\\actions\\location\\locationBySlug.ts": "745", "C:\\web-app\\dukancard\\lib\\actions\\location.ts": "746", "C:\\web-app\\dukancard\\lib\\actions\\posts\\crud.ts": "747", "C:\\web-app\\dukancard\\lib\\actions\\posts\\fetchSinglePost.ts": "748", "C:\\web-app\\dukancard\\lib\\actions\\posts\\index.ts": "749", "C:\\web-app\\dukancard\\lib\\actions\\posts\\types.ts": "750", "C:\\web-app\\dukancard\\lib\\actions\\posts\\unifiedFeed.ts": "751", "C:\\web-app\\dukancard\\lib\\actions\\posts\\utils.ts": "752", "C:\\web-app\\dukancard\\lib\\actions\\posts.ts": "753", "C:\\web-app\\dukancard\\lib\\actions\\products\\fetchProductsByIds.ts": "754", "C:\\web-app\\dukancard\\lib\\actions\\products\\sitemapHelpers.ts": "755", "C:\\web-app\\dukancard\\lib\\actions\\redirectAfterLogin.ts": "756", "C:\\web-app\\dukancard\\lib\\actions\\reviews.ts": "757", "C:\\web-app\\dukancard\\lib\\actions\\secureCustomerProfiles.ts": "758", "C:\\web-app\\dukancard\\lib\\actions\\shared\\delete-customer-post-media.ts": "759", "C:\\web-app\\dukancard\\lib\\actions\\shared\\productActions.ts": "760", "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-business-post-media.ts": "761", "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-customer-post-media.ts": "762", "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-post-media.ts": "763", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\activateTrial.ts": "764", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\centralized.ts": "765", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\confirm.ts": "766", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\create.ts": "767", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\index.ts": "768", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\cancel.ts": "769", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\change.ts": "770", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\index.ts": "771", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\manage.ts": "772", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\schedule.ts": "773", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\switch.ts": "774", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage.ts": "775", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\payment.ts": "776", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\status.ts": "777", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\types.ts": "778", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\utils.ts": "779", "C:\\web-app\\dukancard\\lib\\actions\\subscription.ts": "780", "C:\\web-app\\dukancard\\lib\\actions\\user\\getUserAndProfile.ts": "781", "C:\\web-app\\dukancard\\lib\\api\\response.ts": "782", "C:\\web-app\\dukancard\\lib\\cardDownloader.ts": "783", "C:\\web-app\\dukancard\\lib\\client\\locationUtils.ts": "784", "C:\\web-app\\dukancard\\lib\\config\\categories.ts": "785", "C:\\web-app\\dukancard\\lib\\config\\plans.ts": "786", "C:\\web-app\\dukancard\\lib\\config\\states.ts": "787", "C:\\web-app\\dukancard\\lib\\constants\\predefinedVariants.ts": "788", "C:\\web-app\\dukancard\\lib\\csrf.ts": "789", "C:\\web-app\\dukancard\\lib\\errorHandling.ts": "790", "C:\\web-app\\dukancard\\lib\\PricingPlans.ts": "791", "C:\\web-app\\dukancard\\lib\\qrCodeGenerator.ts": "792", "C:\\web-app\\dukancard\\lib\\rateLimiter.ts": "793", "C:\\web-app\\dukancard\\lib\\razorpay\\razorpayClient.ts": "794", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\create.ts": "795", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\get.ts": "796", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\index.ts": "797", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\types.ts": "798", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\update.ts": "799", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\invoice.ts": "800", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment\\getPayment.ts": "801", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment\\index.ts": "802", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment\\types.ts": "803", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment.ts": "804", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\plan.ts": "805", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\cancel.ts": "806", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\create.ts": "807", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\get.ts": "808", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\index.ts": "809", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\scheduled.ts": "810", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\types.ts": "811", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\update.ts": "812", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\webhook.ts": "813", "C:\\web-app\\dukancard\\lib\\razorpay\\types\\api.ts": "814", "C:\\web-app\\dukancard\\lib\\razorpay\\utils\\auth.ts": "815", "C:\\web-app\\dukancard\\lib\\razorpay\\utils\\loadRazorpaySDK.ts": "816", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\errorTracking.ts": "817", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\eventManager.ts": "818", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\subscriptionManager.ts": "819", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\types.ts": "820", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\validation\\eventOrderValidator.ts": "821", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\validation\\stateTransitionValidator.ts": "822", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\index.ts": "823", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\mainHandler.ts": "824", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\moreSubscriptionHandlers.ts": "825", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\paymentHandlers.ts": "826", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\refundHandlers.ts": "827", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-constants.ts": "828", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-db-updater.ts": "829", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-state-manager.ts": "830", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-state-validator.ts": "831", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionActivated.ts": "832", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionAuthenticated.ts": "833", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionCancelled.ts": "834", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionCharged.ts": "835", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionCompleted.ts": "836", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionExpired.ts": "837", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionHalted.ts": "838", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionPending.ts": "839", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionUpdated.ts": "840", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\index.ts": "841", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionHandlers.ts": "842", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\transactionUtils.ts": "843", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\utils.ts": "844", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\webhook-utils.ts": "845", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\webhookProcessor.ts": "846", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handleWebhook.ts": "847", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\idempotency.ts": "848", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\monitoring.ts": "849", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\types.ts": "850", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\validation.ts": "851", "C:\\web-app\\dukancard\\lib\\schemas\\authSchemas.ts": "852", "C:\\web-app\\dukancard\\lib\\schemas\\locationSchemas.ts": "853", "C:\\web-app\\dukancard\\lib\\services\\realtimeService.ts": "854", "C:\\web-app\\dukancard\\lib\\services\\subscription.ts": "855", "C:\\web-app\\dukancard\\lib\\site-config.ts": "856", "C:\\web-app\\dukancard\\lib\\siteContent.ts": "857", "C:\\web-app\\dukancard\\lib\\subscription\\edge-validation.ts": "858", "C:\\web-app\\dukancard\\lib\\subscription\\SubscriptionFlowManager.ts": "859", "C:\\web-app\\dukancard\\lib\\subscription\\SubscriptionFlowTester.ts": "860", "C:\\web-app\\dukancard\\lib\\subscription\\types.ts": "861", "C:\\web-app\\dukancard\\lib\\supabase\\constants.ts": "862", "C:\\web-app\\dukancard\\lib\\supabase\\services\\businessService.ts": "863", "C:\\web-app\\dukancard\\lib\\supabase\\services\\customerService.ts": "864", "C:\\web-app\\dukancard\\lib\\supabase\\services\\sharedService.ts": "865", "C:\\web-app\\dukancard\\lib\\testing\\database.ts": "866", "C:\\web-app\\dukancard\\lib\\testing\\mswHandlers.ts": "867", "C:\\web-app\\dukancard\\lib\\testing\\SubscriptionScenarioTester.ts": "868", "C:\\web-app\\dukancard\\lib\\testing\\testDataFactories.ts": "869", "C:\\web-app\\dukancard\\lib\\testing\\testUtils.ts": "870", "C:\\web-app\\dukancard\\lib\\testing\\types.ts": "871", "C:\\web-app\\dukancard\\lib\\types\\activities.ts": "872", "C:\\web-app\\dukancard\\lib\\types\\api.ts": "873", "C:\\web-app\\dukancard\\lib\\types\\blog.ts": "874", "C:\\web-app\\dukancard\\lib\\types\\posts.ts": "875", "C:\\web-app\\dukancard\\lib\\types\\subscription.ts": "876", "C:\\web-app\\dukancard\\lib\\utils\\addressUtils.ts": "877", "C:\\web-app\\dukancard\\lib\\utils\\addressValidation.ts": "878", "C:\\web-app\\dukancard\\lib\\utils\\cameraUtils.ts": "879", "C:\\web-app\\dukancard\\lib\\utils\\client-image-compression.ts": "880", "C:\\web-app\\dukancard\\lib\\utils\\customBranding.ts": "881", "C:\\web-app\\dukancard\\lib\\utils\\debounce.ts": "882", "C:\\web-app\\dukancard\\lib\\utils\\feed\\diversityEngine.ts": "883", "C:\\web-app\\dukancard\\lib\\utils\\feed\\feedMerger.ts": "884", "C:\\web-app\\dukancard\\lib\\utils\\feed\\hybridTimeAndPlanAlgorithm.ts": "885", "C:\\web-app\\dukancard\\lib\\utils\\feed\\optimizedHybridAlgorithm.ts": "886", "C:\\web-app\\dukancard\\lib\\utils\\feed\\planPrioritizer.ts": "887", "C:\\web-app\\dukancard\\lib\\utils\\feed\\postCreationHandler.ts": "888", "C:\\web-app\\dukancard\\lib\\utils\\feed\\smartFeedAlgorithm.ts": "889", "C:\\web-app\\dukancard\\lib\\utils\\image-compression.ts": "890", "C:\\web-app\\dukancard\\lib\\utils\\markdown.ts": "891", "C:\\web-app\\dukancard\\lib\\utils\\pagination.ts": "892", "C:\\web-app\\dukancard\\lib\\utils\\postUrl.ts": "893", "C:\\web-app\\dukancard\\lib\\utils\\qrCodeUtils.ts": "894", "C:\\web-app\\dukancard\\lib\\utils\\seo.ts": "895", "C:\\web-app\\dukancard\\lib\\utils\\slugUtils.ts": "896", "C:\\web-app\\dukancard\\lib\\utils\\storage-paths.ts": "897", "C:\\web-app\\dukancard\\lib\\utils\\supabaseErrorHandler.ts": "898", "C:\\web-app\\dukancard\\lib\\utils\\variantHelpers.ts": "899", "C:\\web-app\\dukancard\\lib\\utils.ts": "900"}, {"size": 1984, "mtime": 1753198511183, "results": "901", "hashOfConfig": "902"}, {"size": 11790, "mtime": 1753101910631, "results": "903", "hashOfConfig": "902"}, {"size": 2500, "mtime": 1753201317338, "results": "904", "hashOfConfig": "902"}, {"size": 1884, "mtime": 1753198511183, "results": "905", "hashOfConfig": "902"}, {"size": 30928, "mtime": 1752524337540, "results": "906", "hashOfConfig": "902"}, {"size": 9524, "mtime": 1752524252836, "results": "907", "hashOfConfig": "902"}, {"size": 1657, "mtime": 1753198511183, "results": "908", "hashOfConfig": "902"}, {"size": 15839, "mtime": 1753198511183, "results": "909", "hashOfConfig": "902"}, {"size": 8688, "mtime": 1752523372903, "results": "910", "hashOfConfig": "902"}, {"size": 15287, "mtime": 1752522988158, "results": "911", "hashOfConfig": "902"}, {"size": 2217, "mtime": 1752078894626, "results": "912", "hashOfConfig": "902"}, {"size": 3625, "mtime": 1752523160950, "results": "913", "hashOfConfig": "902"}, {"size": 5348, "mtime": 1752523090171, "results": "914", "hashOfConfig": "902"}, {"size": 4425, "mtime": 1752523255269, "results": "915", "hashOfConfig": "902"}, {"size": 7274, "mtime": 1752523470865, "results": "916", "hashOfConfig": "902"}, {"size": 9690, "mtime": 1752523680880, "results": "917", "hashOfConfig": "902"}, {"size": 1912, "mtime": 1752078894640, "results": "918", "hashOfConfig": "902"}, {"size": 2610, "mtime": 1753198511183, "results": "919", "hashOfConfig": "902"}, {"size": 8196, "mtime": 1753198511183, "results": "920", "hashOfConfig": "902"}, {"size": 6082, "mtime": 1753198511183, "results": "921", "hashOfConfig": "902"}, {"size": 6748, "mtime": 1753198511183, "results": "922", "hashOfConfig": "902"}, {"size": 7445, "mtime": 1753198511183, "results": "923", "hashOfConfig": "902"}, {"size": 273, "mtime": 1752078894640, "results": "924", "hashOfConfig": "902"}, {"size": 2416, "mtime": 1753202042368, "results": "925", "hashOfConfig": "902"}, {"size": 7848, "mtime": 1753202088931, "results": "926", "hashOfConfig": "902"}, {"size": 22168, "mtime": 1752078894640, "results": "927", "hashOfConfig": "902"}, {"size": 20755, "mtime": 1752078894653, "results": "928", "hashOfConfig": "902"}, {"size": 2220, "mtime": 1752078894653, "results": "929", "hashOfConfig": "902"}, {"size": 10914, "mtime": 1752078894653, "results": "930", "hashOfConfig": "902"}, {"size": 1630, "mtime": 1752078894653, "results": "931", "hashOfConfig": "902"}, {"size": 721, "mtime": 1752078894653, "results": "932", "hashOfConfig": "902"}, {"size": 8255, "mtime": 1752078894653, "results": "933", "hashOfConfig": "902"}, {"size": 21052, "mtime": 1752078894664, "results": "934", "hashOfConfig": "902"}, {"size": 4489, "mtime": 1752078894665, "results": "935", "hashOfConfig": "902"}, {"size": 13138, "mtime": 1752078894667, "results": "936", "hashOfConfig": "902"}, {"size": 2814, "mtime": 1752078894667, "results": "937", "hashOfConfig": "902"}, {"size": 14802, "mtime": 1752080815930, "results": "938", "hashOfConfig": "902"}, {"size": 15549, "mtime": 1752078894667, "results": "939", "hashOfConfig": "902"}, {"size": 13946, "mtime": 1752078894667, "results": "940", "hashOfConfig": "902"}, {"size": 1088, "mtime": 1752078894667, "results": "941", "hashOfConfig": "902"}, {"size": 2708, "mtime": 1752078894667, "results": "942", "hashOfConfig": "902"}, {"size": 9768, "mtime": 1752078894667, "results": "943", "hashOfConfig": "902"}, {"size": 23953, "mtime": 1752078894667, "results": "944", "hashOfConfig": "902"}, {"size": 5243, "mtime": 1752078894667, "results": "945", "hashOfConfig": "902"}, {"size": 3361, "mtime": 1752078894667, "results": "946", "hashOfConfig": "902"}, {"size": 3637, "mtime": 1752078894678, "results": "947", "hashOfConfig": "902"}, {"size": 6688, "mtime": 1752078894680, "results": "948", "hashOfConfig": "902"}, {"size": 2107, "mtime": 1752078894682, "results": "949", "hashOfConfig": "902"}, {"size": 3868, "mtime": 1752078894681, "results": "950", "hashOfConfig": "902"}, {"size": 4167, "mtime": 1752078894682, "results": "951", "hashOfConfig": "902"}, {"size": 3004, "mtime": 1752078894683, "results": "952", "hashOfConfig": "902"}, {"size": 6921, "mtime": 1752078894684, "results": "953", "hashOfConfig": "902"}, {"size": 8503, "mtime": 1752078894708, "results": "954", "hashOfConfig": "902"}, {"size": 9562, "mtime": 1752078894708, "results": "955", "hashOfConfig": "902"}, {"size": 4802, "mtime": 1752078894708, "results": "956", "hashOfConfig": "902"}, {"size": 1794, "mtime": 1752078894708, "results": "957", "hashOfConfig": "902"}, {"size": 5533, "mtime": 1752078894708, "results": "958", "hashOfConfig": "902"}, {"size": 2140, "mtime": 1752078894708, "results": "959", "hashOfConfig": "902"}, {"size": 5309, "mtime": 1752078894708, "results": "960", "hashOfConfig": "902"}, {"size": 3245, "mtime": 1752078894717, "results": "961", "hashOfConfig": "902"}, {"size": 8152, "mtime": 1753121025013, "results": "962", "hashOfConfig": "902"}, {"size": 2142, "mtime": 1753202132263, "results": "963", "hashOfConfig": "902"}, {"size": 6639, "mtime": 1753202402181, "results": "964", "hashOfConfig": "902"}, {"size": 1889, "mtime": 1753202478370, "results": "965", "hashOfConfig": "902"}, {"size": 1859, "mtime": 1753202587854, "results": "966", "hashOfConfig": "902"}, {"size": 9011, "mtime": 1752080815930, "results": "967", "hashOfConfig": "902"}, {"size": 2435, "mtime": 1752078894723, "results": "968", "hashOfConfig": "902"}, {"size": 1001, "mtime": 1752078894723, "results": "969", "hashOfConfig": "902"}, {"size": 559, "mtime": 1753031839333, "results": "970", "hashOfConfig": "902"}, {"size": 2573, "mtime": 1752078894723, "results": "971", "hashOfConfig": "902"}, {"size": 404, "mtime": 1752078894723, "results": "972", "hashOfConfig": "902"}, {"size": 1825, "mtime": 1752078894723, "results": "973", "hashOfConfig": "902"}, {"size": 4687, "mtime": 1752520901592, "results": "974", "hashOfConfig": "902"}, {"size": 12464, "mtime": 1752521239777, "results": "975", "hashOfConfig": "902"}, {"size": 6373, "mtime": 1752520966108, "results": "976", "hashOfConfig": "902"}, {"size": 2522, "mtime": 1752652071674, "results": "977", "hashOfConfig": "902"}, {"size": 2722, "mtime": 1752521491570, "results": "978", "hashOfConfig": "902"}, {"size": 6424, "mtime": 1753198511199, "results": "979", "hashOfConfig": "902"}, {"size": 5057, "mtime": 1752521333633, "results": "980", "hashOfConfig": "902"}, {"size": 6735, "mtime": 1752078894735, "results": "981", "hashOfConfig": "902"}, {"size": 8561, "mtime": 1752078894735, "results": "982", "hashOfConfig": "902"}, {"size": 13959, "mtime": 1752521541091, "results": "983", "hashOfConfig": "902"}, {"size": 9855, "mtime": 1753203271581, "results": "984", "hashOfConfig": "902"}, {"size": 1993, "mtime": 1752078894735, "results": "985", "hashOfConfig": "902"}, {"size": 3314, "mtime": 1752522195446, "results": "986", "hashOfConfig": "902"}, {"size": 6696, "mtime": 1753124542976, "results": "987", "hashOfConfig": "902"}, {"size": 2180, "mtime": 1752521782818, "results": "988", "hashOfConfig": "902"}, {"size": 1799, "mtime": 1752078894735, "results": "989", "hashOfConfig": "902"}, {"size": 2794, "mtime": 1752078894751, "results": "990", "hashOfConfig": "902"}, {"size": 3092, "mtime": 1753124556860, "results": "991", "hashOfConfig": "902"}, {"size": 2045, "mtime": 1753124570425, "results": "992", "hashOfConfig": "902"}, {"size": 2833, "mtime": 1752078894751, "results": "993", "hashOfConfig": "902"}, {"size": 6615, "mtime": 1752522674608, "results": "994", "hashOfConfig": "902"}, {"size": 8399, "mtime": 1753124583505, "results": "995", "hashOfConfig": "902"}, {"size": 2305, "mtime": 1752078894751, "results": "996", "hashOfConfig": "902"}, {"size": 1978, "mtime": 1753124602818, "results": "997", "hashOfConfig": "902"}, {"size": 3072, "mtime": 1753124618057, "results": "998", "hashOfConfig": "902"}, {"size": 2135, "mtime": 1753251129486, "results": "999", "hashOfConfig": "902"}, {"size": 738, "mtime": 1753124632897, "results": "1000", "hashOfConfig": "902"}, {"size": 357, "mtime": 1753123542138, "results": "1001", "hashOfConfig": "902"}, {"size": 1142, "mtime": 1752078894766, "results": "1002", "hashOfConfig": "902"}, {"size": 1089, "mtime": 1752078894766, "results": "1003", "hashOfConfig": "902"}, {"size": 3242, "mtime": 1753251129486, "results": "1004", "hashOfConfig": "902"}, {"size": 5091, "mtime": 1753201558302, "results": "1005", "hashOfConfig": "902"}, {"size": 10378, "mtime": 1752524928054, "results": "1006", "hashOfConfig": "902"}, {"size": 4483, "mtime": 1752925036472, "results": "1007", "hashOfConfig": "902"}, {"size": 3675, "mtime": 1752524849734, "results": "1008", "hashOfConfig": "902"}, {"size": 4030, "mtime": 1752078894766, "results": "1009", "hashOfConfig": "902"}, {"size": 1698, "mtime": 1752078894766, "results": "1010", "hashOfConfig": "902"}, {"size": 3857, "mtime": 1753251129486, "results": "1011", "hashOfConfig": "902"}, {"size": 5729, "mtime": 1753251129486, "results": "1012", "hashOfConfig": "902"}, {"size": 2348, "mtime": 1753251129486, "results": "1013", "hashOfConfig": "902"}, {"size": 1439, "mtime": 1752078894807, "results": "1014", "hashOfConfig": "902"}, {"size": 2558, "mtime": 1752078894782, "results": "1015", "hashOfConfig": "902"}, {"size": 4637, "mtime": 1752078894782, "results": "1016", "hashOfConfig": "902"}, {"size": 2863, "mtime": 1752078894782, "results": "1017", "hashOfConfig": "902"}, {"size": 3966, "mtime": 1752078894782, "results": "1018", "hashOfConfig": "902"}, {"size": 3136, "mtime": 1752078894782, "results": "1019", "hashOfConfig": "902"}, {"size": 4782, "mtime": 1752078894782, "results": "1020", "hashOfConfig": "902"}, {"size": 8607, "mtime": 1752078894782, "results": "1021", "hashOfConfig": "902"}, {"size": 4656, "mtime": 1752078894782, "results": "1022", "hashOfConfig": "902"}, {"size": 23325, "mtime": 1753250912472, "results": "1023", "hashOfConfig": "902"}, {"size": 1229, "mtime": 1752078894782, "results": "1024", "hashOfConfig": "902"}, {"size": 6213, "mtime": 1752078894782, "results": "1025", "hashOfConfig": "902"}, {"size": 15610, "mtime": 1752078894782, "results": "1026", "hashOfConfig": "902"}, {"size": 3571, "mtime": 1752078894782, "results": "1027", "hashOfConfig": "902"}, {"size": 10880, "mtime": 1752078894782, "results": "1028", "hashOfConfig": "902"}, {"size": 2393, "mtime": 1752078894782, "results": "1029", "hashOfConfig": "902"}, {"size": 5434, "mtime": 1752078894797, "results": "1030", "hashOfConfig": "902"}, {"size": 9221, "mtime": 1752078894797, "results": "1031", "hashOfConfig": "902"}, {"size": 6955, "mtime": 1752078894797, "results": "1032", "hashOfConfig": "902"}, {"size": 21017, "mtime": 1752078894797, "results": "1033", "hashOfConfig": "902"}, {"size": 1762, "mtime": 1752078894807, "results": "1034", "hashOfConfig": "902"}, {"size": 8288, "mtime": 1752078894807, "results": "1035", "hashOfConfig": "902"}, {"size": 16465, "mtime": 1752078894807, "results": "1036", "hashOfConfig": "902"}, {"size": 12988, "mtime": 1752078894814, "results": "1037", "hashOfConfig": "902"}, {"size": 2432, "mtime": 1752078894814, "results": "1038", "hashOfConfig": "902"}, {"size": 770, "mtime": 1752078894814, "results": "1039", "hashOfConfig": "902"}, {"size": 12287, "mtime": 1752078894814, "results": "1040", "hashOfConfig": "902"}, {"size": 8702, "mtime": 1752078894814, "results": "1041", "hashOfConfig": "902"}, {"size": 27687, "mtime": 1752078894814, "results": "1042", "hashOfConfig": "902"}, {"size": 6021, "mtime": 1752078894814, "results": "1043", "hashOfConfig": "902"}, {"size": 5206, "mtime": 1752078894814, "results": "1044", "hashOfConfig": "902"}, {"size": 3546, "mtime": 1752078894814, "results": "1045", "hashOfConfig": "902"}, {"size": 14866, "mtime": 1752078894814, "results": "1046", "hashOfConfig": "902"}, {"size": 7461, "mtime": 1752078894814, "results": "1047", "hashOfConfig": "902"}, {"size": 2679, "mtime": 1752078894814, "results": "1048", "hashOfConfig": "902"}, {"size": 3078, "mtime": 1752078894823, "results": "1049", "hashOfConfig": "902"}, {"size": 8231, "mtime": 1752078894797, "results": "1050", "hashOfConfig": "902"}, {"size": 14159, "mtime": 1752078894797, "results": "1051", "hashOfConfig": "902"}, {"size": 4456, "mtime": 1752078894797, "results": "1052", "hashOfConfig": "902"}, {"size": 3114, "mtime": 1752078894797, "results": "1053", "hashOfConfig": "902"}, {"size": 4235, "mtime": 1752078894797, "results": "1054", "hashOfConfig": "902"}, {"size": 6620, "mtime": 1752078894797, "results": "1055", "hashOfConfig": "902"}, {"size": 776, "mtime": 1752078894807, "results": "1056", "hashOfConfig": "902"}, {"size": 11893, "mtime": 1752078894823, "results": "1057", "hashOfConfig": "902"}, {"size": 11640, "mtime": 1752078894823, "results": "1058", "hashOfConfig": "902"}, {"size": 6587, "mtime": 1752078894823, "results": "1059", "hashOfConfig": "902"}, {"size": 3524, "mtime": 1752078894830, "results": "1060", "hashOfConfig": "902"}, {"size": 12782, "mtime": 1753101910631, "results": "1061", "hashOfConfig": "902"}, {"size": 1654, "mtime": 1752078894782, "results": "1062", "hashOfConfig": "902"}, {"size": 7526, "mtime": 1753198511206, "results": "1063", "hashOfConfig": "902"}, {"size": 13430, "mtime": 1753253416712, "results": "1064", "hashOfConfig": "902"}, {"size": 7988, "mtime": 1753199780098, "results": "1065", "hashOfConfig": "902"}, {"size": 5205, "mtime": 1753198511212, "results": "1066", "hashOfConfig": "902"}, {"size": 14232, "mtime": 1753200789597, "results": "1067", "hashOfConfig": "902"}, {"size": 3056, "mtime": 1753201621360, "results": "1068", "hashOfConfig": "902"}, {"size": 9259, "mtime": 1753201651769, "results": "1069", "hashOfConfig": "902"}, {"size": 7661, "mtime": 1753253442785, "results": "1070", "hashOfConfig": "902"}, {"size": 8895, "mtime": 1753198511214, "results": "1071", "hashOfConfig": "902"}, {"size": 774, "mtime": 1752078894839, "results": "1072", "hashOfConfig": "902"}, {"size": 5527, "mtime": 1753104677045, "results": "1073", "hashOfConfig": "902"}, {"size": 585, "mtime": 1752170193569, "results": "1074", "hashOfConfig": "902"}, {"size": 8132, "mtime": 1753198511214, "results": "1075", "hashOfConfig": "902"}, {"size": 11057, "mtime": 1753201124444, "results": "1076", "hashOfConfig": "902"}, {"size": 99, "mtime": 1752078894830, "results": "1077", "hashOfConfig": "902"}, {"size": 10730, "mtime": 1753101910644, "results": "1078", "hashOfConfig": "902"}, {"size": 2570, "mtime": 1753251129502, "results": "1079", "hashOfConfig": "902"}, {"size": 16581, "mtime": 1753101910644, "results": "1080", "hashOfConfig": "902"}, {"size": 6920, "mtime": 1752078894861, "results": "1081", "hashOfConfig": "902"}, {"size": 8800, "mtime": 1752078894863, "results": "1082", "hashOfConfig": "902"}, {"size": 7997, "mtime": 1752078894845, "results": "1083", "hashOfConfig": "902"}, {"size": 743, "mtime": 1752083877657, "results": "1084", "hashOfConfig": "902"}, {"size": 2089, "mtime": 1752078894863, "results": "1085", "hashOfConfig": "902"}, {"size": 2029, "mtime": 1752170340365, "results": "1086", "hashOfConfig": "902"}, {"size": 5665, "mtime": 1752087097784, "results": "1087", "hashOfConfig": "902"}, {"size": 9160, "mtime": 1752086184726, "results": "1088", "hashOfConfig": "902"}, {"size": 2212, "mtime": 1752084237937, "results": "1089", "hashOfConfig": "902"}, {"size": 2762, "mtime": 1752165254320, "results": "1090", "hashOfConfig": "902"}, {"size": 4770, "mtime": 1752085989931, "results": "1091", "hashOfConfig": "902"}, {"size": 7051, "mtime": 1752165128789, "results": "1092", "hashOfConfig": "902"}, {"size": 5095, "mtime": 1752084296324, "results": "1093", "hashOfConfig": "902"}, {"size": 18791, "mtime": 1753106058738, "results": "1094", "hashOfConfig": "902"}, {"size": 4427, "mtime": 1752086297714, "results": "1095", "hashOfConfig": "902"}, {"size": 5264, "mtime": 1752078894845, "results": "1096", "hashOfConfig": "902"}, {"size": 16573, "mtime": 1752078894852, "results": "1097", "hashOfConfig": "902"}, {"size": 1339, "mtime": 1752078894852, "results": "1098", "hashOfConfig": "902"}, {"size": 30215, "mtime": 1753101910644, "results": "1099", "hashOfConfig": "902"}, {"size": 17692, "mtime": 1752078894855, "results": "1100", "hashOfConfig": "902"}, {"size": 31435, "mtime": 1753101910657, "results": "1101", "hashOfConfig": "902"}, {"size": 25674, "mtime": 1753101910657, "results": "1102", "hashOfConfig": "902"}, {"size": 10885, "mtime": 1752078894855, "results": "1103", "hashOfConfig": "902"}, {"size": 7850, "mtime": 1752086860223, "results": "1104", "hashOfConfig": "902"}, {"size": 12815, "mtime": 1753104769695, "results": "1105", "hashOfConfig": "902"}, {"size": 4213, "mtime": 1753106603423, "results": "1106", "hashOfConfig": "902"}, {"size": 1796, "mtime": 1753251129502, "results": "1107", "hashOfConfig": "902"}, {"size": 3085, "mtime": 1752086007532, "results": "1108", "hashOfConfig": "902"}, {"size": 2564, "mtime": 1752086713691, "results": "1109", "hashOfConfig": "902"}, {"size": 10120, "mtime": 1752525787411, "results": "1110", "hashOfConfig": "902"}, {"size": 11606, "mtime": 1752525681821, "results": "1111", "hashOfConfig": "902"}, {"size": 5373, "mtime": 1752525844741, "results": "1112", "hashOfConfig": "902"}, {"size": 2006, "mtime": 1753026919824, "results": "1113", "hashOfConfig": "902"}, {"size": 26749, "mtime": 1753251129502, "results": "1114", "hashOfConfig": "902"}, {"size": 23396, "mtime": 1752691514782, "results": "1115", "hashOfConfig": "902"}, {"size": 1822, "mtime": 1752526641904, "results": "1116", "hashOfConfig": "902"}, {"size": 15608, "mtime": 1752763145906, "results": "1117", "hashOfConfig": "902"}, {"size": 4060, "mtime": 1752762369862, "results": "1118", "hashOfConfig": "902"}, {"size": 3182, "mtime": 1752526585020, "results": "1119", "hashOfConfig": "902"}, {"size": 4559, "mtime": 1752763551631, "results": "1120", "hashOfConfig": "902"}, {"size": 2104, "mtime": 1753251129502, "results": "1121", "hashOfConfig": "902"}, {"size": 547, "mtime": 1752761809080, "results": "1122", "hashOfConfig": "902"}, {"size": 8709, "mtime": 1753104987669, "results": "1123", "hashOfConfig": "902"}, {"size": 11679, "mtime": 1752525347906, "results": "1124", "hashOfConfig": "902"}, {"size": 4330, "mtime": 1752078894893, "results": "1125", "hashOfConfig": "902"}, {"size": 2300, "mtime": 1752078894894, "results": "1126", "hashOfConfig": "902"}, {"size": 1807, "mtime": 1752078894894, "results": "1127", "hashOfConfig": "902"}, {"size": 2559, "mtime": 1752078894894, "results": "1128", "hashOfConfig": "902"}, {"size": 2308, "mtime": 1752652107359, "results": "1129", "hashOfConfig": "902"}, {"size": 2136, "mtime": 1752078894894, "results": "1130", "hashOfConfig": "902"}, {"size": 3080, "mtime": 1753026987602, "results": "1131", "hashOfConfig": "902"}, {"size": 6530, "mtime": 1752078894894, "results": "1132", "hashOfConfig": "902"}, {"size": 1372, "mtime": 1752925036472, "results": "1133", "hashOfConfig": "902"}, {"size": 3511, "mtime": 1752078894894, "results": "1134", "hashOfConfig": "902"}, {"size": 2499, "mtime": 1753251129502, "results": "1135", "hashOfConfig": "902"}, {"size": 2793, "mtime": 1753251129502, "results": "1136", "hashOfConfig": "902"}, {"size": 17435, "mtime": 1753201739930, "results": "1137", "hashOfConfig": "902"}, {"size": 6372, "mtime": 1752078894909, "results": "1138", "hashOfConfig": "902"}, {"size": 12390, "mtime": 1753200898929, "results": "1139", "hashOfConfig": "902"}, {"size": 1376, "mtime": 1752688533290, "results": "1140", "hashOfConfig": "902"}, {"size": 5647, "mtime": 1752688601357, "results": "1141", "hashOfConfig": "902"}, {"size": 7356, "mtime": 1752078894910, "results": "1142", "hashOfConfig": "902"}, {"size": 4583, "mtime": 1752078894910, "results": "1143", "hashOfConfig": "902"}, {"size": 6598, "mtime": 1752078894910, "results": "1144", "hashOfConfig": "902"}, {"size": 5978, "mtime": 1752078894910, "results": "1145", "hashOfConfig": "902"}, {"size": 13585, "mtime": 1752678735198, "results": "1146", "hashOfConfig": "902"}, {"size": 6909, "mtime": 1752078894910, "results": "1147", "hashOfConfig": "902"}, {"size": 7677, "mtime": 1752078894910, "results": "1148", "hashOfConfig": "902"}, {"size": 2322, "mtime": 1752078894910, "results": "1149", "hashOfConfig": "902"}, {"size": 7209, "mtime": 1752678129103, "results": "1150", "hashOfConfig": "902"}, {"size": 10385, "mtime": 1752078894910, "results": "1151", "hashOfConfig": "902"}, {"size": 733, "mtime": 1752078894910, "results": "1152", "hashOfConfig": "902"}, {"size": 1203, "mtime": 1752078894910, "results": "1153", "hashOfConfig": "902"}, {"size": 1827, "mtime": 1752078894910, "results": "1154", "hashOfConfig": "902"}, {"size": 1695, "mtime": 1752078894910, "results": "1155", "hashOfConfig": "902"}, {"size": 17621, "mtime": 1753201759207, "results": "1156", "hashOfConfig": "902"}, {"size": 15615, "mtime": 1752763206921, "results": "1157", "hashOfConfig": "902"}, {"size": 4083, "mtime": 1752762551074, "results": "1158", "hashOfConfig": "902"}, {"size": 2929, "mtime": 1752527064479, "results": "1159", "hashOfConfig": "902"}, {"size": 4305, "mtime": 1752763625974, "results": "1160", "hashOfConfig": "902"}, {"size": 5476, "mtime": 1752763083916, "results": "1161", "hashOfConfig": "902"}, {"size": 23276, "mtime": 1752527499610, "results": "1162", "hashOfConfig": "902"}, {"size": 1602, "mtime": 1753251129502, "results": "1163", "hashOfConfig": "902"}, {"size": 1380, "mtime": 1752762960440, "results": "1164", "hashOfConfig": "902"}, {"size": 3464, "mtime": 1752696729504, "results": "1165", "hashOfConfig": "902"}, {"size": 2847, "mtime": 1752925036472, "results": "1166", "hashOfConfig": "902"}, {"size": 2007, "mtime": 1752078894925, "results": "1167", "hashOfConfig": "902"}, {"size": 3579, "mtime": 1752078894925, "results": "1168", "hashOfConfig": "902"}, {"size": 1181, "mtime": 1752078894925, "results": "1169", "hashOfConfig": "902"}, {"size": 2665, "mtime": 1752078894925, "results": "1170", "hashOfConfig": "902"}, {"size": 6679, "mtime": 1752925036472, "results": "1171", "hashOfConfig": "902"}, {"size": 3744, "mtime": 1752078894925, "results": "1172", "hashOfConfig": "902"}, {"size": 6038, "mtime": 1752078894925, "results": "1173", "hashOfConfig": "902"}, {"size": 1270, "mtime": 1752925036472, "results": "1174", "hashOfConfig": "902"}, {"size": 2007, "mtime": 1752078894925, "results": "1175", "hashOfConfig": "902"}, {"size": 5704, "mtime": 1752078894941, "results": "1176", "hashOfConfig": "902"}, {"size": 4915, "mtime": 1752078894941, "results": "1177", "hashOfConfig": "902"}, {"size": 4615, "mtime": 1752078894941, "results": "1178", "hashOfConfig": "902"}, {"size": 5211, "mtime": 1752078894941, "results": "1179", "hashOfConfig": "902"}, {"size": 5725, "mtime": 1752078894941, "results": "1180", "hashOfConfig": "902"}, {"size": 4799, "mtime": 1752078894941, "results": "1181", "hashOfConfig": "902"}, {"size": 7774, "mtime": 1752078894941, "results": "1182", "hashOfConfig": "902"}, {"size": 2147, "mtime": 1752078894941, "results": "1183", "hashOfConfig": "902"}, {"size": 5677, "mtime": 1753101910657, "results": "1184", "hashOfConfig": "902"}, {"size": 737, "mtime": 1752078894941, "results": "1185", "hashOfConfig": "902"}, {"size": 5712, "mtime": 1752078894941, "results": "1186", "hashOfConfig": "902"}, {"size": 8862, "mtime": 1752078894941, "results": "1187", "hashOfConfig": "902"}, {"size": 3996, "mtime": 1752078894941, "results": "1188", "hashOfConfig": "902"}, {"size": 6753, "mtime": 1752078894941, "results": "1189", "hashOfConfig": "902"}, {"size": 6562, "mtime": 1752078894941, "results": "1190", "hashOfConfig": "902"}, {"size": 1779, "mtime": 1752078894941, "results": "1191", "hashOfConfig": "902"}, {"size": 9908, "mtime": 1753198511293, "results": "1192", "hashOfConfig": "902"}, {"size": 161, "mtime": 1752078894941, "results": "1193", "hashOfConfig": "902"}, {"size": 189, "mtime": 1752078894956, "results": "1194", "hashOfConfig": "902"}, {"size": 565, "mtime": 1752078894956, "results": "1195", "hashOfConfig": "902"}, {"size": 8610, "mtime": 1752078894956, "results": "1196", "hashOfConfig": "902"}, {"size": 1870, "mtime": 1752078894956, "results": "1197", "hashOfConfig": "902"}, {"size": 2516, "mtime": 1752078894956, "results": "1198", "hashOfConfig": "902"}, {"size": 14853, "mtime": 1752080815937, "results": "1199", "hashOfConfig": "902"}, {"size": 4727, "mtime": 1753105010421, "results": "1200", "hashOfConfig": "902"}, {"size": 2890, "mtime": 1752078894956, "results": "1201", "hashOfConfig": "902"}, {"size": 1924, "mtime": 1752078894956, "results": "1202", "hashOfConfig": "902"}, {"size": 2239, "mtime": 1752078894956, "results": "1203", "hashOfConfig": "902"}, {"size": 1646, "mtime": 1752078894956, "results": "1204", "hashOfConfig": "902"}, {"size": 5334, "mtime": 1752078894972, "results": "1205", "hashOfConfig": "902"}, {"size": 1048, "mtime": 1752078894956, "results": "1206", "hashOfConfig": "902"}, {"size": 3451, "mtime": 1752078894956, "results": "1207", "hashOfConfig": "902"}, {"size": 5179, "mtime": 1752078894956, "results": "1208", "hashOfConfig": "902"}, {"size": 4986, "mtime": 1752078894956, "results": "1209", "hashOfConfig": "902"}, {"size": 4326, "mtime": 1752078894972, "results": "1210", "hashOfConfig": "902"}, {"size": 411, "mtime": 1752078894988, "results": "1211", "hashOfConfig": "902"}, {"size": 2337, "mtime": 1752078894974, "results": "1212", "hashOfConfig": "902"}, {"size": 4822, "mtime": 1752078894975, "results": "1213", "hashOfConfig": "902"}, {"size": 4706, "mtime": 1752078894975, "results": "1214", "hashOfConfig": "902"}, {"size": 4957, "mtime": 1752078894976, "results": "1215", "hashOfConfig": "902"}, {"size": 3841, "mtime": 1752078894977, "results": "1216", "hashOfConfig": "902"}, {"size": 18438, "mtime": 1752078894978, "results": "1217", "hashOfConfig": "902"}, {"size": 1266, "mtime": 1752078894978, "results": "1218", "hashOfConfig": "902"}, {"size": 8177, "mtime": 1752078894979, "results": "1219", "hashOfConfig": "902"}, {"size": 1581, "mtime": 1752078894980, "results": "1220", "hashOfConfig": "902"}, {"size": 1343, "mtime": 1752080815937, "results": "1221", "hashOfConfig": "902"}, {"size": 1799, "mtime": 1752078894980, "results": "1222", "hashOfConfig": "902"}, {"size": 6966, "mtime": 1752078894981, "results": "1223", "hashOfConfig": "902"}, {"size": 295, "mtime": 1752078894981, "results": "1224", "hashOfConfig": "902"}, {"size": 3916, "mtime": 1752078894982, "results": "1225", "hashOfConfig": "902"}, {"size": 5877, "mtime": 1752078894983, "results": "1226", "hashOfConfig": "902"}, {"size": 18346, "mtime": 1752078894983, "results": "1227", "hashOfConfig": "902"}, {"size": 897, "mtime": 1752078894983, "results": "1228", "hashOfConfig": "902"}, {"size": 3936, "mtime": 1752078894983, "results": "1229", "hashOfConfig": "902"}, {"size": 23617, "mtime": 1752078894983, "results": "1230", "hashOfConfig": "902"}, {"size": 3305, "mtime": 1752078894983, "results": "1231", "hashOfConfig": "902"}, {"size": 3489, "mtime": 1752078894983, "results": "1232", "hashOfConfig": "902"}, {"size": 5456, "mtime": 1752078894988, "results": "1233", "hashOfConfig": "902"}, {"size": 1739, "mtime": 1752078894988, "results": "1234", "hashOfConfig": "902"}, {"size": 6529, "mtime": 1752078894988, "results": "1235", "hashOfConfig": "902"}, {"size": 3222, "mtime": 1752078894988, "results": "1236", "hashOfConfig": "902"}, {"size": 5762, "mtime": 1752078894988, "results": "1237", "hashOfConfig": "902"}, {"size": 3984, "mtime": 1752078894988, "results": "1238", "hashOfConfig": "902"}, {"size": 134, "mtime": 1752078894988, "results": "1239", "hashOfConfig": "902"}, {"size": 848, "mtime": 1752078894988, "results": "1240", "hashOfConfig": "902"}, {"size": 2993, "mtime": 1752078894988, "results": "1241", "hashOfConfig": "902"}, {"size": 5827, "mtime": 1752078894956, "results": "1242", "hashOfConfig": "902"}, {"size": 4054, "mtime": 1752078894988, "results": "1243", "hashOfConfig": "902"}, {"size": 5287, "mtime": 1752078894988, "results": "1244", "hashOfConfig": "902"}, {"size": 3121, "mtime": 1752078894988, "results": "1245", "hashOfConfig": "902"}, {"size": 3264, "mtime": 1752078894988, "results": "1246", "hashOfConfig": "902"}, {"size": 2619, "mtime": 1752078894988, "results": "1247", "hashOfConfig": "902"}, {"size": 3681, "mtime": 1752078894956, "results": "1248", "hashOfConfig": "902"}, {"size": 13610, "mtime": 1752078894988, "results": "1249", "hashOfConfig": "902"}, {"size": 5286, "mtime": 1752080815937, "results": "1250", "hashOfConfig": "902"}, {"size": 4433, "mtime": 1752078895003, "results": "1251", "hashOfConfig": "902"}, {"size": 5822, "mtime": 1752078894988, "results": "1252", "hashOfConfig": "902"}, {"size": 5776, "mtime": 1752078894988, "results": "1253", "hashOfConfig": "902"}, {"size": 13788, "mtime": 1752078894988, "results": "1254", "hashOfConfig": "902"}, {"size": 9337, "mtime": 1752078895003, "results": "1255", "hashOfConfig": "902"}, {"size": 3219, "mtime": 1752078894988, "results": "1256", "hashOfConfig": "902"}, {"size": 3306, "mtime": 1752078895003, "results": "1257", "hashOfConfig": "902"}, {"size": 14293, "mtime": 1752078895003, "results": "1258", "hashOfConfig": "902"}, {"size": 827, "mtime": 1752078895003, "results": "1259", "hashOfConfig": "902"}, {"size": 5416, "mtime": 1752080815952, "results": "1260", "hashOfConfig": "902"}, {"size": 6730, "mtime": 1752080815952, "results": "1261", "hashOfConfig": "902"}, {"size": 13325, "mtime": 1753253495613, "results": "1262", "hashOfConfig": "902"}, {"size": 13586, "mtime": 1753106163893, "results": "1263", "hashOfConfig": "902"}, {"size": 2735, "mtime": 1752078895003, "results": "1264", "hashOfConfig": "902"}, {"size": 925, "mtime": 1752078895003, "results": "1265", "hashOfConfig": "902"}, {"size": 1213, "mtime": 1752078895003, "results": "1266", "hashOfConfig": "902"}, {"size": 8134, "mtime": 1752078895003, "results": "1267", "hashOfConfig": "902"}, {"size": 957, "mtime": 1752078895003, "results": "1268", "hashOfConfig": "902"}, {"size": 2264, "mtime": 1752078895003, "results": "1269", "hashOfConfig": "902"}, {"size": 1677, "mtime": 1752078895003, "results": "1270", "hashOfConfig": "902"}, {"size": 1034, "mtime": 1752078895003, "results": "1271", "hashOfConfig": "902"}, {"size": 5544, "mtime": 1752078895003, "results": "1272", "hashOfConfig": "902"}, {"size": 2483, "mtime": 1752078895019, "results": "1273", "hashOfConfig": "902"}, {"size": 1092, "mtime": 1752078895019, "results": "1274", "hashOfConfig": "902"}, {"size": 4532, "mtime": 1752078895019, "results": "1275", "hashOfConfig": "902"}, {"size": 6920, "mtime": 1752080815952, "results": "1276", "hashOfConfig": "902"}, {"size": 3574, "mtime": 1752080815952, "results": "1277", "hashOfConfig": "902"}, {"size": 794, "mtime": 1752078895019, "results": "1278", "hashOfConfig": "902"}, {"size": 1902, "mtime": 1752078895019, "results": "1279", "hashOfConfig": "902"}, {"size": 1420, "mtime": 1752078895023, "results": "1280", "hashOfConfig": "902"}, {"size": 24194, "mtime": 1752080815952, "results": "1281", "hashOfConfig": "902"}, {"size": 555, "mtime": 1752078895023, "results": "1282", "hashOfConfig": "902"}, {"size": 4100, "mtime": 1752078895023, "results": "1283", "hashOfConfig": "902"}, {"size": 15578, "mtime": 1752078895023, "results": "1284", "hashOfConfig": "902"}, {"size": 3228, "mtime": 1752078895023, "results": "1285", "hashOfConfig": "902"}, {"size": 3514, "mtime": 1752078895023, "results": "1286", "hashOfConfig": "902"}, {"size": 23175, "mtime": 1752078895023, "results": "1287", "hashOfConfig": "902"}, {"size": 2060, "mtime": 1752078895023, "results": "1288", "hashOfConfig": "902"}, {"size": 16492, "mtime": 1752078895023, "results": "1289", "hashOfConfig": "902"}, {"size": 1149, "mtime": 1752078895023, "results": "1290", "hashOfConfig": "902"}, {"size": 3631, "mtime": 1752078895023, "results": "1291", "hashOfConfig": "902"}, {"size": 1859, "mtime": 1752078895023, "results": "1292", "hashOfConfig": "902"}, {"size": 4207, "mtime": 1752078895023, "results": "1293", "hashOfConfig": "902"}, {"size": 5060, "mtime": 1752078895023, "results": "1294", "hashOfConfig": "902"}, {"size": 3993, "mtime": 1752078895023, "results": "1295", "hashOfConfig": "902"}, {"size": 3872, "mtime": 1752078895023, "results": "1296", "hashOfConfig": "902"}, {"size": 1420, "mtime": 1752078895023, "results": "1297", "hashOfConfig": "902"}, {"size": 4730, "mtime": 1752078895035, "results": "1298", "hashOfConfig": "902"}, {"size": 5956, "mtime": 1752078895035, "results": "1299", "hashOfConfig": "902"}, {"size": 244, "mtime": 1752078895035, "results": "1300", "hashOfConfig": "902"}, {"size": 671, "mtime": 1752080815952, "results": "1301", "hashOfConfig": "902"}, {"size": 9220, "mtime": 1752078895035, "results": "1302", "hashOfConfig": "902"}, {"size": 9703, "mtime": 1752080815952, "results": "1303", "hashOfConfig": "902"}, {"size": 10536, "mtime": 1752080815952, "results": "1304", "hashOfConfig": "902"}, {"size": 12559, "mtime": 1752078895035, "results": "1305", "hashOfConfig": "902"}, {"size": 2402, "mtime": 1752080815952, "results": "1306", "hashOfConfig": "902"}, {"size": 1517, "mtime": 1752078895003, "results": "1307", "hashOfConfig": "902"}, {"size": 1951, "mtime": 1752078895003, "results": "1308", "hashOfConfig": "902"}, {"size": 4017, "mtime": 1752078895035, "results": "1309", "hashOfConfig": "902"}, {"size": 3456, "mtime": 1752078895035, "results": "1310", "hashOfConfig": "902"}, {"size": 5757, "mtime": 1753198511303, "results": "1311", "hashOfConfig": "902"}, {"size": 4833, "mtime": 1752078895035, "results": "1312", "hashOfConfig": "902"}, {"size": 3938, "mtime": 1752078895035, "results": "1313", "hashOfConfig": "902"}, {"size": 5522, "mtime": 1752078895035, "results": "1314", "hashOfConfig": "902"}, {"size": 5183, "mtime": 1752078895035, "results": "1315", "hashOfConfig": "902"}, {"size": 7170, "mtime": 1752078895035, "results": "1316", "hashOfConfig": "902"}, {"size": 8695, "mtime": 1752078895051, "results": "1317", "hashOfConfig": "902"}, {"size": 1127, "mtime": 1752078895051, "results": "1318", "hashOfConfig": "902"}, {"size": 1607, "mtime": 1752078895035, "results": "1319", "hashOfConfig": "902"}, {"size": 2053, "mtime": 1752078895051, "results": "1320", "hashOfConfig": "902"}, {"size": 1135, "mtime": 1752078895051, "results": "1321", "hashOfConfig": "902"}, {"size": 9275, "mtime": 1752080815937, "results": "1322", "hashOfConfig": "902"}, {"size": 1655, "mtime": 1752078895051, "results": "1323", "hashOfConfig": "902"}, {"size": 4413, "mtime": 1753253144240, "results": "1324", "hashOfConfig": "902"}, {"size": 1388, "mtime": 1752078895051, "results": "1325", "hashOfConfig": "902"}, {"size": 8153, "mtime": 1752498088570, "results": "1326", "hashOfConfig": "902"}, {"size": 4771, "mtime": 1752485239443, "results": "1327", "hashOfConfig": "902"}, {"size": 4776, "mtime": 1753253192825, "results": "1328", "hashOfConfig": "902"}, {"size": 10897, "mtime": 1753253231970, "results": "1329", "hashOfConfig": "902"}, {"size": 1389, "mtime": 1752596854741, "results": "1330", "hashOfConfig": "902"}, {"size": 3766, "mtime": 1752078895051, "results": "1331", "hashOfConfig": "902"}, {"size": 3999, "mtime": 1752078895051, "results": "1332", "hashOfConfig": "902"}, {"size": 8192, "mtime": 1752078895066, "results": "1333", "hashOfConfig": "902"}, {"size": 4650, "mtime": 1752078895051, "results": "1334", "hashOfConfig": "902"}, {"size": 6600, "mtime": 1752078895051, "results": "1335", "hashOfConfig": "902"}, {"size": 5535, "mtime": 1752078895051, "results": "1336", "hashOfConfig": "902"}, {"size": 6402, "mtime": 1752078895051, "results": "1337", "hashOfConfig": "902"}, {"size": 6618, "mtime": 1752078895051, "results": "1338", "hashOfConfig": "902"}, {"size": 9406, "mtime": 1752078895051, "results": "1339", "hashOfConfig": "902"}, {"size": 4798, "mtime": 1752078895051, "results": "1340", "hashOfConfig": "902"}, {"size": 2182, "mtime": 1752078895066, "results": "1341", "hashOfConfig": "902"}, {"size": 14232, "mtime": 1752078895066, "results": "1342", "hashOfConfig": "902"}, {"size": 1626, "mtime": 1752078895066, "results": "1343", "hashOfConfig": "902"}, {"size": 14197, "mtime": 1752078895066, "results": "1344", "hashOfConfig": "902"}, {"size": 820, "mtime": 1752078895066, "results": "1345", "hashOfConfig": "902"}, {"size": 15316, "mtime": 1752078895066, "results": "1346", "hashOfConfig": "902"}, {"size": 1887, "mtime": 1752078895066, "results": "1347", "hashOfConfig": "902"}, {"size": 13686, "mtime": 1752078895066, "results": "1348", "hashOfConfig": "902"}, {"size": 1905, "mtime": 1752078895066, "results": "1349", "hashOfConfig": "902"}, {"size": 12380, "mtime": 1752078895066, "results": "1350", "hashOfConfig": "902"}, {"size": 1946, "mtime": 1752078895066, "results": "1351", "hashOfConfig": "902"}, {"size": 3999, "mtime": 1752078895066, "results": "1352", "hashOfConfig": "902"}, {"size": 6385, "mtime": 1752078895066, "results": "1353", "hashOfConfig": "902"}, {"size": 9551, "mtime": 1752078895066, "results": "1354", "hashOfConfig": "902"}, {"size": 13651, "mtime": 1752078895066, "results": "1355", "hashOfConfig": "902"}, {"size": 1826, "mtime": 1752078895066, "results": "1356", "hashOfConfig": "902"}, {"size": 1920, "mtime": 1752078895082, "results": "1357", "hashOfConfig": "902"}, {"size": 13936, "mtime": 1752078895082, "results": "1358", "hashOfConfig": "902"}, {"size": 1862, "mtime": 1752078895082, "results": "1359", "hashOfConfig": "902"}, {"size": 13015, "mtime": 1752078895082, "results": "1360", "hashOfConfig": "902"}, {"size": 13703, "mtime": 1752991606206, "results": "1361", "hashOfConfig": "902"}, {"size": 1899, "mtime": 1752078895086, "results": "1362", "hashOfConfig": "902"}, {"size": 11444, "mtime": 1752078895086, "results": "1363", "hashOfConfig": "902"}, {"size": 15279, "mtime": 1752078895086, "results": "1364", "hashOfConfig": "902"}, {"size": 852, "mtime": 1752078895086, "results": "1365", "hashOfConfig": "902"}, {"size": 1743, "mtime": 1753253249492, "results": "1366", "hashOfConfig": "902"}, {"size": 15337, "mtime": 1753253357891, "results": "1367", "hashOfConfig": "902"}, {"size": 759, "mtime": 1752588678090, "results": "1368", "hashOfConfig": "902"}, {"size": 4845, "mtime": 1752078895086, "results": "1369", "hashOfConfig": "902"}, {"size": 2787, "mtime": 1752588678105, "results": "1370", "hashOfConfig": "902"}, {"size": 9997, "mtime": 1752588678105, "results": "1371", "hashOfConfig": "902"}, {"size": 3634, "mtime": 1752078895086, "results": "1372", "hashOfConfig": "902"}, {"size": 10759, "mtime": 1752078895086, "results": "1373", "hashOfConfig": "902"}, {"size": 8359, "mtime": 1752588678105, "results": "1374", "hashOfConfig": "902"}, {"size": 995, "mtime": 1752078895086, "results": "1375", "hashOfConfig": "902"}, {"size": 3185, "mtime": 1752078895098, "results": "1376", "hashOfConfig": "902"}, {"size": 7318, "mtime": 1752078895098, "results": "1377", "hashOfConfig": "902"}, {"size": 4953, "mtime": 1752945992633, "results": "1378", "hashOfConfig": "902"}, {"size": 2913, "mtime": 1752078895099, "results": "1379", "hashOfConfig": "902"}, {"size": 1680, "mtime": 1752213788048, "results": "1380", "hashOfConfig": "902"}, {"size": 586, "mtime": 1752078895100, "results": "1381", "hashOfConfig": "902"}, {"size": 6995, "mtime": 1752078895086, "results": "1382", "hashOfConfig": "902"}, {"size": 1230, "mtime": 1752078895100, "results": "1383", "hashOfConfig": "902"}, {"size": 3561, "mtime": 1752078895100, "results": "1384", "hashOfConfig": "902"}, {"size": 8979, "mtime": 1753033582975, "results": "1385", "hashOfConfig": "902"}, {"size": 5369, "mtime": 1753101910671, "results": "1386", "hashOfConfig": "902"}, {"size": 4342, "mtime": 1753201782171, "results": "1387", "hashOfConfig": "902"}, {"size": 4338, "mtime": 1753201794938, "results": "1388", "hashOfConfig": "902"}, {"size": 7098, "mtime": 1753251129502, "results": "1389", "hashOfConfig": "902"}, {"size": 1375, "mtime": 1753201809508, "results": "1390", "hashOfConfig": "902"}, {"size": 4166, "mtime": 1753201820252, "results": "1391", "hashOfConfig": "902"}, {"size": 4337, "mtime": 1753105360832, "results": "1392", "hashOfConfig": "902"}, {"size": 2787, "mtime": 1753201833930, "results": "1393", "hashOfConfig": "902"}, {"size": 7547, "mtime": 1753201844069, "results": "1394", "hashOfConfig": "902"}, {"size": 1485, "mtime": 1752078895145, "results": "1395", "hashOfConfig": "902"}, {"size": 5051, "mtime": 1753201857717, "results": "1396", "hashOfConfig": "902"}, {"size": 1954, "mtime": 1752078895161, "results": "1397", "hashOfConfig": "902"}, {"size": 4168, "mtime": 1752078895177, "results": "1398", "hashOfConfig": "902"}, {"size": 4297, "mtime": 1753201869253, "results": "1399", "hashOfConfig": "902"}, {"size": 8320, "mtime": 1753201884668, "results": "1400", "hashOfConfig": "902"}, {"size": 6635, "mtime": 1753201900926, "results": "1401", "hashOfConfig": "902"}, {"size": 6411, "mtime": 1753201913192, "results": "1402", "hashOfConfig": "902"}, {"size": 4277, "mtime": 1752078895145, "results": "1403", "hashOfConfig": "902"}, {"size": 3045, "mtime": 1752078895145, "results": "1404", "hashOfConfig": "902"}, {"size": 4119, "mtime": 1753033975155, "results": "1405", "hashOfConfig": "902"}, {"size": 5980, "mtime": 1752078895158, "results": "1406", "hashOfConfig": "902"}, {"size": 2041, "mtime": 1752078895158, "results": "1407", "hashOfConfig": "902"}, {"size": 228, "mtime": 1752078895158, "results": "1408", "hashOfConfig": "902"}, {"size": 7263, "mtime": 1752078895158, "results": "1409", "hashOfConfig": "902"}, {"size": 7147, "mtime": 1752078895161, "results": "1410", "hashOfConfig": "902"}, {"size": 846, "mtime": 1752078895161, "results": "1411", "hashOfConfig": "902"}, {"size": 3451, "mtime": 1752078895161, "results": "1412", "hashOfConfig": "902"}, {"size": 6345, "mtime": 1753101910671, "results": "1413", "hashOfConfig": "902"}, {"size": 4949, "mtime": 1753034133271, "results": "1414", "hashOfConfig": "902"}, {"size": 7441, "mtime": 1752078895161, "results": "1415", "hashOfConfig": "902"}, {"size": 4056, "mtime": 1753032433447, "results": "1416", "hashOfConfig": "902"}, {"size": 3193, "mtime": 1752078895161, "results": "1417", "hashOfConfig": "902"}, {"size": 9321, "mtime": 1753101910685, "results": "1418", "hashOfConfig": "902"}, {"size": 3720, "mtime": 1752078895178, "results": "1419", "hashOfConfig": "902"}, {"size": 3584, "mtime": 1752078895178, "results": "1420", "hashOfConfig": "902"}, {"size": 3246, "mtime": 1752078895178, "results": "1421", "hashOfConfig": "902"}, {"size": 1961, "mtime": 1752078895178, "results": "1422", "hashOfConfig": "902"}, {"size": 2418, "mtime": 1752078895178, "results": "1423", "hashOfConfig": "902"}, {"size": 3537, "mtime": 1752078895178, "results": "1424", "hashOfConfig": "902"}, {"size": 5109, "mtime": 1753101910685, "results": "1425", "hashOfConfig": "902"}, {"size": 9454, "mtime": 1752078895193, "results": "1426", "hashOfConfig": "902"}, {"size": 2906, "mtime": 1752078895195, "results": "1427", "hashOfConfig": "902"}, {"size": 777, "mtime": 1753203644668, "results": "1428", "hashOfConfig": "902"}, {"size": 2508, "mtime": 1752078895195, "results": "1429", "hashOfConfig": "902"}, {"size": 852, "mtime": 1752078895240, "results": "1430", "hashOfConfig": "902"}, {"size": 4790, "mtime": 1752078895240, "results": "1431", "hashOfConfig": "902"}, {"size": 8785, "mtime": 1752696267110, "results": "1432", "hashOfConfig": "902"}, {"size": 696, "mtime": 1752078895240, "results": "1433", "hashOfConfig": "902"}, {"size": 3247, "mtime": 1752078895240, "results": "1434", "hashOfConfig": "902"}, {"size": 16852, "mtime": 1752078895240, "results": "1435", "hashOfConfig": "902"}, {"size": 6060, "mtime": 1752078895240, "results": "1436", "hashOfConfig": "902"}, {"size": 605, "mtime": 1752078895240, "results": "1437", "hashOfConfig": "902"}, {"size": 9700, "mtime": 1752686119805, "results": "1438", "hashOfConfig": "902"}, {"size": 706, "mtime": 1752078895240, "results": "1439", "hashOfConfig": "902"}, {"size": 1299, "mtime": 1752078895256, "results": "1440", "hashOfConfig": "902"}, {"size": 918, "mtime": 1752078895256, "results": "1441", "hashOfConfig": "902"}, {"size": 1155, "mtime": 1752078895256, "results": "1442", "hashOfConfig": "902"}, {"size": 1050, "mtime": 1752078895256, "results": "1443", "hashOfConfig": "902"}, {"size": 573, "mtime": 1752078895256, "results": "1444", "hashOfConfig": "902"}, {"size": 1580, "mtime": 1752078895256, "results": "1445", "hashOfConfig": "902"}, {"size": 766, "mtime": 1752078895256, "results": "1446", "hashOfConfig": "902"}, {"size": 3070, "mtime": 1752078895240, "results": "1447", "hashOfConfig": "902"}, {"size": 1187, "mtime": 1752078895240, "results": "1448", "hashOfConfig": "902"}, {"size": 560, "mtime": 1752078895240, "results": "1449", "hashOfConfig": "902"}, {"size": 9942, "mtime": 1753204368321, "results": "1450", "hashOfConfig": "902"}, {"size": 387, "mtime": 1752078895240, "results": "1451", "hashOfConfig": "902"}, {"size": 13488, "mtime": 1752078895240, "results": "1452", "hashOfConfig": "902"}, {"size": 1007, "mtime": 1752078895240, "results": "1453", "hashOfConfig": "902"}, {"size": 10905, "mtime": 1752078895240, "results": "1454", "hashOfConfig": "902"}, {"size": 1695, "mtime": 1752078895240, "results": "1455", "hashOfConfig": "902"}, {"size": 928, "mtime": 1752078895256, "results": "1456", "hashOfConfig": "902"}, {"size": 11881, "mtime": 1752078895256, "results": "1457", "hashOfConfig": "902"}, {"size": 5051, "mtime": 1752078895256, "results": "1458", "hashOfConfig": "902"}, {"size": 12521, "mtime": 1752078895256, "results": "1459", "hashOfConfig": "902"}, {"size": 8293, "mtime": 1752078895256, "results": "1460", "hashOfConfig": "902"}, {"size": 8100, "mtime": 1752078895256, "results": "1461", "hashOfConfig": "902"}, {"size": 468, "mtime": 1752078895240, "results": "1462", "hashOfConfig": "902"}, {"size": 8404, "mtime": 1752078895256, "results": "1463", "hashOfConfig": "902"}, {"size": 420, "mtime": 1752078895271, "results": "1464", "hashOfConfig": "902"}, {"size": 8986, "mtime": 1752842665103, "results": "1465", "hashOfConfig": "902"}, {"size": 2536, "mtime": 1752078895256, "results": "1466", "hashOfConfig": "902"}, {"size": 3160, "mtime": 1752078895256, "results": "1467", "hashOfConfig": "902"}, {"size": 2764, "mtime": 1752078895256, "results": "1468", "hashOfConfig": "902"}, {"size": 2336, "mtime": 1752078895256, "results": "1469", "hashOfConfig": "902"}, {"size": 15343, "mtime": 1752078895271, "results": "1470", "hashOfConfig": "902"}, {"size": 2399, "mtime": 1752078895271, "results": "1471", "hashOfConfig": "902"}, {"size": 2549, "mtime": 1752078895271, "results": "1472", "hashOfConfig": "902"}, {"size": 532, "mtime": 1752078895292, "results": "1473", "hashOfConfig": "902"}, {"size": 7698, "mtime": 1752842665103, "results": "1474", "hashOfConfig": "902"}, {"size": 2870, "mtime": 1752525255000, "results": "1475", "hashOfConfig": "902"}, {"size": 3578, "mtime": 1752525237933, "results": "1476", "hashOfConfig": "902"}, {"size": 3654, "mtime": 1752078895271, "results": "1477", "hashOfConfig": "902"}, {"size": 2711, "mtime": 1752078895271, "results": "1478", "hashOfConfig": "902"}, {"size": 5073, "mtime": 1752078895240, "results": "1479", "hashOfConfig": "902"}, {"size": 468, "mtime": 1752078895293, "results": "1480", "hashOfConfig": "902"}, {"size": 2384, "mtime": 1752078895293, "results": "1481", "hashOfConfig": "902"}, {"size": 2875, "mtime": 1752078895293, "results": "1482", "hashOfConfig": "902"}, {"size": 6770, "mtime": 1753069585595, "results": "1483", "hashOfConfig": "902"}, {"size": 3476, "mtime": 1752078895293, "results": "1484", "hashOfConfig": "902"}, {"size": 2968, "mtime": 1752078895293, "results": "1485", "hashOfConfig": "902"}, {"size": 6314, "mtime": 1753253629148, "results": "1486", "hashOfConfig": "902"}, {"size": 543, "mtime": 1752078895293, "results": "1487", "hashOfConfig": "902"}, {"size": 881, "mtime": 1752078895303, "results": "1488", "hashOfConfig": "902"}, {"size": 563, "mtime": 1752078895303, "results": "1489", "hashOfConfig": "902"}, {"size": 5611, "mtime": 1752078895303, "results": "1490", "hashOfConfig": "902"}, {"size": 2417, "mtime": 1752078895303, "results": "1491", "hashOfConfig": "902"}, {"size": 2958, "mtime": 1752078895303, "results": "1492", "hashOfConfig": "902"}, {"size": 140, "mtime": 1752078895303, "results": "1493", "hashOfConfig": "902"}, {"size": 5043, "mtime": 1752078895303, "results": "1494", "hashOfConfig": "902"}, {"size": 3818, "mtime": 1752078895303, "results": "1495", "hashOfConfig": "902"}, {"size": 6246, "mtime": 1752078895303, "results": "1496", "hashOfConfig": "902"}, {"size": 6801, "mtime": 1752078895303, "results": "1497", "hashOfConfig": "902"}, {"size": 2232, "mtime": 1752078895303, "results": "1498", "hashOfConfig": "902"}, {"size": 1235, "mtime": 1752078895303, "results": "1499", "hashOfConfig": "902"}, {"size": 2119, "mtime": 1752078895293, "results": "1500", "hashOfConfig": "902"}, {"size": 3747, "mtime": 1752078895293, "results": "1501", "hashOfConfig": "902"}, {"size": 2394, "mtime": 1752078895303, "results": "1502", "hashOfConfig": "902"}, {"size": 431, "mtime": 1752078895303, "results": "1503", "hashOfConfig": "902"}, {"size": 1606, "mtime": 1752078895303, "results": "1504", "hashOfConfig": "902"}, {"size": 3088, "mtime": 1753159210833, "results": "1505", "hashOfConfig": "902"}, {"size": 2689, "mtime": 1752078895303, "results": "1506", "hashOfConfig": "902"}, {"size": 1735, "mtime": 1752080815962, "results": "1507", "hashOfConfig": "902"}, {"size": 1639, "mtime": 1752080815962, "results": "1508", "hashOfConfig": "902"}, {"size": 4950, "mtime": 1753253514054, "results": "1509", "hashOfConfig": "902"}, {"size": 2860, "mtime": 1752078895114, "results": "1510", "hashOfConfig": "902"}, {"size": 8058, "mtime": 1752078895100, "results": "1511", "hashOfConfig": "902"}, {"size": 19772, "mtime": 1752080815952, "results": "1512", "hashOfConfig": "902"}, {"size": 6916, "mtime": 1752078895100, "results": "1513", "hashOfConfig": "902"}, {"size": 5552, "mtime": 1752078895100, "results": "1514", "hashOfConfig": "902"}, {"size": 2806, "mtime": 1752078895100, "results": "1515", "hashOfConfig": "902"}, {"size": 8145, "mtime": 1753124646698, "results": "1516", "hashOfConfig": "902"}, {"size": 13835, "mtime": 1753124664204, "results": "1517", "hashOfConfig": "902"}, {"size": 6718, "mtime": 1752078895111, "results": "1518", "hashOfConfig": "902"}, {"size": 5759, "mtime": 1753124680800, "results": "1519", "hashOfConfig": "902"}, {"size": 3064, "mtime": 1752078895114, "results": "1520", "hashOfConfig": "902"}, {"size": 1050, "mtime": 1752078895114, "results": "1521", "hashOfConfig": "902"}, {"size": 22169, "mtime": 1752078895114, "results": "1522", "hashOfConfig": "902"}, {"size": 4436, "mtime": 1752078895114, "results": "1523", "hashOfConfig": "902"}, {"size": 8840, "mtime": 1753124698936, "results": "1524", "hashOfConfig": "902"}, {"size": 4551, "mtime": 1753101910657, "results": "1525", "hashOfConfig": "902"}, {"size": 1237, "mtime": 1752078895114, "results": "1526", "hashOfConfig": "902"}, {"size": 456, "mtime": 1752078895114, "results": "1527", "hashOfConfig": "902"}, {"size": 11283, "mtime": 1753253900186, "results": "1528", "hashOfConfig": "902"}, {"size": 15806, "mtime": 1753106539818, "results": "1529", "hashOfConfig": "902"}, {"size": 1555, "mtime": 1752078895123, "results": "1530", "hashOfConfig": "902"}, {"size": 12447, "mtime": 1752078895123, "results": "1531", "hashOfConfig": "902"}, {"size": 3075, "mtime": 1752078895123, "results": "1532", "hashOfConfig": "902"}, {"size": 2658, "mtime": 1752078895123, "results": "1533", "hashOfConfig": "902"}, {"size": 4697, "mtime": 1752078895123, "results": "1534", "hashOfConfig": "902"}, {"size": 22440, "mtime": 1753105300361, "results": "1535", "hashOfConfig": "902"}, {"size": 3730, "mtime": 1752078895123, "results": "1536", "hashOfConfig": "902"}, {"size": 12465, "mtime": 1753105319939, "results": "1537", "hashOfConfig": "902"}, {"size": 3044, "mtime": 1752078895129, "results": "1538", "hashOfConfig": "902"}, {"size": 5097, "mtime": 1752078895129, "results": "1539", "hashOfConfig": "902"}, {"size": 10225, "mtime": 1753101910657, "results": "1540", "hashOfConfig": "902"}, {"size": 2253, "mtime": 1752078895123, "results": "1541", "hashOfConfig": "902"}, {"size": 3160, "mtime": 1753124712805, "results": "1542", "hashOfConfig": "902"}, {"size": 5326, "mtime": 1752080815962, "results": "1543", "hashOfConfig": "902"}, {"size": 5995, "mtime": 1752078895316, "results": "1544", "hashOfConfig": "902"}, {"size": 3946, "mtime": 1752078895319, "results": "1545", "hashOfConfig": "902"}, {"size": 8264, "mtime": 1752080815962, "results": "1546", "hashOfConfig": "902"}, {"size": 3007, "mtime": 1752078895320, "results": "1547", "hashOfConfig": "902"}, {"size": 4189, "mtime": 1752078895321, "results": "1548", "hashOfConfig": "902"}, {"size": 9778, "mtime": 1752078895321, "results": "1549", "hashOfConfig": "902"}, {"size": 10169, "mtime": 1752080815962, "results": "1550", "hashOfConfig": "902"}, {"size": 10217, "mtime": 1752080815971, "results": "1551", "hashOfConfig": "902"}, {"size": 6291, "mtime": 1752078895323, "results": "1552", "hashOfConfig": "902"}, {"size": 7264, "mtime": 1752078895323, "results": "1553", "hashOfConfig": "902"}, {"size": 7194, "mtime": 1752078895323, "results": "1554", "hashOfConfig": "902"}, {"size": 3629, "mtime": 1752682891985, "results": "1555", "hashOfConfig": "902"}, {"size": 8662, "mtime": 1752078895323, "results": "1556", "hashOfConfig": "902"}, {"size": 4435, "mtime": 1753101910685, "results": "1557", "hashOfConfig": "902"}, {"size": 19439, "mtime": 1752078895335, "results": "1558", "hashOfConfig": "902"}, {"size": 7315, "mtime": 1752078895335, "results": "1559", "hashOfConfig": "902"}, {"size": 8073, "mtime": 1752078895335, "results": "1560", "hashOfConfig": "902"}, {"size": 1910, "mtime": 1752078895335, "results": "1561", "hashOfConfig": "902"}, {"size": 8788, "mtime": 1752080815971, "results": "1562", "hashOfConfig": "902"}, {"size": 459, "mtime": 1752683108071, "results": "1563", "hashOfConfig": "902"}, {"size": 919, "mtime": 1752078895323, "results": "1564", "hashOfConfig": "902"}, {"size": 25953, "mtime": 1753106301218, "results": "1565", "hashOfConfig": "902"}, {"size": 3391, "mtime": 1752696314876, "results": "1566", "hashOfConfig": "902"}, {"size": 3447, "mtime": 1752078895323, "results": "1567", "hashOfConfig": "902"}, {"size": 41540, "mtime": 1752925036472, "results": "1568", "hashOfConfig": "902"}, {"size": 18331, "mtime": 1752925036472, "results": "1569", "hashOfConfig": "902"}, {"size": 2240, "mtime": 1753159233453, "results": "1570", "hashOfConfig": "902"}, {"size": 935, "mtime": 1752078895335, "results": "1571", "hashOfConfig": "902"}, {"size": 5951, "mtime": 1753101910685, "results": "1572", "hashOfConfig": "902"}, {"size": 1935, "mtime": 1752078895335, "results": "1573", "hashOfConfig": "902"}, {"size": 625, "mtime": 1752078895335, "results": "1574", "hashOfConfig": "902"}, {"size": 7782, "mtime": 1753253554050, "results": "1575", "hashOfConfig": "902"}, {"size": 8672, "mtime": 1753253602044, "results": "1576", "hashOfConfig": "902"}, {"size": 12719, "mtime": 1752078895335, "results": "1577", "hashOfConfig": "902"}, {"size": 7671, "mtime": 1752078895335, "results": "1578", "hashOfConfig": "902"}, {"size": 5281, "mtime": 1752078895335, "results": "1579", "hashOfConfig": "902"}, {"size": 5525, "mtime": 1753204880155, "results": "1580", "hashOfConfig": "902"}, {"size": 4400, "mtime": 1752078895335, "results": "1581", "hashOfConfig": "902"}, {"size": 5366, "mtime": 1753204982635, "results": "1582", "hashOfConfig": "902"}, {"size": 638, "mtime": 1752078895335, "results": "1583", "hashOfConfig": "902"}, {"size": 2119, "mtime": 1752078895335, "results": "1584", "hashOfConfig": "902"}, {"size": 4021, "mtime": 1752078895335, "results": "1585", "hashOfConfig": "902"}, {"size": 1680, "mtime": 1752078895335, "results": "1586", "hashOfConfig": "902"}, {"size": 1150, "mtime": 1752078895335, "results": "1587", "hashOfConfig": "902"}, {"size": 1677, "mtime": 1752078895335, "results": "1588", "hashOfConfig": "902"}, {"size": 2466, "mtime": 1752078895335, "results": "1589", "hashOfConfig": "902"}, {"size": 2140, "mtime": 1752498259457, "results": "1590", "hashOfConfig": "902"}, {"size": 2995, "mtime": 1752078895335, "results": "1591", "hashOfConfig": "902"}, {"size": 2081, "mtime": 1752078895350, "results": "1592", "hashOfConfig": "902"}, {"size": 5798, "mtime": 1752078895350, "results": "1593", "hashOfConfig": "902"}, {"size": 2814, "mtime": 1752078895350, "results": "1594", "hashOfConfig": "902"}, {"size": 10137, "mtime": 1752078895350, "results": "1595", "hashOfConfig": "902"}, {"size": 1258, "mtime": 1752078895350, "results": "1596", "hashOfConfig": "902"}, {"size": 833, "mtime": 1752078895350, "results": "1597", "hashOfConfig": "902"}, {"size": 4833, "mtime": 1752078895350, "results": "1598", "hashOfConfig": "902"}, {"size": 4119, "mtime": 1752078895350, "results": "1599", "hashOfConfig": "902"}, {"size": 8541, "mtime": 1752078895350, "results": "1600", "hashOfConfig": "902"}, {"size": 3926, "mtime": 1752078895350, "results": "1601", "hashOfConfig": "902"}, {"size": 2331, "mtime": 1752078895350, "results": "1602", "hashOfConfig": "902"}, {"size": 988, "mtime": 1752078895350, "results": "1603", "hashOfConfig": "902"}, {"size": 635, "mtime": 1752078895350, "results": "1604", "hashOfConfig": "902"}, {"size": 6792, "mtime": 1752078895350, "results": "1605", "hashOfConfig": "902"}, {"size": 3090, "mtime": 1752078895350, "results": "1606", "hashOfConfig": "902"}, {"size": 1683, "mtime": 1752078895350, "results": "1607", "hashOfConfig": "902"}, {"size": 771, "mtime": 1752078895350, "results": "1608", "hashOfConfig": "902"}, {"size": 1511, "mtime": 1752078895350, "results": "1609", "hashOfConfig": "902"}, {"size": 8203, "mtime": 1752078895350, "results": "1610", "hashOfConfig": "902"}, {"size": 1703, "mtime": 1752078895350, "results": "1611", "hashOfConfig": "902"}, {"size": 2479, "mtime": 1752078895350, "results": "1612", "hashOfConfig": "902"}, {"size": 6438, "mtime": 1752078895350, "results": "1613", "hashOfConfig": "902"}, {"size": 732, "mtime": 1752078895350, "results": "1614", "hashOfConfig": "902"}, {"size": 4244, "mtime": 1752078895350, "results": "1615", "hashOfConfig": "902"}, {"size": 22737, "mtime": 1752078895366, "results": "1616", "hashOfConfig": "902"}, {"size": 289, "mtime": 1752078895366, "results": "1617", "hashOfConfig": "902"}, {"size": 2064, "mtime": 1752078895366, "results": "1618", "hashOfConfig": "902"}, {"size": 589, "mtime": 1752078895366, "results": "1619", "hashOfConfig": "902"}, {"size": 1208, "mtime": 1752078895366, "results": "1620", "hashOfConfig": "902"}, {"size": 2564, "mtime": 1752078895366, "results": "1621", "hashOfConfig": "902"}, {"size": 2035, "mtime": 1752078895366, "results": "1622", "hashOfConfig": "902"}, {"size": 777, "mtime": 1752078895366, "results": "1623", "hashOfConfig": "902"}, {"size": 3457, "mtime": 1752078895366, "results": "1624", "hashOfConfig": "902"}, {"size": 1952, "mtime": 1752078895366, "results": "1625", "hashOfConfig": "902"}, {"size": 145, "mtime": 1752078895366, "results": "1626", "hashOfConfig": "902"}, {"size": 831, "mtime": 1752078895366, "results": "1627", "hashOfConfig": "902"}, {"size": 16844, "mtime": 1753105651907, "results": "1628", "hashOfConfig": "902"}, {"size": 3614, "mtime": 1753105671578, "results": "1629", "hashOfConfig": "902"}, {"size": 2385, "mtime": 1753247176832, "results": "1630", "hashOfConfig": "902"}, {"size": 6785, "mtime": 1753247176840, "results": "1631", "hashOfConfig": "902"}, {"size": 668, "mtime": 1752078895397, "results": "1632", "hashOfConfig": "902"}, {"size": 3684, "mtime": 1752080815978, "results": "1633", "hashOfConfig": "902"}, {"size": 2757, "mtime": 1753198511306, "results": "1634", "hashOfConfig": "902"}, {"size": 5515, "mtime": 1753070785826, "results": "1635", "hashOfConfig": "902"}, {"size": 1819, "mtime": 1753070680799, "results": "1636", "hashOfConfig": "902"}, {"size": 832, "mtime": 1753070800480, "results": "1637", "hashOfConfig": "902"}, {"size": 2364, "mtime": 1752078895397, "results": "1638", "hashOfConfig": "902"}, {"size": 10155, "mtime": 1753105726378, "results": "1639", "hashOfConfig": "902"}, {"size": 178, "mtime": 1752078895397, "results": "1640", "hashOfConfig": "902"}, {"size": 8054, "mtime": 1752925036485, "results": "1641", "hashOfConfig": "902"}, {"size": 198, "mtime": 1752078895397, "results": "1642", "hashOfConfig": "902"}, {"size": 6353, "mtime": 1752078895413, "results": "1643", "hashOfConfig": "902"}, {"size": 9338, "mtime": 1753253646517, "results": "1644", "hashOfConfig": "902"}, {"size": 17868, "mtime": 1752080815978, "results": "1645", "hashOfConfig": "902"}, {"size": 1712, "mtime": 1753101910685, "results": "1646", "hashOfConfig": "902"}, {"size": 6536, "mtime": 1753069810361, "results": "1647", "hashOfConfig": "902"}, {"size": 9989, "mtime": 1752925036485, "results": "1648", "hashOfConfig": "902"}, {"size": 3278, "mtime": 1753101910685, "results": "1649", "hashOfConfig": "902"}, {"size": 651, "mtime": 1752078895413, "results": "1650", "hashOfConfig": "902"}, {"size": 878, "mtime": 1752078895413, "results": "1651", "hashOfConfig": "902"}, {"size": 10666, "mtime": 1753253666886, "results": "1652", "hashOfConfig": "902"}, {"size": 1318, "mtime": 1752078895413, "results": "1653", "hashOfConfig": "902"}, {"size": 152, "mtime": 1752078895413, "results": "1654", "hashOfConfig": "902"}, {"size": 1407, "mtime": 1753070813299, "results": "1655", "hashOfConfig": "902"}, {"size": 3482, "mtime": 1753106562909, "results": "1656", "hashOfConfig": "902"}, {"size": 1682, "mtime": 1753253684571, "results": "1657", "hashOfConfig": "902"}, {"size": 4579, "mtime": 1753253702674, "results": "1658", "hashOfConfig": "902"}, {"size": 8421, "mtime": 1753199262990, "results": "1659", "hashOfConfig": "902"}, {"size": 2424, "mtime": 1753101910697, "results": "1660", "hashOfConfig": "902"}, {"size": 3387, "mtime": 1753101910697, "results": "1661", "hashOfConfig": "902"}, {"size": 3822, "mtime": 1753030756833, "results": "1662", "hashOfConfig": "902"}, {"size": 3630, "mtime": 1753030782254, "results": "1663", "hashOfConfig": "902"}, {"size": 6648, "mtime": 1753101910697, "results": "1664", "hashOfConfig": "902"}, {"size": 5729, "mtime": 1753253720515, "results": "1665", "hashOfConfig": "902"}, {"size": 9159, "mtime": 1752078895423, "results": "1666", "hashOfConfig": "902"}, {"size": 22689, "mtime": 1753253739329, "results": "1667", "hashOfConfig": "902"}, {"size": 13447, "mtime": 1753025305927, "results": "1668", "hashOfConfig": "902"}, {"size": 2009, "mtime": 1752078895429, "results": "1669", "hashOfConfig": "902"}, {"size": 7106, "mtime": 1753025332125, "results": "1670", "hashOfConfig": "902"}, {"size": 15438, "mtime": 1753101910697, "results": "1671", "hashOfConfig": "902"}, {"size": 700, "mtime": 1752078895429, "results": "1672", "hashOfConfig": "902"}, {"size": 7366, "mtime": 1753025372961, "results": "1673", "hashOfConfig": "902"}, {"size": 2827, "mtime": 1753032425142, "results": "1674", "hashOfConfig": "902"}, {"size": 20026, "mtime": 1753025850817, "results": "1675", "hashOfConfig": "902"}, {"size": 391, "mtime": 1752078895429, "results": "1676", "hashOfConfig": "902"}, {"size": 11167, "mtime": 1752078895429, "results": "1677", "hashOfConfig": "902"}, {"size": 14172, "mtime": 1753253759281, "results": "1678", "hashOfConfig": "902"}, {"size": 675, "mtime": 1752078895429, "results": "1679", "hashOfConfig": "902"}, {"size": 10213, "mtime": 1753070867175, "results": "1680", "hashOfConfig": "902"}, {"size": 3534, "mtime": 1752078895423, "results": "1681", "hashOfConfig": "902"}, {"size": 651, "mtime": 1752078895429, "results": "1682", "hashOfConfig": "902"}, {"size": 1264, "mtime": 1752078895429, "results": "1683", "hashOfConfig": "902"}, {"size": 6440, "mtime": 1752078895429, "results": "1684", "hashOfConfig": "902"}, {"size": 7233, "mtime": 1752078895429, "results": "1685", "hashOfConfig": "902"}, {"size": 22742, "mtime": 1752078895429, "results": "1686", "hashOfConfig": "902"}, {"size": 20955, "mtime": 1752078895429, "results": "1687", "hashOfConfig": "902"}, {"size": 2503, "mtime": 1752078895429, "results": "1688", "hashOfConfig": "902"}, {"size": 17737, "mtime": 1752078895444, "results": "1689", "hashOfConfig": "902"}, {"size": 1604, "mtime": 1752078895444, "results": "1690", "hashOfConfig": "902"}, {"size": 4395, "mtime": 1752078895444, "results": "1691", "hashOfConfig": "902"}, {"size": 3461, "mtime": 1752078895397, "results": "1692", "hashOfConfig": "902"}, {"size": 25034, "mtime": 1752078895444, "results": "1693", "hashOfConfig": "902"}, {"size": 2857, "mtime": 1752078895444, "results": "1694", "hashOfConfig": "902"}, {"size": 2531, "mtime": 1752078895444, "results": "1695", "hashOfConfig": "902"}, {"size": 1791, "mtime": 1752078895444, "results": "1696", "hashOfConfig": "902"}, {"size": 4594, "mtime": 1752078895444, "results": "1697", "hashOfConfig": "902"}, {"size": 204, "mtime": 1752078895444, "results": "1698", "hashOfConfig": "902"}, {"size": 1354, "mtime": 1752078895444, "results": "1699", "hashOfConfig": "902"}, {"size": 2050, "mtime": 1752078895444, "results": "1700", "hashOfConfig": "902"}, {"size": 6896, "mtime": 1752078895444, "results": "1701", "hashOfConfig": "902"}, {"size": 5462, "mtime": 1752078895444, "results": "1702", "hashOfConfig": "902"}, {"size": 413, "mtime": 1752078895444, "results": "1703", "hashOfConfig": "902"}, {"size": 6636, "mtime": 1752078895444, "results": "1704", "hashOfConfig": "902"}, {"size": 5603, "mtime": 1752078895444, "results": "1705", "hashOfConfig": "902"}, {"size": 4862, "mtime": 1752078895444, "results": "1706", "hashOfConfig": "902"}, {"size": 2137, "mtime": 1752078895444, "results": "1707", "hashOfConfig": "902"}, {"size": 1973, "mtime": 1752078895460, "results": "1708", "hashOfConfig": "902"}, {"size": 3119, "mtime": 1752078895460, "results": "1709", "hashOfConfig": "902"}, {"size": 660, "mtime": 1752078895460, "results": "1710", "hashOfConfig": "902"}, {"size": 5475, "mtime": 1752078895460, "results": "1711", "hashOfConfig": "902"}, {"size": 1623, "mtime": 1752078895460, "results": "1712", "hashOfConfig": "902"}, {"size": 8552, "mtime": 1752078895460, "results": "1713", "hashOfConfig": "902"}, {"size": 10690, "mtime": 1752078895460, "results": "1714", "hashOfConfig": "902"}, {"size": 6640, "mtime": 1752078895460, "results": "1715", "hashOfConfig": "902"}, {"size": 3241, "mtime": 1752078895460, "results": "1716", "hashOfConfig": "902"}, {"size": 7150, "mtime": 1752078895460, "results": "1717", "hashOfConfig": "902"}, {"size": 4983, "mtime": 1753101910697, "results": "1718", "hashOfConfig": "902"}, {"size": 2445, "mtime": 1753032850435, "results": "1719", "hashOfConfig": "902"}, {"size": 6220, "mtime": 1753032880536, "results": "1720", "hashOfConfig": "902"}, {"size": 489, "mtime": 1752078895476, "results": "1721", "hashOfConfig": "902"}, {"size": 4821, "mtime": 1752078895476, "results": "1722", "hashOfConfig": "902"}, {"size": 5260, "mtime": 1752078895476, "results": "1723", "hashOfConfig": "902"}, {"size": 399, "mtime": 1752078895476, "results": "1724", "hashOfConfig": "902"}, {"size": 10939, "mtime": 1753031432329, "results": "1725", "hashOfConfig": "902"}, {"size": 326, "mtime": 1752078895476, "results": "1726", "hashOfConfig": "902"}, {"size": 16659, "mtime": 1753101910697, "results": "1727", "hashOfConfig": "902"}, {"size": 5532, "mtime": 1752078895476, "results": "1728", "hashOfConfig": "902"}, {"size": 745, "mtime": 1752078895476, "results": "1729", "hashOfConfig": "902"}, {"size": 18910, "mtime": 1753101910697, "results": "1730", "hashOfConfig": "902"}, {"size": 4902, "mtime": 1752078895491, "results": "1731", "hashOfConfig": "902"}, {"size": 5364, "mtime": 1753101910697, "results": "1732", "hashOfConfig": "902"}, {"size": 13619, "mtime": 1753101910697, "results": "1733", "hashOfConfig": "902"}, {"size": 17685, "mtime": 1753101910697, "results": "1734", "hashOfConfig": "902"}, {"size": 6851, "mtime": 1753101910697, "results": "1735", "hashOfConfig": "902"}, {"size": 7605, "mtime": 1753101910697, "results": "1736", "hashOfConfig": "902"}, {"size": 5185, "mtime": 1753101910711, "results": "1737", "hashOfConfig": "902"}, {"size": 5336, "mtime": 1753101910711, "results": "1738", "hashOfConfig": "902"}, {"size": 5761, "mtime": 1753101910711, "results": "1739", "hashOfConfig": "902"}, {"size": 4665, "mtime": 1753101910711, "results": "1740", "hashOfConfig": "902"}, {"size": 2533, "mtime": 1752078895494, "results": "1741", "hashOfConfig": "902"}, {"size": 423, "mtime": 1752078895494, "results": "1742", "hashOfConfig": "902"}, {"size": 730, "mtime": 1752078895494, "results": "1743", "hashOfConfig": "902"}, {"size": 18833, "mtime": 1753101910711, "results": "1744", "hashOfConfig": "902"}, {"size": 215, "mtime": 1752078895494, "results": "1745", "hashOfConfig": "902"}, {"size": 4141, "mtime": 1752078895494, "results": "1746", "hashOfConfig": "902"}, {"size": 5808, "mtime": 1753101910711, "results": "1747", "hashOfConfig": "902"}, {"size": 442, "mtime": 1752078895460, "results": "1748", "hashOfConfig": "902"}, {"size": 6526, "mtime": 1753101910711, "results": "1749", "hashOfConfig": "902"}, {"size": 8817, "mtime": 1753101910711, "results": "1750", "hashOfConfig": "902"}, {"size": 3451, "mtime": 1752078895508, "results": "1751", "hashOfConfig": "902"}, {"size": 14131, "mtime": 1752078895508, "results": "1752", "hashOfConfig": "902"}, {"size": 1425, "mtime": 1752692226155, "results": "1753", "hashOfConfig": "902"}, {"size": 3060, "mtime": 1752080816015, "results": "1754", "hashOfConfig": "902"}, {"size": 5105, "mtime": 1752078895508, "results": "1755", "hashOfConfig": "902"}, {"size": 10030, "mtime": 1753253781224, "results": "1756", "hashOfConfig": "902"}, {"size": 1344, "mtime": 1752078895508, "results": "1757", "hashOfConfig": "902"}, {"size": 2968, "mtime": 1752078895508, "results": "1758", "hashOfConfig": "902"}, {"size": 5366, "mtime": 1752078895508, "results": "1759", "hashOfConfig": "902"}, {"size": 4748, "mtime": 1752078895508, "results": "1760", "hashOfConfig": "902"}, {"size": 15108, "mtime": 1753101910738, "results": "1761", "hashOfConfig": "902"}, {"size": 2252, "mtime": 1752078895508, "results": "1762", "hashOfConfig": "902"}, {"size": 4619, "mtime": 1753253270098, "results": "1763", "hashOfConfig": "902"}, {"size": 82968, "mtime": 1753251129502, "results": "1764", "hashOfConfig": "902"}, {"size": 7462, "mtime": 1753253801932, "results": "1765", "hashOfConfig": "902"}, {"size": 15412, "mtime": 1753253821068, "results": "1766", "hashOfConfig": "902"}, {"size": 5627, "mtime": 1753101910738, "results": "1767", "hashOfConfig": "902"}, {"size": 4228, "mtime": 1752313833841, "results": "1768", "hashOfConfig": "902"}, {"size": 7156, "mtime": 1752078895520, "results": "1769", "hashOfConfig": "902"}, {"size": 9274, "mtime": 1752313704411, "results": "1770", "hashOfConfig": "902"}, {"size": 7254, "mtime": 1752313705953, "results": "1771", "hashOfConfig": "902"}, {"size": 737, "mtime": 1753101910738, "results": "1772", "hashOfConfig": "902"}, {"size": 504, "mtime": 1753101910738, "results": "1773", "hashOfConfig": "902"}, {"size": 416, "mtime": 1752078895520, "results": "1774", "hashOfConfig": "902"}, {"size": 1666, "mtime": 1753101910738, "results": "1775", "hashOfConfig": "902"}, {"size": 5102, "mtime": 1753070702360, "results": "1776", "hashOfConfig": "902"}, {"size": 1838, "mtime": 1752078895524, "results": "1777", "hashOfConfig": "902"}, {"size": 3163, "mtime": 1752078895525, "results": "1778", "hashOfConfig": "902"}, {"size": 4249, "mtime": 1752078895525, "results": "1779", "hashOfConfig": "902"}, {"size": 6892, "mtime": 1752685339243, "results": "1780", "hashOfConfig": "902"}, {"size": 4659, "mtime": 1752078895525, "results": "1781", "hashOfConfig": "902"}, {"size": 5800, "mtime": 1752078895525, "results": "1782", "hashOfConfig": "902"}, {"size": 1337, "mtime": 1752078895525, "results": "1783", "hashOfConfig": "902"}, {"size": 3597, "mtime": 1753158883094, "results": "1784", "hashOfConfig": "902"}, {"size": 5943, "mtime": 1752078895525, "results": "1785", "hashOfConfig": "902"}, {"size": 9205, "mtime": 1753159293990, "results": "1786", "hashOfConfig": "902"}, {"size": 10196, "mtime": 1753159313540, "results": "1787", "hashOfConfig": "902"}, {"size": 4068, "mtime": 1753158935660, "results": "1788", "hashOfConfig": "902"}, {"size": 7483, "mtime": 1752078895525, "results": "1789", "hashOfConfig": "902"}, {"size": 5494, "mtime": 1753158950692, "results": "1790", "hashOfConfig": "902"}, {"size": 6872, "mtime": 1752078895540, "results": "1791", "hashOfConfig": "902"}, {"size": 5929, "mtime": 1752078895540, "results": "1792", "hashOfConfig": "902"}, {"size": 1824, "mtime": 1752078895540, "results": "1793", "hashOfConfig": "902"}, {"size": 2184, "mtime": 1752078895540, "results": "1794", "hashOfConfig": "902"}, {"size": 5432, "mtime": 1752685361427, "results": "1795", "hashOfConfig": "902"}, {"size": 7773, "mtime": 1752080816015, "results": "1796", "hashOfConfig": "902"}, {"size": 1497, "mtime": 1753201183949, "results": "1797", "hashOfConfig": "902"}, {"size": 6965, "mtime": 1752078895540, "results": "1798", "hashOfConfig": "902"}, {"size": 7497, "mtime": 1752427779267, "results": "1799", "hashOfConfig": "902"}, {"size": 10682, "mtime": 1753101910738, "results": "1800", "hashOfConfig": "902"}, {"size": 6604, "mtime": 1752078895525, "results": "1801", "hashOfConfig": "902"}, {"filePath": "1802", "messages": "1803", "suppressedMessages": "1804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "3j0uch", {"filePath": "1805", "messages": "1806", "suppressedMessages": "1807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1808", "messages": "1809", "suppressedMessages": "1810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1811", "messages": "1812", "suppressedMessages": "1813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1814", "messages": "1815", "suppressedMessages": "1816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1817", "messages": "1818", "suppressedMessages": "1819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1820", "messages": "1821", "suppressedMessages": "1822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1823", "messages": "1824", "suppressedMessages": "1825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1826", "messages": "1827", "suppressedMessages": "1828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1829", "messages": "1830", "suppressedMessages": "1831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1832", "messages": "1833", "suppressedMessages": "1834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1835", "messages": "1836", "suppressedMessages": "1837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1838", "messages": "1839", "suppressedMessages": "1840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1841", "messages": "1842", "suppressedMessages": "1843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1844", "messages": "1845", "suppressedMessages": "1846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1847", "messages": "1848", "suppressedMessages": "1849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1850", "messages": "1851", "suppressedMessages": "1852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1853", "messages": "1854", "suppressedMessages": "1855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1856", "messages": "1857", "suppressedMessages": "1858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1859", "messages": "1860", "suppressedMessages": "1861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1862", "messages": "1863", "suppressedMessages": "1864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1865", "messages": "1866", "suppressedMessages": "1867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1868", "messages": "1869", "suppressedMessages": "1870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1871", "messages": "1872", "suppressedMessages": "1873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1874", "messages": "1875", "suppressedMessages": "1876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1877", "messages": "1878", "suppressedMessages": "1879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1880", "messages": "1881", "suppressedMessages": "1882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1883", "messages": "1884", "suppressedMessages": "1885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1886", "messages": "1887", "suppressedMessages": "1888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1889", "messages": "1890", "suppressedMessages": "1891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1892", "messages": "1893", "suppressedMessages": "1894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1895", "messages": "1896", "suppressedMessages": "1897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1898", "messages": "1899", "suppressedMessages": "1900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1901", "messages": "1902", "suppressedMessages": "1903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1904", "messages": "1905", "suppressedMessages": "1906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1907", "messages": "1908", "suppressedMessages": "1909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1910", "messages": "1911", "suppressedMessages": "1912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1913", "messages": "1914", "suppressedMessages": "1915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1916", "messages": "1917", "suppressedMessages": "1918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1919", "messages": "1920", "suppressedMessages": "1921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1922", "messages": "1923", "suppressedMessages": "1924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1925", "messages": "1926", "suppressedMessages": "1927", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1928", "messages": "1929", "suppressedMessages": "1930", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1931", "messages": "1932", "suppressedMessages": "1933", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1934", "messages": "1935", "suppressedMessages": "1936", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1937", "messages": "1938", "suppressedMessages": "1939", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1940", "messages": "1941", "suppressedMessages": "1942", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1943", "messages": "1944", "suppressedMessages": "1945", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1946", "messages": "1947", "suppressedMessages": "1948", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1949", "messages": "1950", "suppressedMessages": "1951", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1952", "messages": "1953", "suppressedMessages": "1954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1955", "messages": "1956", "suppressedMessages": "1957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1958", "messages": "1959", "suppressedMessages": "1960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1961", "messages": "1962", "suppressedMessages": "1963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1964", "messages": "1965", "suppressedMessages": "1966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1967", "messages": "1968", "suppressedMessages": "1969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1970", "messages": "1971", "suppressedMessages": "1972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1973", "messages": "1974", "suppressedMessages": "1975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1976", "messages": "1977", "suppressedMessages": "1978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1979", "messages": "1980", "suppressedMessages": "1981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1982", "messages": "1983", "suppressedMessages": "1984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1985", "messages": "1986", "suppressedMessages": "1987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1988", "messages": "1989", "suppressedMessages": "1990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1991", "messages": "1992", "suppressedMessages": "1993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1994", "messages": "1995", "suppressedMessages": "1996", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1997", "messages": "1998", "suppressedMessages": "1999", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2000", "messages": "2001", "suppressedMessages": "2002", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2003", "messages": "2004", "suppressedMessages": "2005", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2006", "messages": "2007", "suppressedMessages": "2008", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2009", "messages": "2010", "suppressedMessages": "2011", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2012", "messages": "2013", "suppressedMessages": "2014", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2015", "messages": "2016", "suppressedMessages": "2017", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2018", "messages": "2019", "suppressedMessages": "2020", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2021", "messages": "2022", "suppressedMessages": "2023", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2024", "messages": "2025", "suppressedMessages": "2026", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2027", "messages": "2028", "suppressedMessages": "2029", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2030", "messages": "2031", "suppressedMessages": "2032", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2033", "messages": "2034", "suppressedMessages": "2035", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2036", "messages": "2037", "suppressedMessages": "2038", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2039", "messages": "2040", "suppressedMessages": "2041", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2042", "messages": "2043", "suppressedMessages": "2044", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2045", "messages": "2046", "suppressedMessages": "2047", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2048", "messages": "2049", "suppressedMessages": "2050", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2051", "messages": "2052", "suppressedMessages": "2053", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2054", "messages": "2055", "suppressedMessages": "2056", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2057", "messages": "2058", "suppressedMessages": "2059", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2060", "messages": "2061", "suppressedMessages": "2062", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2063", "messages": "2064", "suppressedMessages": "2065", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2066", "messages": "2067", "suppressedMessages": "2068", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2069", "messages": "2070", "suppressedMessages": "2071", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2072", "messages": "2073", "suppressedMessages": "2074", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2075", "messages": "2076", "suppressedMessages": "2077", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2078", "messages": "2079", "suppressedMessages": "2080", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2081", "messages": "2082", "suppressedMessages": "2083", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2084", "messages": "2085", "suppressedMessages": "2086", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2087", "messages": "2088", "suppressedMessages": "2089", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2090", "messages": "2091", "suppressedMessages": "2092", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2093", "messages": "2094", "suppressedMessages": "2095", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2096", "messages": "2097", "suppressedMessages": "2098", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2099", "messages": "2100", "suppressedMessages": "2101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2102", "messages": "2103", "suppressedMessages": "2104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2105", "messages": "2106", "suppressedMessages": "2107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2108", "messages": "2109", "suppressedMessages": "2110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2111", "messages": "2112", "suppressedMessages": "2113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2114", "messages": "2115", "suppressedMessages": "2116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2117", "messages": "2118", "suppressedMessages": "2119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2120", "messages": "2121", "suppressedMessages": "2122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2123", "messages": "2124", "suppressedMessages": "2125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2126", "messages": "2127", "suppressedMessages": "2128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2129", "messages": "2130", "suppressedMessages": "2131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2132", "messages": "2133", "suppressedMessages": "2134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2135", "messages": "2136", "suppressedMessages": "2137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2138", "messages": "2139", "suppressedMessages": "2140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2141", "messages": "2142", "suppressedMessages": "2143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2144", "messages": "2145", "suppressedMessages": "2146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2147", "messages": "2148", "suppressedMessages": "2149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2150", "messages": "2151", "suppressedMessages": "2152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2153", "messages": "2154", "suppressedMessages": "2155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2156", "messages": "2157", "suppressedMessages": "2158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2159", "messages": "2160", "suppressedMessages": "2161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2162", "messages": "2163", "suppressedMessages": "2164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2165", "messages": "2166", "suppressedMessages": "2167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2168", "messages": "2169", "suppressedMessages": "2170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2171", "messages": "2172", "suppressedMessages": "2173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2174", "messages": "2175", "suppressedMessages": "2176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2177", "messages": "2178", "suppressedMessages": "2179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2180", "messages": "2181", "suppressedMessages": "2182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2183", "messages": "2184", "suppressedMessages": "2185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2186", "messages": "2187", "suppressedMessages": "2188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2189", "messages": "2190", "suppressedMessages": "2191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2192", "messages": "2193", "suppressedMessages": "2194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2195", "messages": "2196", "suppressedMessages": "2197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2198", "messages": "2199", "suppressedMessages": "2200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2201", "messages": "2202", "suppressedMessages": "2203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2204", "messages": "2205", "suppressedMessages": "2206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2207", "messages": "2208", "suppressedMessages": "2209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2210", "messages": "2211", "suppressedMessages": "2212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2213", "messages": "2214", "suppressedMessages": "2215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2216", "messages": "2217", "suppressedMessages": "2218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2219", "messages": "2220", "suppressedMessages": "2221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2222", "messages": "2223", "suppressedMessages": "2224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2225", "messages": "2226", "suppressedMessages": "2227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2228", "messages": "2229", "suppressedMessages": "2230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2231", "messages": "2232", "suppressedMessages": "2233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2234", "messages": "2235", "suppressedMessages": "2236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2237", "messages": "2238", "suppressedMessages": "2239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2240", "messages": "2241", "suppressedMessages": "2242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2243", "messages": "2244", "suppressedMessages": "2245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2246", "messages": "2247", "suppressedMessages": "2248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2249", "messages": "2250", "suppressedMessages": "2251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2252", "messages": "2253", "suppressedMessages": "2254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2255", "messages": "2256", "suppressedMessages": "2257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2258", "messages": "2259", "suppressedMessages": "2260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2261", "messages": "2262", "suppressedMessages": "2263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2264", "messages": "2265", "suppressedMessages": "2266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2267", "messages": "2268", "suppressedMessages": "2269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2270", "messages": "2271", "suppressedMessages": "2272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2273", "messages": "2274", "suppressedMessages": "2275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2276", "messages": "2277", "suppressedMessages": "2278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2279", "messages": "2280", "suppressedMessages": "2281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2282", "messages": "2283", "suppressedMessages": "2284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2285", "messages": "2286", "suppressedMessages": "2287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2288", "messages": "2289", "suppressedMessages": "2290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2291", "messages": "2292", "suppressedMessages": "2293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2294", "messages": "2295", "suppressedMessages": "2296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2297", "messages": "2298", "suppressedMessages": "2299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2300", "messages": "2301", "suppressedMessages": "2302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2303", "messages": "2304", "suppressedMessages": "2305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2306", "messages": "2307", "suppressedMessages": "2308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2309", "messages": "2310", "suppressedMessages": "2311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2312", "messages": "2313", "suppressedMessages": "2314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2315", "messages": "2316", "suppressedMessages": "2317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2318", "messages": "2319", "suppressedMessages": "2320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2321", "messages": "2322", "suppressedMessages": "2323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2324", "messages": "2325", "suppressedMessages": "2326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2327", "messages": "2328", "suppressedMessages": "2329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2330", "messages": "2331", "suppressedMessages": "2332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2333", "messages": "2334", "suppressedMessages": "2335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2336", "messages": "2337", "suppressedMessages": "2338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2339", "messages": "2340", "suppressedMessages": "2341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2342", "messages": "2343", "suppressedMessages": "2344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2345", "messages": "2346", "suppressedMessages": "2347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2348", "messages": "2349", "suppressedMessages": "2350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2351", "messages": "2352", "suppressedMessages": "2353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2354", "messages": "2355", "suppressedMessages": "2356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2357", "messages": "2358", "suppressedMessages": "2359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2360", "messages": "2361", "suppressedMessages": "2362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2363", "messages": "2364", "suppressedMessages": "2365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2366", "messages": "2367", "suppressedMessages": "2368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2369", "messages": "2370", "suppressedMessages": "2371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2372", "messages": "2373", "suppressedMessages": "2374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2375", "messages": "2376", "suppressedMessages": "2377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2378", "messages": "2379", "suppressedMessages": "2380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2381", "messages": "2382", "suppressedMessages": "2383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2384", "messages": "2385", "suppressedMessages": "2386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2387", "messages": "2388", "suppressedMessages": "2389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2390", "messages": "2391", "suppressedMessages": "2392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2393", "messages": "2394", "suppressedMessages": "2395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2396", "messages": "2397", "suppressedMessages": "2398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2399", "messages": "2400", "suppressedMessages": "2401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2402", "messages": "2403", "suppressedMessages": "2404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2405", "messages": "2406", "suppressedMessages": "2407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2408", "messages": "2409", "suppressedMessages": "2410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2411", "messages": "2412", "suppressedMessages": "2413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2414", "messages": "2415", "suppressedMessages": "2416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2417", "messages": "2418", "suppressedMessages": "2419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2420", "messages": "2421", "suppressedMessages": "2422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2423", "messages": "2424", "suppressedMessages": "2425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2426", "messages": "2427", "suppressedMessages": "2428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2429", "messages": "2430", "suppressedMessages": "2431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2432", "messages": "2433", "suppressedMessages": "2434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2435", "messages": "2436", "suppressedMessages": "2437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2438", "messages": "2439", "suppressedMessages": "2440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2441", "messages": "2442", "suppressedMessages": "2443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2444", "messages": "2445", "suppressedMessages": "2446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2447", "messages": "2448", "suppressedMessages": "2449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2450", "messages": "2451", "suppressedMessages": "2452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2453", "messages": "2454", "suppressedMessages": "2455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2456", "messages": "2457", "suppressedMessages": "2458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2459", "messages": "2460", "suppressedMessages": "2461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2462", "messages": "2463", "suppressedMessages": "2464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2465", "messages": "2466", "suppressedMessages": "2467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2468", "messages": "2469", "suppressedMessages": "2470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2471", "messages": "2472", "suppressedMessages": "2473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2474", "messages": "2475", "suppressedMessages": "2476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2477", "messages": "2478", "suppressedMessages": "2479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2480", "messages": "2481", "suppressedMessages": "2482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2483", "messages": "2484", "suppressedMessages": "2485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2486", "messages": "2487", "suppressedMessages": "2488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2489", "messages": "2490", "suppressedMessages": "2491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2492", "messages": "2493", "suppressedMessages": "2494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2495", "messages": "2496", "suppressedMessages": "2497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2498", "messages": "2499", "suppressedMessages": "2500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2501", "messages": "2502", "suppressedMessages": "2503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2504", "messages": "2505", "suppressedMessages": "2506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2507", "messages": "2508", "suppressedMessages": "2509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2510", "messages": "2511", "suppressedMessages": "2512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2513", "messages": "2514", "suppressedMessages": "2515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2516", "messages": "2517", "suppressedMessages": "2518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2519", "messages": "2520", "suppressedMessages": "2521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2522", "messages": "2523", "suppressedMessages": "2524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2525", "messages": "2526", "suppressedMessages": "2527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2528", "messages": "2529", "suppressedMessages": "2530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2531", "messages": "2532", "suppressedMessages": "2533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2534", "messages": "2535", "suppressedMessages": "2536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2537", "messages": "2538", "suppressedMessages": "2539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2540", "messages": "2541", "suppressedMessages": "2542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2543", "messages": "2544", "suppressedMessages": "2545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2546", "messages": "2547", "suppressedMessages": "2548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2549", "messages": "2550", "suppressedMessages": "2551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2552", "messages": "2553", "suppressedMessages": "2554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2555", "messages": "2556", "suppressedMessages": "2557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2558", "messages": "2559", "suppressedMessages": "2560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2561", "messages": "2562", "suppressedMessages": "2563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2564", "messages": "2565", "suppressedMessages": "2566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2567", "messages": "2568", "suppressedMessages": "2569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2570", "messages": "2571", "suppressedMessages": "2572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2573", "messages": "2574", "suppressedMessages": "2575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2576", "messages": "2577", "suppressedMessages": "2578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2579", "messages": "2580", "suppressedMessages": "2581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2582", "messages": "2583", "suppressedMessages": "2584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2585", "messages": "2586", "suppressedMessages": "2587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2588", "messages": "2589", "suppressedMessages": "2590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2591", "messages": "2592", "suppressedMessages": "2593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2594", "messages": "2595", "suppressedMessages": "2596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2597", "messages": "2598", "suppressedMessages": "2599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2600", "messages": "2601", "suppressedMessages": "2602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2603", "messages": "2604", "suppressedMessages": "2605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2606", "messages": "2607", "suppressedMessages": "2608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2609", "messages": "2610", "suppressedMessages": "2611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2612", "messages": "2613", "suppressedMessages": "2614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2615", "messages": "2616", "suppressedMessages": "2617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2618", "messages": "2619", "suppressedMessages": "2620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2621", "messages": "2622", "suppressedMessages": "2623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2624", "messages": "2625", "suppressedMessages": "2626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2627", "messages": "2628", "suppressedMessages": "2629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2630", "messages": "2631", "suppressedMessages": "2632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2633", "messages": "2634", "suppressedMessages": "2635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2636", "messages": "2637", "suppressedMessages": "2638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2639", "messages": "2640", "suppressedMessages": "2641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2642", "messages": "2643", "suppressedMessages": "2644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2645", "messages": "2646", "suppressedMessages": "2647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2648", "messages": "2649", "suppressedMessages": "2650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2651", "messages": "2652", "suppressedMessages": "2653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2654", "messages": "2655", "suppressedMessages": "2656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2657", "messages": "2658", "suppressedMessages": "2659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2660", "messages": "2661", "suppressedMessages": "2662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2663", "messages": "2664", "suppressedMessages": "2665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2666", "messages": "2667", "suppressedMessages": "2668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2669", "messages": "2670", "suppressedMessages": "2671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2672", "messages": "2673", "suppressedMessages": "2674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2675", "messages": "2676", "suppressedMessages": "2677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2678", "messages": "2679", "suppressedMessages": "2680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2681", "messages": "2682", "suppressedMessages": "2683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2684", "messages": "2685", "suppressedMessages": "2686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2687", "messages": "2688", "suppressedMessages": "2689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2690", "messages": "2691", "suppressedMessages": "2692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2693", "messages": "2694", "suppressedMessages": "2695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2696", "messages": "2697", "suppressedMessages": "2698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2699", "messages": "2700", "suppressedMessages": "2701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2702", "messages": "2703", "suppressedMessages": "2704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2705", "messages": "2706", "suppressedMessages": "2707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2708", "messages": "2709", "suppressedMessages": "2710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2711", "messages": "2712", "suppressedMessages": "2713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2714", "messages": "2715", "suppressedMessages": "2716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2717", "messages": "2718", "suppressedMessages": "2719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2720", "messages": "2721", "suppressedMessages": "2722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2723", "messages": "2724", "suppressedMessages": "2725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2726", "messages": "2727", "suppressedMessages": "2728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2729", "messages": "2730", "suppressedMessages": "2731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2732", "messages": "2733", "suppressedMessages": "2734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2735", "messages": "2736", "suppressedMessages": "2737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2738", "messages": "2739", "suppressedMessages": "2740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2741", "messages": "2742", "suppressedMessages": "2743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2744", "messages": "2745", "suppressedMessages": "2746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2747", "messages": "2748", "suppressedMessages": "2749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2750", "messages": "2751", "suppressedMessages": "2752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2753", "messages": "2754", "suppressedMessages": "2755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2756", "messages": "2757", "suppressedMessages": "2758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2759", "messages": "2760", "suppressedMessages": "2761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2762", "messages": "2763", "suppressedMessages": "2764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2765", "messages": "2766", "suppressedMessages": "2767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2768", "messages": "2769", "suppressedMessages": "2770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2771", "messages": "2772", "suppressedMessages": "2773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2774", "messages": "2775", "suppressedMessages": "2776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2777", "messages": "2778", "suppressedMessages": "2779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2780", "messages": "2781", "suppressedMessages": "2782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2783", "messages": "2784", "suppressedMessages": "2785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2786", "messages": "2787", "suppressedMessages": "2788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2789", "messages": "2790", "suppressedMessages": "2791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2792", "messages": "2793", "suppressedMessages": "2794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2795", "messages": "2796", "suppressedMessages": "2797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2798", "messages": "2799", "suppressedMessages": "2800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2801", "messages": "2802", "suppressedMessages": "2803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2804", "messages": "2805", "suppressedMessages": "2806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2807", "messages": "2808", "suppressedMessages": "2809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2810", "messages": "2811", "suppressedMessages": "2812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2813", "messages": "2814", "suppressedMessages": "2815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2816", "messages": "2817", "suppressedMessages": "2818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2819", "messages": "2820", "suppressedMessages": "2821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2822", "messages": "2823", "suppressedMessages": "2824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2825", "messages": "2826", "suppressedMessages": "2827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2828", "messages": "2829", "suppressedMessages": "2830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2831", "messages": "2832", "suppressedMessages": "2833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2834", "messages": "2835", "suppressedMessages": "2836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2837", "messages": "2838", "suppressedMessages": "2839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2840", "messages": "2841", "suppressedMessages": "2842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2843", "messages": "2844", "suppressedMessages": "2845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2846", "messages": "2847", "suppressedMessages": "2848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2849", "messages": "2850", "suppressedMessages": "2851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2852", "messages": "2853", "suppressedMessages": "2854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2855", "messages": "2856", "suppressedMessages": "2857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2858", "messages": "2859", "suppressedMessages": "2860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2861", "messages": "2862", "suppressedMessages": "2863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2864", "messages": "2865", "suppressedMessages": "2866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2867", "messages": "2868", "suppressedMessages": "2869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2870", "messages": "2871", "suppressedMessages": "2872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2873", "messages": "2874", "suppressedMessages": "2875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2876", "messages": "2877", "suppressedMessages": "2878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2879", "messages": "2880", "suppressedMessages": "2881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2882", "messages": "2883", "suppressedMessages": "2884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2885", "messages": "2886", "suppressedMessages": "2887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2888", "messages": "2889", "suppressedMessages": "2890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2891", "messages": "2892", "suppressedMessages": "2893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2894", "messages": "2895", "suppressedMessages": "2896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2897", "messages": "2898", "suppressedMessages": "2899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2900", "messages": "2901", "suppressedMessages": "2902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2903", "messages": "2904", "suppressedMessages": "2905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2906", "messages": "2907", "suppressedMessages": "2908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2909", "messages": "2910", "suppressedMessages": "2911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2912", "messages": "2913", "suppressedMessages": "2914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2915", "messages": "2916", "suppressedMessages": "2917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2918", "messages": "2919", "suppressedMessages": "2920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2921", "messages": "2922", "suppressedMessages": "2923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2924", "messages": "2925", "suppressedMessages": "2926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2927", "messages": "2928", "suppressedMessages": "2929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2930", "messages": "2931", "suppressedMessages": "2932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2933", "messages": "2934", "suppressedMessages": "2935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2936", "messages": "2937", "suppressedMessages": "2938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2939", "messages": "2940", "suppressedMessages": "2941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2942", "messages": "2943", "suppressedMessages": "2944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2945", "messages": "2946", "suppressedMessages": "2947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2948", "messages": "2949", "suppressedMessages": "2950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2951", "messages": "2952", "suppressedMessages": "2953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2954", "messages": "2955", "suppressedMessages": "2956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2957", "messages": "2958", "suppressedMessages": "2959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2960", "messages": "2961", "suppressedMessages": "2962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2963", "messages": "2964", "suppressedMessages": "2965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2966", "messages": "2967", "suppressedMessages": "2968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2969", "messages": "2970", "suppressedMessages": "2971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2972", "messages": "2973", "suppressedMessages": "2974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2975", "messages": "2976", "suppressedMessages": "2977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2978", "messages": "2979", "suppressedMessages": "2980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2981", "messages": "2982", "suppressedMessages": "2983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2984", "messages": "2985", "suppressedMessages": "2986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2987", "messages": "2988", "suppressedMessages": "2989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2990", "messages": "2991", "suppressedMessages": "2992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2993", "messages": "2994", "suppressedMessages": "2995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2996", "messages": "2997", "suppressedMessages": "2998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2999", "messages": "3000", "suppressedMessages": "3001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3002", "messages": "3003", "suppressedMessages": "3004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3005", "messages": "3006", "suppressedMessages": "3007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3008", "messages": "3009", "suppressedMessages": "3010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3011", "messages": "3012", "suppressedMessages": "3013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3014", "messages": "3015", "suppressedMessages": "3016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3017", "messages": "3018", "suppressedMessages": "3019", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3020", "messages": "3021", "suppressedMessages": "3022", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3023", "messages": "3024", "suppressedMessages": "3025", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3026", "messages": "3027", "suppressedMessages": "3028", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3029", "messages": "3030", "suppressedMessages": "3031", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3032", "messages": "3033", "suppressedMessages": "3034", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3035", "messages": "3036", "suppressedMessages": "3037", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3038", "messages": "3039", "suppressedMessages": "3040", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3041", "messages": "3042", "suppressedMessages": "3043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3044", "messages": "3045", "suppressedMessages": "3046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3047", "messages": "3048", "suppressedMessages": "3049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3050", "messages": "3051", "suppressedMessages": "3052", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3053", "messages": "3054", "suppressedMessages": "3055", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3056", "messages": "3057", "suppressedMessages": "3058", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3059", "messages": "3060", "suppressedMessages": "3061", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3062", "messages": "3063", "suppressedMessages": "3064", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3065", "messages": "3066", "suppressedMessages": "3067", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3068", "messages": "3069", "suppressedMessages": "3070", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3071", "messages": "3072", "suppressedMessages": "3073", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3074", "messages": "3075", "suppressedMessages": "3076", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3077", "messages": "3078", "suppressedMessages": "3079", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3080", "messages": "3081", "suppressedMessages": "3082", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3083", "messages": "3084", "suppressedMessages": "3085", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3086", "messages": "3087", "suppressedMessages": "3088", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3089", "messages": "3090", "suppressedMessages": "3091", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3092", "messages": "3093", "suppressedMessages": "3094", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3095", "messages": "3096", "suppressedMessages": "3097", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3098", "messages": "3099", "suppressedMessages": "3100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3101", "messages": "3102", "suppressedMessages": "3103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3104", "messages": "3105", "suppressedMessages": "3106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3107", "messages": "3108", "suppressedMessages": "3109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3110", "messages": "3111", "suppressedMessages": "3112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3113", "messages": "3114", "suppressedMessages": "3115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3116", "messages": "3117", "suppressedMessages": "3118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3119", "messages": "3120", "suppressedMessages": "3121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3122", "messages": "3123", "suppressedMessages": "3124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3125", "messages": "3126", "suppressedMessages": "3127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3128", "messages": "3129", "suppressedMessages": "3130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3131", "messages": "3132", "suppressedMessages": "3133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3134", "messages": "3135", "suppressedMessages": "3136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3137", "messages": "3138", "suppressedMessages": "3139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3140", "messages": "3141", "suppressedMessages": "3142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3143", "messages": "3144", "suppressedMessages": "3145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3146", "messages": "3147", "suppressedMessages": "3148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3149", "messages": "3150", "suppressedMessages": "3151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3152", "messages": "3153", "suppressedMessages": "3154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3155", "messages": "3156", "suppressedMessages": "3157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3158", "messages": "3159", "suppressedMessages": "3160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3161", "messages": "3162", "suppressedMessages": "3163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3164", "messages": "3165", "suppressedMessages": "3166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3167", "messages": "3168", "suppressedMessages": "3169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3170", "messages": "3171", "suppressedMessages": "3172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3173", "messages": "3174", "suppressedMessages": "3175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3176", "messages": "3177", "suppressedMessages": "3178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3179", "messages": "3180", "suppressedMessages": "3181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3182", "messages": "3183", "suppressedMessages": "3184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3185", "messages": "3186", "suppressedMessages": "3187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3188", "messages": "3189", "suppressedMessages": "3190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3191", "messages": "3192", "suppressedMessages": "3193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3194", "messages": "3195", "suppressedMessages": "3196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3197", "messages": "3198", "suppressedMessages": "3199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3200", "messages": "3201", "suppressedMessages": "3202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3203", "messages": "3204", "suppressedMessages": "3205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3206", "messages": "3207", "suppressedMessages": "3208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3209", "messages": "3210", "suppressedMessages": "3211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3212", "messages": "3213", "suppressedMessages": "3214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3215", "messages": "3216", "suppressedMessages": "3217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3218", "messages": "3219", "suppressedMessages": "3220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3221", "messages": "3222", "suppressedMessages": "3223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3224", "messages": "3225", "suppressedMessages": "3226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3227", "messages": "3228", "suppressedMessages": "3229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3230", "messages": "3231", "suppressedMessages": "3232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3233", "messages": "3234", "suppressedMessages": "3235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3236", "messages": "3237", "suppressedMessages": "3238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3239", "messages": "3240", "suppressedMessages": "3241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3242", "messages": "3243", "suppressedMessages": "3244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3245", "messages": "3246", "suppressedMessages": "3247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3248", "messages": "3249", "suppressedMessages": "3250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3251", "messages": "3252", "suppressedMessages": "3253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3254", "messages": "3255", "suppressedMessages": "3256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3257", "messages": "3258", "suppressedMessages": "3259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3260", "messages": "3261", "suppressedMessages": "3262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3263", "messages": "3264", "suppressedMessages": "3265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3266", "messages": "3267", "suppressedMessages": "3268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3269", "messages": "3270", "suppressedMessages": "3271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3272", "messages": "3273", "suppressedMessages": "3274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3275", "messages": "3276", "suppressedMessages": "3277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3278", "messages": "3279", "suppressedMessages": "3280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3281", "messages": "3282", "suppressedMessages": "3283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3284", "messages": "3285", "suppressedMessages": "3286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3287", "messages": "3288", "suppressedMessages": "3289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3290", "messages": "3291", "suppressedMessages": "3292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3293", "messages": "3294", "suppressedMessages": "3295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3296", "messages": "3297", "suppressedMessages": "3298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3299", "messages": "3300", "suppressedMessages": "3301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3302", "messages": "3303", "suppressedMessages": "3304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3305", "messages": "3306", "suppressedMessages": "3307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3308", "messages": "3309", "suppressedMessages": "3310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3311", "messages": "3312", "suppressedMessages": "3313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3314", "messages": "3315", "suppressedMessages": "3316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3317", "messages": "3318", "suppressedMessages": "3319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3320", "messages": "3321", "suppressedMessages": "3322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3323", "messages": "3324", "suppressedMessages": "3325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3326", "messages": "3327", "suppressedMessages": "3328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3329", "messages": "3330", "suppressedMessages": "3331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3332", "messages": "3333", "suppressedMessages": "3334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3335", "messages": "3336", "suppressedMessages": "3337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3338", "messages": "3339", "suppressedMessages": "3340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3341", "messages": "3342", "suppressedMessages": "3343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3344", "messages": "3345", "suppressedMessages": "3346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3347", "messages": "3348", "suppressedMessages": "3349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3350", "messages": "3351", "suppressedMessages": "3352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3353", "messages": "3354", "suppressedMessages": "3355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3356", "messages": "3357", "suppressedMessages": "3358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3359", "messages": "3360", "suppressedMessages": "3361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3362", "messages": "3363", "suppressedMessages": "3364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3365", "messages": "3366", "suppressedMessages": "3367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3368", "messages": "3369", "suppressedMessages": "3370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3371", "messages": "3372", "suppressedMessages": "3373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3374", "messages": "3375", "suppressedMessages": "3376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3377", "messages": "3378", "suppressedMessages": "3379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3380", "messages": "3381", "suppressedMessages": "3382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3383", "messages": "3384", "suppressedMessages": "3385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3386", "messages": "3387", "suppressedMessages": "3388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3389", "messages": "3390", "suppressedMessages": "3391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3392", "messages": "3393", "suppressedMessages": "3394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3395", "messages": "3396", "suppressedMessages": "3397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3398", "messages": "3399", "suppressedMessages": "3400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3401", "messages": "3402", "suppressedMessages": "3403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3404", "messages": "3405", "suppressedMessages": "3406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3407", "messages": "3408", "suppressedMessages": "3409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3410", "messages": "3411", "suppressedMessages": "3412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3413", "messages": "3414", "suppressedMessages": "3415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3416", "messages": "3417", "suppressedMessages": "3418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3419", "messages": "3420", "suppressedMessages": "3421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3422", "messages": "3423", "suppressedMessages": "3424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3425", "messages": "3426", "suppressedMessages": "3427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3428", "messages": "3429", "suppressedMessages": "3430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3431", "messages": "3432", "suppressedMessages": "3433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3434", "messages": "3435", "suppressedMessages": "3436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3437", "messages": "3438", "suppressedMessages": "3439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3440", "messages": "3441", "suppressedMessages": "3442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3443", "messages": "3444", "suppressedMessages": "3445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3446", "messages": "3447", "suppressedMessages": "3448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3449", "messages": "3450", "suppressedMessages": "3451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3452", "messages": "3453", "suppressedMessages": "3454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3455", "messages": "3456", "suppressedMessages": "3457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3458", "messages": "3459", "suppressedMessages": "3460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3461", "messages": "3462", "suppressedMessages": "3463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3464", "messages": "3465", "suppressedMessages": "3466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3467", "messages": "3468", "suppressedMessages": "3469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3470", "messages": "3471", "suppressedMessages": "3472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3473", "messages": "3474", "suppressedMessages": "3475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3476", "messages": "3477", "suppressedMessages": "3478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3479", "messages": "3480", "suppressedMessages": "3481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3482", "messages": "3483", "suppressedMessages": "3484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3485", "messages": "3486", "suppressedMessages": "3487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3488", "messages": "3489", "suppressedMessages": "3490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3491", "messages": "3492", "suppressedMessages": "3493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3494", "messages": "3495", "suppressedMessages": "3496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3497", "messages": "3498", "suppressedMessages": "3499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3500", "messages": "3501", "suppressedMessages": "3502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3503", "messages": "3504", "suppressedMessages": "3505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3506", "messages": "3507", "suppressedMessages": "3508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3509", "messages": "3510", "suppressedMessages": "3511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3512", "messages": "3513", "suppressedMessages": "3514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3515", "messages": "3516", "suppressedMessages": "3517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3518", "messages": "3519", "suppressedMessages": "3520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3521", "messages": "3522", "suppressedMessages": "3523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3524", "messages": "3525", "suppressedMessages": "3526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3527", "messages": "3528", "suppressedMessages": "3529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3530", "messages": "3531", "suppressedMessages": "3532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3533", "messages": "3534", "suppressedMessages": "3535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3536", "messages": "3537", "suppressedMessages": "3538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3539", "messages": "3540", "suppressedMessages": "3541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3542", "messages": "3543", "suppressedMessages": "3544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3545", "messages": "3546", "suppressedMessages": "3547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3548", "messages": "3549", "suppressedMessages": "3550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3551", "messages": "3552", "suppressedMessages": "3553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3554", "messages": "3555", "suppressedMessages": "3556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3557", "messages": "3558", "suppressedMessages": "3559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3560", "messages": "3561", "suppressedMessages": "3562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3563", "messages": "3564", "suppressedMessages": "3565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3566", "messages": "3567", "suppressedMessages": "3568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3569", "messages": "3570", "suppressedMessages": "3571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3572", "messages": "3573", "suppressedMessages": "3574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3575", "messages": "3576", "suppressedMessages": "3577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3578", "messages": "3579", "suppressedMessages": "3580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3581", "messages": "3582", "suppressedMessages": "3583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3584", "messages": "3585", "suppressedMessages": "3586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3587", "messages": "3588", "suppressedMessages": "3589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3590", "messages": "3591", "suppressedMessages": "3592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3593", "messages": "3594", "suppressedMessages": "3595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3596", "messages": "3597", "suppressedMessages": "3598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3599", "messages": "3600", "suppressedMessages": "3601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3602", "messages": "3603", "suppressedMessages": "3604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3605", "messages": "3606", "suppressedMessages": "3607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3608", "messages": "3609", "suppressedMessages": "3610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3611", "messages": "3612", "suppressedMessages": "3613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3614", "messages": "3615", "suppressedMessages": "3616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3617", "messages": "3618", "suppressedMessages": "3619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3620", "messages": "3621", "suppressedMessages": "3622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3623", "messages": "3624", "suppressedMessages": "3625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3626", "messages": "3627", "suppressedMessages": "3628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3629", "messages": "3630", "suppressedMessages": "3631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3632", "messages": "3633", "suppressedMessages": "3634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3635", "messages": "3636", "suppressedMessages": "3637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3638", "messages": "3639", "suppressedMessages": "3640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3641", "messages": "3642", "suppressedMessages": "3643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3644", "messages": "3645", "suppressedMessages": "3646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3647", "messages": "3648", "suppressedMessages": "3649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3650", "messages": "3651", "suppressedMessages": "3652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3653", "messages": "3654", "suppressedMessages": "3655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3656", "messages": "3657", "suppressedMessages": "3658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3659", "messages": "3660", "suppressedMessages": "3661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3662", "messages": "3663", "suppressedMessages": "3664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3665", "messages": "3666", "suppressedMessages": "3667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3668", "messages": "3669", "suppressedMessages": "3670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3671", "messages": "3672", "suppressedMessages": "3673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3674", "messages": "3675", "suppressedMessages": "3676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3677", "messages": "3678", "suppressedMessages": "3679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3680", "messages": "3681", "suppressedMessages": "3682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3683", "messages": "3684", "suppressedMessages": "3685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3686", "messages": "3687", "suppressedMessages": "3688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3689", "messages": "3690", "suppressedMessages": "3691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3692", "messages": "3693", "suppressedMessages": "3694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3695", "messages": "3696", "suppressedMessages": "3697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3698", "messages": "3699", "suppressedMessages": "3700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3701", "messages": "3702", "suppressedMessages": "3703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3704", "messages": "3705", "suppressedMessages": "3706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3707", "messages": "3708", "suppressedMessages": "3709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3710", "messages": "3711", "suppressedMessages": "3712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3713", "messages": "3714", "suppressedMessages": "3715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3716", "messages": "3717", "suppressedMessages": "3718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3719", "messages": "3720", "suppressedMessages": "3721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3722", "messages": "3723", "suppressedMessages": "3724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3725", "messages": "3726", "suppressedMessages": "3727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3728", "messages": "3729", "suppressedMessages": "3730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3731", "messages": "3732", "suppressedMessages": "3733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3734", "messages": "3735", "suppressedMessages": "3736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3737", "messages": "3738", "suppressedMessages": "3739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3740", "messages": "3741", "suppressedMessages": "3742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3743", "messages": "3744", "suppressedMessages": "3745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3746", "messages": "3747", "suppressedMessages": "3748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3749", "messages": "3750", "suppressedMessages": "3751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3752", "messages": "3753", "suppressedMessages": "3754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3755", "messages": "3756", "suppressedMessages": "3757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3758", "messages": "3759", "suppressedMessages": "3760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3761", "messages": "3762", "suppressedMessages": "3763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3764", "messages": "3765", "suppressedMessages": "3766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3767", "messages": "3768", "suppressedMessages": "3769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3770", "messages": "3771", "suppressedMessages": "3772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3773", "messages": "3774", "suppressedMessages": "3775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3776", "messages": "3777", "suppressedMessages": "3778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3779", "messages": "3780", "suppressedMessages": "3781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3782", "messages": "3783", "suppressedMessages": "3784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3785", "messages": "3786", "suppressedMessages": "3787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3788", "messages": "3789", "suppressedMessages": "3790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3791", "messages": "3792", "suppressedMessages": "3793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3794", "messages": "3795", "suppressedMessages": "3796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3797", "messages": "3798", "suppressedMessages": "3799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3800", "messages": "3801", "suppressedMessages": "3802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3803", "messages": "3804", "suppressedMessages": "3805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3806", "messages": "3807", "suppressedMessages": "3808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3809", "messages": "3810", "suppressedMessages": "3811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3812", "messages": "3813", "suppressedMessages": "3814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3815", "messages": "3816", "suppressedMessages": "3817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3818", "messages": "3819", "suppressedMessages": "3820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3821", "messages": "3822", "suppressedMessages": "3823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3824", "messages": "3825", "suppressedMessages": "3826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3827", "messages": "3828", "suppressedMessages": "3829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3830", "messages": "3831", "suppressedMessages": "3832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3833", "messages": "3834", "suppressedMessages": "3835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3836", "messages": "3837", "suppressedMessages": "3838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3839", "messages": "3840", "suppressedMessages": "3841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3842", "messages": "3843", "suppressedMessages": "3844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3845", "messages": "3846", "suppressedMessages": "3847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3848", "messages": "3849", "suppressedMessages": "3850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3851", "messages": "3852", "suppressedMessages": "3853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3854", "messages": "3855", "suppressedMessages": "3856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3857", "messages": "3858", "suppressedMessages": "3859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3860", "messages": "3861", "suppressedMessages": "3862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3863", "messages": "3864", "suppressedMessages": "3865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3866", "messages": "3867", "suppressedMessages": "3868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3869", "messages": "3870", "suppressedMessages": "3871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3872", "messages": "3873", "suppressedMessages": "3874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3875", "messages": "3876", "suppressedMessages": "3877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3878", "messages": "3879", "suppressedMessages": "3880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3881", "messages": "3882", "suppressedMessages": "3883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3884", "messages": "3885", "suppressedMessages": "3886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3887", "messages": "3888", "suppressedMessages": "3889", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3890", "messages": "3891", "suppressedMessages": "3892", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3893", "messages": "3894", "suppressedMessages": "3895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3896", "messages": "3897", "suppressedMessages": "3898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3899", "messages": "3900", "suppressedMessages": "3901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3902", "messages": "3903", "suppressedMessages": "3904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3905", "messages": "3906", "suppressedMessages": "3907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3908", "messages": "3909", "suppressedMessages": "3910", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3911", "messages": "3912", "suppressedMessages": "3913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3914", "messages": "3915", "suppressedMessages": "3916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3917", "messages": "3918", "suppressedMessages": "3919", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3920", "messages": "3921", "suppressedMessages": "3922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3923", "messages": "3924", "suppressedMessages": "3925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3926", "messages": "3927", "suppressedMessages": "3928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3929", "messages": "3930", "suppressedMessages": "3931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3932", "messages": "3933", "suppressedMessages": "3934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3935", "messages": "3936", "suppressedMessages": "3937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3938", "messages": "3939", "suppressedMessages": "3940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3941", "messages": "3942", "suppressedMessages": "3943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3944", "messages": "3945", "suppressedMessages": "3946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3947", "messages": "3948", "suppressedMessages": "3949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3950", "messages": "3951", "suppressedMessages": "3952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3953", "messages": "3954", "suppressedMessages": "3955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3956", "messages": "3957", "suppressedMessages": "3958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3959", "messages": "3960", "suppressedMessages": "3961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3962", "messages": "3963", "suppressedMessages": "3964", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3965", "messages": "3966", "suppressedMessages": "3967", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3968", "messages": "3969", "suppressedMessages": "3970", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3971", "messages": "3972", "suppressedMessages": "3973", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3974", "messages": "3975", "suppressedMessages": "3976", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3977", "messages": "3978", "suppressedMessages": "3979", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3980", "messages": "3981", "suppressedMessages": "3982", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3983", "messages": "3984", "suppressedMessages": "3985", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3986", "messages": "3987", "suppressedMessages": "3988", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3989", "messages": "3990", "suppressedMessages": "3991", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3992", "messages": "3993", "suppressedMessages": "3994", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3995", "messages": "3996", "suppressedMessages": "3997", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3998", "messages": "3999", "suppressedMessages": "4000", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4001", "messages": "4002", "suppressedMessages": "4003", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4004", "messages": "4005", "suppressedMessages": "4006", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4007", "messages": "4008", "suppressedMessages": "4009", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4010", "messages": "4011", "suppressedMessages": "4012", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4013", "messages": "4014", "suppressedMessages": "4015", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4016", "messages": "4017", "suppressedMessages": "4018", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4019", "messages": "4020", "suppressedMessages": "4021", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4022", "messages": "4023", "suppressedMessages": "4024", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4025", "messages": "4026", "suppressedMessages": "4027", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4028", "messages": "4029", "suppressedMessages": "4030", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4031", "messages": "4032", "suppressedMessages": "4033", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4034", "messages": "4035", "suppressedMessages": "4036", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4037", "messages": "4038", "suppressedMessages": "4039", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4040", "messages": "4041", "suppressedMessages": "4042", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4043", "messages": "4044", "suppressedMessages": "4045", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4046", "messages": "4047", "suppressedMessages": "4048", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4049", "messages": "4050", "suppressedMessages": "4051", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4052", "messages": "4053", "suppressedMessages": "4054", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4055", "messages": "4056", "suppressedMessages": "4057", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4058", "messages": "4059", "suppressedMessages": "4060", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4061", "messages": "4062", "suppressedMessages": "4063", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4064", "messages": "4065", "suppressedMessages": "4066", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4067", "messages": "4068", "suppressedMessages": "4069", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4070", "messages": "4071", "suppressedMessages": "4072", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4073", "messages": "4074", "suppressedMessages": "4075", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4076", "messages": "4077", "suppressedMessages": "4078", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4079", "messages": "4080", "suppressedMessages": "4081", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4082", "messages": "4083", "suppressedMessages": "4084", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4085", "messages": "4086", "suppressedMessages": "4087", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4088", "messages": "4089", "suppressedMessages": "4090", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4091", "messages": "4092", "suppressedMessages": "4093", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4094", "messages": "4095", "suppressedMessages": "4096", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4097", "messages": "4098", "suppressedMessages": "4099", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4100", "messages": "4101", "suppressedMessages": "4102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4103", "messages": "4104", "suppressedMessages": "4105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4106", "messages": "4107", "suppressedMessages": "4108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4109", "messages": "4110", "suppressedMessages": "4111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4112", "messages": "4113", "suppressedMessages": "4114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4115", "messages": "4116", "suppressedMessages": "4117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4118", "messages": "4119", "suppressedMessages": "4120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4121", "messages": "4122", "suppressedMessages": "4123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4124", "messages": "4125", "suppressedMessages": "4126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4127", "messages": "4128", "suppressedMessages": "4129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4130", "messages": "4131", "suppressedMessages": "4132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4133", "messages": "4134", "suppressedMessages": "4135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4136", "messages": "4137", "suppressedMessages": "4138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4139", "messages": "4140", "suppressedMessages": "4141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4142", "messages": "4143", "suppressedMessages": "4144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4145", "messages": "4146", "suppressedMessages": "4147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4148", "messages": "4149", "suppressedMessages": "4150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4151", "messages": "4152", "suppressedMessages": "4153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4154", "messages": "4155", "suppressedMessages": "4156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4157", "messages": "4158", "suppressedMessages": "4159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4160", "messages": "4161", "suppressedMessages": "4162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4163", "messages": "4164", "suppressedMessages": "4165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4166", "messages": "4167", "suppressedMessages": "4168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4169", "messages": "4170", "suppressedMessages": "4171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4172", "messages": "4173", "suppressedMessages": "4174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4175", "messages": "4176", "suppressedMessages": "4177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4178", "messages": "4179", "suppressedMessages": "4180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4181", "messages": "4182", "suppressedMessages": "4183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4184", "messages": "4185", "suppressedMessages": "4186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4187", "messages": "4188", "suppressedMessages": "4189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4190", "messages": "4191", "suppressedMessages": "4192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4193", "messages": "4194", "suppressedMessages": "4195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4196", "messages": "4197", "suppressedMessages": "4198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4199", "messages": "4200", "suppressedMessages": "4201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4202", "messages": "4203", "suppressedMessages": "4204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4205", "messages": "4206", "suppressedMessages": "4207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4208", "messages": "4209", "suppressedMessages": "4210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4211", "messages": "4212", "suppressedMessages": "4213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4214", "messages": "4215", "suppressedMessages": "4216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4217", "messages": "4218", "suppressedMessages": "4219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4220", "messages": "4221", "suppressedMessages": "4222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4223", "messages": "4224", "suppressedMessages": "4225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4226", "messages": "4227", "suppressedMessages": "4228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4229", "messages": "4230", "suppressedMessages": "4231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4232", "messages": "4233", "suppressedMessages": "4234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4235", "messages": "4236", "suppressedMessages": "4237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4238", "messages": "4239", "suppressedMessages": "4240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4241", "messages": "4242", "suppressedMessages": "4243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4244", "messages": "4245", "suppressedMessages": "4246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4247", "messages": "4248", "suppressedMessages": "4249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4250", "messages": "4251", "suppressedMessages": "4252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4253", "messages": "4254", "suppressedMessages": "4255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4256", "messages": "4257", "suppressedMessages": "4258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4259", "messages": "4260", "suppressedMessages": "4261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4262", "messages": "4263", "suppressedMessages": "4264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4265", "messages": "4266", "suppressedMessages": "4267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4268", "messages": "4269", "suppressedMessages": "4270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4271", "messages": "4272", "suppressedMessages": "4273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4274", "messages": "4275", "suppressedMessages": "4276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4277", "messages": "4278", "suppressedMessages": "4279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4280", "messages": "4281", "suppressedMessages": "4282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4283", "messages": "4284", "suppressedMessages": "4285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4286", "messages": "4287", "suppressedMessages": "4288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4289", "messages": "4290", "suppressedMessages": "4291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4292", "messages": "4293", "suppressedMessages": "4294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4295", "messages": "4296", "suppressedMessages": "4297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4298", "messages": "4299", "suppressedMessages": "4300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4301", "messages": "4302", "suppressedMessages": "4303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4304", "messages": "4305", "suppressedMessages": "4306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4307", "messages": "4308", "suppressedMessages": "4309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4310", "messages": "4311", "suppressedMessages": "4312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4313", "messages": "4314", "suppressedMessages": "4315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4316", "messages": "4317", "suppressedMessages": "4318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4319", "messages": "4320", "suppressedMessages": "4321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4322", "messages": "4323", "suppressedMessages": "4324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4325", "messages": "4326", "suppressedMessages": "4327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4328", "messages": "4329", "suppressedMessages": "4330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4331", "messages": "4332", "suppressedMessages": "4333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4334", "messages": "4335", "suppressedMessages": "4336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4337", "messages": "4338", "suppressedMessages": "4339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4340", "messages": "4341", "suppressedMessages": "4342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4343", "messages": "4344", "suppressedMessages": "4345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4346", "messages": "4347", "suppressedMessages": "4348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4349", "messages": "4350", "suppressedMessages": "4351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4352", "messages": "4353", "suppressedMessages": "4354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4355", "messages": "4356", "suppressedMessages": "4357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4358", "messages": "4359", "suppressedMessages": "4360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4361", "messages": "4362", "suppressedMessages": "4363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4364", "messages": "4365", "suppressedMessages": "4366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4367", "messages": "4368", "suppressedMessages": "4369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4370", "messages": "4371", "suppressedMessages": "4372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4373", "messages": "4374", "suppressedMessages": "4375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4376", "messages": "4377", "suppressedMessages": "4378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4379", "messages": "4380", "suppressedMessages": "4381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4382", "messages": "4383", "suppressedMessages": "4384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4385", "messages": "4386", "suppressedMessages": "4387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4388", "messages": "4389", "suppressedMessages": "4390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4391", "messages": "4392", "suppressedMessages": "4393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4394", "messages": "4395", "suppressedMessages": "4396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4397", "messages": "4398", "suppressedMessages": "4399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4400", "messages": "4401", "suppressedMessages": "4402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4403", "messages": "4404", "suppressedMessages": "4405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4406", "messages": "4407", "suppressedMessages": "4408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4409", "messages": "4410", "suppressedMessages": "4411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4412", "messages": "4413", "suppressedMessages": "4414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4415", "messages": "4416", "suppressedMessages": "4417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4418", "messages": "4419", "suppressedMessages": "4420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4421", "messages": "4422", "suppressedMessages": "4423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4424", "messages": "4425", "suppressedMessages": "4426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4427", "messages": "4428", "suppressedMessages": "4429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4430", "messages": "4431", "suppressedMessages": "4432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4433", "messages": "4434", "suppressedMessages": "4435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4436", "messages": "4437", "suppressedMessages": "4438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4439", "messages": "4440", "suppressedMessages": "4441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4442", "messages": "4443", "suppressedMessages": "4444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4445", "messages": "4446", "suppressedMessages": "4447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4448", "messages": "4449", "suppressedMessages": "4450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4451", "messages": "4452", "suppressedMessages": "4453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4454", "messages": "4455", "suppressedMessages": "4456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4457", "messages": "4458", "suppressedMessages": "4459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4460", "messages": "4461", "suppressedMessages": "4462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4463", "messages": "4464", "suppressedMessages": "4465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4466", "messages": "4467", "suppressedMessages": "4468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4469", "messages": "4470", "suppressedMessages": "4471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4472", "messages": "4473", "suppressedMessages": "4474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4475", "messages": "4476", "suppressedMessages": "4477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4478", "messages": "4479", "suppressedMessages": "4480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4481", "messages": "4482", "suppressedMessages": "4483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4484", "messages": "4485", "suppressedMessages": "4486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4487", "messages": "4488", "suppressedMessages": "4489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4490", "messages": "4491", "suppressedMessages": "4492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4493", "messages": "4494", "suppressedMessages": "4495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4496", "messages": "4497", "suppressedMessages": "4498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4499", "messages": "4500", "suppressedMessages": "4501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\ChooseRoleClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(auth)\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\activities\\components\\ActivitiesPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\activities\\components\\ActivityItem.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\activities\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\DailyVisitTrendChart.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedAnalyticsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedChartCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedEngagementMetricsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedMetricCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedVisitMetricsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\HourlyVisitTrendChart.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\MonthlyVisitTrendChart.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\PremiumFeatureLock.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\customAdUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\customHeaderUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\themeHeaderActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\themeSpecificHeaderUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\business-card\\getBusinessCardData.ts", ["4502"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\business-card\\updateBusinessCard.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\CardEditorClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\BusinessCardPreview.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardBackgroundEffects.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardBusinessInfo.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardCornerDecorations.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardDivider.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\AppearanceSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BasicInfoSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BusinessDetailsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BusinessHoursEditor.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\CardEditFormContent.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\ContactLocationSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\CustomAdUpload.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\CustomBrandingSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\formStyles.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\FormSubmitButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\LinksSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\StatusSlugSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\ThemeSpecificHeaderUpload.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardGlowEffects.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\DownloadButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\index.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\ShareButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardProfile.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardTextures.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CustomAdCropDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\EnhancedInteractionButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\useLogoUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\usePincodeDetails.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\useSlugCheck.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\ImageCropDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\LogoDeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\UnsavedChangesReminder.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\utils\\cardUtils.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\data\\businessCardMapper.ts", ["4503"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\data\\subscriptionChecker.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\logo\\logoActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\public\\publicCardActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\schema.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\slug\\slugUtils.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\businessHoursProcessor.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\constants.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\scrollToError.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\slugGenerator.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\validation\\businessCardValidation.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\AnimatedMetricCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\AnimatedSubscriptionStatus.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClientLayout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessStatusAlert.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\DashboardOverviewClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\EnhancedQuickActions.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\EnhancedSubscriptionStatus.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\FlipTimer.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\RecentActivities.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\DeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\EmptyGallery.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\GalleryGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\GalleryHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\Lightbox.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\ReorderControls.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\SortableImageItem.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\StaticImageItem.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\UploadBox.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\UploadDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\GalleryPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useDragAndDrop.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useGalleryState.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useReordering.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\types\\galleryTypes.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\utils\\fileValidation.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\utils.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\actions.ts", ["4504"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessLikesPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessLikesReceivedList.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessMyLikesList.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\LikeCardClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\LikeListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\overview\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\animations.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\BillingToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\BusinessPlanSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\ConfirmationDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\DialogBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\DialogManager.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedActionButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedCurrentPlanSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedGlowButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedInvoiceHistoryCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedPlanPageWithManager.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedPlanSelectionSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedSubscriptionDetailsCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedTrialAlert.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\FirstTimePaidPlanDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\PaymentMethodLimitationsDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\PlanBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\PlanPageContainer.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\RealtimePlanPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SimplifiedPlanActionDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\DialogComponents.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\EligiblePaymentMethodsCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\EnhancedPaymentHistoryCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\EnhancedSubscriptionActionCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\hooks.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernCancellationDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernRefundDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernSubscriptionStatusCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernTabs.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionManager.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionProcessingIndicator.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionTabsToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SubscriptionStatusBadge.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SubscriptionStatusIndicator.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SubscriptionTabContent.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\TrialManagement.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\TrialSubscriptionWarningDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\UpiPaymentMethodWarning.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\WebhookWaitingIndicator.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\context\\SubscriptionProcessingContext.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\hooks\\useSubscriptionHandler.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\hooks\\useSubscriptionLogic.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\hooks\\useUrlParameterHandler.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\PlanPageWrapper.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\addProduct.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\addVariant.ts", ["4505", "4506", "4507", "4508", "4509", "4510", "4511"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\bulkVariantOperations.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\deleteProduct.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\deleteVariant.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\getProducts.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\getProductWithVariants.ts", ["4512", "4513"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\image-library.ts", ["4514", "4515", "4516"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\imageHandlers.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\schemas.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\updateProduct.ts", ["4517"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\updateVariant.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\AddProductClient.tsx", ["4518", "4519", "4520", "4521"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\BulkVariantOperations.tsx", ["4522"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\hooks\\useProductImageUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\hooks\\useProductMultiImageUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ImageLibraryDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductDeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductEmptyState.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductFilters.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductItemsPerPageSelector.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductLoadingState.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductPagination.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductStats.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductViewToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductImageCropDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductMultiImageUpload.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\StandaloneProductForm.tsx", ["4523", "4524", "4525"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantCombinationGenerator.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantForm.tsx", ["4526", "4527"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantTable.tsx", ["4528", "4529"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantTypeSelector.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\context\\ProductsContext.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\EditProductClient.tsx", ["4530"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\page.tsx", ["4531"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\ProductsClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessMyReviewListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessReviewListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessReviewsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\AccountDeletionSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\CardEditorLinkSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\EmailUpdateDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\LinkEmailSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\LinkPhoneSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\SettingsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\schema.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\components\\BusinessSubscriptionsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerAnimatedMetricCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerDashboardClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerMetricsOverview.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\CustomerDashboardClientLayout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\actions.ts", ["4532"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\components\\LikesPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\LikeListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\overview\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\avatar-actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AddressForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AvatarDeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AvatarUpload.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\EmailForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\hooks\\usePincodeDetails.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\MobileForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\PhoneForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\ProfilePageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\ProfileRequirementDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\hooks\\useAvatarUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\ProfileForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\EnhancedReviewListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\ReviewsBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\ReviewsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\ReviewListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\EmailUpdateDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\LinkEmailSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\LinkPhoneSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\SettingsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\CustomerSettingsForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\DeleteAccountSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\schema.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\UpdateEmailForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionPagination.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionsBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionSearch.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\SubscriptionCardClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\SubscriptionListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\AboutUsClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\AboutCTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\AboutHeroSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\animations\\AboutAnimatedBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\CoreValuesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\MilestonesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\MissionVisionSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\StorySection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\actions\\getHomepageBusinessCard.ts", ["4533", "4534"], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\AdvertisePageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\BenefitsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\ContactSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\FAQSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\HeroSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\HubSpotForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\AuthCallbackClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\AuthCallbackWrapper.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\metadata.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\components\\BlogListingClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\[blogSlug]\\components\\BlogPostClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\[blogSlug]\\page.tsx", ["4535"], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\AnimatedBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedAuthCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AuthPageBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\CardShowcase.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\DeviceFrame.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\FeatureElements.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\FuturisticScanner.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ActionButtonsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\animations.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\BillingToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\CardBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\CTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\DigitalCardFeature.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedCardShowcase.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedFeatureElements.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedMetricsContainer.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedMetricsDisplay.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ErrorDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\exampleCardData.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FeatureCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FeaturesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FloatingParticlesBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FloatingPricingElements.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HeroActionButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HeroSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HomeCategoriesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\MobileFeatureCarousel.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ModernSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\NewArrivalsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\PopularBusinessesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\PricingSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\SectionDivider.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\StickyHeroSectionClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialCarousel.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\MetricsContainer.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\MetricsParticles.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\MetricsDisplay.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyCTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyHeroSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyNavigation.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicySection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\SectionBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\shared\\FeatureComparisonTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\shared\\PopularCategoriesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\animations\\ContactAnimatedBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactCTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactHeroSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactInfoSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactMapSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\ModernContactClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\cookies\\ModernCookiePolicyClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\cookies\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\businessActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\combinedActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\locationActions.ts", ["4536", "4537", "4538"], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\productActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBadge.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessGridSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedDivider.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessCardTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessCardTableSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessResultsGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessSortControls.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CategoryCarousel.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CategoryFilter.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CitySearchSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ErrorSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\FloatingCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ImprovedSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\LoadingSpinner.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\LocationIndicator.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernBusinessFilterGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernBusinessResults.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernResultsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\NoResultsMessage.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductGridSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductResults.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductResultsGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductSortControls.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ResultsSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\RevealTitle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SearchResultsHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SectionTitle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SortingControls.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ViewToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\constants\\paginationConstants.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\constants\\urlParamConstants.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\businessContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\commonContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\DiscoverContext.tsx", [], ["4539"], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\productContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\ModernDiscoverClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\ModernResultsSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\utils\\sortMappings.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\email-change-success\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\BusinessUseCasesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureAnimation.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeaturesCTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\HeroFeatureSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\PlanComparisonSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\ModernFeaturesClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\hooks\\useIntersectionAnimation.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\LandingPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\AuthMethodToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\EmailOTPForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\MobilePasswordForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\SocialLoginButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\LoginForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\animations\\AnimatedTitle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\animations\\PricingAnimatedBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingCards.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingCTA.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingFAQ.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingHero.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\FeatureComparisonTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\EnhancedPricingPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\privacy\\ModernPrivacyPolicyClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\privacy\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\refund\\ModernRefundPolicyClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\refund\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\account-billing\\AccountBillingClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\account-billing\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\analytics\\AnalyticsClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\analytics\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\business-card-setup\\BusinessCardSetupClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\business-card-setup\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\AnimatedTitle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedFAQSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedSupportFAQ.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedSupportSubPage.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\product-management\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\product-management\\ProductManagementClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\SettingsClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\SupportPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\TechnicalIssuesClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\terms\\ModernTermsOfServiceClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\terms\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\actions.ts", ["4540", "4541", "4542", "4543"], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\LoadingOverlay.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\NavigationButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\StepProgress.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\AddressStep.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\BusinessDetailsStep.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\CardInformationStep.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\PlanSelectionStep.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\constants\\onboardingSteps.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useExistingData.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useOnboardingForm.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\usePincodeDetails.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useSlugAvailability.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useUserData.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\OnboardingClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\types\\onboarding.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\admin\\fix-subscription-inconsistency\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\business\\likes\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\business\\my-likes\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\business\\my-reviews\\route.ts", ["4544"], [], "C:\\web-app\\dukancard\\app\\api\\business\\reviews\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\check-user-type\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\customer\\likes\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\customer\\reviews\\route.ts", ["4545"], [], "C:\\web-app\\dukancard\\app\\api\\customer\\reviews\\update\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\health\\subscription\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\razorpay\\key\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscription\\[id]\\payments\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\centralized\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\list\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\my\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\cancel\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\invoices\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\pause\\route.ts", ["4546", "4547"], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\payments\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\payments\\[paymentId]\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\pending-update\\route.ts", ["4548", "4549", "4550", "4551", "4552", "4553"], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\databaseOperations.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\errorHandlers.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\paymentHelpers.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\razorpayApi.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\validators.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\resume\\route.ts", ["4554", "4555"], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\route.ts", ["4556", "4557"], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\scheduled-changes\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\switch\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\switch-with-new-payment\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\update\\route.ts", ["4558", "4559", "4560"], [], "C:\\web-app\\dukancard\\app\\api\\test\\subscription-flow\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\test\\subscription-scenarios\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\create\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\delete\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\get\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\list\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\retry\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\update\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\auth\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\cards\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\app\\components\\AdSlot.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\AdvertiseButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\BottomNav.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\EnhancedPublicCardActions.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\FloatingAIButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\FloatingInteractionButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\Footer.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\GoogleAnalytics.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\Header.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\FacebookIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\InstagramIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\LinkedInIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\PinterestIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\TelegramIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\TwitterIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\WhatsAppIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\YouTubeIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\LoadingOverlay.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\MetaPixel.tsx", [], ["4561"], "C:\\web-app\\dukancard\\app\\components\\MinimalFooter.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\MinimalHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\MobileFooter.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\PricingCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\PricingCardContext.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\ProductListItem.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\PublicCardView.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\AuthMessage.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewSignInPrompt.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsList.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsSummary.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsTab.tsx", [], ["4562"], "C:\\web-app\\dukancard\\app\\components\\ReviewsTab.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\EnhancedCardActions.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeList.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikePagination.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeSearch.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewSortDropdown.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionList.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionPagination.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionSearch.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\ThemeToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\ui\\container.tsx", [], [], "C:\\web-app\\dukancard\\app\\context\\PaymentMethodLimitationsContext.tsx", [], [], "C:\\web-app\\dukancard\\app\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\actions\\businessActions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\actions\\combinedActions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\actions\\locationActions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\actions\\productActions.ts", ["4563"], ["4564"], "C:\\web-app\\dukancard\\app\\locality\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\BreadcrumbNav.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\ErrorSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\ImprovedSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\LocationIndicator.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\ModernResultsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\constants\\paginationConstants.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\context\\businessContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\context\\commonContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\context\\LocalityContext.tsx", [], ["4565"], "C:\\web-app\\dukancard\\app\\locality\\context\\productContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\context\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\LocalityPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\post\\[postId]\\error.tsx", [], [], "C:\\web-app\\dukancard\\app\\post\\[postId]\\loading.tsx", [], [], "C:\\web-app\\dukancard\\app\\post\\[postId]\\not-found.tsx", [], [], "C:\\web-app\\dukancard\\app\\post\\[postId]\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\products\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\app\\robots.ts", [], [], "C:\\web-app\\dukancard\\app\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\animations.ts", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\BusinessDetails\\EnhancedMetricsCards.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\BusinessDetails\\ProfessionalBusinessTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedAdSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessCardSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessDetails.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessGallery.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedPublicCardPageWrapper.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedTabsToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\GalleryTab.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\OfflineBusinessMessage.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\ProductGridSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\ProductsTab.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\VisitTracker.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\GalleryPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\page.tsx", ["4566"], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\loading.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\page.tsx", ["4567", "4568"], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\actions.ts", ["4569"], ["4570"], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\BuyNowButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ImageZoomModal.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\OfflineProductMessage.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\PhoneButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductAdSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductDetail.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductRecommendations.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\VariantSelector.tsx", ["4571", "4572", "4573", "4574"], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\WhatsAppButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\hooks\\usePinchZoom.ts", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\page.tsx", ["4575"], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\ProductDetailClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\PublicCardPageClient.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogCard.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogContent.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogListingSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogNavigation.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogPagination.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogPostSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogSearch.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\ModernBusinessFeedList.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\ModernCustomerFeedList.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\dialogs\\PostDeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\editors\\InlinePostAndProductEditor.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\editors\\InlinePostEditor.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\FilterPills.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\ImageCropper.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\LocationDisplay.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\ProductSelector.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\useCustomerPostMediaUpload.ts", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\usePostMediaUpload.ts", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\usePostOwnership.ts", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernCustomerPostCard.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernFeedContainer.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernFeedHeader.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernPostCard.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\PostActions.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\PostCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\SocialMediaBusinessPostCreator.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\SocialMediaPostCreator.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\UnifiedPostCard.tsx", [], [], "C:\\web-app\\dukancard\\components\\post\\BackNavigation.tsx", [], [], "C:\\web-app\\dukancard\\components\\post\\ConditionalPostLayout.tsx", [], [], "C:\\web-app\\dukancard\\components\\post\\PostShareButton.tsx", [], [], "C:\\web-app\\dukancard\\components\\post\\SinglePostView.tsx", [], [], "C:\\web-app\\dukancard\\components\\qr\\QRScanner.tsx", ["4576"], [], "C:\\web-app\\dukancard\\components\\qr\\QRScannerModal.tsx", ["4577"], [], "C:\\web-app\\dukancard\\components\\sidebar\\BusinessAppSidebar.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\CustomerAppSidebar.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\NavBusinessMain.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\NavBusinessUser.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\NavCustomerMain.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\NavCustomerUser.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\SidebarLink.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\accordion.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\alert-dialog.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\alert.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\avatar.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\badge.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\breadcrumb.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\button.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\calendar.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\card.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\carousel.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\category-combobox.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\chart.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\checkbox.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\collapsible.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\command.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\dialog.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\form.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\input-otp.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\input.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\label.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\multi-select-combobox.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\pagination.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\popover.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\progress.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\radio-group.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\resizable-navbar.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\scroll-area.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\scroll-to-top-button.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\select.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\separator.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\sheet.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\sidebar.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\skeleton.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\slider.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\sonner.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\switch.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\table.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\tabs.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\textarea.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\toast.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\tooltip.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\use-toast.ts", [], [], "C:\\web-app\\dukancard\\components\\ui\\visually-hidden.tsx", [], [], "C:\\web-app\\dukancard\\lib\\actions\\activities.ts", ["4578"], [], "C:\\web-app\\dukancard\\lib\\actions\\blogs.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\access.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\discovery.ts", ["4579", "4580", "4581"], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\location.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\profileRetrieval.ts", ["4582"], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\search.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\utils.ts", [], ["4583", "4584"], "C:\\web-app\\dukancard\\lib\\actions\\categories\\locationBasedFetching.ts", ["4585"], [], "C:\\web-app\\dukancard\\lib\\actions\\categories\\pincodeTypes.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\customerPosts\\crud.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\customerPosts\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\customerProfiles\\addressValidation.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\gallery.ts", ["4586", "4587", "4588", "4589", "4590", "4591", "4592", "4593"], [], "C:\\web-app\\dukancard\\lib\\actions\\interactions.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\location\\locationBySlug.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\location.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\crud.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\fetchSinglePost.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\unifiedFeed.ts", ["4594", "4595", "4596", "4597"], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\utils.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\products\\fetchProductsByIds.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\products\\sitemapHelpers.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\redirectAfterLogin.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\reviews.ts", ["4598"], [], "C:\\web-app\\dukancard\\lib\\actions\\secureCustomerProfiles.ts", ["4599", "4600", "4601", "4602"], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\delete-customer-post-media.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\productActions.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-business-post-media.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-customer-post-media.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-post-media.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\activateTrial.ts", ["4603", "4604", "4605", "4606"], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\centralized.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\confirm.ts", ["4607", "4608", "4609", "4610", "4611", "4612"], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\create.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\cancel.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\change.ts", ["4613", "4614", "4615"], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\manage.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\schedule.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\switch.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\payment.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\status.ts", ["4616", "4617", "4618", "4619", "4620"], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\utils.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\user\\getUserAndProfile.ts", [], [], "C:\\web-app\\dukancard\\lib\\api\\response.ts", [], [], "C:\\web-app\\dukancard\\lib\\cardDownloader.ts", [], [], "C:\\web-app\\dukancard\\lib\\client\\locationUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\config\\categories.ts", [], [], "C:\\web-app\\dukancard\\lib\\config\\plans.ts", [], [], "C:\\web-app\\dukancard\\lib\\config\\states.ts", [], [], "C:\\web-app\\dukancard\\lib\\constants\\predefinedVariants.ts", [], [], "C:\\web-app\\dukancard\\lib\\csrf.ts", [], [], "C:\\web-app\\dukancard\\lib\\errorHandling.ts", [], ["4621", "4622"], "C:\\web-app\\dukancard\\lib\\PricingPlans.ts", [], [], "C:\\web-app\\dukancard\\lib\\qrCodeGenerator.ts", [], [], "C:\\web-app\\dukancard\\lib\\rateLimiter.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\razorpayClient.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\create.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\get.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\update.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\invoice.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment\\getPayment.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\plan.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\cancel.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\create.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\get.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\scheduled.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\update.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\webhook.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\types\\api.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\utils\\auth.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\utils\\loadRazorpaySDK.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\errorTracking.ts", ["4623", "4624", "4625"], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\eventManager.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\subscriptionManager.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\validation\\eventOrderValidator.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\validation\\stateTransitionValidator.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\mainHandler.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\moreSubscriptionHandlers.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\paymentHandlers.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\refundHandlers.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-constants.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-db-updater.ts", ["4626", "4627", "4628"], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-state-manager.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-state-validator.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionActivated.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionAuthenticated.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionCancelled.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionCharged.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionCompleted.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionExpired.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionHalted.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionPending.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionUpdated.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionHandlers.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\transactionUtils.ts", ["4629", "4630", "4631", "4632", "4633", "4634", "4635", "4636", "4637", "4638"], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\utils.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\webhook-utils.ts", [], ["4639"], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\webhookProcessor.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handleWebhook.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\idempotency.ts", ["4640"], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\monitoring.ts", ["4641", "4642", "4643"], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\validation.ts", [], [], "C:\\web-app\\dukancard\\lib\\schemas\\authSchemas.ts", [], [], "C:\\web-app\\dukancard\\lib\\schemas\\locationSchemas.ts", [], [], "C:\\web-app\\dukancard\\lib\\services\\realtimeService.ts", [], ["4644"], "C:\\web-app\\dukancard\\lib\\services\\subscription.ts", ["4645"], [], "C:\\web-app\\dukancard\\lib\\site-config.ts", [], [], "C:\\web-app\\dukancard\\lib\\siteContent.ts", [], [], "C:\\web-app\\dukancard\\lib\\subscription\\edge-validation.ts", [], [], "C:\\web-app\\dukancard\\lib\\subscription\\SubscriptionFlowManager.ts", [], [], "C:\\web-app\\dukancard\\lib\\subscription\\SubscriptionFlowTester.ts", [], [], "C:\\web-app\\dukancard\\lib\\subscription\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\supabase\\constants.ts", [], [], "C:\\web-app\\dukancard\\lib\\supabase\\services\\businessService.ts", ["4646", "4647"], [], "C:\\web-app\\dukancard\\lib\\supabase\\services\\customerService.ts", [], [], "C:\\web-app\\dukancard\\lib\\supabase\\services\\sharedService.ts", ["4648"], [], "C:\\web-app\\dukancard\\lib\\testing\\database.ts", ["4649"], [], "C:\\web-app\\dukancard\\lib\\testing\\mswHandlers.ts", [], ["4650", "4651"], "C:\\web-app\\dukancard\\lib\\testing\\SubscriptionScenarioTester.ts", [], [], "C:\\web-app\\dukancard\\lib\\testing\\testDataFactories.ts", [], [], "C:\\web-app\\dukancard\\lib\\testing\\testUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\testing\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\activities.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\api.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\blog.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\posts.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\subscription.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\addressUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\addressValidation.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\cameraUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\client-image-compression.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\customBranding.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\debounce.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\diversityEngine.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\feedMerger.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\hybridTimeAndPlanAlgorithm.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\optimizedHybridAlgorithm.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\planPrioritizer.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\postCreationHandler.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\smartFeedAlgorithm.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\image-compression.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\markdown.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\pagination.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\postUrl.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\qrCodeUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\seo.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\slugUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\storage-paths.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\supabaseErrorHandler.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\variantHelpers.ts", ["4652", "4653"], [], "C:\\web-app\\dukancard\\lib\\utils.ts", [], [], {"ruleId": "4654", "severity": 1, "message": "4655", "line": 75, "column": 50, "nodeType": "4656", "messageId": "4657", "endLine": 75, "endColumn": 53, "suggestions": "4658"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 107, "column": 32, "nodeType": "4656", "messageId": "4657", "endLine": 107, "endColumn": 35, "suggestions": "4659"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 154, "column": 118, "nodeType": "4656", "messageId": "4657", "endLine": 154, "endColumn": 121, "suggestions": "4660"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 141, "column": 41, "nodeType": "4656", "messageId": "4657", "endLine": 141, "endColumn": 44, "suggestions": "4661"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 145, "column": 21, "nodeType": "4656", "messageId": "4657", "endLine": 145, "endColumn": 24, "suggestions": "4662"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 153, "column": 170, "nodeType": "4656", "messageId": "4657", "endLine": 153, "endColumn": 173, "suggestions": "4663"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 258, "column": 65, "nodeType": "4656", "messageId": "4657", "endLine": 258, "endColumn": 68, "suggestions": "4664"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 262, "column": 21, "nodeType": "4656", "messageId": "4657", "endLine": 262, "endColumn": 24, "suggestions": "4665"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 263, "column": 41, "nodeType": "4656", "messageId": "4657", "endLine": 263, "endColumn": 44, "suggestions": "4666"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 263, "column": 95, "nodeType": "4656", "messageId": "4657", "endLine": 263, "endColumn": 98, "suggestions": "4667"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 60, "column": 35, "nodeType": "4656", "messageId": "4657", "endLine": 60, "endColumn": 38, "suggestions": "4668"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 61, "column": 35, "nodeType": "4656", "messageId": "4657", "endLine": 61, "endColumn": 38, "suggestions": "4669"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 66, "column": 54, "nodeType": "4656", "messageId": "4657", "endLine": 66, "endColumn": 57, "suggestions": "4670"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 110, "column": 52, "nodeType": "4656", "messageId": "4657", "endLine": 110, "endColumn": 55, "suggestions": "4671"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 150, "column": 60, "nodeType": "4656", "messageId": "4657", "endLine": 150, "endColumn": 63, "suggestions": "4672"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 113, "column": 20, "nodeType": "4656", "messageId": "4657", "endLine": 113, "endColumn": 23, "suggestions": "4673"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 142, "column": 33, "nodeType": "4656", "messageId": "4657", "endLine": 142, "endColumn": 36, "suggestions": "4674"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 142, "column": 65, "nodeType": "4656", "messageId": "4657", "endLine": 142, "endColumn": 68, "suggestions": "4675"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 143, "column": 31, "nodeType": "4656", "messageId": "4657", "endLine": 143, "endColumn": 34, "suggestions": "4676"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 143, "column": 63, "nodeType": "4656", "messageId": "4657", "endLine": 143, "endColumn": 66, "suggestions": "4677"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 424, "column": 63, "nodeType": "4656", "messageId": "4657", "endLine": 424, "endColumn": 66, "suggestions": "4678"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 291, "column": 70, "nodeType": "4656", "messageId": "4657", "endLine": 291, "endColumn": 73, "suggestions": "4679"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 295, "column": 31, "nodeType": "4656", "messageId": "4657", "endLine": 295, "endColumn": 34, "suggestions": "4680"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 308, "column": 27, "nodeType": "4656", "messageId": "4657", "endLine": 308, "endColumn": 30, "suggestions": "4681"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 70, "column": 37, "nodeType": "4656", "messageId": "4657", "endLine": 70, "endColumn": 40, "suggestions": "4682"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 106, "column": 55, "nodeType": "4656", "messageId": "4657", "endLine": 106, "endColumn": 58, "suggestions": "4683"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 292, "column": 65, "nodeType": "4656", "messageId": "4657", "endLine": 292, "endColumn": 68, "suggestions": "4684"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 429, "column": 79, "nodeType": "4656", "messageId": "4657", "endLine": 429, "endColumn": 82, "suggestions": "4685"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 191, "column": 54, "nodeType": "4656", "messageId": "4657", "endLine": 191, "endColumn": 57, "suggestions": "4686"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 115, "column": 73, "nodeType": "4656", "messageId": "4657", "endLine": 115, "endColumn": 76, "suggestions": "4687"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 106, "column": 119, "nodeType": "4656", "messageId": "4657", "endLine": 106, "endColumn": 122, "suggestions": "4688"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 89, "column": 59, "nodeType": "4656", "messageId": "4657", "endLine": 89, "endColumn": 62, "suggestions": "4689"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 90, "column": 49, "nodeType": "4656", "messageId": "4657", "endLine": 90, "endColumn": 52, "suggestions": "4690"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 79, "column": 37, "nodeType": "4656", "messageId": "4657", "endLine": 79, "endColumn": 40, "suggestions": "4691"}, {"ruleId": "4692", "severity": 1, "message": "4693", "line": 317, "column": 13, "nodeType": "4694", "messageId": "4695", "endLine": 317, "endColumn": 17}, {"ruleId": "4696", "severity": 1, "message": "4693", "line": 317, "column": 13, "nodeType": null, "messageId": "4695", "endLine": 317, "endColumn": 17}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 341, "column": 31, "nodeType": "4656", "messageId": "4657", "endLine": 341, "endColumn": 34, "suggestions": "4697"}, {"ruleId": "4698", "severity": 1, "message": "4699", "line": 282, "column": 6, "nodeType": "4700", "endLine": 282, "endColumn": 8, "suggestions": "4701", "suppressions": "4702"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 372, "column": 19, "nodeType": "4656", "messageId": "4657", "endLine": 372, "endColumn": 22, "suggestions": "4703"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 373, "column": 67, "nodeType": "4656", "messageId": "4657", "endLine": 373, "endColumn": 70, "suggestions": "4704"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 374, "column": 20, "nodeType": "4656", "messageId": "4657", "endLine": 374, "endColumn": 23, "suggestions": "4705"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 380, "column": 32, "nodeType": "4656", "messageId": "4657", "endLine": 380, "endColumn": 35, "suggestions": "4706"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 119, "column": 43, "nodeType": "4656", "messageId": "4657", "endLine": 119, "endColumn": 46, "suggestions": "4707"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 117, "column": 43, "nodeType": "4656", "messageId": "4657", "endLine": 117, "endColumn": 46, "suggestions": "4708"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 168, "column": 42, "nodeType": "4656", "messageId": "4657", "endLine": 168, "endColumn": 45, "suggestions": "4709"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 169, "column": 113, "nodeType": "4656", "messageId": "4657", "endLine": 169, "endColumn": 116, "suggestions": "4710"}, {"ruleId": "4692", "severity": 1, "message": "4711", "line": 5, "column": 10, "nodeType": "4694", "messageId": "4695", "endLine": 5, "endColumn": 18, "suggestions": "4712"}, {"ruleId": "4696", "severity": 1, "message": "4711", "line": 5, "column": 10, "nodeType": null, "messageId": "4695", "endLine": 5, "endColumn": 18}, {"ruleId": "4692", "severity": 1, "message": "4713", "line": 59, "column": 11, "nodeType": "4694", "messageId": "4695", "endLine": 59, "endColumn": 15, "suggestions": "4714"}, {"ruleId": "4696", "severity": 1, "message": "4713", "line": 59, "column": 11, "nodeType": null, "messageId": "4695", "endLine": 59, "endColumn": 15}, {"ruleId": "4692", "severity": 1, "message": "4715", "line": 60, "column": 11, "nodeType": "4694", "messageId": "4695", "endLine": 60, "endColumn": 16, "suggestions": "4716"}, {"ruleId": "4696", "severity": 1, "message": "4715", "line": 60, "column": 11, "nodeType": null, "messageId": "4695", "endLine": 60, "endColumn": 16}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 166, "column": 42, "nodeType": "4656", "messageId": "4657", "endLine": 166, "endColumn": 45, "suggestions": "4717"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 167, "column": 113, "nodeType": "4656", "messageId": "4657", "endLine": 167, "endColumn": 116, "suggestions": "4718"}, {"ruleId": "4692", "severity": 1, "message": "4711", "line": 5, "column": 10, "nodeType": "4694", "messageId": "4695", "endLine": 5, "endColumn": 18, "suggestions": "4719"}, {"ruleId": "4696", "severity": 1, "message": "4711", "line": 5, "column": 10, "nodeType": null, "messageId": "4695", "endLine": 5, "endColumn": 18}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 246, "column": 44, "nodeType": "4656", "messageId": "4657", "endLine": 246, "endColumn": 47, "suggestions": "4720"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 254, "column": 42, "nodeType": "4656", "messageId": "4657", "endLine": 254, "endColumn": 45, "suggestions": "4721"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 255, "column": 113, "nodeType": "4656", "messageId": "4657", "endLine": 255, "endColumn": 116, "suggestions": "4722"}, {"ruleId": "4723", "severity": 1, "message": "4724", "line": 25, "column": 9, "nodeType": "4725", "endLine": 31, "endColumn": 11, "suppressions": "4726"}, {"ruleId": "4698", "severity": 1, "message": "4727", "line": 53, "column": 6, "nodeType": "4700", "endLine": 53, "endColumn": 46, "suggestions": "4728", "suppressions": "4729"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 133, "column": 54, "nodeType": "4656", "messageId": "4657", "endLine": 133, "endColumn": 57, "suggestions": "4730"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 142, "column": 57, "nodeType": "4656", "messageId": "4657", "endLine": 142, "endColumn": 60, "suggestions": "4731", "suppressions": "4732"}, {"ruleId": "4698", "severity": 1, "message": "4733", "line": 172, "column": 6, "nodeType": "4700", "endLine": 172, "endColumn": 8, "suggestions": "4734", "suppressions": "4735"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 92, "column": 35, "nodeType": "4656", "messageId": "4657", "endLine": 92, "endColumn": 38, "suggestions": "4736"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 200, "column": 57, "nodeType": "4656", "messageId": "4657", "endLine": 200, "endColumn": 60, "suggestions": "4737"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 201, "column": 47, "nodeType": "4656", "messageId": "4657", "endLine": 201, "endColumn": 50, "suggestions": "4738"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 244, "column": 17, "nodeType": "4656", "messageId": "4657", "endLine": 244, "endColumn": 20, "suggestions": "4739"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 441, "column": 53, "nodeType": "4656", "messageId": "4657", "endLine": 441, "endColumn": 56, "suggestions": "4740", "suppressions": "4741"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 67, "column": 79, "nodeType": "4656", "messageId": "4657", "endLine": 67, "endColumn": 82, "suggestions": "4742"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 117, "column": 81, "nodeType": "4656", "messageId": "4657", "endLine": 117, "endColumn": 84, "suggestions": "4743"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 156, "column": 88, "nodeType": "4656", "messageId": "4657", "endLine": 156, "endColumn": 91, "suggestions": "4744"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 169, "column": 69, "nodeType": "4656", "messageId": "4657", "endLine": 169, "endColumn": 72, "suggestions": "4745"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 205, "column": 55, "nodeType": "4656", "messageId": "4657", "endLine": 205, "endColumn": 58, "suggestions": "4746"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 45, "column": 21, "nodeType": "4656", "messageId": "4657", "endLine": 45, "endColumn": 24, "suggestions": "4747"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 78, "column": 21, "nodeType": "4656", "messageId": "4657", "endLine": 78, "endColumn": 24, "suggestions": "4748"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 362, "column": 38, "nodeType": "4656", "messageId": "4657", "endLine": 362, "endColumn": 41, "suggestions": "4749"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 115, "column": 23, "nodeType": "4656", "messageId": "4657", "endLine": 115, "endColumn": 26, "suggestions": "4750"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 122, "column": 24, "nodeType": "4656", "messageId": "4657", "endLine": 122, "endColumn": 27, "suggestions": "4751"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 123, "column": 19, "nodeType": "4656", "messageId": "4657", "endLine": 123, "endColumn": 22, "suggestions": "4752"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 80, "column": 42, "nodeType": "4656", "messageId": "4657", "endLine": 80, "endColumn": 45, "suggestions": "4753"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 9, "column": 37, "nodeType": "4656", "messageId": "4657", "endLine": 9, "endColumn": 40, "suggestions": "4754", "suppressions": "4755"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 9, "column": 67, "nodeType": "4656", "messageId": "4657", "endLine": 9, "endColumn": 70, "suggestions": "4756", "suppressions": "4757"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 289, "column": 56, "nodeType": "4656", "messageId": "4657", "endLine": 289, "endColumn": 59, "suggestions": "4758"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 35, "column": 24, "nodeType": "4656", "messageId": "4657", "endLine": 35, "endColumn": 27, "suggestions": "4759"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 35, "column": 68, "nodeType": "4656", "messageId": "4657", "endLine": 35, "endColumn": 71, "suggestions": "4760"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 102, "column": 24, "nodeType": "4656", "messageId": "4657", "endLine": 102, "endColumn": 27, "suggestions": "4761"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 102, "column": 68, "nodeType": "4656", "messageId": "4657", "endLine": 102, "endColumn": 71, "suggestions": "4762"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 174, "column": 24, "nodeType": "4656", "messageId": "4657", "endLine": 174, "endColumn": 27, "suggestions": "4763"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 174, "column": 68, "nodeType": "4656", "messageId": "4657", "endLine": 174, "endColumn": 71, "suggestions": "4764"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 271, "column": 24, "nodeType": "4656", "messageId": "4657", "endLine": 271, "endColumn": 27, "suggestions": "4765"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 271, "column": 68, "nodeType": "4656", "messageId": "4657", "endLine": 271, "endColumn": 71, "suggestions": "4766"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 98, "column": 64, "nodeType": "4656", "messageId": "4657", "endLine": 98, "endColumn": 67, "suggestions": "4767"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 242, "column": 26, "nodeType": "4656", "messageId": "4657", "endLine": 242, "endColumn": 29, "suggestions": "4768"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 251, "column": 49, "nodeType": "4656", "messageId": "4657", "endLine": 251, "endColumn": 52, "suggestions": "4769"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 255, "column": 18, "nodeType": "4656", "messageId": "4657", "endLine": 255, "endColumn": 21, "suggestions": "4770"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 122, "column": 70, "nodeType": "4656", "messageId": "4657", "endLine": 122, "endColumn": 73, "suggestions": "4771"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 82, "column": 42, "nodeType": "4656", "messageId": "4657", "endLine": 82, "endColumn": 45, "suggestions": "4772"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 111, "column": 42, "nodeType": "4656", "messageId": "4657", "endLine": 111, "endColumn": 45, "suggestions": "4773"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 282, "column": 41, "nodeType": "4656", "messageId": "4657", "endLine": 282, "endColumn": 44, "suggestions": "4774"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 292, "column": 41, "nodeType": "4656", "messageId": "4657", "endLine": 292, "endColumn": 44, "suggestions": "4775"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 98, "column": 34, "nodeType": "4656", "messageId": "4657", "endLine": 98, "endColumn": 37, "suggestions": "4776"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 105, "column": 12, "nodeType": "4656", "messageId": "4657", "endLine": 105, "endColumn": 15, "suggestions": "4777"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 109, "column": 42, "nodeType": "4656", "messageId": "4657", "endLine": 109, "endColumn": 45, "suggestions": "4778"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 110, "column": 91, "nodeType": "4656", "messageId": "4657", "endLine": 110, "endColumn": 94, "suggestions": "4779"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 413, "column": 42, "nodeType": "4656", "messageId": "4657", "endLine": 413, "endColumn": 45, "suggestions": "4780"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 417, "column": 44, "nodeType": "4656", "messageId": "4657", "endLine": 417, "endColumn": 47, "suggestions": "4781"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 418, "column": 121, "nodeType": "4656", "messageId": "4657", "endLine": 418, "endColumn": 124, "suggestions": "4782"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 448, "column": 32, "nodeType": "4656", "messageId": "4657", "endLine": 448, "endColumn": 35, "suggestions": "4783"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 452, "column": 42, "nodeType": "4656", "messageId": "4657", "endLine": 452, "endColumn": 45, "suggestions": "4784"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 453, "column": 100, "nodeType": "4656", "messageId": "4657", "endLine": 453, "endColumn": 103, "suggestions": "4785"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 159, "column": 10, "nodeType": "4656", "messageId": "4657", "endLine": 159, "endColumn": 13, "suggestions": "4786"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 163, "column": 40, "nodeType": "4656", "messageId": "4657", "endLine": 163, "endColumn": 43, "suggestions": "4787"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 164, "column": 94, "nodeType": "4656", "messageId": "4657", "endLine": 164, "endColumn": 97, "suggestions": "4788"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 120, "column": 12, "nodeType": "4656", "messageId": "4657", "endLine": 120, "endColumn": 15, "suggestions": "4789"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 124, "column": 42, "nodeType": "4656", "messageId": "4657", "endLine": 124, "endColumn": 45, "suggestions": "4790"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 125, "column": 113, "nodeType": "4656", "messageId": "4657", "endLine": 125, "endColumn": 116, "suggestions": "4791"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 239, "column": 42, "nodeType": "4656", "messageId": "4657", "endLine": 239, "endColumn": 45, "suggestions": "4792"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 240, "column": 113, "nodeType": "4656", "messageId": "4657", "endLine": 240, "endColumn": 116, "suggestions": "4793"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 6, "column": 18, "nodeType": "4656", "messageId": "4657", "endLine": 6, "endColumn": 21, "suggestions": "4794", "suppressions": "4795"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 13, "column": 10, "nodeType": "4656", "messageId": "4657", "endLine": 13, "endColumn": 13, "suggestions": "4796", "suppressions": "4797"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 57, "column": 27, "nodeType": "4656", "messageId": "4657", "endLine": 57, "endColumn": 30, "suggestions": "4798"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 112, "column": 29, "nodeType": "4656", "messageId": "4657", "endLine": 112, "endColumn": 32, "suggestions": "4799"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 157, "column": 12, "nodeType": "4656", "messageId": "4657", "endLine": 157, "endColumn": 15, "suggestions": "4800"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 386, "column": 60, "nodeType": "4656", "messageId": "4657", "endLine": 386, "endColumn": 63, "suggestions": "4801"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 398, "column": 21, "nodeType": "4656", "messageId": "4657", "endLine": 398, "endColumn": 24, "suggestions": "4802"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 402, "column": 29, "nodeType": "4656", "messageId": "4657", "endLine": 402, "endColumn": 32, "suggestions": "4803"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 149, "column": 58, "nodeType": "4656", "messageId": "4657", "endLine": 149, "endColumn": 61, "suggestions": "4804"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 153, "column": 42, "nodeType": "4656", "messageId": "4657", "endLine": 153, "endColumn": 45, "suggestions": "4805"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 154, "column": 130, "nodeType": "4656", "messageId": "4657", "endLine": 154, "endColumn": 133, "suggestions": "4806"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 157, "column": 89, "nodeType": "4656", "messageId": "4657", "endLine": 157, "endColumn": 92, "suggestions": "4807"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 279, "column": 42, "nodeType": "4656", "messageId": "4657", "endLine": 279, "endColumn": 45, "suggestions": "4808"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 280, "column": 171, "nodeType": "4656", "messageId": "4657", "endLine": 280, "endColumn": 174, "suggestions": "4809"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 283, "column": 87, "nodeType": "4656", "messageId": "4657", "endLine": 283, "endColumn": 90, "suggestions": "4810"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 371, "column": 42, "nodeType": "4656", "messageId": "4657", "endLine": 371, "endColumn": 45, "suggestions": "4811"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 372, "column": 138, "nodeType": "4656", "messageId": "4657", "endLine": 372, "endColumn": 141, "suggestions": "4812"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 375, "column": 93, "nodeType": "4656", "messageId": "4657", "endLine": 375, "endColumn": 96, "suggestions": "4813"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 12, "column": 50, "nodeType": "4656", "messageId": "4657", "endLine": 12, "endColumn": 53, "suggestions": "4814", "suppressions": "4815"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 121, "column": 14, "nodeType": "4656", "messageId": "4657", "endLine": 121, "endColumn": 17, "suggestions": "4816"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 64, "column": 61, "nodeType": "4656", "messageId": "4657", "endLine": 64, "endColumn": 64, "suggestions": "4817"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 69, "column": 87, "nodeType": "4656", "messageId": "4657", "endLine": 69, "endColumn": 90, "suggestions": "4818"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 206, "column": 37, "nodeType": "4656", "messageId": "4657", "endLine": 206, "endColumn": 40, "suggestions": "4819"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 55, "column": 31, "nodeType": "4656", "messageId": "4657", "endLine": 55, "endColumn": 34, "suggestions": "4820", "suppressions": "4821"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 246, "column": 40, "nodeType": "4656", "messageId": "4657", "endLine": 246, "endColumn": 43, "suggestions": "4822"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 911, "column": 20, "nodeType": "4656", "messageId": "4657", "endLine": 911, "endColumn": 23, "suggestions": "4823"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 1042, "column": 20, "nodeType": "4656", "messageId": "4657", "endLine": 1042, "endColumn": 23, "suggestions": "4824"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 294, "column": 68, "nodeType": "4656", "messageId": "4657", "endLine": 294, "endColumn": 71, "suggestions": "4825"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 150, "column": 12, "nodeType": "4656", "messageId": "4657", "endLine": 150, "endColumn": 15, "suggestions": "4826"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 131, "column": 60, "nodeType": "4656", "messageId": "4657", "endLine": 131, "endColumn": 63, "suggestions": "4827", "suppressions": "4828"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 138, "column": 61, "nodeType": "4656", "messageId": "4657", "endLine": 138, "endColumn": 64, "suggestions": "4829", "suppressions": "4830"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 318, "column": 47, "nodeType": "4656", "messageId": "4657", "endLine": 318, "endColumn": 50, "suggestions": "4831"}, {"ruleId": "4654", "severity": 1, "message": "4655", "line": 334, "column": 50, "nodeType": "4656", "messageId": "4657", "endLine": 334, "endColumn": 53, "suggestions": "4832"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["4833", "4834"], ["4835", "4836"], ["4837", "4838"], ["4839", "4840"], ["4841", "4842"], ["4843", "4844"], ["4845", "4846"], ["4847", "4848"], ["4849", "4850"], ["4851", "4852"], ["4853", "4854"], ["4855", "4856"], ["4857", "4858"], ["4859", "4860"], ["4861", "4862"], ["4863", "4864"], ["4865", "4866"], ["4867", "4868"], ["4869", "4870"], ["4871", "4872"], ["4873", "4874"], ["4875", "4876"], ["4877", "4878"], ["4879", "4880"], ["4881", "4882"], ["4883", "4884"], ["4885", "4886"], ["4887", "4888"], ["4889", "4890"], ["4891", "4892"], ["4893", "4894"], ["4895", "4896"], ["4897", "4898"], ["4899", "4900"], "no-unused-vars", "'from' is assigned a value but never used. Allowed unused vars must match /^_/u.", "Identifier", "unusedVar", "@typescript-eslint/no-unused-vars", ["4901", "4902"], "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'initialBusinessName', 'initialCategory', 'initialCity', 'initialLocality', 'initialPincode', 'performSearch', 'productFilterBy', and 'productSortBy'. Either include them or remove the dependency array.", "ArrayExpression", ["4903"], ["4904"], ["4905", "4906"], ["4907", "4908"], ["4909", "4910"], ["4911", "4912"], ["4913", "4914"], ["4915", "4916"], ["4917", "4918"], ["4919", "4920"], "'Database' is defined but never used. Allowed unused vars must match /^_/u.", ["4921"], "'page' is assigned a value but never used. Allowed unused vars must match /^_/u.", ["4922"], "'count' is assigned a value but never used. Allowed unused vars must match /^_/u.", ["4923"], ["4924", "4925"], ["4926", "4927"], ["4928"], ["4929", "4930"], ["4931", "4932"], ["4933", "4934"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["4935"], "React Hook useEffect has a missing dependency: 'loadReviews'. Either include it or remove the dependency array.", ["4936"], ["4937"], ["4938", "4939"], ["4940", "4941"], ["4942"], "React Hook useEffect has missing dependencies: 'isSearching', 'performSearch', 'products.length', and 'viewType'. Either include them or remove the dependency array.", ["4943"], ["4944"], ["4945", "4946"], ["4947", "4948"], ["4949", "4950"], ["4951", "4952"], ["4953", "4954"], ["4955"], ["4956", "4957"], ["4958", "4959"], ["4960", "4961"], ["4962", "4963"], ["4964", "4965"], ["4966", "4967"], ["4968", "4969"], ["4970", "4971"], ["4972", "4973"], ["4974", "4975"], ["4976", "4977"], ["4978", "4979"], ["4980", "4981"], ["4982"], ["4983", "4984"], ["4985"], ["4986", "4987"], ["4988", "4989"], ["4990", "4991"], ["4992", "4993"], ["4994", "4995"], ["4996", "4997"], ["4998", "4999"], ["5000", "5001"], ["5002", "5003"], ["5004", "5005"], ["5006", "5007"], ["5008", "5009"], ["5010", "5011"], ["5012", "5013"], ["5014", "5015"], ["5016", "5017"], ["5018", "5019"], ["5020", "5021"], ["5022", "5023"], ["5024", "5025"], ["5026", "5027"], ["5028", "5029"], ["5030", "5031"], ["5032", "5033"], ["5034", "5035"], ["5036", "5037"], ["5038", "5039"], ["5040", "5041"], ["5042", "5043"], ["5044", "5045"], ["5046", "5047"], ["5048", "5049"], ["5050", "5051"], ["5052", "5053"], ["5054", "5055"], ["5056", "5057"], ["5058", "5059"], ["5060"], ["5061", "5062"], ["5063"], ["5064", "5065"], ["5066", "5067"], ["5068", "5069"], ["5070", "5071"], ["5072", "5073"], ["5074", "5075"], ["5076", "5077"], ["5078", "5079"], ["5080", "5081"], ["5082", "5083"], ["5084", "5085"], ["5086", "5087"], ["5088", "5089"], ["5090", "5091"], ["5092", "5093"], ["5094", "5095"], ["5096", "5097"], ["5098"], ["5099", "5100"], ["5101", "5102"], ["5103", "5104"], ["5105", "5106"], ["5107", "5108"], ["5109"], ["5110", "5111"], ["5112", "5113"], ["5114", "5115"], ["5116", "5117"], ["5118", "5119"], ["5120", "5121"], ["5122"], ["5123", "5124"], ["5125"], ["5126", "5127"], ["5128", "5129"], {"messageId": "5130", "fix": "5131", "desc": "5132"}, {"messageId": "5133", "fix": "5134", "desc": "5135"}, {"messageId": "5130", "fix": "5136", "desc": "5132"}, {"messageId": "5133", "fix": "5137", "desc": "5135"}, {"messageId": "5130", "fix": "5138", "desc": "5132"}, {"messageId": "5133", "fix": "5139", "desc": "5135"}, {"messageId": "5130", "fix": "5140", "desc": "5132"}, {"messageId": "5133", "fix": "5141", "desc": "5135"}, {"messageId": "5130", "fix": "5142", "desc": "5132"}, {"messageId": "5133", "fix": "5143", "desc": "5135"}, {"messageId": "5130", "fix": "5144", "desc": "5132"}, {"messageId": "5133", "fix": "5145", "desc": "5135"}, {"messageId": "5130", "fix": "5146", "desc": "5132"}, {"messageId": "5133", "fix": "5147", "desc": "5135"}, {"messageId": "5130", "fix": "5148", "desc": "5132"}, {"messageId": "5133", "fix": "5149", "desc": "5135"}, {"messageId": "5130", "fix": "5150", "desc": "5132"}, {"messageId": "5133", "fix": "5151", "desc": "5135"}, {"messageId": "5130", "fix": "5152", "desc": "5132"}, {"messageId": "5133", "fix": "5153", "desc": "5135"}, {"messageId": "5130", "fix": "5154", "desc": "5132"}, {"messageId": "5133", "fix": "5155", "desc": "5135"}, {"messageId": "5130", "fix": "5156", "desc": "5132"}, {"messageId": "5133", "fix": "5157", "desc": "5135"}, {"messageId": "5130", "fix": "5158", "desc": "5132"}, {"messageId": "5133", "fix": "5159", "desc": "5135"}, {"messageId": "5130", "fix": "5160", "desc": "5132"}, {"messageId": "5133", "fix": "5161", "desc": "5135"}, {"messageId": "5130", "fix": "5162", "desc": "5132"}, {"messageId": "5133", "fix": "5163", "desc": "5135"}, {"messageId": "5130", "fix": "5164", "desc": "5132"}, {"messageId": "5133", "fix": "5165", "desc": "5135"}, {"messageId": "5130", "fix": "5166", "desc": "5132"}, {"messageId": "5133", "fix": "5167", "desc": "5135"}, {"messageId": "5130", "fix": "5168", "desc": "5132"}, {"messageId": "5133", "fix": "5169", "desc": "5135"}, {"messageId": "5130", "fix": "5170", "desc": "5132"}, {"messageId": "5133", "fix": "5171", "desc": "5135"}, {"messageId": "5130", "fix": "5172", "desc": "5132"}, {"messageId": "5133", "fix": "5173", "desc": "5135"}, {"messageId": "5130", "fix": "5174", "desc": "5132"}, {"messageId": "5133", "fix": "5175", "desc": "5135"}, {"messageId": "5130", "fix": "5176", "desc": "5132"}, {"messageId": "5133", "fix": "5177", "desc": "5135"}, {"messageId": "5130", "fix": "5178", "desc": "5132"}, {"messageId": "5133", "fix": "5179", "desc": "5135"}, {"messageId": "5130", "fix": "5180", "desc": "5132"}, {"messageId": "5133", "fix": "5181", "desc": "5135"}, {"messageId": "5130", "fix": "5182", "desc": "5132"}, {"messageId": "5133", "fix": "5183", "desc": "5135"}, {"messageId": "5130", "fix": "5184", "desc": "5132"}, {"messageId": "5133", "fix": "5185", "desc": "5135"}, {"messageId": "5130", "fix": "5186", "desc": "5132"}, {"messageId": "5133", "fix": "5187", "desc": "5135"}, {"messageId": "5130", "fix": "5188", "desc": "5132"}, {"messageId": "5133", "fix": "5189", "desc": "5135"}, {"messageId": "5130", "fix": "5190", "desc": "5132"}, {"messageId": "5133", "fix": "5191", "desc": "5135"}, {"messageId": "5130", "fix": "5192", "desc": "5132"}, {"messageId": "5133", "fix": "5193", "desc": "5135"}, {"messageId": "5130", "fix": "5194", "desc": "5132"}, {"messageId": "5133", "fix": "5195", "desc": "5135"}, {"messageId": "5130", "fix": "5196", "desc": "5132"}, {"messageId": "5133", "fix": "5197", "desc": "5135"}, {"messageId": "5130", "fix": "5198", "desc": "5132"}, {"messageId": "5133", "fix": "5199", "desc": "5135"}, {"messageId": "5130", "fix": "5200", "desc": "5132"}, {"messageId": "5133", "fix": "5201", "desc": "5135"}, {"messageId": "5130", "fix": "5202", "desc": "5132"}, {"messageId": "5133", "fix": "5203", "desc": "5135"}, {"desc": "5204", "fix": "5205"}, {"kind": "5206", "justification": "5207"}, {"messageId": "5130", "fix": "5208", "desc": "5132"}, {"messageId": "5133", "fix": "5209", "desc": "5135"}, {"messageId": "5130", "fix": "5210", "desc": "5132"}, {"messageId": "5133", "fix": "5211", "desc": "5135"}, {"messageId": "5130", "fix": "5212", "desc": "5132"}, {"messageId": "5133", "fix": "5213", "desc": "5135"}, {"messageId": "5130", "fix": "5214", "desc": "5132"}, {"messageId": "5133", "fix": "5215", "desc": "5135"}, {"messageId": "5130", "fix": "5216", "desc": "5132"}, {"messageId": "5133", "fix": "5217", "desc": "5135"}, {"messageId": "5130", "fix": "5218", "desc": "5132"}, {"messageId": "5133", "fix": "5219", "desc": "5135"}, {"messageId": "5130", "fix": "5220", "desc": "5132"}, {"messageId": "5133", "fix": "5221", "desc": "5135"}, {"messageId": "5130", "fix": "5222", "desc": "5132"}, {"messageId": "5133", "fix": "5223", "desc": "5135"}, {"messageId": "5224", "data": "5225", "fix": "5226", "desc": "5227"}, {"messageId": "5224", "data": "5228", "fix": "5229", "desc": "5230"}, {"messageId": "5224", "data": "5231", "fix": "5232", "desc": "5233"}, {"messageId": "5130", "fix": "5234", "desc": "5132"}, {"messageId": "5133", "fix": "5235", "desc": "5135"}, {"messageId": "5130", "fix": "5236", "desc": "5132"}, {"messageId": "5133", "fix": "5237", "desc": "5135"}, {"messageId": "5224", "data": "5238", "fix": "5239", "desc": "5227"}, {"messageId": "5130", "fix": "5240", "desc": "5132"}, {"messageId": "5133", "fix": "5241", "desc": "5135"}, {"messageId": "5130", "fix": "5242", "desc": "5132"}, {"messageId": "5133", "fix": "5243", "desc": "5135"}, {"messageId": "5130", "fix": "5244", "desc": "5132"}, {"messageId": "5133", "fix": "5245", "desc": "5135"}, {"kind": "5206", "justification": "5207"}, {"desc": "5246", "fix": "5247"}, {"kind": "5206", "justification": "5207"}, {"messageId": "5130", "fix": "5248", "desc": "5132"}, {"messageId": "5133", "fix": "5249", "desc": "5135"}, {"messageId": "5130", "fix": "5250", "desc": "5132"}, {"messageId": "5133", "fix": "5251", "desc": "5135"}, {"kind": "5206", "justification": "5207"}, {"desc": "5252", "fix": "5253"}, {"kind": "5206", "justification": "5207"}, {"messageId": "5130", "fix": "5254", "desc": "5132"}, {"messageId": "5133", "fix": "5255", "desc": "5135"}, {"messageId": "5130", "fix": "5256", "desc": "5132"}, {"messageId": "5133", "fix": "5257", "desc": "5135"}, {"messageId": "5130", "fix": "5258", "desc": "5132"}, {"messageId": "5133", "fix": "5259", "desc": "5135"}, {"messageId": "5130", "fix": "5260", "desc": "5132"}, {"messageId": "5133", "fix": "5261", "desc": "5135"}, {"messageId": "5130", "fix": "5262", "desc": "5132"}, {"messageId": "5133", "fix": "5263", "desc": "5135"}, {"kind": "5206", "justification": "5207"}, {"messageId": "5130", "fix": "5264", "desc": "5132"}, {"messageId": "5133", "fix": "5265", "desc": "5135"}, {"messageId": "5130", "fix": "5266", "desc": "5132"}, {"messageId": "5133", "fix": "5267", "desc": "5135"}, {"messageId": "5130", "fix": "5268", "desc": "5132"}, {"messageId": "5133", "fix": "5269", "desc": "5135"}, {"messageId": "5130", "fix": "5270", "desc": "5132"}, {"messageId": "5133", "fix": "5271", "desc": "5135"}, {"messageId": "5130", "fix": "5272", "desc": "5132"}, {"messageId": "5133", "fix": "5273", "desc": "5135"}, {"messageId": "5130", "fix": "5274", "desc": "5132"}, {"messageId": "5133", "fix": "5275", "desc": "5135"}, {"messageId": "5130", "fix": "5276", "desc": "5132"}, {"messageId": "5133", "fix": "5277", "desc": "5135"}, {"messageId": "5130", "fix": "5278", "desc": "5132"}, {"messageId": "5133", "fix": "5279", "desc": "5135"}, {"messageId": "5130", "fix": "5280", "desc": "5132"}, {"messageId": "5133", "fix": "5281", "desc": "5135"}, {"messageId": "5130", "fix": "5282", "desc": "5132"}, {"messageId": "5133", "fix": "5283", "desc": "5135"}, {"messageId": "5130", "fix": "5284", "desc": "5132"}, {"messageId": "5133", "fix": "5285", "desc": "5135"}, {"messageId": "5130", "fix": "5286", "desc": "5132"}, {"messageId": "5133", "fix": "5287", "desc": "5135"}, {"messageId": "5130", "fix": "5288", "desc": "5132"}, {"messageId": "5133", "fix": "5289", "desc": "5135"}, {"kind": "5206", "justification": "5207"}, {"messageId": "5130", "fix": "5290", "desc": "5132"}, {"messageId": "5133", "fix": "5291", "desc": "5135"}, {"kind": "5206", "justification": "5207"}, {"messageId": "5130", "fix": "5292", "desc": "5132"}, {"messageId": "5133", "fix": "5293", "desc": "5135"}, {"messageId": "5130", "fix": "5294", "desc": "5132"}, {"messageId": "5133", "fix": "5295", "desc": "5135"}, {"messageId": "5130", "fix": "5296", "desc": "5132"}, {"messageId": "5133", "fix": "5297", "desc": "5135"}, {"messageId": "5130", "fix": "5298", "desc": "5132"}, {"messageId": "5133", "fix": "5299", "desc": "5135"}, {"messageId": "5130", "fix": "5300", "desc": "5132"}, {"messageId": "5133", "fix": "5301", "desc": "5135"}, {"messageId": "5130", "fix": "5302", "desc": "5132"}, {"messageId": "5133", "fix": "5303", "desc": "5135"}, {"messageId": "5130", "fix": "5304", "desc": "5132"}, {"messageId": "5133", "fix": "5305", "desc": "5135"}, {"messageId": "5130", "fix": "5306", "desc": "5132"}, {"messageId": "5133", "fix": "5307", "desc": "5135"}, {"messageId": "5130", "fix": "5308", "desc": "5132"}, {"messageId": "5133", "fix": "5309", "desc": "5135"}, {"messageId": "5130", "fix": "5310", "desc": "5132"}, {"messageId": "5133", "fix": "5311", "desc": "5135"}, {"messageId": "5130", "fix": "5312", "desc": "5132"}, {"messageId": "5133", "fix": "5313", "desc": "5135"}, {"messageId": "5130", "fix": "5314", "desc": "5132"}, {"messageId": "5133", "fix": "5315", "desc": "5135"}, {"messageId": "5130", "fix": "5316", "desc": "5132"}, {"messageId": "5133", "fix": "5317", "desc": "5135"}, {"messageId": "5130", "fix": "5318", "desc": "5132"}, {"messageId": "5133", "fix": "5319", "desc": "5135"}, {"messageId": "5130", "fix": "5320", "desc": "5132"}, {"messageId": "5133", "fix": "5321", "desc": "5135"}, {"messageId": "5130", "fix": "5322", "desc": "5132"}, {"messageId": "5133", "fix": "5323", "desc": "5135"}, {"messageId": "5130", "fix": "5324", "desc": "5132"}, {"messageId": "5133", "fix": "5325", "desc": "5135"}, {"messageId": "5130", "fix": "5326", "desc": "5132"}, {"messageId": "5133", "fix": "5327", "desc": "5135"}, {"messageId": "5130", "fix": "5328", "desc": "5132"}, {"messageId": "5133", "fix": "5329", "desc": "5135"}, {"messageId": "5130", "fix": "5330", "desc": "5132"}, {"messageId": "5133", "fix": "5331", "desc": "5135"}, {"messageId": "5130", "fix": "5332", "desc": "5132"}, {"messageId": "5133", "fix": "5333", "desc": "5135"}, {"messageId": "5130", "fix": "5334", "desc": "5132"}, {"messageId": "5133", "fix": "5335", "desc": "5135"}, {"messageId": "5130", "fix": "5336", "desc": "5132"}, {"messageId": "5133", "fix": "5337", "desc": "5135"}, {"messageId": "5130", "fix": "5338", "desc": "5132"}, {"messageId": "5133", "fix": "5339", "desc": "5135"}, {"messageId": "5130", "fix": "5340", "desc": "5132"}, {"messageId": "5133", "fix": "5341", "desc": "5135"}, {"messageId": "5130", "fix": "5342", "desc": "5132"}, {"messageId": "5133", "fix": "5343", "desc": "5135"}, {"messageId": "5130", "fix": "5344", "desc": "5132"}, {"messageId": "5133", "fix": "5345", "desc": "5135"}, {"messageId": "5130", "fix": "5346", "desc": "5132"}, {"messageId": "5133", "fix": "5347", "desc": "5135"}, {"messageId": "5130", "fix": "5348", "desc": "5132"}, {"messageId": "5133", "fix": "5349", "desc": "5135"}, {"messageId": "5130", "fix": "5350", "desc": "5132"}, {"messageId": "5133", "fix": "5351", "desc": "5135"}, {"messageId": "5130", "fix": "5352", "desc": "5132"}, {"messageId": "5133", "fix": "5353", "desc": "5135"}, {"messageId": "5130", "fix": "5354", "desc": "5132"}, {"messageId": "5133", "fix": "5355", "desc": "5135"}, {"messageId": "5130", "fix": "5356", "desc": "5132"}, {"messageId": "5133", "fix": "5357", "desc": "5135"}, {"messageId": "5130", "fix": "5358", "desc": "5132"}, {"messageId": "5133", "fix": "5359", "desc": "5135"}, {"messageId": "5130", "fix": "5360", "desc": "5132"}, {"messageId": "5133", "fix": "5361", "desc": "5135"}, {"messageId": "5130", "fix": "5362", "desc": "5132"}, {"messageId": "5133", "fix": "5363", "desc": "5135"}, {"messageId": "5130", "fix": "5364", "desc": "5132"}, {"messageId": "5133", "fix": "5365", "desc": "5135"}, {"kind": "5206", "justification": "5207"}, {"messageId": "5130", "fix": "5366", "desc": "5132"}, {"messageId": "5133", "fix": "5367", "desc": "5135"}, {"kind": "5206", "justification": "5207"}, {"messageId": "5130", "fix": "5368", "desc": "5132"}, {"messageId": "5133", "fix": "5369", "desc": "5135"}, {"messageId": "5130", "fix": "5370", "desc": "5132"}, {"messageId": "5133", "fix": "5371", "desc": "5135"}, {"messageId": "5130", "fix": "5372", "desc": "5132"}, {"messageId": "5133", "fix": "5373", "desc": "5135"}, {"messageId": "5130", "fix": "5374", "desc": "5132"}, {"messageId": "5133", "fix": "5375", "desc": "5135"}, {"messageId": "5130", "fix": "5376", "desc": "5132"}, {"messageId": "5133", "fix": "5377", "desc": "5135"}, {"messageId": "5130", "fix": "5378", "desc": "5132"}, {"messageId": "5133", "fix": "5379", "desc": "5135"}, {"messageId": "5130", "fix": "5380", "desc": "5132"}, {"messageId": "5133", "fix": "5381", "desc": "5135"}, {"messageId": "5130", "fix": "5382", "desc": "5132"}, {"messageId": "5133", "fix": "5383", "desc": "5135"}, {"messageId": "5130", "fix": "5384", "desc": "5132"}, {"messageId": "5133", "fix": "5385", "desc": "5135"}, {"messageId": "5130", "fix": "5386", "desc": "5132"}, {"messageId": "5133", "fix": "5387", "desc": "5135"}, {"messageId": "5130", "fix": "5388", "desc": "5132"}, {"messageId": "5133", "fix": "5389", "desc": "5135"}, {"messageId": "5130", "fix": "5390", "desc": "5132"}, {"messageId": "5133", "fix": "5391", "desc": "5135"}, {"messageId": "5130", "fix": "5392", "desc": "5132"}, {"messageId": "5133", "fix": "5393", "desc": "5135"}, {"messageId": "5130", "fix": "5394", "desc": "5132"}, {"messageId": "5133", "fix": "5395", "desc": "5135"}, {"messageId": "5130", "fix": "5396", "desc": "5132"}, {"messageId": "5133", "fix": "5397", "desc": "5135"}, {"messageId": "5130", "fix": "5398", "desc": "5132"}, {"messageId": "5133", "fix": "5399", "desc": "5135"}, {"messageId": "5130", "fix": "5400", "desc": "5132"}, {"messageId": "5133", "fix": "5401", "desc": "5135"}, {"kind": "5206", "justification": "5207"}, {"messageId": "5130", "fix": "5402", "desc": "5132"}, {"messageId": "5133", "fix": "5403", "desc": "5135"}, {"messageId": "5130", "fix": "5404", "desc": "5132"}, {"messageId": "5133", "fix": "5405", "desc": "5135"}, {"messageId": "5130", "fix": "5406", "desc": "5132"}, {"messageId": "5133", "fix": "5407", "desc": "5135"}, {"messageId": "5130", "fix": "5408", "desc": "5132"}, {"messageId": "5133", "fix": "5409", "desc": "5135"}, {"messageId": "5130", "fix": "5410", "desc": "5132"}, {"messageId": "5133", "fix": "5411", "desc": "5135"}, {"kind": "5206", "justification": "5207"}, {"messageId": "5130", "fix": "5412", "desc": "5132"}, {"messageId": "5133", "fix": "5413", "desc": "5135"}, {"messageId": "5130", "fix": "5414", "desc": "5132"}, {"messageId": "5133", "fix": "5415", "desc": "5135"}, {"messageId": "5130", "fix": "5416", "desc": "5132"}, {"messageId": "5133", "fix": "5417", "desc": "5135"}, {"messageId": "5130", "fix": "5418", "desc": "5132"}, {"messageId": "5133", "fix": "5419", "desc": "5135"}, {"messageId": "5130", "fix": "5420", "desc": "5132"}, {"messageId": "5133", "fix": "5421", "desc": "5135"}, {"messageId": "5130", "fix": "5422", "desc": "5132"}, {"messageId": "5133", "fix": "5423", "desc": "5135"}, {"kind": "5206", "justification": "5207"}, {"messageId": "5130", "fix": "5424", "desc": "5132"}, {"messageId": "5133", "fix": "5425", "desc": "5135"}, {"kind": "5206", "justification": "5207"}, {"messageId": "5130", "fix": "5426", "desc": "5132"}, {"messageId": "5133", "fix": "5427", "desc": "5135"}, {"messageId": "5130", "fix": "5428", "desc": "5132"}, {"messageId": "5133", "fix": "5429", "desc": "5135"}, "suggestUnknown", {"range": "5430", "text": "5431"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "5432", "text": "5433"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "5434", "text": "5431"}, {"range": "5435", "text": "5433"}, {"range": "5436", "text": "5431"}, {"range": "5437", "text": "5433"}, {"range": "5438", "text": "5431"}, {"range": "5439", "text": "5433"}, {"range": "5440", "text": "5431"}, {"range": "5441", "text": "5433"}, {"range": "5442", "text": "5431"}, {"range": "5443", "text": "5433"}, {"range": "5444", "text": "5431"}, {"range": "5445", "text": "5433"}, {"range": "5446", "text": "5431"}, {"range": "5447", "text": "5433"}, {"range": "5448", "text": "5431"}, {"range": "5449", "text": "5433"}, {"range": "5450", "text": "5431"}, {"range": "5451", "text": "5433"}, {"range": "5452", "text": "5431"}, {"range": "5453", "text": "5433"}, {"range": "5454", "text": "5431"}, {"range": "5455", "text": "5433"}, {"range": "5456", "text": "5431"}, {"range": "5457", "text": "5433"}, {"range": "5458", "text": "5431"}, {"range": "5459", "text": "5433"}, {"range": "5460", "text": "5431"}, {"range": "5461", "text": "5433"}, {"range": "5462", "text": "5431"}, {"range": "5463", "text": "5433"}, {"range": "5464", "text": "5431"}, {"range": "5465", "text": "5433"}, {"range": "5466", "text": "5431"}, {"range": "5467", "text": "5433"}, {"range": "5468", "text": "5431"}, {"range": "5469", "text": "5433"}, {"range": "5470", "text": "5431"}, {"range": "5471", "text": "5433"}, {"range": "5472", "text": "5431"}, {"range": "5473", "text": "5433"}, {"range": "5474", "text": "5431"}, {"range": "5475", "text": "5433"}, {"range": "5476", "text": "5431"}, {"range": "5477", "text": "5433"}, {"range": "5478", "text": "5431"}, {"range": "5479", "text": "5433"}, {"range": "5480", "text": "5431"}, {"range": "5481", "text": "5433"}, {"range": "5482", "text": "5431"}, {"range": "5483", "text": "5433"}, {"range": "5484", "text": "5431"}, {"range": "5485", "text": "5433"}, {"range": "5486", "text": "5431"}, {"range": "5487", "text": "5433"}, {"range": "5488", "text": "5431"}, {"range": "5489", "text": "5433"}, {"range": "5490", "text": "5431"}, {"range": "5491", "text": "5433"}, {"range": "5492", "text": "5431"}, {"range": "5493", "text": "5433"}, {"range": "5494", "text": "5431"}, {"range": "5495", "text": "5433"}, {"range": "5496", "text": "5431"}, {"range": "5497", "text": "5433"}, {"range": "5498", "text": "5431"}, {"range": "5499", "text": "5433"}, {"range": "5500", "text": "5431"}, {"range": "5501", "text": "5433"}, "Update the dependencies array to be: [initialBusinessName, initialCategory, initialCity, initialLocality, initialPincode, performSearch, productFilterBy, productSortBy]", {"range": "5502", "text": "5503"}, "directive", "", {"range": "5504", "text": "5431"}, {"range": "5505", "text": "5433"}, {"range": "5506", "text": "5431"}, {"range": "5507", "text": "5433"}, {"range": "5508", "text": "5431"}, {"range": "5509", "text": "5433"}, {"range": "5510", "text": "5431"}, {"range": "5511", "text": "5433"}, {"range": "5512", "text": "5431"}, {"range": "5513", "text": "5433"}, {"range": "5514", "text": "5431"}, {"range": "5515", "text": "5433"}, {"range": "5516", "text": "5431"}, {"range": "5517", "text": "5433"}, {"range": "5518", "text": "5431"}, {"range": "5519", "text": "5433"}, "removeVar", {"varName": "5520"}, {"range": "5521", "text": "5207"}, "Remove unused variable 'Database'.", {"varName": "5522"}, {"range": "5523", "text": "5207"}, "Remove unused variable 'page'.", {"varName": "5524"}, {"range": "5525", "text": "5207"}, "Remove unused variable 'count'.", {"range": "5526", "text": "5431"}, {"range": "5527", "text": "5433"}, {"range": "5528", "text": "5431"}, {"range": "5529", "text": "5433"}, {"varName": "5520"}, {"range": "5530", "text": "5207"}, {"range": "5531", "text": "5431"}, {"range": "5532", "text": "5433"}, {"range": "5533", "text": "5431"}, {"range": "5534", "text": "5433"}, {"range": "5535", "text": "5431"}, {"range": "5536", "text": "5433"}, "Update the dependencies array to be: [currentPage, sortBy, businessProfileId, loadReviews]", {"range": "5537", "text": "5538"}, {"range": "5539", "text": "5431"}, {"range": "5540", "text": "5433"}, {"range": "5541", "text": "5431"}, {"range": "5542", "text": "5433"}, "Update the dependencies array to be: [isSearching, performSearch, products.length, viewType]", {"range": "5543", "text": "5544"}, {"range": "5545", "text": "5431"}, {"range": "5546", "text": "5433"}, {"range": "5547", "text": "5431"}, {"range": "5548", "text": "5433"}, {"range": "5549", "text": "5431"}, {"range": "5550", "text": "5433"}, {"range": "5551", "text": "5431"}, {"range": "5552", "text": "5433"}, {"range": "5553", "text": "5431"}, {"range": "5554", "text": "5433"}, {"range": "5555", "text": "5431"}, {"range": "5556", "text": "5433"}, {"range": "5557", "text": "5431"}, {"range": "5558", "text": "5433"}, {"range": "5559", "text": "5431"}, {"range": "5560", "text": "5433"}, {"range": "5561", "text": "5431"}, {"range": "5562", "text": "5433"}, {"range": "5563", "text": "5431"}, {"range": "5564", "text": "5433"}, {"range": "5565", "text": "5431"}, {"range": "5566", "text": "5433"}, {"range": "5567", "text": "5431"}, {"range": "5568", "text": "5433"}, {"range": "5569", "text": "5431"}, {"range": "5570", "text": "5433"}, {"range": "5571", "text": "5431"}, {"range": "5572", "text": "5433"}, {"range": "5573", "text": "5431"}, {"range": "5574", "text": "5433"}, {"range": "5575", "text": "5431"}, {"range": "5576", "text": "5433"}, {"range": "5577", "text": "5431"}, {"range": "5578", "text": "5433"}, {"range": "5579", "text": "5431"}, {"range": "5580", "text": "5433"}, {"range": "5581", "text": "5431"}, {"range": "5582", "text": "5433"}, {"range": "5583", "text": "5431"}, {"range": "5584", "text": "5433"}, {"range": "5585", "text": "5431"}, {"range": "5586", "text": "5433"}, {"range": "5587", "text": "5431"}, {"range": "5588", "text": "5433"}, {"range": "5589", "text": "5431"}, {"range": "5590", "text": "5433"}, {"range": "5591", "text": "5431"}, {"range": "5592", "text": "5433"}, {"range": "5593", "text": "5431"}, {"range": "5594", "text": "5433"}, {"range": "5595", "text": "5431"}, {"range": "5596", "text": "5433"}, {"range": "5597", "text": "5431"}, {"range": "5598", "text": "5433"}, {"range": "5599", "text": "5431"}, {"range": "5600", "text": "5433"}, {"range": "5601", "text": "5431"}, {"range": "5602", "text": "5433"}, {"range": "5603", "text": "5431"}, {"range": "5604", "text": "5433"}, {"range": "5605", "text": "5431"}, {"range": "5606", "text": "5433"}, {"range": "5607", "text": "5431"}, {"range": "5608", "text": "5433"}, {"range": "5609", "text": "5431"}, {"range": "5610", "text": "5433"}, {"range": "5611", "text": "5431"}, {"range": "5612", "text": "5433"}, {"range": "5613", "text": "5431"}, {"range": "5614", "text": "5433"}, {"range": "5615", "text": "5431"}, {"range": "5616", "text": "5433"}, {"range": "5617", "text": "5431"}, {"range": "5618", "text": "5433"}, {"range": "5619", "text": "5431"}, {"range": "5620", "text": "5433"}, {"range": "5621", "text": "5431"}, {"range": "5622", "text": "5433"}, {"range": "5623", "text": "5431"}, {"range": "5624", "text": "5433"}, {"range": "5625", "text": "5431"}, {"range": "5626", "text": "5433"}, {"range": "5627", "text": "5431"}, {"range": "5628", "text": "5433"}, {"range": "5629", "text": "5431"}, {"range": "5630", "text": "5433"}, {"range": "5631", "text": "5431"}, {"range": "5632", "text": "5433"}, {"range": "5633", "text": "5431"}, {"range": "5634", "text": "5433"}, {"range": "5635", "text": "5431"}, {"range": "5636", "text": "5433"}, {"range": "5637", "text": "5431"}, {"range": "5638", "text": "5433"}, {"range": "5639", "text": "5431"}, {"range": "5640", "text": "5433"}, {"range": "5641", "text": "5431"}, {"range": "5642", "text": "5433"}, {"range": "5643", "text": "5431"}, {"range": "5644", "text": "5433"}, {"range": "5645", "text": "5431"}, {"range": "5646", "text": "5433"}, {"range": "5647", "text": "5431"}, {"range": "5648", "text": "5433"}, {"range": "5649", "text": "5431"}, {"range": "5650", "text": "5433"}, {"range": "5651", "text": "5431"}, {"range": "5652", "text": "5433"}, {"range": "5653", "text": "5431"}, {"range": "5654", "text": "5433"}, {"range": "5655", "text": "5431"}, {"range": "5656", "text": "5433"}, {"range": "5657", "text": "5431"}, {"range": "5658", "text": "5433"}, {"range": "5659", "text": "5431"}, {"range": "5660", "text": "5433"}, {"range": "5661", "text": "5431"}, {"range": "5662", "text": "5433"}, {"range": "5663", "text": "5431"}, {"range": "5664", "text": "5433"}, {"range": "5665", "text": "5431"}, {"range": "5666", "text": "5433"}, {"range": "5667", "text": "5431"}, {"range": "5668", "text": "5433"}, {"range": "5669", "text": "5431"}, {"range": "5670", "text": "5433"}, {"range": "5671", "text": "5431"}, {"range": "5672", "text": "5433"}, {"range": "5673", "text": "5431"}, {"range": "5674", "text": "5433"}, {"range": "5675", "text": "5431"}, {"range": "5676", "text": "5433"}, {"range": "5677", "text": "5431"}, {"range": "5678", "text": "5433"}, {"range": "5679", "text": "5431"}, {"range": "5680", "text": "5433"}, {"range": "5681", "text": "5431"}, {"range": "5682", "text": "5433"}, {"range": "5683", "text": "5431"}, {"range": "5684", "text": "5433"}, {"range": "5685", "text": "5431"}, {"range": "5686", "text": "5433"}, {"range": "5687", "text": "5431"}, {"range": "5688", "text": "5433"}, {"range": "5689", "text": "5431"}, {"range": "5690", "text": "5433"}, {"range": "5691", "text": "5431"}, {"range": "5692", "text": "5433"}, {"range": "5693", "text": "5431"}, {"range": "5694", "text": "5433"}, {"range": "5695", "text": "5431"}, {"range": "5696", "text": "5433"}, {"range": "5697", "text": "5431"}, {"range": "5698", "text": "5433"}, {"range": "5699", "text": "5431"}, {"range": "5700", "text": "5433"}, {"range": "5701", "text": "5431"}, {"range": "5702", "text": "5433"}, {"range": "5703", "text": "5431"}, {"range": "5704", "text": "5433"}, {"range": "5705", "text": "5431"}, {"range": "5706", "text": "5433"}, {"range": "5707", "text": "5431"}, {"range": "5708", "text": "5433"}, {"range": "5709", "text": "5431"}, {"range": "5710", "text": "5433"}, {"range": "5711", "text": "5431"}, {"range": "5712", "text": "5433"}, {"range": "5713", "text": "5431"}, {"range": "5714", "text": "5433"}, {"range": "5715", "text": "5431"}, {"range": "5716", "text": "5433"}, {"range": "5717", "text": "5431"}, {"range": "5718", "text": "5433"}, {"range": "5719", "text": "5431"}, {"range": "5720", "text": "5433"}, [2378, 2381], "unknown", [2378, 2381], "never", [5187, 5190], [5187, 5190], [4599, 4602], [4599, 4602], [5607, 5610], [5607, 5610], [5786, 5789], [5786, 5789], [6341, 6344], [6341, 6344], [10302, 10305], [10302, 10305], [10485, 10488], [10485, 10488], [10531, 10534], [10531, 10534], [10585, 10588], [10585, 10588], [2728, 2731], [2728, 2731], [2816, 2819], [2816, 2819], [1954, 1957], [1954, 1957], [3496, 3499], [3496, 3499], [5105, 5108], [5105, 5108], [4401, 4404], [4401, 4404], [4527, 4530], [4527, 4530], [4559, 4562], [4559, 4562], [4621, 4624], [4621, 4624], [4653, 4656], [4653, 4656], [15428, 15431], [15428, 15431], [9228, 9231], [9228, 9231], [9438, 9441], [9438, 9441], [9809, 9812], [9809, 9812], [2386, 2389], [2386, 2389], [3553, 3556], [3553, 3556], [10077, 10080], [10077, 10080], [16494, 16497], [16494, 16497], [6043, 6046], [6043, 6046], [3496, 3499], [3496, 3499], [2640, 2643], [2640, 2643], [3752, 3755], [3752, 3755], [3819, 3822], [3819, 3822], [2107, 2110], [2107, 2110], [11048, 11051], [11048, 11051], [9509, 9511], "[initialBusinessName, initialCategory, initialCity, initialLocality, initialPincode, performSearch, productFilterBy, productSortBy]", [13865, 13868], [13865, 13868], [13949, 13952], [13949, 13952], [13983, 13986], [13983, 13986], [14237, 14240], [14237, 14240], [3639, 3642], [3639, 3642], [3602, 3605], [3602, 3605], [5310, 5313], [5310, 5313], [5440, 5443], [5440, 5443], "Database", [249, 293], "page", [1753, 1812], "count", [1817, 1879], [5109, 5112], [5109, 5112], [5239, 5242], [5239, 5242], [245, 289], [7881, 7884], [7881, 7884], [8246, 8249], [8246, 8249], [8376, 8379], [8376, 8379], [1800, 1840], "[currentPage, sortBy, businessProfileId, loadReviews]", [3479, 3482], [3479, 3482], [3952, 3955], [3952, 3955], [5544, 5546], "[isSearching, performSearch, products.length, viewType]", [3267, 3270], [3267, 3270], [6665, 6668], [6665, 6668], [6716, 6719], [6716, 6719], [6590, 6593], [6590, 6593], [12572, 12575], [12572, 12575], [2152, 2155], [2152, 2155], [3970, 3973], [3970, 3973], [5410, 5413], [5410, 5413], [5983, 5986], [5983, 5986], [7360, 7363], [7360, 7363], [1660, 1663], [1660, 1663], [2569, 2572], [2569, 2572], [11116, 11119], [11116, 11119], [3732, 3735], [3732, 3735], [4043, 4046], [4043, 4046], [4067, 4070], [4067, 4070], [2501, 2504], [2501, 2504], [394, 397], [394, 397], [424, 427], [424, 427], [9442, 9445], [9442, 9445], [1164, 1167], [1164, 1167], [1208, 1211], [1208, 1211], [3281, 3284], [3281, 3284], [3325, 3328], [3325, 3328], [5556, 5559], [5556, 5559], [5600, 5603], [5600, 5603], [8328, 8331], [8328, 8331], [8372, 8375], [8372, 8375], [3122, 3125], [3122, 3125], [8698, 8701], [8698, 8701], [9169, 9172], [9169, 9172], [9258, 9261], [9258, 9261], [3860, 3863], [3860, 3863], [2232, 2235], [2232, 2235], [3067, 3070], [3067, 3070], [7659, 7662], [7659, 7662], [7920, 7923], [7920, 7923], [4267, 4270], [4267, 4270], [4695, 4698], [4695, 4698], [4791, 4794], [4791, 4794], [4899, 4902], [4899, 4902], [19104, 19107], [19104, 19107], [19206, 19209], [19206, 19209], [19344, 19347], [19344, 19347], [20809, 20812], [20809, 20812], [20905, 20908], [20905, 20908], [21022, 21025], [21022, 21025], [6218, 6221], [6218, 6221], [6308, 6311], [6308, 6311], [6419, 6422], [6419, 6422], [4977, 4980], [4977, 4980], [5073, 5076], [5073, 5076], [5203, 5206], [5203, 5206], [9883, 9886], [9883, 9886], [10013, 10016], [10013, 10016], [170, 173], [170, 173], [362, 365], [362, 365], [1899, 1902], [1899, 1902], [3493, 3496], [3493, 3496], [4841, 4844], [4841, 4844], [17809, 17812], [17809, 17812], [18166, 18169], [18166, 18169], [18366, 18369], [18366, 18369], [6359, 6362], [6359, 6362], [6506, 6509], [6506, 6509], [6653, 6656], [6653, 6656], [6797, 6800], [6797, 6800], [11147, 11150], [11147, 11150], [11335, 11338], [11335, 11338], [11477, 11480], [11477, 11480], [15260, 15263], [15260, 15263], [15415, 15418], [15415, 15418], [15563, 15566], [15563, 15566], [478, 481], [478, 481], [4829, 4832], [4829, 4832], [2134, 2137], [2134, 2137], [2340, 2343], [2340, 2343], [7100, 7103], [7100, 7103], [1622, 1625], [1622, 1625], [8315, 8318], [8315, 8318], [35559, 35562], [35559, 35562], [40404, 40407], [40404, 40407], [10576, 10579], [10576, 10579], [4671, 4674], [4671, 4674], [3617, 3620], [3617, 3620], [3858, 3861], [3858, 3861], [9079, 9082], [9079, 9082], [9584, 9587], [9584, 9587]]