const CHUNK_PUBLIC_PATH = "server/app/(main)/page.js";
const runtime = require("../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_c336f25b._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__4c58c055._.js");
runtime.loadChunk("server/chunks/ssr/app_1f3630ef._.js");
runtime.loadChunk("server/chunks/ssr/app_0a91cb44._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__0716f283._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_96715ba7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_forbidden-error_ea7ea172.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_unauthorized-error_c8949b27.js");
runtime.loadChunk("server/chunks/ssr/_fb6337ac._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@supabase_node-fetch_lib_index_34de111f.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__471be63c._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_49371ab1._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_tr46_1a859af0._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_ws_34fa8cd4._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@supabase_auth-js_dist_module_2a35e6ef._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_zod_lib_index_mjs_8e707260._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_fe7dedd4._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/(main)/page { METADATA_0 => \"[project]/app/favicon.ico.mjs { IMAGE => \\\"[project]/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", METADATA_1 => \"[project]/app/opengraph-image.png.mjs { IMAGE => \\\"[project]/app/opengraph-image.png (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_2 => \"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/app/(main)/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_8 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_9 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_10 => \"[project]/app/(main)/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/(main)/page { METADATA_0 => \"[project]/app/favicon.ico.mjs { IMAGE => \\\"[project]/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", METADATA_1 => \"[project]/app/opengraph-image.png.mjs { IMAGE => \\\"[project]/app/opengraph-image.png (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_2 => \"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/app/(main)/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_8 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_9 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_10 => \"[project]/app/(main)/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
