Files in dukancard project with direct Supabase calls (supabase.) - excluding service files:
Total files found: 138

app\(dashboard)\dashboard\business\overview\page.tsx
  Line 80: } = await supabase.auth.getUser();
  Line 87: const { data: profileData, error: profileError } = await supabase
  Line 88: .from("business_profiles")
  Line 96: const { data: subscription, error: subscriptionError } = await supabase
  Line 97: .from("payment_subscriptions")

app\(dashboard)\dashboard\business\page.tsx
  Line 16: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 23: const { data: businessProfile, error: profileError } = await supabase
  Line 24: .from('business_profiles')

app\(dashboard)\dashboard\business\plan\components\EnhancedInvoiceHistoryCard.tsx
  Line 193: const { data: subscriptions, error: subscriptionError } = await supabase
  Line 194: .from("payment_subscriptions")

app\(dashboard)\dashboard\business\plan\page.tsx
  Line 140: } = await supabase.auth.getUser();
  Line 147: const { data: profile, error: profileError } = await supabase
  Line 148: .from("business_profiles")
  Line 201: const { data: subscription, error: subscriptionError } = await supabase
  Line 202: .from("payment_subscriptions")

app\(dashboard)\dashboard\business\products\add\page.tsx
  Line 17: } = await supabase.auth.getUser();
  Line 24: const { error: profileError } = await supabase
  Line 25: .from("business_profiles")
  Line 36: const { data: subscriptionData, error: subscriptionError } = await supabase
  Line 37: .from("payment_subscriptions")
  Line 53: const { count: availableCount, error: availableCountError } = await supabase
  Line 54: .from("products_services")
  Line 64: const { count: totalCount, error: countError } = await supabase
  Line 65: .from("products_services")

app\(dashboard)\dashboard\business\products\edit\[productId]\page.tsx
  Line 24: } = await supabase.auth.getUser();
  Line 31: const { data: product, error: productError } = await supabase
  Line 32: .from("products_services")
  Line 60: const { data: subscriptionData, error: subscriptionError } = await supabase
  Line 61: .from("payment_subscriptions")
  Line 77: const { count: availableCount, error: countError } = await supabase
  Line 78: .from("products_services")
  Line 92: const { data: variantsData, error: variantsError } = await supabase
  Line 93: .from("product_variants")

app\(dashboard)\dashboard\business\products\page.tsx
  Line 20: } = await supabase.auth.getUser();
  Line 30: const { data: subscriptionData, error: subscriptionError } = await supabase
  Line 31: .from("payment_subscriptions")

app\(dashboard)\dashboard\business\reviews\page.tsx
  Line 18: } = await supabase.auth.getUser();
  Line 25: const { data: businessProfile, error: profileError } = await supabase
  Line 26: .from('business_profiles')
  Line 40: const { count: reviewsReceivedCount } = await supabase
  Line 41: .from('ratings_reviews')
  Line 47: const { count: myReviewsCount } = await supabase
  Line 48: .from('ratings_reviews')

app\(dashboard)\dashboard\business\settings\actions.ts
  Line 96: } = await supabase.auth.getUser();
  Line 114: const { error: verifyError } = await supabase.auth.verifyOtp({
  Line 158: } = await supabase.auth.getUser();
  Line 190: } = await supabase.auth.getUser();
  Line 200: const { error } = await supabase.auth.signInWithOtp({
  Line 244: const { error } = await supabase.auth.verifyOtp({
  Line 286: } = await supabase.auth.getUser();
  Line 297: const { error } = await supabase.auth.signInWithPassword({
  Line 327: } = await supabase.auth.getUser();
  Line 335: const { data: subscription, error: subscriptionError } = await supabase
  Line 336: .from("payment_subscriptions")
  Line 444: const { data: items, error: listError } = await supabase.storage
  Line 475: const { error: deleteError } = await supabase.storage
  Line 508: const { error: deleteProfileError } = await supabase
  Line 509: .from('business_profiles')
  Line 524: await supabase.auth.signOut();
  Line 528: const { error: deleteUserError } = await supabase.auth.admin.deleteUser(user.id, false);
  Line 562: } = await supabase.auth.getUser();
  Line 597: const { error: updateError } = await supabase.auth.updateUser(
  Line 648: } = await supabase.auth.getUser();
  Line 669: const { error: authUpdateError } = await supabase.auth.updateUser(
  Line 736: } = await supabase.auth.getUser();
  Line 759: const { error: verifyError } = await supabase.auth.verifyOtp({
  Line 784: const { error: updateError } = await supabase.auth.updateUser({

app\(dashboard)\dashboard\business\settings\page.tsx
  Line 35: } = await supabase.auth.getUser();

app\(dashboard)\dashboard\business\subscriptions\actions.ts
  Line 71: const { count: totalCount, error: countError } = await supabase
  Line 72: .from('subscriptions')
  Line 90: const { data: subscriptions, error: subsError } = await supabase
  Line 91: .from('subscriptions')
  Line 115: supabase
  Line 116: .from('customer_profiles_public')
  Line 119: supabase
  Line 120: .from('business_profiles')
  Line 220: let query = supabase
  Line 221: .from('subscriptions')

app\(dashboard)\dashboard\business\subscriptions\page.tsx
  Line 35: } = await supabase.auth.getUser();

app\(dashboard)\dashboard\customer\layout.tsx
  Line 26: } = await supabase.auth.getUser();
  Line 32: const { data: profile, error: profileError } = await supabase
  Line 33: .from("customer_profiles")

app\(dashboard)\dashboard\customer\likes\actions.ts
  Line 41: let query = supabase
  Line 42: .from('likes')
  Line 63: let countQuery = supabase
  Line 64: .from('likes')

app\(dashboard)\dashboard\customer\likes\page.tsx
  Line 35: } = await supabase.auth.getUser();

app\(dashboard)\dashboard\customer\overview\page.tsx
  Line 17: } = await supabase.auth.getUser();
  Line 27: const { data: profile, error: profileError } = await supabase
  Line 28: .from("customer_profiles")
  Line 42: const { count: reviewCount, error: reviewError } = await supabase
  Line 43: .from('ratings_reviews')
  Line 52: const { count: subscriptionCount, error: subscriptionError } = await supabase
  Line 53: .from('subscriptions')
  Line 62: const { count: likesCount, error: likesError } = await supabase
  Line 63: .from('likes')

app\(dashboard)\dashboard\customer\page.tsx
  Line 17: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 27: const { data: customerProfile, error: profileError } = await supabase
  Line 28: .from('customer_profiles')
  Line 40: const { data: subscriptions } = await supabase
  Line 41: .from('subscriptions')

app\(dashboard)\dashboard\customer\profile\actions.ts
  Line 152: } = await supabase.auth.getUser();
  Line 175: const { error: authUpdateError } = await supabase.auth.updateUser({
  Line 204: } = await supabase.auth.getUser();
  Line 229: const { error: updateError } = await supabase
  Line 230: .from('customer_profiles')
  Line 265: } = await supabase.auth.getUser();
  Line 289: const { error: updateError } = await supabase
  Line 290: .from('customer_profiles')
  Line 300: const { error: authUpdateError } = await supabase.auth.updateUser({
  Line 330: } = await supabase.auth.getUser();
  Line 361: const { data: authData } = await supabase.auth.admin.getUserById(user.id);
  Line 395: const { error: authUpdateError } = await supabase.auth.updateUser({
  Line 443: } = await supabase.auth.getUser();
  Line 477: const { error: authUpdateError } = await supabase.auth.updateUser({
  Line 525: } = await supabase.auth.getUser();
  Line 563: const { error: authUpdateError } = await supabase.auth.updateUser({
  Line 574: const { error: updateError } = await supabase
  Line 575: .from('customer_profiles')

app\(dashboard)\dashboard\customer\profile\avatar-actions.ts
  Line 16: } = await supabase.auth.getUser();
  Line 47: const { error: uploadError } = await supabase.storage
  Line 63: const { data: urlData } = supabase.storage
  Line 93: } = await supabase.auth.getUser();
  Line 99: const { error: updateError } = await supabase
  Line 100: .from("customer_profiles")
  Line 130: } = await supabase.auth.getUser();
  Line 144: const { error: deleteError } = await supabase.storage
  Line 157: const { error: updateError } = await supabase
  Line 158: .from("customer_profiles")

app\(dashboard)\dashboard\customer\profile\page.tsx
  Line 19: } = await supabase.auth.getUser();
  Line 26: const { data: profile, error: profileError } = await supabase
  Line 27: .from('customer_profiles')

app\(dashboard)\dashboard\customer\reviews\page.tsx
  Line 38: } = await supabase.auth.getUser();
  Line 49: const { count: reviewsCount } = await supabase
  Line 50: .from('ratings_reviews')

app\(dashboard)\dashboard\customer\settings\actions.ts
  Line 22: } = await supabase.auth.getUser();
  Line 57: const { error: updateError } = await supabase.auth.updateUser(
  Line 108: } = await supabase.auth.getUser();
  Line 129: const { error: authUpdateError } = await supabase.auth.updateUser(
  Line 201: } = await supabase.auth.getUser();
  Line 208: const { error } = await supabase.auth.signInWithOtp({
  Line 238: } = await supabase.auth.getUser();
  Line 256: const { error: verifyError } = await supabase.auth.verifyOtp({
  Line 301: } = await supabase.auth.getUser();
  Line 333: } = await supabase.auth.getUser();
  Line 343: const { error } = await supabase.auth.signInWithOtp({
  Line 387: const { error } = await supabase.auth.verifyOtp({
  Line 429: } = await supabase.auth.getUser();
  Line 440: const { error } = await supabase.auth.signInWithPassword({
  Line 476: } = await supabase.auth.getUser();
  Line 491: const { data: items, error: listError } = await supabase.storage
  Line 522: const { error: deleteError } = await supabase.storage
  Line 554: const { error: deleteProfileError } = await supabase
  Line 555: .from('customer_profiles')
  Line 567: await supabase.auth.signOut();
  Line 571: const { error: deleteUserError } = await supabase.auth.admin.deleteUser(user.id, false);

app\(dashboard)\dashboard\customer\settings\page.tsx
  Line 20: } = await supabase.auth.getUser();

app\(dashboard)\dashboard\customer\subscriptions\actions.ts
  Line 45: let query = supabase
  Line 46: .from('subscriptions')

app\(dashboard)\dashboard\customer\subscriptions\page.tsx
  Line 35: } = await supabase.auth.getUser();

app\(main)\actions\getHomepageBusinessCard.ts
  Line 21: } = await supabase.auth.getUser();
  Line 29: const { data: businessProfile, error: profileError } = await supabase
  Line 30: .from("business_profiles")
  Line 115: } = await supabase.auth.getUser();
  Line 123: supabase
  Line 124: .from("customer_profiles")
  Line 128: supabase
  Line 129: .from("business_profiles")

app\(main)\auth\callback\AuthCallbackClient.tsx
  Line 33: const { data: authData, error: authError } = await supabase.auth.getUser();
  Line 200: const { data: authData, error: authError } = await supabase.auth.getUser();

app\(main)\blog\sitemap.ts
  Line 30: const { data: blogs, error } = await supabaseClient
  Line 31: .from("blogs")

app\(main)\blog\[blogSlug]\page.tsx
  Line 23: const { data: blog, error } = await supabase
  Line 24: .from("blogs")
  Line 58: let query = supabase
  Line 59: .from("blogs")

app\(main)\discover\actions\businessActions.ts
  Line 87: } = await supabase.auth.getUser();

app\(main)\discover\actions\combinedActions.ts
  Line 49: } = await supabase.auth.getUser();

app\(main)\discover\actions\productActions.ts
  Line 150: } = await supabase.auth.getUser();
  Line 158: let businessQuery = supabase
  Line 159: .from("business_profiles")
  Line 200: let countQuery = supabase
  Line 201: .from("products_services")
  Line 225: let productsQuery = supabase
  Line 226: .from("products_services")
  Line 337: } = await supabase.auth.getUser();
  Line 345: const { data: validBusinesses, error: businessError } = await supabase
  Line 346: .from("business_profiles")
  Line 373: let countQuery = supabase
  Line 374: .from("products_services")
  Line 393: let productsQuery = supabase
  Line 394: .from("products_services")

app\(main)\email-change-success\page.tsx
  Line 24: const { data: { user }, error: userError } = await supabase.auth.getUser();

app\(main)\LandingPageClient.tsx
  Line 63: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 76: supabase
  Line 77: .from("customer_profiles")
  Line 81: supabase
  Line 82: .from("business_profiles")
  Line 105: const { data: subscription } = await supabase
  Line 106: .from("payment_subscriptions")

app\(onboarding)\onboarding\actions.ts
  Line 56: const { data: { user }, error: userError, } = await supabase.auth.getUser();
  Line 201: } = await supabase.auth.getUser();

app\(onboarding)\onboarding\hooks\useUserData.ts
  Line 35: const sessionResponse = await supabase.auth.getSession();
  Line 47: }, [router, supabase.auth]);

app\api\admin\fix-subscription-inconsistency\route.ts
  Line 72: const { data: currentSubscription, error: subError } = await supabase
  Line 73: .from('payment_subscriptions')
  Line 95: const { data: currentProfile, error: profileError } = await supabase
  Line 96: .from('business_profiles')
  Line 156: const { data: fixResult, error: fixError } = await supabase
  Line 157: .rpc('fix_subscription_inconsistency', {
  Line 184: await supabase
  Line 185: .from('system_alerts')
  Line 251: const { data: inconsistencies, error } = await supabase
  Line 252: .rpc('find_subscription_inconsistencies');

app\api\business\likes\route.ts
  Line 43: const { data: { user }, error: authError } = await supabase.auth.getUser();

app\api\business\my-likes\route.ts
  Line 40: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 56: let countQuery = supabase
  Line 57: .from(TABLES.LIKES)
  Line 95: let dataQuery = supabase
  Line 96: .from(TABLES.LIKES)

app\api\business\my-reviews\route.ts
  Line 36: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 58: let baseQuery = supabase
  Line 59: .from(TABLES.RATINGS_REVIEWS)
  Line 114: const { data: businessProfiles } = await supabase
  Line 115: .from(TABLES.BUSINESS_PROFILES)

app\api\business\reviews\route.ts
  Line 49: const { data: requestedProfile, error: requestedProfileError } = await supabase
  Line 50: .from(TABLES.BUSINESS_PROFILES)
  Line 66: let baseQuery = supabase
  Line 67: .from(TABLES.RATINGS_REVIEWS)
  Line 140: supabase
  Line 141: .from(TABLES.BUSINESS_PROFILES)
  Line 144: supabase
  Line 145: .from(TABLES.CUSTOMER_PROFILES)

app\api\check-user-type\route.ts
  Line 11: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 21: const { data: businessProfile, error: profileError } = await supabase
  Line 22: .from(TABLES.BUSINESS_PROFILES)

app\api\customer\likes\route.ts
  Line 34: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 50: let countQuery = supabase
  Line 51: .from(TABLES.LIKES)
  Line 89: let dataQuery = supabase
  Line 90: .from(TABLES.LIKES)

app\api\customer\reviews\route.ts
  Line 35: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 54: let baseQuery = supabase
  Line 55: .from(TABLES.RATINGS_REVIEWS)
  Line 112: const { data: businessProfiles } = await supabase
  Line 113: .from(TABLES.BUSINESS_PROFILES)

app\api\customer\reviews\update\route.ts
  Line 13: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 42: const { data: reviewData, error: reviewError } = await supabase
  Line 43: .from(TABLES.RATINGS_REVIEWS)
  Line 57: const { error: updateError } = await supabase
  Line 58: .from(TABLES.RATINGS_REVIEWS)
  Line 79: const { data: businessData } = await supabase
  Line 80: .from(TABLES.BUSINESS_PROFILES)

app\api\health\subscription\route.ts
  Line 93: const { data: subscriptionHealth, error: healthError } = await supabase
  Line 94: .rpc('get_subscription_health_metrics');
  Line 190: const { data: criticalAlerts, error } = await supabase
  Line 191: .from(TABLES.SYSTEM_ALERTS)

app\api\razorpay\key\route.ts
  Line 26: } = await supabase.auth.getUser();

app\api\subscription\[id]\payments\route.ts
  Line 44: } = await supabase.auth.getUser();
  Line 54: const { data: subscription, error: subscriptionError } = await supabase
  Line 55: .from(TABLES.PAYMENT_SUBSCRIPTIONS)
  Line 77: const { data: businessProfiles } = await supabase
  Line 78: .from(TABLES.BUSINESS_PROFILES)
  Line 96: const { data: profile, error: profileError } = await supabase
  Line 97: .from(TABLES.BUSINESS_PROFILES)

app\api\subscriptions\list\route.ts
  Line 75: } = await supabase.auth.getUser();

app\api\subscriptions\my\route.ts
  Line 55: } = await supabase.auth.getUser();
  Line 65: const { data: subscription, error: subscriptionError } = await supabase
  Line 66: .from(TABLES.PAYMENT_SUBSCRIPTIONS)

app\api\subscriptions\[id]\cancel\route.ts
  Line 79: } = await supabase.auth.getUser();
  Line 89: const { data: subscription, error: subscriptionError } = await supabase
  Line 90: .from(TABLES.PAYMENT_SUBSCRIPTIONS)
  Line 151: const { error: updateError } = await supabase
  Line 152: .from(TABLES.PAYMENT_SUBSCRIPTIONS)
  Line 203: const { error: updateError } = await supabase
  Line 204: .from(TABLES.PAYMENT_SUBSCRIPTIONS)

app\api\subscriptions\[id]\invoices\route.ts
  Line 120: } = await supabase.auth.getUser();
  Line 130: const { data: subscription, error: subscriptionError } = await supabase
  Line 131: .from(TABLES.PAYMENT_SUBSCRIPTIONS)
  Line 150: const { data: businessProfile } = await supabase
  Line 151: .from(TABLES.BUSINESS_PROFILES)

app\api\subscriptions\[id]\pause\route.ts
  Line 79: } = await supabase.auth.getUser();
  Line 89: const { data: subscription, error: subscriptionError } = await supabase
  Line 90: .from(TABLES.PAYMENT_SUBSCRIPTIONS)
  Line 141: const { error: updateError } = await supabase
  Line 142: .from(TABLES.PAYMENT_SUBSCRIPTIONS)
  Line 156: const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {

app\api\subscriptions\[id]\payments\route.ts
  Line 42: } = await supabase.auth.getUser();
  Line 52: const { data: subscription, error: subscriptionError } = await supabase
  Line 53: .from("payment_subscriptions")
  Line 75: const { data: businessProfiles } = await supabase
  Line 76: .from("business_profiles")
  Line 94: const { data: profile, error: profileError } = await supabase
  Line 95: .from("business_profiles")

app\api\subscriptions\[id]\payments\[paymentId]\route.ts
  Line 39: } = await supabase.auth.getUser();
  Line 49: const { data: subscription, error: subscriptionError } = await supabase
  Line 50: .from("payment_subscriptions")
  Line 64: const { data: profile, error: profileError } = await supabase
  Line 65: .from("business_profiles")

app\api\subscriptions\[id]\pending-update\route.ts
  Line 67: } = await supabase.auth.getUser();
  Line 77: const { data: subscription, error: subscriptionError } = await supabase
  Line 78: .from(TABLES.PAYMENT_SUBSCRIPTIONS)

app\api\subscriptions\[id]\refund\utils\databaseOperations.ts
  Line 38: const { data: subscription } = await supabase
  Line 39: .from("payment_subscriptions")
  Line 50: const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {
  Line 84: const { data: subscription } = await supabase
  Line 85: .from("payment_subscriptions")
  Line 97: const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {

app\api\subscriptions\[id]\refund\utils\validators.ts
  Line 30: } = await supabase.auth.getUser();
  Line 53: const { data: profile, error: profileError } = await supabase
  Line 54: .from("business_profiles")
  Line 82: const { data: subscription, error: subscriptionError } = await supabase
  Line 83: .from("payment_subscriptions")

app\api\subscriptions\[id]\resume\route.ts
  Line 74: } = await supabase.auth.getUser();
  Line 84: const { data: subscription, error: subscriptionError } = await supabase
  Line 85: .from("payment_subscriptions")
  Line 138: const { error: updateError } = await supabase
  Line 139: .from("payment_subscriptions")
  Line 155: const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {

app\api\subscriptions\[id]\route.ts
  Line 58: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 69: const { data: subscription, error: subscriptionError } = await supabase
  Line 70: .from(TABLES.PAYMENT_SUBSCRIPTIONS)

app\api\subscriptions\[id]\scheduled-changes\route.ts
  Line 57: } = await supabase.auth.getUser();
  Line 67: const { data: subscription, error: subscriptionError } = await supabase
  Line 68: .from("payment_subscriptions")
  Line 178: } = await supabase.auth.getUser();
  Line 188: const { data: subscription, error: subscriptionError } = await supabase
  Line 189: .from("payment_subscriptions")

app\api\subscriptions\[id]\switch\route.ts
  Line 54: } = await supabase.auth.getUser();
  Line 64: const { data: subscription, error: subscriptionError } = await supabase
  Line 65: .from("payment_subscriptions")

app\api\subscriptions\[id]\switch-with-new-payment\route.ts
  Line 60: } = await supabase.auth.getUser();

app\api\subscriptions\[id]\update\route.ts
  Line 96: } = await supabase.auth.getUser();
  Line 106: const { data: subscription, error: subscriptionError } = await supabase
  Line 107: .from("payment_subscriptions")
  Line 245: const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {

app\api\test\subscription-flow\route.ts
  Line 35: const { data: { user }, error: authError } = await supabase.auth.getUser();

app\api\test\subscription-scenarios\route.ts
  Line 36: const { data: { user }, error: authError } = await supabase.auth.getUser();

app\api\webhooks\razorpay\retry\route.ts
  Line 133: const { data: stats, error } = await supabase
  Line 134: .rpc('get_webhook_error_stats');

app\components\BottomNav.tsx
  Line 129: const { data: { user } } = await supabase.auth.getUser();
  Line 133: const { data: businessProfile } = await supabase
  Line 134: .from('business_profiles')
  Line 144: const { data: customerProfile } = await supabase
  Line 145: .from('customer_profiles')

app\components\Header.tsx
  Line 44: } = await supabase.auth.getUser();
  Line 49: supabase
  Line 50: .from("customer_profiles")
  Line 54: supabase
  Line 55: .from("business_profiles")

app\locality\actions\businessActions.ts
  Line 69: let query = supabase
  Line 70: .from("business_profiles")
  Line 171: let query = supabase
  Line 172: .from("business_profiles")

app\locality\actions\combinedActions.ts
  Line 41: const { data: { session } } = await supabase.auth.getSession();

app\locality\actions\locationActions.ts
  Line 43: const { data: { session }, } = await supabase.auth.getSession();

app\locality\actions\productActions.ts
  Line 40: const businessQuery = supabase
  Line 41: .from("business_profiles")
  Line 69: let productQuery = supabase
  Line 70: .from("products_services")

app\[cardSlug]\gallery\page.tsx
  Line 78: } = await supabase.auth.getUser();
  Line 83: const { data: subscription } = await supabase
  Line 84: .from("payment_subscriptions")

app\[cardSlug]\product\actions.ts
  Line 28: const { data, error } = await supabase
  Line 29: .from("products_services")
  Line 99: const { data, error } = await supabase
  Line 100: .from("products_services")
  Line 176: const { data: productData, error: productError } = await supabase
  Line 177: .from("products_services")
  Line 217: const { data: variantsData, error: variantsError } = await supabase
  Line 218: .from("product_variants")
  Line 291: const { data, error } = await supabase
  Line 292: .from("products_services")
  Line 380: const { data: validBusinesses, error: businessError } = await supabase
  Line 381: .from("business_profiles")
  Line 400: const { data, error } = await supabase
  Line 401: .from("products_services")
  Line 511: const { data, error } = await supabase
  Line 512: .from("business_profiles")

app\[cardSlug]\product\[productSlug]\page.tsx
  Line 114: const { count, error: tableCheckError } = await supabase
  Line 115: .from("custom_ad_targets")
  Line 122: const { data: adData, error: adError } = await supabase.rpc(
  Line 143: const { data: customAd } = await supabase
  Line 144: .from("custom_ads")
  Line 166: const { data: globalAd } = await supabase
  Line 167: .from("custom_ads")

components\feed\shared\forms\LocationDisplay.tsx
  Line 32: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 40: const { data: businessProfile } = await supabase
  Line 41: .from('business_profiles')
  Line 58: const { data: customerProfile } = await supabase
  Line 59: .from('customer_profiles')

components\feed\shared\hooks\usePostOwnership.ts
  Line 28: const { data: { user }, error } = await supabase.auth.getUser();

components\feed\shared\ModernCustomerPostCard.tsx
  Line 65: const { data: { user } } = await supabase.auth.getUser();

components\feed\shared\SocialMediaBusinessPostCreator.tsx
  Line 213: const { data: { user } } = await supabase.auth.getUser();
  Line 216: const { data: profile } = await supabase
  Line 217: .from('business_profiles')
  Line 454: const { data: { user } } = await supabase.auth.getUser();
  Line 461: const { data: profile, error } = await supabase
  Line 462: .from('business_profiles')

components\feed\shared\SocialMediaPostCreator.tsx
  Line 52: const { data: { user } } = await supabase.auth.getUser();
  Line 55: const { data: profile } = await supabase
  Line 56: .from('customer_profiles')
  Line 159: const { data: { user } } = await supabase.auth.getUser();
  Line 166: const { data: profile, error } = await supabase
  Line 167: .from('customer_profiles')

components\post\ConditionalPostLayout.tsx
  Line 40: const { data: { user } } = await supabase.auth.getUser();
  Line 45: const { data: businessProfile } = await supabase
  Line 46: .from('business_profiles')
  Line 59: const { data: customerProfile } = await supabase
  Line 60: .from('customer_profiles')

components\sidebar\BusinessAppSidebar.tsx
  Line 101: } = await supabase.auth.getUser();

docs\supabase-docs\custom-ads-management.md
  Line 323: const { data: adData, error: adError } = await supabase.rpc(

docs\supabase-docs\custom-ads-technical-implementation.md
  Line 615: const { count, error: tableCheckError } = await supabase
  Line 616: .from("custom_ad_targets")
  Line 623: const { data: adData, error: adError } = await supabase.rpc(

lib\actions\activities.ts
  Line 259: } = await supabase.auth.getUser();
  Line 276: let query = supabase
  Line 277: .from("business_activities")
  Line 324: supabase
  Line 325: .from("customer_profiles_public")
  Line 328: supabase
  Line 329: .from("business_profiles")
  Line 377: const { error: markError } = await supabase
  Line 378: .from("business_activities")
  Line 426: } = await supabase.auth.getUser();
  Line 441: const { error } = await supabase
  Line 442: .from("business_activities")
  Line 459: const { data: unreadActivities, error: fetchError } = await supabase
  Line 460: .from("business_activities")
  Line 481: const { error: updateError } = await supabase
  Line 482: .from("business_activities")
  Line 521: } = await supabase.auth.getUser();
  Line 533: const { count, error } = await supabase
  Line 534: .from("business_activities")

lib\actions\blogs.ts
  Line 45: let supabaseQuery = supabase
  Line 46: .from("blogs")
  Line 127: const { data, error } = await supabase
  Line 128: .from("blogs")

lib\actions\businessProfiles\access.ts
  Line 18: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 25: const { data, error } = await supabase
  Line 26: .from("business_profiles")
  Line 55: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 62: const { data, error } = await supabase
  Line 63: .from("business_profiles")

lib\actions\businessProfiles\discovery.ts
  Line 34: const countQuery = supabase
  Line 35: .from("business_profiles")
  Line 68: let businessQuery = supabase
  Line 69: .from("business_profiles")
  Line 177: let validBusinessQuery = supabase
  Line 178: .from("business_profiles")

lib\actions\businessProfiles\location.ts
  Line 71: let query = supabase
  Line 72: .from("business_profiles")

lib\actions\businessProfiles\search.ts
  Line 46: let countQuery = supabase
  Line 47: .from("business_profiles")
  Line 51: let businessQuery = supabase
  Line 52: .from("business_profiles")
  Line 127: await supabase
  Line 128: .from("payment_subscriptions")

lib\actions\businessProfiles\sitemap.ts
  Line 20: const { data: profiles, error: profilesError } = await supabase
  Line 21: .from("business_profiles")

lib\actions\categories\locationBasedFetching.ts
  Line 61: let query = supabase
  Line 62: .from("business_profiles")
  Line 174: let businessQuery = supabase
  Line 175: .from("business_profiles")
  Line 227: let productQuery = supabase
  Line 228: .from("products_services")

lib\actions\customerPosts\crud.ts
  Line 18: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 29: const { data: customerProfile, error: profileError } = await supabase
  Line 30: .from('customer_profiles')
  Line 56: const { data, error } = await supabase
  Line 57: .from('customer_posts')
  Line 85: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 96: const { data: existingPost, error: postError } = await supabase
  Line 97: .from('customer_posts')
  Line 120: const { data, error } = await supabase
  Line 121: .from('customer_posts')
  Line 150: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 161: const { data: existingPost, error: postError } = await supabase
  Line 162: .from('customer_posts')
  Line 200: const { error } = await supabase
  Line 201: .from('customer_posts')
  Line 226: let query = supabase
  Line 227: .from('customer_posts')
  Line 279: const { data, error } = await supabase
  Line 280: .from('customer_posts')

lib\actions\customerProfiles\addressValidation.ts
  Line 21: const { data: profile, error } = await supabase
  Line 22: .from('customer_profiles')
  Line 97: const { data: profile, error } = await supabase
  Line 98: .from('customer_profiles')
  Line 176: const { data: profile, error } = await supabase
  Line 177: .from('customer_profiles')

lib\actions\interactions.ts
  Line 18: } = await supabase.auth.getUser();
  Line 33: const { data: userBusinessProfile } = await supabase
  Line 34: .from("business_profiles")
  Line 41: const { error: insertError } = await supabase
  Line 42: .from("subscriptions")
  Line 65: const { data: cardData } = await supabase
  Line 66: .from("business_profiles")
  Line 105: } = await supabase.auth.getUser();
  Line 120: const { data: userBusinessProfile } = await supabase
  Line 121: .from("business_profiles")
  Line 128: const { error: deleteError } = await supabase
  Line 129: .from("subscriptions")
  Line 143: const { data: cardData } = await supabase
  Line 144: .from("business_profiles")
  Line 183: } = await supabase.auth.getUser();
  Line 200: const { error: upsertError } = await supabase
  Line 201: .from("ratings_reviews")
  Line 224: const { data: cardData } = await supabase
  Line 225: .from("business_profiles")
  Line 256: } = await supabase.auth.getUser();
  Line 264: const { error: deleteError } = await supabase
  Line 265: .from("ratings_reviews")
  Line 278: const { data: cardData } = await supabase
  Line 279: .from("business_profiles")
  Line 310: } = await supabase.auth.getUser();
  Line 323: const { error: insertError } = await supabase
  Line 324: .from("likes")
  Line 345: const { data: cardData } = await supabase
  Line 346: .from("business_profiles")
  Line 356: const { data: userBusinessProfile } = await supabase
  Line 357: .from("business_profiles")
  Line 388: } = await supabase.auth.getUser();
  Line 404: const { error: deleteError } = await supabase
  Line 405: .from("likes")
  Line 419: const { data: cardData } = await supabase
  Line 420: .from("business_profiles")
  Line 430: const { data: userBusinessProfile } = await supabase
  Line 431: .from("business_profiles")
  Line 467: } = await supabase.auth.getUser();
  Line 488: supabase
  Line 489: .from("subscriptions")
  Line 492: supabase
  Line 493: .from("likes")
  Line 496: supabase
  Line 497: .from("ratings_reviews")

lib\actions\location\locationBySlug.ts
  Line 34: const { data, error } = await supabase
  Line 35: .from("pincodes")

lib\actions\location.ts
  Line 27: const { data: pincodeData, error: pincodeError } = await supabase
  Line 28: .from("pincodes")
  Line 85: const { data: cityData, error: cityError } = await supabase
  Line 86: .from("pincodes")
  Line 162: const { data: cityData, error: cityError } = await supabase
  Line 163: .rpc('get_distinct_cities', {
  Line 174: const { data: fallbackData, error: fallbackError } = await supabase
  Line 175: .from("pincodes")

lib\actions\posts\crud.ts
  Line 16: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 27: const { data: businessProfile, error: profileError } = await supabase
  Line 28: .from('business_profiles')
  Line 56: const { data, error } = await supabase
  Line 57: .from('business_posts')
  Line 89: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 100: const { data: existingPost, error: postError } = await supabase
  Line 101: .from('business_posts')
  Line 116: const { data, error } = await supabase
  Line 117: .from('business_posts')
  Line 154: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 165: const { data: existingPost, error: postError } = await supabase
  Line 166: .from('business_posts')
  Line 181: const { data, error } = await supabase
  Line 182: .from('business_posts')
  Line 219: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 230: const { data: existingPost, error: postError } = await supabase
  Line 231: .from('business_posts')
  Line 255: const { data, error } = await supabase
  Line 256: .from('business_posts')
  Line 290: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 301: const { data: existingPost, error: postError } = await supabase
  Line 302: .from('business_posts')
  Line 338: const { error } = await supabase
  Line 339: .from('business_posts')

lib\actions\posts\fetchSinglePost.ts
  Line 32: const { data, error } = await supabase
  Line 33: .from('unified_posts')

lib\actions\products\fetchProductsByIds.ts
  Line 33: const { data, error } = await supabase
  Line 34: .from("products_services")

lib\actions\products\sitemapHelpers.ts
  Line 25: const { data: businessProfiles, error: businessError } = await supabase
  Line 26: .from("business_profiles")
  Line 45: const { data: products, error: productsError } = await supabase
  Line 46: .from("products_services")

lib\actions\reviews.ts
  Line 45: let query = supabase
  Line 46: .from("ratings_reviews")
  Line 109: const { data: businessSlugs } = await supabase
  Line 110: .from("business_profiles")

lib\actions\secureCustomerProfiles.ts
  Line 139: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 250: const { data: customerProfiles, error: customerError } = await supabase
  Line 251: .from("customer_profiles_public")
  Line 261: const { data: businessProfiles, error: businessError } = await supabase
  Line 262: .from("business_profiles")

lib\actions\shared\productActions.ts
  Line 29: } = await supabase.auth.getUser();
  Line 92: } = await supabase.auth.getUser();
  Line 103: const { data, error } = await supabase
  Line 104: .from("products_services")

lib\actions\shared\upload-business-post-media.ts
  Line 28: } = await supabase.auth.getUser();
  Line 69: const { data: businessProfile, error: profileError } = await supabase
  Line 70: .from('business_profiles')

lib\actions\shared\upload-customer-post-media.ts
  Line 30: } = await supabase.auth.getUser();
  Line 62: const { data: existingPost, error: postError } = await supabase
  Line 63: .from('customer_posts')

lib\actions\shared\upload-post-media.ts
  Line 30: } = await supabase.auth.getUser();

lib\actions\subscription\activateTrial.ts
  Line 44: const { data: { user }, error: userError } = await supabase.auth.getUser();
  Line 52: const { data: profile, error: profileError } = await supabase
  Line 53: .from("business_profiles")
  Line 98: const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {

lib\actions\subscription\centralized.ts
  Line 30: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 112: const { data: profile, error: profileError } = await supabase
  Line 113: .from('business_profiles')
  Line 124: const { data: subscription, error: subscriptionError } = await supabase
  Line 125: .from('payment_subscriptions')

lib\actions\subscription\confirm.ts
  Line 25: const { data: { user }, error: authError } = await supabase.auth.getUser();
  Line 33: const { data: profile, error: profileError } = await supabase
  Line 34: .from("business_profiles")
  Line 45: const { data: existingSubscriptionData } = await supabase
  Line 46: .from("payment_subscriptions")
  Line 54: const { data: newSubscriptionCheck } = await supabase
  Line 55: .from("payment_subscriptions")
  Line 128: const { data: subscription, error: subscriptionError } = await supabase
  Line 129: .from("payment_subscriptions")
  Line 197: const { data: existingBusinessSubscription, error: existingBusinessError } = await supabase
  Line 198: .from("payment_subscriptions")
  Line 228: const { data: existingSubDetails, error: existingSubError } = await supabase
  Line 229: .from("payment_subscriptions")
  Line 287: const { error: updateError } = await supabase
  Line 288: .from("payment_subscriptions")
  Line 310: const { error: insertError } = await supabase
  Line 311: .from("payment_subscriptions")
  Line 322: const { data: raceSubscription, error: raceError } = await supabase
  Line 323: .from("payment_subscriptions")
  Line 334: const { error: raceUpdateError } = await supabase
  Line 335: .from("payment_subscriptions")
  Line 425: const { data: currentSubscription, error: currentSubscriptionError } = await supabase
  Line 426: .from("payment_subscriptions")
  Line 444: const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {

lib\actions\subscription\create.ts
  Line 46: } = await supabase.auth.getUser();
  Line 52: const { data: profile, error: profileError } = await supabase
  Line 53: .from("business_profiles")
  Line 166: await supabase
  Line 167: .from("payment_subscriptions")
  Line 279: const { data: subscription, error: subscriptionError } = await supabase
  Line 280: .from("payment_subscriptions")
  Line 297: const { data: profile, error: profileError } = await supabase
  Line 298: .from("business_profiles")

lib\actions\subscription\manage\cancel.ts
  Line 32: const { data: activeSubscription, error: activeSubscriptionError } = await supabase
  Line 33: .from("payment_subscriptions")
  Line 50: const { data: authSubscription, error: authSubscriptionError } = await supabase
  Line 51: .from("payment_subscriptions")
  Line 92: const { error: updateError } = await supabase
  Line 93: .from("payment_subscriptions")
  Line 155: const { error: updateError } = await supabase
  Line 156: .from("payment_subscriptions")

lib\actions\subscription\manage\change.ts
  Line 44: const { data: subscription, error: subscriptionError } = await supabase
  Line 45: .from("payment_subscriptions")
  Line 151: const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {
  Line 213: const { data: subscription, error: subscriptionError } = await supabase
  Line 214: .from("payment_subscriptions")

lib\actions\subscription\manage\manage.ts
  Line 33: const { data: activeSubscription, error: activeSubscriptionError } = await supabase
  Line 34: .from("payment_subscriptions")
  Line 51: const { data: authSubscription, error: authSubscriptionError } = await supabase
  Line 52: .from("payment_subscriptions")
  Line 106: const { error: updateError } = await supabase
  Line 107: .from("payment_subscriptions")
  Line 146: const { error: updateError } = await supabase
  Line 147: .from("payment_subscriptions")

lib\actions\subscription\manage\schedule.ts
  Line 36: const { data: subscription, error: subscriptionError } = await supabase
  Line 37: .from("payment_subscriptions")
  Line 62: const { error: updateError } = await supabase
  Line 63: .from("payment_subscriptions")

lib\actions\subscription\manage\switch.ts
  Line 44: const { data: subscription, error: subscriptionError } = await supabase
  Line 45: .from("payment_subscriptions")
  Line 148: const { data: _businessProfile } = await supabase
  Line 149: .from("business_profiles")
  Line 201: const { data: profileData } = await supabase
  Line 202: .from("business_profiles")
  Line 306: const { data: subscription, error: subscriptionError } = await supabase
  Line 307: .from("payment_subscriptions")
  Line 399: const { data: subscription, error: subscriptionError } = await supabase
  Line 400: .from("payment_subscriptions")
  Line 480: const { data: profile } = await supabase
  Line 481: .from("business_profiles")
  Line 533: const { data: profileData } = await supabase
  Line 534: .from("business_profiles")

lib\actions\subscription\status.ts
  Line 44: const { data: subscriptions, error: subscriptionError } = await supabase
  Line 45: .from('payment_subscriptions')
  Line 70: const { data: currentSubscription, error: fetchError } = await supabase
  Line 71: .from("payment_subscriptions")
  Line 87: const { error: updateError } = await supabase
  Line 88: .from("payment_subscriptions")
  Line 113: const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {
  Line 167: const { data: subscriptions, error: subscriptionError } = await supabase
  Line 168: .from("payment_subscriptions")
  Line 205: const { error: restoreError } = await supabase
  Line 206: .from("payment_subscriptions")
  Line 228: const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {
  Line 298: const { data: subscriptions, error: subscriptionError } = await supabase
  Line 299: .from("payment_subscriptions")

lib\actions\subscription\utils.ts
  Line 21: const { data, error } = await supabase.auth.getUser();
  Line 43: const { count, error: countError } = await supabase
  Line 44: .from("business_profiles")
  Line 65: const { data, error } = await supabase
  Line 66: .from("business_profiles")
  Line 132: const { data, error } = await supabase
  Line 133: .from("payment_subscriptions")
  Line 347: const { data, error } = await supabase
  Line 348: .from("payment_subscriptions")

lib\client\locationUtils.ts
  Line 44: const { data: cityData, error: cityError } = await supabase
  Line 45: .rpc('get_distinct_cities', {
  Line 56: const { data: fallbackData, error: fallbackError } = await supabase
  Line 57: .from("pincodes")
  Line 118: const { data: pincodeData, error: pincodeError } = await supabase
  Line 119: .from("pincodes")
  Line 180: const { data: cityData, error: cityError } = await supabase
  Line 181: .from("pincodes")
  Line 193: const { data: fallbackData, error: fallbackError } = await supabase
  Line 194: .from("pincodes")

lib\razorpay\webhooks\handlers\core\subscriptionManager.ts
  Line 119: const { data: result, error } = await this.supabase.rpc('update_subscription_atomic', {

lib\razorpay\webhooks\handlers\subscriptionEventHandlers\handleSubscriptionAuthenticated.ts
  Line 80: const { data: existingSubscription, error: checkError } = await supabase
  Line 81: .from("payment_subscriptions")
  Line 165: const { data: existingSubscriptions, error: findError } = await supabase
  Line 166: .from('payment_subscriptions')
  Line 198: const { error: updateError } = await supabase
  Line 199: .from("payment_subscriptions")
  Line 263: const { data: currentSubscriptionState, error: trialCheckError } = await supabase
  Line 264: .from("payment_subscriptions")

lib\razorpay\webhooks\idempotency.ts
  Line 106: const { error } = await supabase
  Line 107: .from('processed_webhook_events')

lib\razorpay\webhooks\monitoring.ts
  Line 34: const { data: statsData, error: statsError } = await supabase
  Line 35: .rpc('get_webhook_error_stats');
  Line 57: const { data: timeData, error: timeError } = await supabase
  Line 58: .from('processed_webhook_events')
  Line 109: const { data: inconsistencies, error } = await supabase
  Line 110: .rpc('find_subscription_inconsistencies');
  Line 198: await supabase
  Line 199: .from('system_alerts')

lib\services\realtimeService.ts
  Line 172: this.supabase.removeChannel(channel);
  Line 182: this.supabase.removeChannel(channel);

lib\services\subscription.ts
  Line 152: const { data: subscription, error: fetchError } = await supabase
  Line 153: .from("payment_subscriptions")
  Line 163: const { data: subscriptionData, error: planFetchError } = await supabase
  Line 164: .from("payment_subscriptions")
  Line 181: const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {
  Line 234: const { data: subscriptions, error } = await supabase
  Line 235: .from("payment_subscriptions")

lib\subscription\SubscriptionFlowTester.ts
  Line 254: const { data: existingEvent } = await supabase
  Line 255: .from('processed_webhook_events')
  Line 263: const { error: insertError } = await supabase
  Line 264: .from('processed_webhook_events')
  Line 277: const { error: duplicateError } = await supabase
  Line 278: .from('processed_webhook_events')
  Line 291: await supabase
  Line 292: .from('processed_webhook_events')
  Line 329: const { data: originalProfile } = await supabase
  Line 330: .from('business_profiles')
  Line 335: const { data: originalSubscription } = await supabase
  Line 336: .from('payment_subscriptions')
  Line 342: await supabase
  Line 343: .from('business_profiles')
  Line 351: await supabase
  Line 352: .from('payment_subscriptions')
  Line 361: const { data: validationResult, error: validationError } = await supabase
  Line 362: .rpc('validate_and_fix_subscription_state', {
  Line 372: await supabase
  Line 373: .from('business_profiles')
  Line 383: await supabase
  Line 384: .from('payment_subscriptions')

lib\testing\database.ts
  Line 27: const { data: subscription } = await supabase
  Line 28: .from('payment_subscriptions')
  Line 33: const { data: profile } = await supabase
  Line 34: .from('business_profiles')
  Line 59: await supabase
  Line 60: .from('payment_subscriptions')
  Line 72: await supabase
  Line 73: .from('business_profiles')
  Line 87: await supabase
  Line 88: .from('payment_subscriptions')
  Line 106: await supabase
  Line 107: .from('business_profiles')
  Line 116: await supabase
  Line 117: .from('processed_webhook_events')
  Line 127: const { data } = await supabase
  Line 128: .from('processed_webhook_events')
  Line 141: const { data } = await supabase
  Line 142: .from('processed_webhook_events')
  Line 158: const { data } = await supabase
  Line 159: .from('business_profiles')
  Line 177: await supabase
  Line 178: .from('processed_webhook_events')

lib\utils\addressUtils.ts
  Line 27: let query = supabase
  Line 28: .from('pincodes')
  Line 52: const { data: fallbackData, error: fallbackError } = await supabase
  Line 53: .from('pincodes')

next.config.ts
  Line 10: hostname: "rnjolcoecogzgglnblqn.supabase.co",

package.json
  Line 12: "gen-types": "npx supabase gen types typescript --schema public > types/supabase.ts"

README.md
  Line 33: This command generates `types/supabase.ts` based on your Supabase schema.

scripts\test-webhook-handlers.ts
  Line 42: await supabase
  Line 43: .from('payment_subscriptions')
  Line 53: await supabase
  Line 54: .from('business_profiles')
  Line 62: const { data: initialSub } = await supabase
  Line 63: .from('payment_subscriptions')
  Line 68: const { data: initialProfile } = await supabase
  Line 69: .from('business_profiles')
  Line 106: const { data: finalSub } = await supabase
  Line 107: .from('payment_subscriptions')
  Line 112: const { data: finalProfile } = await supabase
  Line 113: .from('business_profiles')
  Line 173: await supabase
  Line 174: .from('payment_subscriptions')
  Line 211: const { data: firstState } = await supabase
  Line 212: .from('payment_subscriptions')
  Line 222: const { data: secondState } = await supabase
  Line 223: .from('payment_subscriptions')
  Line 275: await supabase
  Line 276: .from('payment_subscriptions')
  Line 319: const { data: finalState } = await supabase
  Line 320: .from('payment_subscriptions')

scripts\webhook-testing\payment-method-handling.md
  Line 97: await supabase.rpc('update_subscription_atomic', {
  Line 130: await supabase.from('payment_subscriptions').insert({
  Line 266: await supabase.rpc('update_subscription_atomic', {

scripts\webhook-testing\README.md
  Line 169: NEXT_PUBLIC_SUPABASE_URL=https://rnjolcoecogzgglnblqn.supabase.co

scripts\webhook-testing\subscription-statuses.md
  Line 225: const result = await supabase.rpc('update_subscription_atomic', {

scripts\webhook-testing\test-webhook-events.ts
  Line 166: await supabase
  Line 167: .from('payment_subscriptions')
  Line 176: await supabase
  Line 177: .from('business_profiles')

utils\supabase\middleware.ts
  Line 38: } = await supabase.auth.getUser();
  Line 65: supabase
  Line 66: .from("customer_profiles")
  Line 70: supabase
  Line 71: .from("business_profiles")
  Line 120: const { data: subscriptionData } = await supabase
  Line 121: .from("payment_subscriptions")
