"use server";

import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import { createClient } from "@/utils/supabase/server";
import { NearbyProduct } from "@/app/(main)/discover/actions/types";
import { getSortingColumn, getSortingDirection } from "@/app/(main)/discover/utils/sortMappings";

// Fetch products by locality
export async function fetchProductsByLocality(params: {
  localityName: string;
  pincode: string;
  productName?: string;
  page?: number;
  limit?: number;
  sortBy?: BusinessSortBy | "price_asc" | "price_desc";
  productType?: "physical" | "service" | null;
}): Promise<{
  data?: {
    products: NearbyProduct[];
    totalCount: number;
    hasMore: boolean;
    nextPage: number | null;
  };
  error?: string;
}> {
  const {
    pincode,
    productName,
    page = 1,
    limit = 20,
    sortBy = "created_desc",
    productType = null,
  } = params;

  try {
    const supabase = await createClient();
    const offset = (page - 1) * limit;

    // First, get business IDs that match the locality
    const businessQuery = supabase
      .from("business_profiles")
      .select("id")
      .eq("status", "online")
      .eq("pincode", pincode);

    const { data: businesses, error: businessError } = await businessQuery;

    if (businessError) {
      console.error("Error fetching businesses for products:", businessError);
      return { error: "Failed to fetch businesses for products." };
    }

    if (!businesses || businesses.length === 0) {
      return {
        data: {
          products: [],
          totalCount: 0,
          hasMore: false,
          nextPage: null,
        },
      };
    }

    // Get business IDs
    interface BusinessId { id: string; }
    const businessIds = businesses.map((b: BusinessId) => b.id);

    // Now fetch products from these businesses
    let productQuery = supabase
      .from("products_services")
      .select(
        `
        id,
        name,
        description,
        price,
        discounted_price,
        currency,
        type,
        status,
        slug,
        created_at,
        updated_at,
        business_id,
        featured_image_url,
        business_profiles:business_id (
          id,
          business_name,
          business_slug,
          logo_url,
          city,
          state,
          pincode
        )
      `,
        { count: "exact" }
      )
      .in("business_id", businessIds)
      .eq("is_available", true);

    // Add product type filter if provided
    if (productType) {
      productQuery = productQuery.eq("type", productType);
    }

    // Add product name filter if provided
    if (productName) {
      productQuery = productQuery.ilike("name", `%${productName}%`);
    }

    // Add sorting
    productQuery = productQuery.order(getSortingColumn(sortBy), {
      ascending: getSortingDirection(sortBy),
    });

    // Add pagination
    productQuery = productQuery.range(offset, offset + limit - 1);

    // Execute the query
    const { data: products, count, error: productError } = await productQuery;

    if (productError) {
      console.error("Error fetching products:", productError);
      return { error: "Failed to fetch products." };
    }

    // Calculate if there are more results
    const hasMore = count ? offset + limit < count : false;
    const nextPage = hasMore ? page + 1 : null;

    // Format the products to match NearbyProduct type

    const formattedProducts = products.map((product: any) => {
      // Extract business_slug from business_profiles
      // Handle different response formats from Supabase
      let business_slug = null;
      if (product.business_profiles) {
        if (Array.isArray(product.business_profiles)) {
          business_slug = product.business_profiles[0]?.business_slug || null;
        } else {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          business_slug = (product.business_profiles as any).business_slug || null;
        }
      }

      return {
        id: product.id,
        name: product.name,
        description: product.description,
        base_price: product.price, // Map price to base_price
        discounted_price: product.discounted_price,
        product_type: product.type === "physical" ? "physical" : "service", // Map type to product_type
        is_available: true, // We already filtered for is_available=true
        slug: product.slug,
        created_at: product.created_at ? new Date(product.created_at) : undefined,
        updated_at: product.updated_at ? new Date(product.updated_at) : undefined,
        business_id: product.business_id,
        image_url: product.featured_image_url, // Map featured_image_url to image_url
        business_slug: business_slug,
      };
    });

    return {
      data: {
        products: formattedProducts as NearbyProduct[],
        totalCount: count || 0,
        hasMore,
        nextPage,
      },
    };
  } catch (error) {
    console.error("Error in fetchProductsByLocality:", error);
    return { error: "An error occurred while fetching products." };
  }
}

// Fetch more products by locality combined
export async function fetchMoreProductsByLocalityCombined(params: {
  localityName: string;
  pincode: string;
  productName?: string;
  page?: number;
  limit?: number;
  sortBy?: BusinessSortBy | "price_asc" | "price_desc";
  productType?: "physical" | "service" | null;
}): Promise<{
  data?: {
    products: NearbyProduct[];
    hasMore: boolean;
    nextPage: number | null;
  };
  error?: string;
}> {
  const {
    localityName,
    pincode,
    productName,
    page = 1,
    limit = 20,
    sortBy = "created_desc",
    productType = null,
  } = params;

  try {
    const result = await fetchProductsByLocality({
      localityName,
      pincode,
      productName,
      page,
      limit,
      sortBy,
      productType,
    });

    if (result.error) {
      return { error: result.error };
    }

    return {
      data: {
        products: result.data?.products || [],
        hasMore: result.data?.hasMore || false,
        nextPage: result.data?.nextPage || null,
      },
    };
  } catch (error) {
    console.error("Error in fetchMoreProductsByLocalityCombined:", error);
    return { error: "An error occurred while fetching products." };
  }
}
