
import { getCurrentUser, getCustomerProfile, hasCustomerProfile, hasBusinessProfile, getUserType, validateCustomerProfile, signOut } from '@/lib/auth/customerAuth';
import { AuthService } from '@/backend/supabase/services/auth/authService';
import { router } from 'expo-router';

jest.mock('@/backend/supabase/services/auth/authService');
jest.mock('expo-router');

describe('customerAuth', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getCurrentUser', () => {
    it('should return the current user when authenticated', async () => {
      const mockUser = { id: '123', email: '<EMAIL>' };
      (AuthService.getCurrentUser as jest.Mock).mockResolvedValue({ data: { user: mockUser } });
      const user = await getCurrentUser();
      expect(user).toEqual(mockUser);
    });

    it('should return null when not authenticated', async () => {
      (AuthService.getCurrentUser as jest.Mock).mockResolvedValue({ data: { user: null } });
      const user = await getCurrentUser();
      expect(user).toBeNull();
    });

    it('should redirect to login on specific auth errors', async () => {
      const error = new Error('User from sub claim in JWT does not exist');
      (AuthService.getCurrentUser as jest.Mock).mockResolvedValue({ data: { user: null }, error });
      await getCurrentUser();
      expect(router.replace).toHaveBeenCalledWith('/(auth)/login');
    });
  });

  describe('getCustomerProfile', () => {
    it('should return customer profile data', async () => {
      const mockProfile = { id: '123', name: 'Test User' };
      (AuthService.getCustomerProfile as jest.Mock).mockResolvedValue({ data: mockProfile });
      const { data } = await getCustomerProfile('123');
      expect(data).toEqual(mockProfile);
    });
  });

  describe('hasCustomerProfile', () => {
    it('should return true if customer profile exists', async () => {
      (AuthService.hasCustomerProfile as jest.Mock).mockResolvedValue(true);
      const result = await hasCustomerProfile('123');
      expect(result).toBe(true);
    });
  });

  describe('hasBusinessProfile', () => {
    it('should return true if business profile exists', async () => {
      (AuthService.hasBusinessProfile as jest.Mock).mockResolvedValue(true);
      const result = await hasBusinessProfile('123');
      expect(result).toBe(true);
    });
  });

  describe('getUserType', () => {
    it('should return "customer" if customer profile exists', async () => {
      (AuthService.hasCustomerProfile as jest.Mock).mockResolvedValue(true);
      (AuthService.hasBusinessProfile as jest.Mock).mockResolvedValue(false);
      const result = await getUserType('123');
      expect(result).toBe('customer');
    });

    it('should return "business" if business profile exists', async () => {
      (AuthService.hasCustomerProfile as jest.Mock).mockResolvedValue(false);
      (AuthService.hasBusinessProfile as jest.Mock).mockResolvedValue(true);
      const result = await getUserType('123');
      expect(result).toBe('business');
    });
  });

  describe('validateCustomerProfile', () => {
    it('should return valid for a complete profile', async () => {
      const mockProfile = { id: '123', name: 'Test User', pincode: '123456', state: 'Test State', city: 'Test City', locality: 'Test Locality', address: 'Test Address' };
      (AuthService.getCustomerProfileForValidation as jest.Mock).mockResolvedValue({ data: mockProfile });
      const result = await validateCustomerProfile('123');
      expect(result.isValid).toBe(true);
    });

    it('should return invalid for an incomplete profile', async () => {
      const mockProfile = { id: '123', name: '', pincode: '123456', state: 'Test State', city: 'Test City', locality: 'Test Locality', address: 'Test Address' };
      (AuthService.getCustomerProfileForValidation as jest.Mock).mockResolvedValue({ data: mockProfile });
      const result = await validateCustomerProfile('123');
      expect(result.isValid).toBe(false);
      expect(result.missingFields).toContain('name');
    });
  });

  describe('signOut', () => {
    it('should sign out the user and redirect to login', async () => {
      (AuthService.signOut as jest.Mock).mockResolvedValue({});
      await signOut();
      expect(AuthService.signOut).toHaveBeenCalled();
      expect(router.replace).toHaveBeenCalledWith('/(auth)/login');
    });
  });
});
