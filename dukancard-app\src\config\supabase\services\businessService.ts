import { supabase } from "@/src/config/supabase";
import { TABLES, COLUMNS, BUCKETS } from "../constants";
import { Database, Tables } from "@/src/types/supabase";
import { getProductBaseImagePath, getProductVariantImagePath } from "@/src/utils/storage-paths";

// Upload size limits (75MB for up to 5 images)
const MAX_UPLOAD_SIZE_BYTES = 75 * 1024 * 1024; // 75MB
const MAX_IMAGES_PER_GROUP = 5;

/**
 * Fetches subscriptions for a user, including associated business profiles.
 * @param userId The ID of the user.
 * @returns Promise<{ data: { business_profiles: Tables<'business_profiles'> | null }[] | null, error: Error | null }>
 */
/**
 * Fetches subscriptions for a user, including associated business profiles.
 * This function queries the 'payment_subscriptions' table and joins with 'business_profiles'
 * to retrieve location-related information for the business associated with the subscription.
 * It limits the result to one subscription per user.
 * @param userId The unique identifier of the user.
 * @returns A Promise that resolves to an object containing:
 *   - `data`: An array of subscription objects, each potentially containing a business_profiles object, or null if no data is found.
 *   - `error`: An Error object if the query fails, otherwise null.
 */
export const getSubscriptionsWithBusinessProfiles = async (userId: string) => {
  const { data, error } = await supabase
    .from(TABLES.PAYMENT_SUBSCRIPTIONS)
    .select(
      `
      ${COLUMNS.ID},
      ${TABLES.BUSINESS_PROFILES}!inner (
        ${COLUMNS.CITY_SLUG},
        ${COLUMNS.STATE_SLUG},
        ${COLUMNS.LOCALITY_SLUG},
        ${COLUMNS.PINCODE}
      )
    `
    )
    .eq(COLUMNS.USER_ID, userId)
    .limit(1);

  return { data, error };
};

/**
 * Fetches location data from the business_profiles table for a given user ID.
 * @param userId The ID of the user.
 * @returns Promise<{ data: Pick<Tables<'business_profiles'>, 'city_slug' | 'state_slug' | 'locality_slug' | 'pincode'> | null, error: Error | null }>
 */
/**
 * Fetches location data from the 'business_profiles' table for a given user ID.
 * This function retrieves specific location-related columns for a business profile.
 * @param userId The unique identifier of the user whose business profile location is to be fetched.
 * @returns A Promise that resolves to an object containing:
 *   - `data`: An object with 'city_slug', 'state_slug', 'locality_slug', and 'pincode' of the business profile, or null if not found.
 *   - `error`: An Error object if the query fails, otherwise null.
 */
export const getBusinessProfileLocation = async (userId: string) => {
  const { data, error } = await supabase
    .from(TABLES.BUSINESS_PROFILES)
    .select(`${COLUMNS.CITY_SLUG}, ${COLUMNS.STATE_SLUG}, ${COLUMNS.LOCALITY_SLUG}, ${COLUMNS.PINCODE}`)
    .eq(COLUMNS.ID, userId)
    .single();

  return { data, error };
};

/**
 * Fetches a product/service by its ID and business ID.
 * @param itemId The ID of the product/service.
 * @param businessId The ID of the business.
 * @returns Promise<{ data: Tables<'products_services'> | null, error: Error | null }>
 */
/**
 * Fetches a product/service by its ID and business ID from the 'products_services' table.
 * This function retrieves specific product/service details including image URLs and name.
 * @param itemId The unique identifier of the product or service.
 * @param businessId The unique identifier of the business that owns the product/service.
 * @returns A Promise that resolves to an object containing:
 *   - `data`: The product/service object, or null if not found.
 *   - `error`: An Error object if the query fails, otherwise null.
 */
export const fetchProductServiceById = async (itemId: string, businessId: string) => {
  return await supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .select(`${COLUMNS.IMAGE_URL}, ${COLUMNS.IMAGES}, ${COLUMNS.FEATURED_IMAGE_INDEX}, ${COLUMNS.NAME}`)
    .eq(COLUMNS.ID, itemId)
    .eq(COLUMNS.BUSINESS_ID, businessId)
    .single();
};

/**
 * Updates an existing product/service in the database.
 * @param itemId The ID of the product/service to update.
 * @param businessId The ID of the business owning the product/service.
 * @param dataToUpdate The data to update.
 * @returns Promise<{ data: Tables<'products_services'> | null, error: Error | null }>
 */
/**
 * Updates an existing product/service in the 'products_services' table.
 * This function updates the specified fields for a product/service identified by its ID and business ID.
 * @param itemId The unique identifier of the product/service to update.
 * @param businessId The unique identifier of the business owning the product/service.
 * @param dataToUpdate The partial data object containing fields to be updated.
 * @returns A Promise that resolves to an object containing:
 *   - `data`: The updated product/service object, or null if the update fails.
 *   - `error`: An Error object if the update fails, otherwise null.
 */
export const updateProductServiceData = async (
  itemId: string,
  businessId: string,
  dataToUpdate: Partial<Tables<'products_services'>>
) => {
  return await supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .update(dataToUpdate)
    .eq(COLUMNS.ID, itemId)
    .eq(COLUMNS.BUSINESS_ID, businessId)
    .select(
      `${COLUMNS.ID}, ${COLUMNS.PRODUCT_TYPE}, ${COLUMNS.NAME}, ${COLUMNS.DESCRIPTION}, ${COLUMNS.BASE_PRICE}, ${COLUMNS.DISCOUNTED_PRICE}, ${COLUMNS.IS_AVAILABLE}, ${COLUMNS.IMAGE_URL}, ${COLUMNS.IMAGES}, ${COLUMNS.FEATURED_IMAGE_INDEX}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}, ${COLUMNS.SLUG}`
    )
    .single();
};

/**
 * Inserts a new product/service into the database.
 * @param dataToInsert The data to insert.
 * @returns Promise<{ data: Tables<'products_services'> | null, error: Error | null }>
 */
/**
 * Inserts a new product/service into the 'products_services' table.
 * This function adds a new product or service entry to the database.
 * @param dataToInsert The data object containing the details of the product/service to be inserted.
 * @returns A Promise that resolves to an object containing:
 *   - `data`: The newly inserted product/service object, or null if the insertion fails.
 *   - `error`: An Error object if the insertion fails, otherwise null.
 */
export const insertProductServiceData = async (dataToInsert: Partial<Tables<'products_services'>>) => {
  return await supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .insert(dataToInsert)
    .select(
      `${COLUMNS.ID}, ${COLUMNS.PRODUCT_TYPE}, ${COLUMNS.NAME}, ${COLUMNS.DESCRIPTION}, ${COLUMNS.BASE_PRICE}, ${COLUMNS.DISCOUNTED_PRICE}, ${COLUMNS.IS_AVAILABLE}, ${COLUMNS.IMAGE_URL}, ${COLUMNS.IMAGES}, ${COLUMNS.FEATURED_IMAGE_INDEX}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}, ${COLUMNS.SLUG}`
    )
    .single();
};

/**
 * Deletes a product/service by its ID and business ID.
 * @param itemId The ID of the product/service to delete.
 * @param businessId The ID of the business owning the product/service.
 * @returns Promise<{ error: Error | null }>
 */
/**
 * Deletes a product/service by its ID and business ID from the 'products_services' table.
 * This function removes a specific product or service entry from the database.
 * @param itemId The unique identifier of the product/service to delete.
 * @param businessId The unique identifier of the business that owns the product/service.
 * @returns A Promise that resolves to an object containing:
 *   - `error`: An Error object if the deletion fails, otherwise null.
 */
export const deleteProductServiceById = async (itemId: string, businessId: string) => {
  return await supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .delete()
    .eq(COLUMNS.ID, itemId)
    .eq(COLUMNS.BUSINESS_ID, businessId);
};

/**
 * Helper function to handle multiple image uploads to Supabase storage.
 * Each image is processed individually and compressed to <100KB.
 * Total input: up to 5 × 15MB = 75MB, Total output: ~5 × 100KB = 500KB.
 * @param userId The ID of the user.
 * @param productId The ID of the product.
 * @param imageFiles An array of image files to upload.
 * @param existingImageUrls An array of existing image URLs.
 * @param removedIndices An array of indices of images to remove.
 * @param pathType The type of path (base or variant).
 * @param variantId The ID of the variant (if pathType is variant).
 * @returns Promise<{ urls: string[]; error?: string }> - The URLs of the uploaded images or an error.
 */
/**
 * Handles the upload of multiple images to Supabase storage, including compression, removal of old images, and generation of public URLs.
 * Each image is processed individually and compressed to <100KB.
 * Total input: up to 5 × 15MB = 75MB, Total output: ~5 × 100KB = 500KB.
 * @param userId The ID of the user.
 * @param productId The ID of the product.
 * @param imageFiles An array of image files to upload (can contain null for skipped files).
 * @param existingImageUrls An array of existing image URLs associated with the product/variant.
 * @param removedIndices An array of indices corresponding to `existingImageUrls` that should be removed from storage.
 * @param pathType The type of path for image storage ('base' for product, 'variant' for product variant).
 * @param variantId The ID of the variant (required if `pathType` is 'variant').
 * @returns A Promise that resolves to an object containing:
 *   - `urls`: An array of public URLs for all images (existing and newly uploaded) after processing.
 *   - `error`: A string containing an error message if any operation fails, otherwise undefined.
 */
export async function handleMultipleImageUpload(
  userId: string,
  productId: string,
  imageFiles: (File | null)[],
  existingImageUrls: string[] = [],
  removedIndices: number[] = [],
  pathType: 'base' | 'variant' = 'base',
  variantId?: string
): Promise<{ urls: string[]; error?: string }> {
  // Validate input parameters
  if (!userId || typeof userId !== 'string') {
    console.error('Invalid userId provided to handleMultipleImageUpload:', userId);
    return { urls: [], error: `Invalid userId: expected string, got ${typeof userId}` };
  }

  if (!productId || typeof productId !== 'string') {
    console.error('Invalid productId provided to handleMultipleImageUpload:', productId);
    return { urls: [], error: `Invalid productId: expected string, got ${typeof productId}` };
  }

  // Validate variantId if pathType is 'variant'
  if (pathType === 'variant' && (!variantId || typeof variantId !== 'string')) {
    console.error('Invalid variantId provided for variant path type:', variantId);
    return { urls: [], error: `Invalid variantId: expected string when pathType is 'variant', got ${typeof variantId}` };
  }

  // Validate upload limits
  const validImageFiles = imageFiles.filter(file => file !== null) as File[];

  console.log(`=== Image Upload Handler Debug ===`);
  console.log(`Path type: ${pathType}, Variant ID: ${variantId || 'N/A'}`);
  console.log(`Valid image files: ${validImageFiles.length}`);
  console.log(`Existing images: ${existingImageUrls.length}`);
  console.log(`Removed indices: ${removedIndices.length}`);

  if (validImageFiles.length === 0 && removedIndices.length === 0) {
    console.log("No image operations to perform, returning existing URLs");
    return { urls: existingImageUrls };
  }

  if (validImageFiles.length > MAX_IMAGES_PER_GROUP) {
    return { urls: [], error: `Maximum of ${MAX_IMAGES_PER_GROUP} images allowed per ${pathType === 'variant' ? 'variant' : 'product'}.` };
  }

  const totalUploadSize = validImageFiles.reduce((total, file) => total + file.size, 0);
  if (totalUploadSize > MAX_UPLOAD_SIZE_BYTES) {
    const totalSizeMB = (totalUploadSize / (1024 * 1024)).toFixed(1);
    const maxSizeMB = (MAX_UPLOAD_SIZE_BYTES / (1024 * 1024)).toFixed(0);
    return { urls: [], error: `Total upload size (${totalSizeMB}MB) exceeds the ${maxSizeMB}MB limit for ${pathType === 'variant' ? 'variant' : 'product'} images.` };
  }

  const bucketName = BUCKETS.BUSINESS;
  const urls: string[] = [...existingImageUrls];

  // Individual image paths are generated using scalable structure in getProductImagePath utility

  // Use admin client for storage operations to bypass RLS
  const supabaseClient = supabase;

  // First, handle removals
  for (const index of removedIndices) {
    if (index >= 0 && index < existingImageUrls.length) {
      const imageUrl = existingImageUrls[index];
      if (imageUrl) {
        try {
          console.log(`Removing image at index ${index}: ${imageUrl}`);

          // Extract the storage path from the URL
          // Parse the URL to extract the correct path
          const url = new URL(imageUrl);
          const pathParts = url.pathname.split('/');

          // The path will be in format like /storage/v1/object/public/business/userId/products/productId_name/image_0.webp
          // We need to extract the part after 'business/'
          const businessIndex = pathParts.findIndex(part => part === BUCKETS.BUSINESS);

          if (businessIndex !== -1 && businessIndex < pathParts.length - 1) {
            // Extract the path after 'business/'
            const storagePath = pathParts.slice(businessIndex + 1).join('/').split('?')[0];

            console.log(`Attempting to delete from storage path: ${storagePath}`);

            // Delete the file from storage using admin client
            const { error: deleteError } = await supabaseClient.storage
              .from(bucketName)
              .remove([storagePath]);

            if (deleteError && deleteError.message !== "The resource was not found") {
              console.error(`Error deleting image at index ${index}:`, deleteError);
              console.error(`Delete error details:`, deleteError);
              // Don't fail the entire operation for storage deletion errors
            } else {
              console.log(`Successfully deleted image at path: ${storagePath}`);
            }
          } else {
            console.warn(`Could not extract storage path from URL: ${imageUrl}`);
            console.warn(`URL pathname: ${url.pathname}`);
            console.warn(`Path parts:`, pathParts);
          }
        } catch (error) {
          console.error(`Error processing image URL for deletion at index ${index}:`, error);
          // Don't fail the entire operation for individual image deletion errors
        }

        // Remove from the URLs array regardless of storage deletion success
        // This ensures the database is updated even if storage deletion fails
        urls[index] = '';
      }
    }
  }

  // Filter out empty strings from the URLs array to actually remove the deleted images
  const filteredUrls = [...urls.filter(url => url !== '')];

  // Calculate the starting index for new images based on remaining images after deletions
  const startingImageIndex = filteredUrls.length;

  // Then, handle uploads
  for (let i = 0; i < imageFiles.length; i++) {
    const imageFile = imageFiles[i];
    if (!imageFile) {
      continue;
    }

    try {
      // Create path with scalable structure and precise timestamp to prevent caching
      const timestamp = new Date().getTime() + Math.floor(Math.random() * 1000);

      // Calculate the correct image index for naming (starting from the count of remaining images)
      const imageIndex = startingImageIndex + i;

      // Generate appropriate path based on type
      const imagePath = pathType === 'variant' && variantId
        ? getProductVariantImagePath(userId, productId, variantId, imageIndex, timestamp)
        : getProductBaseImagePath(userId, productId, imageIndex, timestamp);

      console.log(`Uploading image ${i} (index ${imageIndex}) to path: ${imagePath}`);

      // File is already compressed on client-side, just upload it
      const fileBuffer = Buffer.from(await imageFile.arrayBuffer());

      const { error: uploadError } = await supabaseClient.storage
        .from(bucketName)
        .upload(imagePath, fileBuffer, {
          contentType: imageFile.type, // Use original file type (already compressed)
          upsert: true
        });

      if (uploadError) {
        console.error(`Failed to upload image ${i}:`, uploadError);
        continue;
      }

      const { data: urlData } = supabaseClient.storage
        .from(bucketName)
        .getPublicUrl(imagePath);

      if (!urlData?.publicUrl) {
        console.error(`Could not retrieve public URL for image ${i}`);
        continue;
      }

      console.log(`Successfully uploaded image ${i}, URL: ${urlData.publicUrl}`);

      // Append the new image URL to the filtered URLs array (after existing images)
      filteredUrls.push(urlData.publicUrl);
    } catch (error) {
      console.error(`Error processing image ${i}:`, error);
    }
  }

  console.log(`Image upload complete. Returning ${filteredUrls.length} URLs:`, filteredUrls);
  console.log(`=== End Image Upload Handler Debug ===`);

  // Return the final URLs array (existing images after deletions + new uploads)
  return { urls: filteredUrls };
}

/**
 * Helper function specifically for base product images
 * @param userId The ID of the user.
 * @param productId The ID of the product.
 * @param imageFiles An array of image files to upload.
 * @param existingImageUrls An array of existing image URLs.
 * @param removedIndices An array of indices of images to remove.
 * @returns Promise<{ urls: string[]; error?: string }> - The URLs of the uploaded images or an error.
 */
/**
 * Helper function specifically for base product images.
 * This function wraps `handleMultipleImageUpload` for base product image handling.
 * @param userId The ID of the user.
 * @param productId The ID of the product.
 * @param imageFiles An array of image files to upload.
 * @param existingImageUrls An array of existing image URLs.
 * @param removedIndices An array of indices of images to remove.
 * @returns Promise<{ urls: string[]; error?: string }> - The URLs of the uploaded images or an error.
 */
export async function handleBaseProductImageUpload(
  userId: string,
  productId: string,
  imageFiles: (File | null)[],
  existingImageUrls: string[] = [],
  removedIndices: number[] = []
): Promise<{ urls: string[]; error?: string }> {
  return handleMultipleImageUpload(
    userId,
    productId,
    imageFiles,
    existingImageUrls,
    removedIndices,
    'base'
  );
}

/**
 * Helper function specifically for variant images.
 * This function wraps `handleMultipleImageUpload` for product variant image handling.
 * @param userId The ID of the user.
 * @param productId The ID of the product.
 * @param variantId The ID of the variant.
 * @param imageFiles An array of image files to upload.
 * @param existingImageUrls An array of existing image URLs.
 * @param removedIndices An array of indices of images to remove.
 * @returns Promise<{ urls: string[]; error?: string }> - The URLs of the uploaded images or an error.
 */
export async function handleVariantImageUpload(
  userId: string,
  productId: string,
  variantId: string,
  imageFiles: (File | null)[],
  existingImageUrls: string[] = [],
  removedIndices: number[] = []
): Promise<{ urls: string[]; error?: string }> {
  return handleMultipleImageUpload(
    userId,
    productId,
    imageFiles,
    existingImageUrls,
    removedIndices,
    'variant',
    variantId
  );
}

/**
 * Tracks a business card visit.
 * This function records a visit to a business profile in the 'card_visits' table.
 * It first checks if the business profile is online before recording the visit.
 * @param businessProfileId The ID of the business profile being visited.
 * @param visitorIdentifier A unique identifier for the visitor (e.g., device ID or user ID).
 * @returns A Promise that resolves to an object indicating success or failure.
 */
export async function trackBusinessCardVisit(
  businessProfileId: string,
  visitorIdentifier: string
): Promise<{ success: boolean; error?: string }> {
  if (!businessProfileId) {
    return { success: false, error: 'Business profile ID is required' };
  }

  try {
    // Check if the business profile is online
    const { data: profile, error: profileError } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(COLUMNS.STATUS)
      .eq(COLUMNS.ID, businessProfileId)
      .single();

    if (profileError) {
      console.error('Error checking business profile status:', profileError);
      return { success: false, error: 'Failed to verify business status' };
    }

    // Only track visits for online businesses
    if (profile?.status !== 'online') {
      return { success: true }; // Don't track offline businesses
    }

    // Record the visit
    const { error: visitError } = await supabase
      .from(TABLES.CARD_VISITS)
      .insert({
        business_profile_id: businessProfileId,
        visitor_identifier: visitorIdentifier,
        visited_at: new Date().toISOString(),
      });

    if (visitError) {
      console.error('Error recording visit:', visitError);
      return { success: false, error: 'Failed to record visit' };
    }

    return { success: true };
  } catch (error) {
    console.error('Error tracking visit:', error);
    return { success: false, error: 'Failed to track visit' };
  }
}

/**
 * Fetches a product/service by its ID.
 * @param productId The ID of the product/service.
 * @returns Promise<{ data: Tables<'products_services'> | null, error: Error | null }>
 */
export const fetchProductById = async (productId: string): Promise<{ success: boolean; data: Tables<'products_services'> | null; error: string | null }> => {
  const { data, error } = await supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .select(
      `
      *,
      product_variants (
        ${COLUMNS.ID},
        ${COLUMNS.VARIANT_NAME},
        ${COLUMNS.VARIANT_VALUES},
        ${COLUMNS.BASE_PRICE},
        ${COLUMNS.DISCOUNTED_PRICE},
        ${COLUMNS.IS_AVAILABLE},
        ${COLUMNS.IMAGES},
        ${COLUMNS.FEATURED_IMAGE_INDEX}
      )
    `
    )
    .eq(COLUMNS.ID, productId)
    .eq(COLUMNS.IS_AVAILABLE, true)
    .single();

  if (error) {
    console.error("Error fetching product by ID:", error);
    return { success: false, data: null, error: error.message };
  }
  return { success: true, data, error: null };
};

/**
 * Fetches a business profile by its ID.
 * @param businessId The ID of the business profile.
 * @returns Promise<{ data: Tables<'business_profiles'> | null, error: Error | null }>
 */
export const fetchBusinessProfileById = async (businessId: string): Promise<{ success: boolean; data: Tables<'business_profiles'> | null; error: string | null }> => {
  const { data, error } = await supabase
    .from(TABLES.BUSINESS_PROFILES)
    .select('*')
    .eq(COLUMNS.ID, businessId)
    .single();

  if (error) {
    console.error("Error fetching business profile by ID:", error);
    return { success: false, data: null, error: error.message };
  }
  return { success: true, data, error: null };
};

/**
 * Fetches more products from the same business.
 * @param businessId The ID of the business.
 * @param currentProductId The ID of the current product to exclude.
 * @param limit The maximum number of products to fetch.
 * @returns Promise<{ data: Tables<'products_services'>[] | null, error: Error | null }>
 */
export const fetchMoreProductsFromBusiness = async (
  businessId: string,
  currentProductId: string,
  limit: number = 8
) => {
  return await supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .select(
      `
      ${COLUMNS.ID},
      ${COLUMNS.NAME},
      ${COLUMNS.DESCRIPTION},
      ${COLUMNS.BASE_PRICE},
      ${COLUMNS.DISCOUNTED_PRICE},
      ${COLUMNS.IMAGE_URL},
      ${COLUMNS.IMAGES},
      ${COLUMNS.FEATURED_IMAGE_INDEX},
      ${COLUMNS.BUSINESS_ID},
      ${COLUMNS.IS_AVAILABLE}
      `
    )
    .eq(COLUMNS.BUSINESS_ID, businessId)
    .eq(COLUMNS.IS_AVAILABLE, true)
    .neq(COLUMNS.ID, currentProductId)
    .order(COLUMNS.CREATED_AT, { ascending: false })
    .limit(limit);
};

/**
 * Fetches products from other businesses.
 * @param businessId The ID of the current business to exclude.
 * @param limit The maximum number of products to fetch.
 * @returns Promise<{ data: (Tables<'products_services'> & { business_slug: string })[] | null, error: Error | null }>
 */
export const fetchProductsFromOtherBusinesses = async (
  businessId: string,
  limit: number = 8
) => {
  // Get all online business IDs
  const { data: validBusinesses, error: businessError } = await supabase
    .from(TABLES.BUSINESS_PROFILES)
    .select(COLUMNS.ID)
    .neq(COLUMNS.ID, businessId)
    .eq(COLUMNS.STATUS, 'online');

  if (businessError || !validBusinesses || validBusinesses.length === 0) {
    return { data: [], error: businessError };
  }

  const validBusinessIds = validBusinesses.map((b) => b.id);

  // Fetch products from valid businesses
  return await supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .select(
      `
      ${COLUMNS.ID},
      ${COLUMNS.NAME},
      ${COLUMNS.DESCRIPTION},
      ${COLUMNS.BASE_PRICE},
      ${COLUMNS.DISCOUNTED_PRICE},
      ${COLUMNS.IMAGE_URL},
      ${COLUMNS.IMAGES},
      ${COLUMNS.FEATURED_IMAGE_INDEX},
      ${COLUMNS.BUSINESS_ID},
      ${COLUMNS.IS_AVAILABLE},
      ${TABLES.BUSINESS_PROFILES}!${COLUMNS.BUSINESS_ID}(${COLUMNS.BUSINESS_SLUG})
      `
    )
    .in(COLUMNS.BUSINESS_ID, validBusinessIds)
    .eq(COLUMNS.IS_AVAILABLE, true)
    .order(COLUMNS.CREATED_AT, { ascending: false })
    .limit(limit);
};

/**
 * Fetches business activities with pagination and filtering.
 * @param businessProfileId The ID of the business profile.
 * @param from The starting index for pagination.
 * @param to The ending index for pagination.
 * @param filterBy Optional filter for activity type.
 * @returns Promise<{ data: any[] | null, error: Error | null, count: number | null }>
 */
export const fetchBusinessActivities = async (
  businessProfileId: string,
  from: number,
  to: number,
  filterBy?: 'like' | 'subscribe' | 'rating' | 'unread'
) => {
  let query = supabase
    .from(TABLES.BUSINESS_ACTIVITIES)
    .select('*', { count: 'exact' })
    .eq('business_profile_id', businessProfileId);

  // Apply filters
  if (filterBy === 'like') {
    query = query.eq('activity_type', 'like');
  } else if (filterBy === 'subscribe') {
    query = query.eq('activity_type', 'subscribe');
  } else if (filterBy === 'rating') {
    query = query.eq('activity_type', 'rating');
  } else if (filterBy === 'unread') {
    query = query.eq('is_read', false);
  }

  query = query
    .order(COLUMNS.CREATED_AT, { ascending: false })
    .range(from, to);

  return await query;
};

/**
 * Updates business activities to mark them as read.
 * @param businessProfileId The ID of the business profile.
 * @param activityIds Array of activity IDs to mark as read, or 'all' to mark all as read.
 * @returns Promise<{ data: any[] | null, error: Error | null }>
 */
export const markBusinessActivitiesAsRead = async (
  businessProfileId: string,
  activityIds: string[] | 'all'
) => {
  let query = supabase
    .from(TABLES.BUSINESS_ACTIVITIES)
    .update({ is_read: true })
    .eq('business_profile_id', businessProfileId);

  if (activityIds !== 'all') {
    query = query.in(COLUMNS.ID, activityIds);
  }

  return await query;
};

/**
 * Gets the count of unread business activities.
 * @param businessProfileId The ID of the business profile.
 * @returns Promise<{ count: number | null, error: Error | null }>
 */
export const getUnreadBusinessActivitiesCount = async (businessProfileId: string) => {
  return await supabase
    .from(TABLES.BUSINESS_ACTIVITIES)
    .select('*', { count: 'exact', head: true })
    .eq('business_profile_id', businessProfileId)
    .eq('is_read', false);
};