{"node": {"4004efaa33bf176b062db6f6fae76b568de8e96eb5": {"workers": {"app/[cardSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/page": "rsc"}}, "402f24810a766d0d430e5836c3512992e6677ea96f": {"workers": {"app/[cardSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/page": "rsc"}}, "40d08ae16ea4ae523a49582b7f4802b4f201ad0ffd": {"workers": {"app/[cardSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/page": "rsc"}}, "705db0368d449a63be0cdf8c9b55978983b5d2053c": {"workers": {"app/[cardSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/page": "rsc"}}, "00c2d9624329cded09d08193b88cb56d91c74b75b9": {"workers": {"app/[cardSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/page": "rsc"}}, "40ffc7a7de1c66ef068d94ef44ea0c4dd1fe826a68": {"workers": {"app/[cardSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/page": "action-browser"}}, "40796a449be22e4515d3e23b81252e0a149dac475e": {"workers": {"app/[cardSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/page": "action-browser"}}, "40f0adb120de9f3bad924fc8986f9159e19e49c98a": {"workers": {"app/[cardSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/page": "action-browser"}}, "4007ef8feffe7c70e5e3985caa4dcb6cf89c22bff9": {"workers": {"app/[cardSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/page": "action-browser"}}, "404e7fd84e2385eed21c771a2d81de71bc03c257c1": {"workers": {"app/[cardSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/page": "action-browser"}}, "7e7f42308ee715af64062c4a8c93140c99f6b10bcf": {"workers": {"app/[cardSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/page": "action-browser"}}, "4087fea01b98636856a1c9c1c75e269d43242ac8a2": {"workers": {"app/[cardSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/page": "action-browser"}}, "7819fa25faa13e98e17d82eebb21cc9dc660cb6e9e": {"workers": {"app/[cardSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/page": "action-browser"}}, "7004905b104910f7a038a07205afd74fefbf03df0f": {"workers": {"app/[cardSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/page": "action-browser"}}, "40582cec41753c809debb89593f731de59f975f5fa": {"workers": {"app/[cardSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/page": "action-browser"}}}, "edge": {}}