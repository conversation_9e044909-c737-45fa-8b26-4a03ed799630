module.exports = {

"[project]/lib/supabase/constants.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// lib/supabase/constants.ts
__turbopack_context__.s({
    "BUCKETS": (()=>BUCKETS),
    "COLUMNS": (()=>COLUMNS),
    "RPC_FUNCTIONS": (()=>RPC_FUNCTIONS),
    "RPC_PARAMS": (()=>RPC_PARAMS),
    "TABLES": (()=>TABLES)
});
const TABLES = {
    BLOGS: "blogs",
    BUSINESS_ACTIVITIES: "business_activities",
    BUSINESS_PROFILES: "business_profiles",
    CARD_VISITS: "card_visits",
    CUSTOM_ADS: "custom_ads",
    CUSTOM_AD_TARGETS: "custom_ad_targets",
    CUSTOMER_POSTS: "customer_posts",
    CUSTOMER_PROFILES: "customer_profiles",
    CUSTOMER_PROFILES_PUBLIC: "customer_profiles_public",
    LIKES: "likes",
    PAYMENT_SUBSCRIPTIONS: "payment_subscriptions",
    PINCODES: "pincodes",
    PRODUCTS_SERVICES: "products_services",
    PRODUCT_VARIANTS: "product_variants",
    PUBLIC_SUBSCRIPTION_STATUS: "public_subscription_status",
    STORAGE_CLEANUP_CONFIG: "storage_cleanup_config",
    STORAGE_CLEANUP_PROGRESS: "storage_cleanup_progress",
    SUBSCRIPTIONS: "subscriptions",
    SYSTEM_ALERTS: "system_alerts",
    UNIFIED_POSTS: "unified_posts",
    RATINGS_REVIEWS: "ratings_reviews"
};
const BUCKETS = {
    BUSINESS: "business",
    CUSTOMERS: "customers"
};
const COLUMNS = {
    ID: "id",
    CREATED_AT: "created_at",
    UPDATED_AT: "updated_at",
    NAME: "name",
    EMAIL: "email",
    PHONE: "phone",
    CITY: "city",
    STATE: "state",
    PINCODE: "pincode",
    PLAN_ID: "plan_id",
    LOCALITY: "locality",
    CITY_SLUG: "city_slug",
    STATE_SLUG: "state_slug",
    LOCALITY_SLUG: "locality_slug",
    OFFICE_NAME: "office_name",
    AVATAR_URL: "avatar_url",
    LOGO_URL: "logo_url",
    IMAGE_URL: "image_url",
    IMAGES: "images",
    SLUG: "slug",
    STATUS: "status",
    CONTENT: "content",
    GALLERY: "gallery",
    DESCRIPTION: "description",
    TITLE: "title",
    USER_ID: "user_id",
    BUSINESS_ID: "business_id",
    BUSINESS_NAME: "business_name",
    BUSINESS_SLUG: "business_slug",
    PRODUCT_ID: "product_id",
    LATITUDE: "latitude",
    LONGITUDE: "longitude",
    PRODUCT_TYPE: "product_type",
    BASE_PRICE: "base_price",
    DISCOUNTED_PRICE: "discounted_price",
    IS_AVAILABLE: "is_available",
    CUSTOM_AD_TARGETS: "custom_ad_targets",
    AD_IMAGE_URL: "ad_image_url",
    AD_LINK_URL: "ad_link_url",
    IS_ACTIVE: "is_active",
    TARGETING_LOCATIONS: "targeting_locations",
    RATINGS_REVIEWS: "ratings_reviews",
    BUSINESS_PROFILE_ID: "business_profile_id",
    RAZORPAY_SUBSCRIPTION_ID: "razorpay_subscription_id",
    SUBSCRIPTION_STATUS: "subscription_status",
    TOTAL_LIKES: "total_likes",
    TOTAL_SUBSCRIPTIONS: "total_subscriptions",
    AVERAGE_RATING: "average_rating",
    TOTAL_VISITS: "total_visits",
    TODAY_VISITS: "today_visits",
    YESTERDAY_VISITS: "yesterday_visits",
    VISITS_7_DAYS: "visits_7_days",
    VISITS_30_DAYS: "visits_30_days",
    CUSTOM_ADS: "custom_ads",
    CUSTOM_BRANDING: "custom_branding",
    CONTACT_EMAIL: "contact_email",
    HAS_ACTIVE_SUBSCRIPTION: "has_active_subscription",
    TRIAL_END_DATE: "trial_end_date",
    MEMBER_NAME: "member_name",
    ADDRESS_LINE: "address_line",
    INSTAGRAM_URL: "instagram_url",
    FACEBOOK_URL: "facebook_url",
    WHATSAPP_NUMBER: "whatsapp_number",
    ABOUT_BIO: "about_bio",
    THEME_COLOR: "theme_color",
    DELIVERY_INFO: "delivery_info",
    BUSINESS_HOURS: "business_hours",
    BUSINESS_CATEGORY: "business_category",
    ESTABLISHED_YEAR: "established_year",
    VARIANT_VALUES: "variant_values",
    VARIANT_NAME: "variant_name",
    FEATURED_IMAGE_INDEX: "featured_image_index",
    STATE_NAME: "StateName",
    DIVISION_NAME: "DivisionName"
};
const RPC_FUNCTIONS = {
    GET_DAILY_UNIQUE_VISIT_TREND: "get_daily_unique_visit_trend",
    GET_HOURLY_UNIQUE_VISIT_TREND: "get_hourly_unique_visit_trend",
    GET_MONTHLY_UNIQUE_VISITS: "get_monthly_unique_visits",
    GET_MONTHLY_UNIQUE_VISIT_TREND: "get_monthly_unique_visit_trend",
    GET_AVAILABLE_YEARS_FOR_MONTHLY_METRICS: "get_available_years_for_monthly_metrics",
    GET_TOTAL_UNIQUE_VISITS: "get_total_unique_visits",
    GET_AD_FOR_PINCODE: "get_ad_for_pincode",
    GET_PRODUCT_WITH_VARIANTS: "get_product_with_variants",
    GET_AVAILABLE_PRODUCT_VARIANTS: "get_available_product_variants",
    GET_BUSINESS_VARIANT_STATS: "get_business_variant_stats",
    IS_VARIANT_COMBINATION_UNIQUE: "is_variant_combination_unique",
    CREATE_BUSINESS_PROFILE_ATOMIC: "create_business_profile_atomic"
};
const RPC_PARAMS = {
    BUSINESS_ID: "business_id",
    START_DATE: "start_date",
    END_DATE: "end_date",
    TARGET_DATE: "target_date",
    TARGET_YEAR: "target_year",
    TARGET_MONTH: "target_month",
    START_YEAR: "start_year",
    START_MONTH: "start_month",
    END_YEAR: "end_year",
    END_MONTH: "end_month",
    TARGET_PINCODE: "target_pincode"
};
}}),
"[project]/lib/supabase/services/businessService.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "bulkUpdateProductVariants": (()=>bulkUpdateProductVariants),
    "bulkUpdateProductVariantsData": (()=>bulkUpdateProductVariantsData),
    "checkBusinessSlugUniqueness": (()=>checkBusinessSlugUniqueness),
    "checkIfBusinessProfileExists": (()=>checkIfBusinessProfileExists),
    "createBusinessProfileAtomic": (()=>createBusinessProfileAtomic),
    "deleteProductByIdAndBusinessId": (()=>deleteProductByIdAndBusinessId),
    "deleteProductVariant": (()=>deleteProductVariant),
    "deleteProductVariantById": (()=>deleteProductVariantById),
    "deleteProductVariantsByProductId": (()=>deleteProductVariantsByProductId),
    "getAdDataForPincode": (()=>getAdDataForPincode),
    "getAvailableYearsForMonthlyMetrics": (()=>getAvailableYearsForMonthlyMetrics),
    "getBusinessIdsByCity": (()=>getBusinessIdsByCity),
    "getBusinessLikes": (()=>getBusinessLikes),
    "getBusinessLikesCount": (()=>getBusinessLikesCount),
    "getBusinessLogoUrl": (()=>getBusinessLogoUrl),
    "getBusinessProfileAnalyticsData": (()=>getBusinessProfileAnalyticsData),
    "getBusinessProfileById": (()=>getBusinessProfileById),
    "getBusinessProfileCustomAds": (()=>getBusinessProfileCustomAds),
    "getBusinessProfileCustomBranding": (()=>getBusinessProfileCustomBranding),
    "getBusinessProfileForOnboarding": (()=>getBusinessProfileForOnboarding),
    "getBusinessProfileGallery": (()=>getBusinessProfileGallery),
    "getBusinessProfileIdAndStatusBySlug": (()=>getBusinessProfileIdAndStatusBySlug),
    "getBusinessProfileLocation": (()=>getBusinessProfileLocation),
    "getBusinessProfilePhone": (()=>getBusinessProfilePhone),
    "getBusinessProfileStatus": (()=>getBusinessProfileStatus),
    "getBusinessProfileSubscriptionInfo": (()=>getBusinessProfileSubscriptionInfo),
    "getBusinessProfileWithAllDetails": (()=>getBusinessProfileWithAllDetails),
    "getBusinessProfileWithInteractionMetrics": (()=>getBusinessProfileWithInteractionMetrics),
    "getBusinessProfilesByCity": (()=>getBusinessProfilesByCity),
    "getBusinessProfilesByIds": (()=>getBusinessProfilesByIds),
    "getDailyUniqueVisitTrend": (()=>getDailyUniqueVisitTrend),
    "getExistingProductVariants": (()=>getExistingProductVariants),
    "getFilteredProductVariants": (()=>getFilteredProductVariants),
    "getHourlyUniqueVisitTrend": (()=>getHourlyUniqueVisitTrend),
    "getLatestSubscription": (()=>getLatestSubscription),
    "getLatestSubscriptionStatus": (()=>getLatestSubscriptionStatus),
    "getMonthlyUniqueVisitTrend": (()=>getMonthlyUniqueVisitTrend),
    "getMonthlyUniqueVisits": (()=>getMonthlyUniqueVisits),
    "getMyLikes": (()=>getMyLikes),
    "getMyLikesCount": (()=>getMyLikesCount),
    "getPaymentSubscriptionByBusinessProfileId": (()=>getPaymentSubscriptionByBusinessProfileId),
    "getProductBusinessId": (()=>getProductBusinessId),
    "getProductById": (()=>getProductById),
    "getProductByIdAndBusinessId": (()=>getProductByIdAndBusinessId),
    "getProductCountByBusinessIds": (()=>getProductCountByBusinessIds),
    "getProductDetailsByIdAndBusinessId": (()=>getProductDetailsByIdAndBusinessId),
    "getProductsByBusinessIds": (()=>getProductsByBusinessIds),
    "getProductsForBusiness": (()=>getProductsForBusiness),
    "getProductsWithFiltersAndPagination": (()=>getProductsWithFiltersAndPagination),
    "getProductsWithVariantInfo": (()=>getProductsWithVariantInfo),
    "getPublicSubscriptionStatus": (()=>getPublicSubscriptionStatus),
    "getReviewsCountForBusiness": (()=>getReviewsCountForBusiness),
    "getRpcAvailableProductVariants": (()=>getRpcAvailableProductVariants),
    "getRpcBusinessVariantStats": (()=>getRpcBusinessVariantStats),
    "getRpcIsVariantCombinationUnique": (()=>getRpcIsVariantCombinationUnique),
    "getRpcProductWithVariants": (()=>getRpcProductWithVariants),
    "getSecureBusinessProfileBySlug": (()=>getSecureBusinessProfileBySlug),
    "getSecureBusinessProfileWithProductsBySlug": (()=>getSecureBusinessProfileWithProductsBySlug),
    "getTotalUniqueVisits": (()=>getTotalUniqueVisits),
    "getVariantDetailsWithProductBusinessId": (()=>getVariantDetailsWithProductBusinessId),
    "getVariantsByProductId": (()=>getVariantsByProductId),
    "getVariantsWithProductBusinessId": (()=>getVariantsWithProductBusinessId),
    "insertCardVisit": (()=>insertCardVisit),
    "insertMultipleProductVariants": (()=>insertMultipleProductVariants),
    "insertProduct": (()=>insertProduct),
    "insertProductVariant": (()=>insertProductVariant),
    "updateBusinessLogoUrl": (()=>updateBusinessLogoUrl),
    "updateBusinessProfile": (()=>updateBusinessProfile),
    "updateProduct": (()=>updateProduct),
    "updateProductVariant": (()=>updateProductVariant),
    "updateProductVariantData": (()=>updateProductVariantData),
    "updateProductVariantImages": (()=>updateProductVariantImages)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/constants.ts [app-rsc] (ecmascript)");
;
async function checkIfBusinessProfileExists(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).maybeSingle();
        if (error) {
            console.error(`Error checking existing business profile: ${error.message}`);
            return {
                exists: false,
                error: "Database error checking business profile."
            };
        }
        return {
            exists: !!data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error checking business profile: ${err}`);
        return {
            exists: false,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileAnalyticsData(supabase, businessProfileId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_VISITS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TODAY_VISITS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].YESTERDAY_VISITS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].VISITS_7_DAYS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].VISITS_30_DAYS}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, businessProfileId).single();
        if (error) {
            console.error(`Error fetching business profile analytics data: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data: data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile analytics data: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getDailyUniqueVisitTrend(supabase, businessId, startDate, endDate) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_DAILY_UNIQUE_VISIT_TREND, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].BUSINESS_ID]: businessId,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].START_DATE]: startDate,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].END_DATE]: endDate
        });
        if (error) {
            console.error(`Error fetching daily unique visit trend: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching daily unique visit trend: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getHourlyUniqueVisitTrend(supabase, businessId, targetDate) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_HOURLY_UNIQUE_VISIT_TREND, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].BUSINESS_ID]: businessId,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].TARGET_DATE]: targetDate
        });
        if (error) {
            console.error(`Error fetching hourly unique visit trend: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching hourly unique visit trend: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getMonthlyUniqueVisits(supabase, businessId, targetYear, targetMonth) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_MONTHLY_UNIQUE_VISITS, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].BUSINESS_ID]: businessId,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].TARGET_YEAR]: targetYear,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].TARGET_MONTH]: targetMonth
        });
        if (error) {
            console.error(`Error fetching monthly unique visits: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching monthly unique visits: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getMonthlyUniqueVisitTrend(supabase, businessId, startYear, startMonth, endYear, endMonth) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_MONTHLY_UNIQUE_VISIT_TREND, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].BUSINESS_ID]: businessId,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].START_YEAR]: startYear,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].START_MONTH]: startMonth,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].END_YEAR]: endYear,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].END_MONTH]: endMonth
        });
        if (error) {
            console.error(`Error fetching monthly unique visit trend: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching monthly unique visit trend: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getAvailableYearsForMonthlyMetrics(supabase, businessId) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_AVAILABLE_YEARS_FOR_MONTHLY_METRICS, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].BUSINESS_ID]: businessId
        });
        if (error) {
            console.error(`Error fetching available years for monthly metrics: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching available years for monthly metrics: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getTotalUniqueVisits(supabase, businessId) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_TOTAL_UNIQUE_VISITS, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].BUSINESS_ID]: businessId
        });
        if (error) {
            console.error(`Error fetching total unique visits: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching total unique visits: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileWithInteractionMetrics(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_LIKES}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_SUBSCRIPTIONS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].AVERAGE_RATING}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_VISITS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TODAY_VISITS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].YESTERDAY_VISITS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].VISITS_7_DAYS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].VISITS_30_DAYS}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error(`Error fetching business profile with interaction metrics: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile with interaction metrics: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getLatestSubscription(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PAYMENT_SUBSCRIPTIONS).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PLAN_ID).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, userId).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
            ascending: false
        }).limit(1).maybeSingle();
        if (error) {
            console.error(`Error fetching latest subscription: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching latest subscription: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileCustomAds(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CUSTOM_ADS).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error(`Error fetching business profile custom ads: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data: data.custom_ads,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile custom ads: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function updateBusinessProfile(supabase, userId, updates) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).update(updates).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).select().single();
        if (error) {
            console.error(`Error updating business profile: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error updating business profile: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileCustomBranding(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CUSTOM_BRANDING).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error(`Error fetching business profile custom branding: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data: data.custom_branding,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile custom branding: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfilePhone(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PHONE).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error(`Error fetching business profile phone: ${error.message}`);
            return {
                phone: null,
                error: error.message
            };
        }
        return {
            phone: data.phone,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile phone: ${err}`);
        return {
            phone: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileWithAllDetails(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CONTACT_EMAIL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].HAS_ACTIVE_SUBSCRIPTION},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TRIAL_END_DATE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].MEMBER_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TITLE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ADDRESS_LINE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PHONE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].INSTAGRAM_URL},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].FACEBOOK_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].WHATSAPP_NUMBER}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ABOUT_BIO}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_LIKES}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_SUBSCRIPTIONS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].AVERAGE_RATING}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].THEME_COLOR}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DELIVERY_INFO}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_HOURS},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_CATEGORY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CUSTOM_BRANDING}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CUSTOM_ADS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ESTABLISHED_YEAR}
      `).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error(`Error fetching business profile with all details: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile with all details: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getLatestSubscriptionStatus(supabase, userId) {
    try {
        const { data: subscription, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PAYMENT_SUBSCRIPTIONS).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PLAN_ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].SUBSCRIPTION_STATUS}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, userId).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
            ascending: false
        }).limit(1).maybeSingle();
        if (error) {
            console.error(`Error fetching latest subscription status: ${error.message}`);
            return {
                subscriptionStatus: null,
                error: error.message
            };
        }
        return {
            subscriptionStatus: subscription,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching latest subscription status: ${err}`);
        return {
            subscriptionStatus: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getSecureBusinessProfileBySlug(supabase, slug) {
    try {
        const { data: profileData, error: profileError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`
        *,
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PAYMENT_SUBSCRIPTIONS}!${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID} (
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PLAN_ID},
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].SUBSCRIPTION_STATUS}
        )
      `).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG, slug).maybeSingle();
        if (profileError) {
            console.error(`Error fetching secure business profile by slug: ${profileError.message}`);
            return {
                data: null,
                error: profileError.message
            };
        }
        if (!profileData) {
            return {
                data: null,
                error: "Profile not found."
            };
        }
        const safeData = {
            ...profileData,
            subscription_status: profileData[__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PAYMENT_SUBSCRIPTIONS]?.subscription_status || null,
            plan_id: profileData[__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PAYMENT_SUBSCRIPTIONS]?.plan_id || null
        };
        return {
            data: safeData,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching secure business profile by slug: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getSecureBusinessProfileWithProductsBySlug(supabase, slug) {
    try {
        const { data: profileData, error: profileError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`
        *,
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES} (
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DESCRIPTION}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGE_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE}
        )
      `).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG, slug).maybeSingle();
        if (profileError) {
            console.error(`Error fetching secure business profile with products by slug: ${profileError.message}`);
            return {
                data: null,
                error: profileError.message
            };
        }
        if (!profileData) {
            return {
                data: null,
                error: "Profile not found."
            };
        }
        const safeData = {
            ...profileData,
            products_services: profileData[__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES] || []
        };
        return {
            data: safeData,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching secure business profile with products by slug: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getAdDataForPincode(supabase, pincode) {
    try {
        // First, check if the custom_ad_targets table exists (for backward compatibility)
        const { count, error: tableCheckError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].CUSTOM_AD_TARGETS).select("*", {
            count: "exact",
            head: true
        });
        if (tableCheckError) {
            console.error(`Error checking custom_ad_targets table: ${tableCheckError.message}`);
            // Fallback to old approach if table check fails
            return {
                adData: null,
                error: tableCheckError.message
            };
        }
        // If the table exists and migration has been applied
        if (count !== null) {
            const { data: adData, error: adError } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_AD_FOR_PINCODE, {
                [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].TARGET_PINCODE]: pincode
            });
            if (adError) {
                console.error(`Error fetching ad for pincode ${pincode}: ${adError.message}`);
                return {
                    adData: null,
                    error: adError.message
                };
            }
            return {
                adData: adData && adData.length > 0 ? adData[0] : null,
                error: null
            };
        } else {
            // Fallback to old approach if migration hasn't been applied yet
            const { data: customAd, error: customAdError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].CUSTOM_ADS).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].AD_IMAGE_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].AD_LINK_URL}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_ACTIVE, true).or(`targeting_locations.eq.'"global"',targeting_locations.cs.'["${pincode}"]'`).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                ascending: false
            }).limit(1).maybeSingle();
            if (customAdError) {
                console.error(`Error fetching custom ad (fallback): ${customAdError.message}`);
                return {
                    adData: null,
                    error: customAdError.message
                };
            }
            return {
                adData: customAd,
                error: null
            };
        }
    } catch (err) {
        console.error(`Unexpected error fetching ad data for pincode: ${err}`);
        return {
            adData: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductsForBusiness(supabase, businessId, limit) {
    try {
        const { data, error, count } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select("*", {
            count: "exact"
        }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessId).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE, true).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
            ascending: false
        }).limit(limit);
        if (error) {
            console.error(`Error fetching products for business ${businessId}: ${error.message}`);
            return {
                products: null,
                count: 0,
                error: error.message
            };
        }
        return {
            products: data,
            count: count || 0,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching products for business: ${err}`);
        return {
            products: null,
            count: 0,
            error: "An unexpected error occurred."
        };
    }
}
async function getReviewsCountForBusiness(supabase, businessId) {
    try {
        const { count, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].RATINGS_REVIEWS).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, {
            count: "exact",
            head: true
        }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, businessId).neq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID, businessId); // Don't count self-reviews
        if (error) {
            console.error(`Error fetching reviews count for business ${businessId}: ${error.message}`);
            return {
                count: 0,
                error: error.message
            };
        }
        return {
            count: count || 0,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching reviews count for business: ${err}`);
        return {
            count: 0,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileGallery(supabase, businessId) {
    try {
        const { data: profileData, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].GALLERY).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, businessId).single();
        if (error) {
            console.error(`Error fetching business profile gallery: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data: profileData.gallery
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile gallery: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileIdAndStatusBySlug(supabase, businessSlug) {
    try {
        const { data: business, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG, businessSlug).single();
        if (error) {
            console.error(`Error fetching business ID and status by slug: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data: business,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business ID and status by slug: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getPublicSubscriptionStatus(supabase, businessProfileId) {
    try {
        const { data: subscription, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PUBLIC_SUBSCRIPTION_STATUS).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PLAN_ID).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, businessProfileId).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
            ascending: false
        }).limit(1).maybeSingle();
        if (error) {
            console.error(`Error fetching public subscription status: ${error.message}`);
            return {
                planId: null,
                error: error.message
            };
        }
        return {
            planId: subscription?.plan_id || null,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching public subscription status: ${err}`);
        return {
            planId: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductsWithFiltersAndPagination(supabase, businessId, page = 1, sortBy = "created_desc", pageSize = 20, searchTerm, productType) {
    const offset = (page - 1) * pageSize;
    let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(`
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DESCRIPTION},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGE_URL},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].SLUG}
    `, {
        count: "exact"
    }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessId).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE, true);
    if (searchTerm && searchTerm.trim().length > 0) {
        query = query.ilike(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME, `%${searchTerm.trim()}%`);
    }
    if (productType && productType !== "all") {
        query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE, productType);
    }
    switch(sortBy){
        case "created_asc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                ascending: true
            });
            break;
        case "updated_desc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT, {
                ascending: false
            });
            break;
        case "price_asc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE, {
                ascending: true,
                nullsFirst: false
            }).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE, {
                ascending: true,
                nullsFirst: false
            });
            break;
        case "price_desc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE, {
                ascending: false,
                nullsFirst: false
            }).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE, {
                ascending: false,
                nullsFirst: false
            });
            break;
        case "name_asc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME, {
                ascending: true
            });
            break;
        case "name_desc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME, {
                ascending: false
            });
            break;
        case "created_desc":
        default:
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                ascending: false
            });
            break;
    }
    query = query.range(offset, offset + pageSize - 1);
    const { data, error, count } = await query;
    if (error) {
        console.error(`Error fetching products: ${error.message}`);
        return {
            data: null,
            error: error.message,
            totalCount: 0
        };
    }
    return {
        data: data,
        error: null,
        totalCount: count || 0
    };
}
async function getBusinessProfileStatus(supabase, businessProfileId) {
    try {
        const { data: profile, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, businessProfileId).single();
        if (error) {
            console.error(`Error fetching business profile status: ${error.message}`);
            return {
                status: null,
                error: error.message
            };
        }
        return {
            status: profile.status,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile status: ${err}`);
        return {
            status: null,
            error: "An unexpected error occurred."
        };
    }
}
async function insertCardVisit(supabase, visitData) {
    try {
        const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].CARD_VISITS).insert([
            visitData
        ]);
        if (error) {
            console.error(`Error inserting card visit: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error inserting card visit: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function updateBusinessLogoUrl(supabase, userId, logoUrl) {
    const { error: updateError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).update({
        [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL]: logoUrl,
        [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT]: new Date().toISOString()
    }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId);
    if (updateError) {
        console.error("Update Business Logo URL Error:", updateError);
        return {
            success: false,
            error: `Failed to update business logo URL: ${updateError.message}`
        };
    }
    return {
        success: true
    };
}
async function getBusinessLogoUrl(supabase, userId) {
    const { data, error: fetchError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
    if (fetchError) {
        console.error("Error fetching business logo URL:", fetchError);
        return {
            logoUrl: null,
            error: "Failed to fetch business logo URL."
        };
    }
    return {
        logoUrl: data?.logo_url || null
    };
}
async function checkBusinessSlugUniqueness(supabase, slug, excludeUserId = null) {
    try {
        let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID).ilike(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG, slug);
        if (excludeUserId) {
            query = query.neq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, excludeUserId);
        }
        const { data, error } = await query.maybeSingle();
        if (error) {
            console.error(`Error checking business slug uniqueness: ${error.message}`);
            return {
                available: false,
                error: error.message
            };
        }
        return {
            available: !data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error checking business slug uniqueness: ${err}`);
        return {
            available: false,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfilesByCity(supabase, city, status, category, page, limit, sortBy, ascending) {
    try {
        const offset = (page - 1) * limit;
        let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].MEMBER_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TITLE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ADDRESS_LINE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PHONE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_CATEGORY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].INSTAGRAM_URL},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].FACEBOOK_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].WHATSAPP_NUMBER}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ABOUT_BIO}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].THEME_COLOR},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DELIVERY_INFO}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_LIKES}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_SUBSCRIPTIONS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].AVERAGE_RATING}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_HOURS},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TRIAL_END_DATE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CONTACT_EMAIL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ESTABLISHED_YEAR}
        `, {
            count: "exact"
        }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY, city).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS, status);
        if (category && category.trim()) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_CATEGORY, category.trim());
        }
        const { data, count, error } = await query.range(offset, offset + limit - 1).order(sortBy, {
            ascending
        });
        if (error) {
            console.error(`Error fetching business profiles by city: ${error.message}`);
            return {
                data: null,
                count: null,
                error: error.message
            };
        }
        return {
            data,
            count,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profiles by city: ${err}`);
        return {
            data: null,
            count: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessIdsByCity(supabase, city, status, category) {
    try {
        let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY, city).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS, status);
        if (category && category.trim()) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_CATEGORY, category.trim());
        }
        const { data, error } = await query;
        if (error) {
            console.error(`Error fetching business IDs by city: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data: data.map((item)=>item.id),
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business IDs by city: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductCountByBusinessIds(supabase, businessIds, productType) {
    try {
        let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, {
            count: "exact"
        }).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessIds).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE, true);
        if (productType) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE, productType);
        }
        const { count, error } = await query;
        if (error) {
            console.error(`Error counting products by business IDs: ${error.message}`);
            return {
                count: null,
                error: error.message
            };
        }
        return {
            count,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error counting products by business IDs: ${err}`);
        return {
            count: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductsByBusinessIds(supabase, businessIds, page, limit, sortBy, ascending, productType) {
    try {
        const from = (page - 1) * limit;
        const to = from + limit - 1;
        let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(`
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DESCRIPTION}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGE_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].SLUG},
        business_profiles!${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID}(${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG})
        `).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessIds).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE, true);
        if (productType) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE, productType);
        }
        // Apply sorting based on the sortBy parameter
        if (sortBy === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE) {
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE, {
                ascending,
                nullsFirst: false
            }).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE, {
                ascending,
                nullsFirst: false
            });
        } else {
            query = query.order(sortBy, {
                ascending
            });
        }
        const { data, error } = await query.range(from, to);
        if (error) {
            console.error(`Error fetching products by business IDs: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching products by business IDs: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductByIdAndBusinessId(supabase, productId, businessId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, productId).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessId).single();
        if (error) {
            console.error(`Error fetching product by ID and business ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching product by ID and business ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getExistingProductVariants(supabase, productId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].VARIANT_VALUES}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_ID, productId);
        if (error) {
            console.error(`Error fetching existing product variants: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching existing product variants: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function insertProductVariant(supabase, variantData) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).insert(variantData).select().single();
        if (error) {
            console.error(`Error inserting product variant: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error inserting product variant: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function deleteProductVariant(supabase, variantId) {
    try {
        const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).delete().eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantId);
        if (error) {
            console.error(`Error deleting product variant: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error deleting product variant: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function updateProductVariantImages(supabase, variantId, imageUrls, featuredImageIndex) {
    try {
        const updateData = {
            images: imageUrls,
            featured_image_index: featuredImageIndex
        };
        const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).update(updateData).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantId);
        if (error) {
            console.error(`Error updating product variant images: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error updating product variant images: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function insertMultipleProductVariants(supabase, variantDataArray) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).insert(variantDataArray).select();
        if (error) {
            console.error(`Error inserting multiple product variants: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error inserting multiple product variants: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileLocation(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY_SLUG}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY_SLUG}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE_SLUG}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error(`Error fetching business profile location: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile location: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessLikesCount(supabase, businessId) {
    const { count, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].LIKES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, {
        count: 'exact',
        head: true
    }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, businessId);
    if (error) {
        console.error('Error fetching business likes count:', error);
        throw new Error('Failed to get total count');
    }
    return count || 0;
}
async function getBusinessLikes(supabase, businessId, page, limit) {
    const from = (page - 1) * limit;
    const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].LIKES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, businessId).range(from, from + limit - 1);
    if (error) {
        console.error('Error fetching business likes:', error);
        throw new Error('Failed to fetch likes');
    }
    return data;
}
async function getMyLikesCount(supabase, businessId, searchTerm) {
    let countQuery = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].LIKES).select(`
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}!inner (
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}
      )
    `, {
        count: 'exact',
        head: true
    }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID, businessId);
    if (searchTerm) {
        countQuery = countQuery.ilike(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}.${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}`, `%${searchTerm}%`);
    }
    const { count, error } = await countQuery;
    if (error) {
        console.error('Error fetching my likes count:', error);
        throw new Error('Failed to get total count');
    }
    return count || 0;
}
async function getMyLikes(supabase, businessId, page, limit, searchTerm) {
    const from = (page - 1) * limit;
    let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].LIKES).select(`
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}!inner (
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ADDRESS_LINE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY}
      )
    `).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID, businessId);
    if (searchTerm) {
        query = query.ilike(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}.${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}`, `%${searchTerm}%`);
    }
    const { data, error } = await query.range(from, from + limit - 1);
    if (error) {
        console.error('Error fetching my likes:', error);
        throw new Error('Failed to fetch likes');
    }
    return data;
}
async function getBusinessProfilesByIds(supabase, userIds) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ADDRESS_LINE}`).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userIds);
        if (error) {
            console.error(`Error fetching business profiles by IDs: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profiles by IDs: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function insertProduct(supabase, productData) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).insert(productData).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DESCRIPTION}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGE_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGES}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].FEATURED_IMAGE_INDEX}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].SLUG}`).single();
        if (error) {
            console.error(`Error inserting product: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error inserting product: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function updateProduct(supabase, productId, updates) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).update(updates).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, productId).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGE_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGES}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].FEATURED_IMAGE_INDEX}`).single();
        if (error) {
            console.error(`Error updating product: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error updating product: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductById(supabase, productId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DESCRIPTION}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGE_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGES}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].FEATURED_IMAGE_INDEX}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].SLUG}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, productId).single();
        if (error) {
            console.error(`Error fetching product by ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching product by ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getVariantsWithProductBusinessId(supabase, variantIds) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).select(`
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_ID},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES}!inner(${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID})
      `).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantIds);
        if (error) {
            console.error(`Error fetching variants with product business ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching variants with product business ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileById(supabase, businessId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, businessId).single();
        if (error) {
            console.error(`Error fetching business profile by ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile by ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileSubscriptionInfo(supabase, businessId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].HAS_ACTIVE_SUBSCRIPTION}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TRIAL_END_DATE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, businessId).maybeSingle();
        if (error) {
            console.error(`Error fetching business profile subscription info: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile subscription info: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getPaymentSubscriptionByBusinessProfileId(supabase, businessProfileId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PAYMENT_SUBSCRIPTIONS).select("*").eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, businessProfileId).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
            ascending: false
        }).limit(1).maybeSingle();
        if (error) {
            console.error(`Error fetching payment subscription: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching payment subscription: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function bulkUpdateProductVariants(supabase, variantIds, updateData) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).update(updateData).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantIds).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID);
        if (error) {
            console.error(`Error bulk updating product variants: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error bulk updating product variants: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function updateProductVariant(supabase, variantId, updateData) {
    try {
        const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).update(updateData).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantId);
        if (error) {
            console.error(`Error updating product variant: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error updating product variant: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductDetailsByIdAndBusinessId(supabase, productId, businessId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGES}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, productId).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessId).single();
        if (error) {
            console.error(`Error fetching product details by ID and business ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching product details by ID and business ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function deleteProductByIdAndBusinessId(supabase, productId, businessId) {
    try {
        const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).delete().eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, productId).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessId);
        if (error) {
            console.error(`Error deleting product by ID and business ID: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error deleting product by ID and business ID: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function deleteProductVariantsByProductId(supabase, productId) {
    try {
        const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).delete().eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_ID, productId);
        if (error) {
            console.error(`Error deleting product variants by product ID: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error deleting product variants by product ID: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function getRpcProductWithVariants(supabase, productId) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_PRODUCT_WITH_VARIANTS, {
            product_uuid: productId
        });
        if (error) {
            console.error(`Error calling get_product_with_variants: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error in getRpcProductWithVariants: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductBusinessId(supabase, productId, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, productId).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, userId).single();
        if (error) {
            console.error(`Error fetching product business ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching product business ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getRpcAvailableProductVariants(supabase, productId) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_AVAILABLE_PRODUCT_VARIANTS, {
            product_uuid: productId
        });
        if (error) {
            console.error(`Error calling get_available_product_variants: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error in getRpcAvailableProductVariants: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getFilteredProductVariants(supabase, productId, options = {}) {
    try {
        let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).select("*", {
            count: "exact"
        }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_ID, productId);
        if (!options.includeUnavailable) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE, true);
        }
        switch(options.sortBy){
            case "created_asc":
                query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                    ascending: true
                });
                break;
            case "created_desc":
                query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                    ascending: false
                });
                break;
            case "name_asc":
                query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].VARIANT_NAME, {
                    ascending: true
                });
                break;
            case "name_desc":
                query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].VARIANT_NAME, {
                    ascending: false
                });
                break;
            case "price_asc":
                query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE, {
                    ascending: true,
                    nullsFirst: false
                });
                break;
            case "price_desc":
                query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE, {
                    ascending: false,
                    nullsFirst: true
                });
                break;
            default:
                query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                    ascending: false
                });
        }
        if (options.limit) {
            query = query.limit(options.limit);
        }
        if (options.offset) {
            query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
        }
        const { data, error: queryError, count } = await query;
        if (queryError) {
            console.error(`Error fetching filtered product variants: ${queryError.message}`);
            return {
                data: null,
                error: queryError.message,
                count: null
            };
        }
        return {
            data,
            error: null,
            count
        };
    } catch (error) {
        console.error(`Unexpected error in getFilteredProductVariants: ${error}`);
        return {
            data: null,
            error: "An unexpected error occurred.",
            count: null
        };
    }
}
async function getRpcBusinessVariantStats(supabase, businessId) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_BUSINESS_VARIANT_STATS, {
            business_uuid: businessId
        });
        if (error) {
            console.error(`Error calling get_business_variant_stats: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error in getRpcBusinessVariantStats: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getRpcIsVariantCombinationUnique(supabase, productId, variantValues, excludeVariantId) {
    try {
        const { data: result, error: functionError } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].IS_VARIANT_COMBINATION_UNIQUE, {
            product_uuid: productId,
            variant_vals: variantValues,
            exclude_variant_id: excludeVariantId || undefined
        });
        if (functionError) {
            console.error(`Error calling is_variant_combination_unique: ${functionError.message}`);
            return {
                isUnique: undefined,
                error: functionError.message
            };
        }
        return {
            isUnique: result || undefined,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error in getRpcIsVariantCombinationUnique: ${err}`);
        return {
            isUnique: undefined,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductsWithVariantInfo(supabase, userId, page = 1, limit = 10, filters = {}, sortBy = "created_desc") {
    const offset = (page - 1) * limit;
    let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(`
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DESCRIPTION},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGE_URL},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGES},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].FEATURED_IMAGE_INDEX},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].SLUG},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS}(${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE})
    `, {
        count: "exact"
    }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, userId);
    // Apply Filters
    if (filters.searchTerm) query = query.or(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}.ilike.%${filters.searchTerm}%,${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DESCRIPTION}.ilike.%${filters.searchTerm}%`);
    if (filters.hasVariants !== undefined) {
        if (filters.hasVariants) {
            // Only products that have variants
            query = query.not(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS, "is", null);
        } else {
            // Only products that don't have variants
            query = query.is(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS, null);
        }
    }
    if (filters.productType) query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE, filters.productType);
    // Apply Sorting
    switch(sortBy){
        case "created_asc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                ascending: true
            });
            break;
        case "price_asc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE, {
                ascending: true,
                nullsFirst: false
            }).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE, {
                ascending: true,
                nullsFirst: false
            });
            break;
        case "price_desc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE, {
                ascending: false,
                nullsFirst: false
            }).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE, {
                ascending: false,
                nullsFirst: false
            });
            break;
        case "name_asc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME, {
                ascending: true
            });
            break;
        case "name_desc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME, {
                ascending: false
            });
            break;
        case "available_first":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE, {
                ascending: false
            }).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                ascending: false
            });
            break;
        case "unavailable_first":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE, {
                ascending: true
            }).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                ascending: false
            });
            break;
        case "created_desc":
        case "variant_count_asc":
        case "variant_count_desc":
        default:
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                ascending: false
            });
            break;
    }
    query = query.range(offset, offset + limit - 1);
    const { data, error, count } = await query;
    if (error) {
        console.error("Fetch Products Error:", error);
        return {
            data: null,
            error: error.message,
            count: null
        };
    }
    return {
        data,
        count,
        error: null
    };
}
async function getVariantDetailsWithProductBusinessId(supabase, variantId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).select(`
        *,
        products_services!inner(business_id)
      `).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantId).single();
        if (error) {
            console.error(`Error fetching variant details by ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching variant details by ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function updateProductVariantData(supabase, variantId, updateData) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).update(updateData).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantId).select().single();
        if (error) {
            console.error(`Error updating product variant data: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error updating product variant data: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function bulkUpdateProductVariantsData(supabase, updates) {
    try {
        const updatePromises = updates.map((update)=>supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).update(update.data).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, update.id));
        const results = await Promise.all(updatePromises);
        const errors = results.filter((result)=>result.error).map((result)=>result.error?.message);
        if (errors.length > 0) {
            console.error(`Errors during bulk update of product variants: ${errors.join(", ")}`);
            return {
                success: false,
                error: `Failed to update some variants: ${errors.join(", ")}`
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error during bulk update of product variants: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function getVariantsByProductId(supabase, productId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].VARIANT_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGES}, products_services!inner(${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID}))`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_ID, productId);
        if (error) {
            console.error(`Error fetching variants by product ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        // Explicitly map the data to the correct type
        const typedData = data.map((item)=>({
                ...item,
                products_services: item.products_services
            }));
        return {
            data: typedData,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching variants by product ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileForOnboarding(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CONTACT_EMAIL},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].MEMBER_NAME},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TITLE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PHONE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_CATEGORY},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ADDRESS_LINE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS}
      `).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).maybeSingle();
        if (error) {
            console.error(`Error fetching business profile for onboarding: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile for onboarding: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function createBusinessProfileAtomic(supabase, businessData, subscriptionData) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].CREATE_BUSINESS_PROFILE_ATOMIC, {
            p_business_data: businessData,
            p_subscription_data: subscriptionData
        });
        if (error) {
            console.error(`Error calling create_business_profile_atomic: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error in createBusinessProfileAtomic: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function deleteProductVariantById(supabase, variantId) {
    try {
        const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).delete().eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantId);
        if (error) {
            console.error(`Error deleting product variant by ID: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error deleting product variant by ID: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
}}),
"[project]/app/(dashboard)/dashboard/business/gallery/utils.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "canAddMoreGalleryImages": (()=>canAddMoreGalleryImages),
    "getGalleryLimit": (()=>getGalleryLimit)
});
function getGalleryLimit(planType) {
    if (!planType) return 0;
    switch(planType){
        case "free":
            return 1;
        case "basic":
            return 3;
        case "growth":
            return 10;
        case "pro":
            return 50;
        case "enterprise":
            return 100;
        case "trial":
            return 3; // Trial users get the same as basic plan
        default:
            return 0;
    }
}
function canAddMoreGalleryImages(planType, currentCount) {
    const limit = getGalleryLimit(planType);
    return currentCount < limit;
}
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"00c2d9624329cded09d08193b88cb56d91c74b75b9":"createClient"},"",""] */ __turbopack_context__.s({
    "createClient": (()=>createClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createServerClient.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
async function createClient() {
    const supabaseUrl = ("TURBOPACK compile-time value", "https://rnjolcoecogzgglnblqn.supabase.co");
    const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o");
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // Check if we're in a test environment
    const headersList = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["headers"])();
    const isTestEnvironment = ("TURBOPACK compile-time value", "development") === 'test' || process.env.PLAYWRIGHT_TESTING === 'true' || headersList.get('x-playwright-testing') === 'true';
    if (isTestEnvironment) {
        // Return a mocked Supabase client for testing
        return createMockSupabaseClient(headersList);
    }
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createServerClient"])(supabaseUrl, supabaseAnonKey, {
        cookies: {
            async getAll () {
                return await cookieStore.getAll();
            },
            async setAll (cookiesToSet) {
                try {
                    for (const { name, value, options } of cookiesToSet){
                        await cookieStore.set(name, value, options);
                    }
                } catch  {
                // The `setAll` method was called from a Server Component.
                // This can be ignored if you have middleware refreshing
                // user sessions.
                }
            }
        }
    });
}
function createMockSupabaseClient(headersList) {
    const testAuthState = headersList.get('x-test-auth-state');
    const testUserType = headersList.get('x-test-user-type');
    const testHasProfile = testUserType === 'customer' || testUserType === 'business';
    const testBusinessSlug = headersList.get('x-test-business-slug');
    const testPlanId = headersList.get('x-test-plan-id') || 'free';
    return {
        auth: {
            getUser: async ()=>{
                if (testAuthState === 'authenticated') {
                    return {
                        data: {
                            user: {
                                id: 'test-user-id',
                                email: '<EMAIL>'
                            }
                        },
                        error: null
                    };
                }
                return {
                    data: {
                        user: null
                    },
                    error: {
                        message: 'Unauthorized',
                        name: 'AuthApiError',
                        status: 401
                    }
                };
            },
            getSession: async ()=>{
                if (testAuthState === 'authenticated') {
                    return {
                        data: {
                            session: {
                                user: {
                                    id: 'test-user-id',
                                    email: '<EMAIL>'
                                }
                            }
                        },
                        error: null
                    };
                }
                return {
                    data: {
                        session: null
                    },
                    error: {
                        message: 'Unauthorized',
                        name: 'AuthApiError',
                        status: 401
                    }
                };
            },
            signInWithOtp: async ()=>({
                    data: {
                        user: null,
                        session: null
                    },
                    error: null
                }),
            signOut: async ()=>({
                    error: null
                })
        },
        from: (table)=>createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId)
    };
}
function createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId) {
    const getMockData = ()=>getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId);
    const createChainableMock = (data)=>({
            select: (_columns)=>createChainableMock(data),
            eq: (_column, _value)=>createChainableMock(data),
            neq: (_column, _value)=>createChainableMock(data),
            gt: (_column, _value)=>createChainableMock(data),
            gte: (_column, _value)=>createChainableMock(data),
            lt: (_column, _value)=>createChainableMock(data),
            lte: (_column, _value)=>createChainableMock(data),
            like: (_column, _pattern)=>createChainableMock(data),
            ilike: (_column, _pattern)=>createChainableMock(data),
            is: (_column, _value)=>createChainableMock(data),
            in: (_column, _values)=>createChainableMock(data),
            contains: (_column, _value)=>createChainableMock(data),
            containedBy: (_column, _value)=>createChainableMock(data),
            rangeGt: (_column, _value)=>createChainableMock(data),
            rangeGte: (_column, _value)=>createChainableMock(data),
            rangeLt: (_column, _value)=>createChainableMock(data),
            rangeLte: (_column, _value)=>createChainableMock(data),
            rangeAdjacent: (_column, _value)=>createChainableMock(data),
            overlaps: (_column, _value)=>createChainableMock(data),
            textSearch: (_column, _query)=>createChainableMock(data),
            match: (_query)=>createChainableMock(data),
            not: (_column, _operator, _value)=>createChainableMock(data),
            or: (_filters)=>createChainableMock(data),
            filter: (_column, _operator, _value)=>createChainableMock(data),
            order: (_column, _options)=>createChainableMock(data),
            limit: (_count, _options)=>createChainableMock(data),
            range: (_from, _to, _options)=>createChainableMock(data),
            abortSignal: (_signal)=>createChainableMock(data),
            single: async ()=>getMockData(),
            maybeSingle: async ()=>getMockData(),
            then: async (callback)=>{
                const result = getMockData();
                return callback ? callback(result) : result;
            },
            data: data || [],
            error: null,
            count: data ? data.length : 0,
            status: 200,
            statusText: 'OK'
        });
    return {
        select: (_columns)=>createChainableMock(),
        insert: (data)=>({
                select: (_columns)=>({
                        single: async ()=>({
                                data: Array.isArray(data) ? data[0] : data,
                                error: null
                            }),
                        maybeSingle: async ()=>({
                                data: Array.isArray(data) ? data[0] : data,
                                error: null
                            }),
                        then: async (_callback)=>{
                            const result = {
                                data: Array.isArray(data) ? data : [
                                    data
                                ],
                                error: null
                            };
                            return _callback ? _callback(result) : result;
                        }
                    }),
                then: async (_callback)=>{
                    const result = {
                        data: Array.isArray(data) ? data : [
                            data
                        ],
                        error: null
                    };
                    return _callback ? _callback(result) : result;
                }
            }),
        update: (data)=>createChainableMock(data),
        upsert: (data)=>createChainableMock(data),
        delete: ()=>createChainableMock(),
        rpc: (_functionName, _params)=>createChainableMock()
    };
}
/**
 * Helper function to get mock table data based on test state
 */ function getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId) {
    if (table === 'customer_profiles') {
        const hasCustomerProfile = testHasProfile && testUserType === 'customer';
        return {
            data: hasCustomerProfile ? {
                id: 'test-user-id',
                name: 'Test Customer',
                avatar_url: null,
                phone: '+1234567890',
                email: '<EMAIL>',
                address: 'Test Address',
                city: 'Test City',
                state: 'Test State',
                pincode: '123456'
            } : null,
            error: null
        };
    }
    if (table === 'business_profiles') {
        const hasBusinessProfile = testHasProfile && testUserType === 'business';
        return {
            data: hasBusinessProfile ? {
                id: 'test-user-id',
                business_slug: testBusinessSlug || null,
                trial_end_date: null,
                has_active_subscription: true,
                business_name: 'Test Business',
                city_slug: 'test-city',
                state_slug: 'test-state',
                locality_slug: 'test-locality',
                pincode: '123456',
                business_description: 'Test business description',
                business_category: 'retail',
                phone: '+1234567890',
                email: '<EMAIL>',
                website: 'https://testbusiness.com'
            } : null,
            error: null
        };
    }
    if (table === 'payment_subscriptions') {
        return {
            data: testUserType === 'business' ? {
                id: 'test-subscription-id',
                plan_id: testPlanId,
                business_profile_id: 'test-user-id',
                status: 'active',
                created_at: '2024-01-01T00:00:00Z'
            } : null,
            error: null
        };
    }
    if (table === 'products') {
        return {
            data: testUserType === 'business' ? [
                {
                    id: 'test-product-1',
                    name: 'Test Product 1',
                    price: 100,
                    business_profile_id: 'test-user-id',
                    available: true
                },
                {
                    id: 'test-product-2',
                    name: 'Test Product 2',
                    price: 200,
                    business_profile_id: 'test-user-id',
                    available: false
                }
            ] : [],
            error: null
        };
    }
    // Default return for unknown tables
    return {
        data: null,
        error: null
    };
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    createClient
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(createClient, "00c2d9624329cded09d08193b88cb56d91c74b75b9", null);
}}),
"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"4004efaa33bf176b062db6f6fae76b568de8e96eb5":"getBusinessGalleryImages","402f24810a766d0d430e5836c3512992e6677ea96f":"getBusinessGalleryImagesForTab","40d08ae16ea4ae523a49582b7f4802b4f201ad0ffd":"getBusinessGalleryImagesBySlug","705db0368d449a63be0cdf8c9b55978983b5d2053c":"getBusinessGalleryImagesPaginated"},"",""] */ __turbopack_context__.s({
    "getBusinessGalleryImages": (()=>getBusinessGalleryImages),
    "getBusinessGalleryImagesBySlug": (()=>getBusinessGalleryImagesBySlug),
    "getBusinessGalleryImagesForTab": (()=>getBusinessGalleryImagesForTab),
    "getBusinessGalleryImagesPaginated": (()=>getBusinessGalleryImagesPaginated)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/services/businessService.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$gallery$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/gallery/utils.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
async function getBusinessGalleryImages(businessId) {
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: galleryData, error } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessProfileGallery"])(supabase, businessId);
        if (error) {
            console.error("Error fetching business profile:", error);
            return {
                images: [],
                error: `Failed to fetch gallery images: ${error}`
            };
        }
        const gallery = Array.isArray(galleryData) ? galleryData : [];
        const images = Array.isArray(gallery) ? gallery : [];
        images.sort((a, b)=>{
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });
        return {
            images: images
        };
    } catch (error) {
        console.error("Unexpected error fetching gallery images:", error);
        return {
            images: [],
            error: `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`
        };
    }
}
async function getBusinessGalleryImagesForTab(businessSlug) {
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: business, error: businessError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessProfileIdAndStatusBySlug"])(supabase, businessSlug);
        if (businessError || !business) {
            console.error("Error fetching business profile:", businessError);
            return {
                images: [],
                totalCount: 0,
                error: "Business not found"
            };
        }
        if (business.status !== "online") {
            return {
                images: [],
                totalCount: 0,
                error: "Business is not online"
            };
        }
        const { planId, error: subscriptionError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getPublicSubscriptionStatus"])(supabase, business.id);
        if (subscriptionError) {
            console.error("Error fetching subscription data:", subscriptionError);
        }
        const planIdValue = planId || "free";
        const galleryLimit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$gallery$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getGalleryLimit"])(planIdValue);
        const { data: galleryData, error: galleryError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessProfileGallery"])(supabase, business.id);
        if (galleryError) {
            console.error("Error fetching business gallery:", galleryError);
            return {
                images: [],
                totalCount: 0,
                error: `Failed to fetch gallery images: ${galleryError}`
            };
        }
        const gallery = Array.isArray(galleryData) ? galleryData : [];
        const images = Array.isArray(gallery) ? gallery : [];
        images.sort((a, b)=>{
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });
        const planLimitedImages = images.slice(0, galleryLimit);
        const finalLimit = Math.min(planLimitedImages.length, 4);
        const limitedImages = planLimitedImages.slice(0, finalLimit);
        const totalCount = planLimitedImages.length;
        return {
            images: limitedImages,
            totalCount
        };
    } catch (error) {
        console.error("Unexpected error fetching gallery images for tab:", error);
        return {
            images: [],
            totalCount: 0,
            error: `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`
        };
    }
}
async function getBusinessGalleryImagesBySlug(businessSlug) {
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: business, error: businessError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessProfileIdAndStatusBySlug"])(supabase, businessSlug);
        if (businessError || !business) {
            console.error("Error fetching business profile:", businessError);
            return {
                images: [],
                error: "Business not found"
            };
        }
        if (business.status !== "online") {
            return {
                images: [],
                error: "Business is not online"
            };
        }
        const { planId, error: subscriptionError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getPublicSubscriptionStatus"])(supabase, business.id);
        if (subscriptionError) {
            console.error("Error fetching subscription data:", subscriptionError);
        }
        const planIdValue = planId || "free";
        const galleryLimit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$gallery$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getGalleryLimit"])(planIdValue);
        const { data: galleryData, error: galleryError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessProfileGallery"])(supabase, business.id);
        if (galleryError) {
            console.error("Error fetching business gallery:", galleryError);
            return {
                images: [],
                error: `Failed to fetch gallery images: ${galleryError}`
            };
        }
        const gallery = Array.isArray(galleryData) ? galleryData : [];
        const images = Array.isArray(gallery) ? gallery : [];
        images.sort((a, b)=>{
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });
        const limitedImages = images.slice(0, galleryLimit);
        return {
            images: limitedImages
        };
    } catch (error) {
        console.error("Unexpected error fetching gallery images by slug:", error);
        return {
            images: [],
            error: `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`
        };
    }
}
async function getBusinessGalleryImagesPaginated(businessSlug, page = 1, limit = 20) {
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: business, error: businessError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessProfileIdAndStatusBySlug"])(supabase, businessSlug);
        if (businessError || !business) {
            console.error("Error fetching business profile:", businessError);
            return {
                images: [],
                totalCount: 0,
                totalPages: 0,
                currentPage: page,
                hasNextPage: false,
                hasPrevPage: false,
                error: "Business not found"
            };
        }
        if (business.status !== "online") {
            return {
                images: [],
                totalCount: 0,
                totalPages: 0,
                currentPage: page,
                hasNextPage: false,
                hasPrevPage: false,
                error: "Business is not online"
            };
        }
        const { planId, error: subscriptionError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getPublicSubscriptionStatus"])(supabase, business.id);
        if (subscriptionError) {
            console.error("Error fetching subscription data:", subscriptionError);
        }
        const planIdValue = planId || "free";
        const galleryLimit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$gallery$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getGalleryLimit"])(planIdValue);
        const { data: galleryData, error: galleryError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessProfileGallery"])(supabase, business.id);
        if (galleryError) {
            console.error("Error fetching business gallery:", galleryError);
            return {
                images: [],
                totalCount: 0,
                totalPages: 0,
                currentPage: page,
                hasNextPage: false,
                hasPrevPage: false,
                error: `Failed to fetch gallery images: ${galleryError}`
            };
        }
        const gallery = Array.isArray(galleryData) ? galleryData : [];
        const allImages = Array.isArray(gallery) ? gallery : [];
        allImages.sort((a, b)=>{
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });
        const planLimitedImages = allImages.slice(0, galleryLimit);
        const totalCount = planLimitedImages.length;
        const totalPages = Math.ceil(totalCount / limit);
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedImages = planLimitedImages.slice(startIndex, endIndex);
        return {
            images: paginatedImages,
            totalCount,
            totalPages,
            currentPage: page,
            hasNextPage: page < totalPages,
            hasPrevPage: page > 1
        };
    } catch (error) {
        console.error("Unexpected error fetching paginated gallery images:", error);
        return {
            images: [],
            totalCount: 0,
            totalPages: 0,
            currentPage: page,
            hasNextPage: false,
            hasPrevPage: false,
            error: `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getBusinessGalleryImages,
    getBusinessGalleryImagesForTab,
    getBusinessGalleryImagesBySlug,
    getBusinessGalleryImagesPaginated
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getBusinessGalleryImages, "4004efaa33bf176b062db6f6fae76b568de8e96eb5", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getBusinessGalleryImagesForTab, "402f24810a766d0d430e5836c3512992e6677ea96f", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getBusinessGalleryImagesBySlug, "40d08ae16ea4ae523a49582b7f4802b4f201ad0ffd", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getBusinessGalleryImagesPaginated, "705db0368d449a63be0cdf8c9b55978983b5d2053c", null);
}}),
"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"4007ef8feffe7c70e5e3985caa4dcb6cf89c22bff9":"unlikeBusiness","404e7fd84e2385eed21c771a2d81de71bc03c257c1":"unsubscribeFromBusiness","40582cec41753c809debb89593f731de59f975f5fa":"deleteReview","40796a449be22e4515d3e23b81252e0a149dac475e":"likeBusiness","40f0adb120de9f3bad924fc8986f9159e19e49c98a":"subscribeToBusiness","40ffc7a7de1c66ef068d94ef44ea0c4dd1fe826a68":"getInteractionStatus","7004905b104910f7a038a07205afd74fefbf03df0f":"submitReview"},"",""] */ __turbopack_context__.s({
    "deleteReview": (()=>deleteReview),
    "getInteractionStatus": (()=>getInteractionStatus),
    "likeBusiness": (()=>likeBusiness),
    "submitReview": (()=>submitReview),
    "subscribeToBusiness": (()=>subscribeToBusiness),
    "unlikeBusiness": (()=>unlikeBusiness),
    "unsubscribeFromBusiness": (()=>unsubscribeFromBusiness)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/cache.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
async function subscribeToBusiness(businessProfileId) {
    // const cookieStore = cookies(); // No longer needed here
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])(); // Await the async function
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    // Prevent a business from subscribing to their own business card
    if (user.id === businessProfileId) {
        return {
            success: false,
            error: "You cannot subscribe to your own business card."
        };
    }
    // Check if the current user is a business (has a business profile)
    const { data: userBusinessProfile } = await supabase.from("business_profiles").select("id").eq("id", user.id).maybeSingle();
    try {
        // 1. Insert subscription - Use regular client with proper RLS
        const { error: insertError } = await supabase.from("subscriptions").insert({
            user_id: user.id,
            business_profile_id: businessProfileId
        });
        if (insertError) {
            // Handle potential unique constraint violation (already subscribed) gracefully
            if (insertError.code === "23505") {
                // unique_violation
                console.log(`User ${user.id} already subscribed to business ${businessProfileId}.`);
                // Optionally return success true if already subscribed is acceptable
                return {
                    success: true
                };
            }
            console.error("Error inserting subscription:", insertError);
            throw new Error(insertError.message);
        }
        // Note: We don't need to manually update the subscription count
        // The database trigger 'update_total_subscriptions' will handle this automatically
        // 3. Revalidate paths
        // Revalidate the specific card page and potentially the user's dashboard
        // Use regular client - business_profiles has public read access
        const { data: cardData } = await supabase.from("business_profiles").select("business_slug").eq("id", businessProfileId).single();
        if (cardData?.business_slug) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/${cardData.business_slug}`);
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/customer"); // Revalidate customer dashboard
        // Check if the current user is a business and revalidate business dashboard
        if (userBusinessProfile) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business"); // Revalidate business dashboard
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business/subscriptions"); // Revalidate business subscriptions page
        }
        // Revalidate the activities page for the business
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/dashboard/business/activities`);
        return {
            success: true
        };
    } catch (error) {
        console.error("Unexpected error in subscribeToBusiness:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        return {
            success: false,
            error: errorMessage
        };
    }
}
async function unsubscribeFromBusiness(businessProfileId) {
    // const cookieStore = cookies();
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])(); // Await the async function
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    // Prevent a business from unsubscribing from their own business card
    if (user.id === businessProfileId) {
        return {
            success: false,
            error: "You cannot unsubscribe from your own business card."
        };
    }
    // Check if the current user is a business (has a business profile)
    const { data: userBusinessProfile } = await supabase.from("business_profiles").select("id").eq("id", user.id).maybeSingle();
    try {
        // 1. Delete subscription - Use regular client with proper RLS
        const { error: deleteError } = await supabase.from("subscriptions").delete().match({
            user_id: user.id,
            business_profile_id: businessProfileId
        });
        if (deleteError) {
            console.error("Error deleting subscription:", deleteError);
            throw new Error(deleteError.message);
        }
        // Note: We don't need to manually update the subscription count
        // The database trigger 'update_total_subscriptions' will handle this automatically
        // 3. Revalidate paths
        // Use regular client - business_profiles has public read access
        const { data: cardData } = await supabase.from("business_profiles").select("business_slug").eq("id", businessProfileId).single();
        if (cardData?.business_slug) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/${cardData.business_slug}`);
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/customer");
        // Check if the current user is a business and revalidate business dashboard
        if (userBusinessProfile) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business"); // Revalidate business dashboard
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business/subscriptions"); // Revalidate business subscriptions page
        }
        // Revalidate the activities page for the business
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/dashboard/business/activities`);
        return {
            success: true
        };
    } catch (error) {
        console.error("Unexpected error in unsubscribeFromBusiness:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        return {
            success: false,
            error: errorMessage
        };
    }
}
async function submitReview(businessProfileId, rating, reviewText// Allow null for review text
) {
    // const cookieStore = cookies();
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])(); // Await the async function
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    // Prevent a business from reviewing their own business card
    if (user.id === businessProfileId) {
        return {
            success: false,
            error: "You cannot review your own business card."
        };
    }
    if (rating < 1 || rating > 5) {
        return {
            success: false,
            error: "Rating must be between 1 and 5."
        };
    }
    try {
        // Upsert the review: insert if not exists, update if exists - Use regular client with proper RLS
        const { error: upsertError } = await supabase.from("ratings_reviews").upsert({
            user_id: user.id,
            business_profile_id: businessProfileId,
            rating: rating,
            review_text: reviewText,
            updated_at: new Date().toISOString()
        }, {
            onConflict: "user_id, business_profile_id"
        });
        if (upsertError) {
            console.error("Error submitting review:", upsertError);
            throw new Error(upsertError.message);
        }
        // Average rating is handled by the database trigger
        // Revalidate paths
        // Use regular client - business_profiles has public read access
        const { data: cardData } = await supabase.from("business_profiles").select("business_slug").eq("id", businessProfileId).single();
        if (cardData?.business_slug) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/${cardData.business_slug}`);
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/customer"); // Revalidate customer dashboard where reviews might be shown
        // Revalidate the activities page for the business
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/dashboard/business/activities`);
        return {
            success: true
        };
    } catch (error) {
        console.error("Unexpected error in submitReview:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        return {
            success: false,
            error: errorMessage
        };
    }
}
async function deleteReview(businessProfileId) {
    // const cookieStore = cookies();
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])(); // Await the async function
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    try {
        // Use regular client with proper RLS
        const { error: deleteError } = await supabase.from("ratings_reviews").delete().match({
            user_id: user.id,
            business_profile_id: businessProfileId
        });
        if (deleteError) {
            console.error("Error deleting review:", deleteError);
            throw new Error(deleteError.message);
        }
        // Average rating is handled by the database trigger
        // Revalidate paths
        // Use regular client - business_profiles has public read access
        const { data: cardData } = await supabase.from("business_profiles").select("business_slug").eq("id", businessProfileId).single();
        if (cardData?.business_slug) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/${cardData.business_slug}`);
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/customer");
        // Revalidate the activities page for the business
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/dashboard/business/activities`);
        return {
            success: true
        };
    } catch (error) {
        console.error("Unexpected error in deleteReview:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        return {
            success: false,
            error: errorMessage
        };
    }
}
async function likeBusiness(businessProfileId) {
    // const cookieStore = cookies();
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])(); // Await the async function
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    // Prevent a business from liking their own business card
    if (user.id === businessProfileId) {
        return {
            success: false,
            error: "You cannot like your own business card."
        };
    }
    try {
        // 1. Insert like - Use regular client with proper RLS
        const { error: insertError } = await supabase.from("likes").insert({
            user_id: user.id,
            business_profile_id: businessProfileId
        });
        if (insertError) {
            // Handle potential unique constraint violation (already liked) gracefully
            if (insertError.code === "23505") {
                // unique_violation
                console.log(`User ${user.id} already liked business ${businessProfileId}.`);
                return {
                    success: true
                }; // Consider it success if already liked
            }
            console.error("Error inserting like:", insertError);
            throw new Error(insertError.message);
        }
        // Note: We don't need to manually update the like count
        // The database trigger 'update_total_likes' will handle this automatically
        // 3. Revalidate paths
        // Use regular client - business_profiles has public read access
        const { data: cardData } = await supabase.from("business_profiles").select("business_slug").eq("id", businessProfileId).single();
        if (cardData?.business_slug) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/${cardData.business_slug}`);
        }
        // Check if the current user is a business and revalidate business dashboard
        const { data: userBusinessProfile } = await supabase.from("business_profiles").select("id").eq("id", user.id).maybeSingle();
        if (userBusinessProfile) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business"); // Revalidate business dashboard
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business/likes"); // Revalidate business likes page
        }
        // Revalidate the activities page for the business
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/dashboard/business/activities`);
        return {
            success: true
        };
    } catch (error) {
        console.error("Unexpected error in likeBusiness:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        return {
            success: false,
            error: errorMessage
        };
    }
}
async function unlikeBusiness(businessProfileId) {
    // const cookieStore = cookies();
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])(); // Await the async function
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    // Prevent a business from unliking their own business card
    if (user.id === businessProfileId) {
        return {
            success: false,
            error: "You cannot unlike your own business card."
        };
    }
    try {
        // 1. Delete like - Use regular client with proper RLS
        const { error: deleteError } = await supabase.from("likes").delete().match({
            user_id: user.id,
            business_profile_id: businessProfileId
        });
        if (deleteError) {
            console.error("Error deleting like:", deleteError);
            throw new Error(deleteError.message);
        }
        // Note: We don't need to manually update the like count
        // The database trigger 'update_total_likes' will handle this automatically
        // 3. Revalidate paths
        // Use regular client - business_profiles has public read access
        const { data: cardData } = await supabase.from("business_profiles").select("business_slug").eq("id", businessProfileId).single();
        if (cardData?.business_slug) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/${cardData.business_slug}`);
        }
        // Check if the current user is a business and revalidate business dashboard
        const { data: userBusinessProfile } = await supabase.from("business_profiles").select("id").eq("id", user.id).maybeSingle();
        if (userBusinessProfile) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business"); // Revalidate business dashboard
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business/likes"); // Revalidate business likes page
        }
        // Revalidate the activities page for the business
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/dashboard/business/activities`);
        return {
            success: true
        };
    } catch (error) {
        console.error("Unexpected error in unlikeBusiness:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        return {
            success: false,
            error: errorMessage
        };
    }
}
async function getInteractionStatus(businessProfileId) {
    // const cookieStore = cookies();
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])(); // Await the async function
    let userId = null;
    // Try to get authenticated user, but proceed even if not logged in
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
        userId = user.id;
    }
    // Default status for anonymous users
    const defaultStatus = {
        isSubscribed: false,
        hasLiked: false,
        userRating: null,
        userReview: null
    };
    if (!userId) {
        return defaultStatus; // Return default if no user is logged in
    }
    try {
        // Use regular client - all these tables have public read access
        // Fetch all statuses in parallel
        const [subscriptionRes, likeRes, reviewRes] = await Promise.all([
            supabase.from("subscriptions").select("id", {
                count: "exact",
                head: true
            }) // Just check existence
            .match({
                user_id: userId,
                business_profile_id: businessProfileId
            }),
            supabase.from("likes").select("id", {
                count: "exact",
                head: true
            }) // Just check existence
            .match({
                user_id: userId,
                business_profile_id: businessProfileId
            }),
            supabase.from("ratings_reviews").select("rating, review_text").match({
                user_id: userId,
                business_profile_id: businessProfileId
            }).maybeSingle()
        ]);
        // Check for errors in parallel fetches
        if (subscriptionRes.error) throw new Error(`Subscription fetch error: ${subscriptionRes.error.message}`);
        if (likeRes.error) throw new Error(`Like fetch error: ${likeRes.error.message}`);
        if (reviewRes.error) throw new Error(`Review fetch error: ${reviewRes.error.message}`);
        const reviewData = reviewRes.data;
        return {
            isSubscribed: (subscriptionRes.count ?? 0) > 0,
            hasLiked: (likeRes.count ?? 0) > 0,
            userRating: reviewData?.rating ?? null,
            userReview: reviewData?.review_text ?? null
        };
    } catch (error) {
        console.error("Error fetching interaction status:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        // Return default status but include the error message
        return {
            ...defaultStatus,
            error: errorMessage
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    subscribeToBusiness,
    unsubscribeFromBusiness,
    submitReview,
    deleteReview,
    likeBusiness,
    unlikeBusiness,
    getInteractionStatus
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(subscribeToBusiness, "40f0adb120de9f3bad924fc8986f9159e19e49c98a", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(unsubscribeFromBusiness, "404e7fd84e2385eed21c771a2d81de71bc03c257c1", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(submitReview, "7004905b104910f7a038a07205afd74fefbf03df0f", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(deleteReview, "40582cec41753c809debb89593f731de59f975f5fa", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(likeBusiness, "40796a449be22e4515d3e23b81252e0a149dac475e", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(unlikeBusiness, "4007ef8feffe7c70e5e3985caa4dcb6cf89c22bff9", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getInteractionStatus, "40ffc7a7de1c66ef068d94ef44ea0c4dd1fe826a68", null);
}}),
"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"4087fea01b98636856a1c9c1c75e269d43242ac8a2":"recordCardVisit","7e7f42308ee715af64062c4a8c93140c99f6b10bcf":"fetchMoreProducts"},"",""] */ __turbopack_context__.s({
    "fetchMoreProducts": (()=>fetchMoreProducts),
    "recordCardVisit": (()=>recordCardVisit)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/services/businessService.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/cache.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
async function fetchMoreProducts(businessId, page = 1, sortBy = "created_desc", pageSize = 20, searchTerm, productType) {
    if (!businessId) {
        return {
            error: "Business ID is required."
        };
    }
    if (page < 1) {
        return {
            error: "Page number must be 1 or greater."
        };
    }
    if (pageSize < 1) {
        return {
            error: "Page size must be 1 or greater."
        };
    }
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data, error, totalCount } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getProductsWithFiltersAndPagination"])(supabase, businessId, page, sortBy, pageSize, searchTerm, productType);
    if (error) {
        return {
            error: "Failed to fetch products."
        };
    }
    return {
        data: data ?? [],
        totalCount: totalCount || 0
    };
}
async function recordCardVisit(params) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unstable_noStore"])();
    const { businessProfileId, visitorIdentifier } = params;
    if (!businessProfileId || !visitorIdentifier) {
        return {
            success: false,
            error: "Missing required parameters for visit tracking."
        };
    }
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        const { status, error: profileError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessProfileStatus"])(supabase, businessProfileId);
        if (profileError) {
            return {
                success: false,
                error: "Failed to verify card status."
            };
        }
        if (status !== "online") {
            return {
                success: true
            };
        }
        const { success: insertSuccess, error: insertError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["insertCardVisit"])(supabase, {
            business_profile_id: businessProfileId,
            visitor_identifier: visitorIdentifier,
            id: crypto.randomUUID(),
            visit_date: new Date().toISOString().split('T')[0],
            visited_at: new Date().toISOString()
        });
        if (!insertSuccess) {
            if (insertError === "23505") {
                return {
                    success: true
                };
            }
            return {
                success: false,
                error: "Failed to record visit."
            };
        }
        return {
            success: true
        };
    } catch (_err) {
        return {
            success: false,
            error: "An unexpected error occurred during visit recording."
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    fetchMoreProducts,
    recordCardVisit
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchMoreProducts, "7e7f42308ee715af64062c4a8c93140c99f6b10bcf", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(recordCardVisit, "4087fea01b98636856a1c9c1c75e269d43242ac8a2", null);
}}),
"[project]/lib/supabase/services/customerService.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "checkIfCustomerProfileExists": (()=>checkIfCustomerProfileExists),
    "createUserProfile": (()=>createUserProfile),
    "getCustomerProfileLocation": (()=>getCustomerProfileLocation),
    "getCustomerProfilesByIds": (()=>getCustomerProfilesByIds),
    "getPublicCustomerProfileById": (()=>getPublicCustomerProfileById),
    "getPublicCustomerProfilesByIds": (()=>getPublicCustomerProfilesByIds),
    "getUserProfile": (()=>getUserProfile),
    "updateUserProfile": (()=>updateUserProfile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/constants.ts [app-rsc] (ecmascript)");
;
async function getUserProfile(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES).select("*").eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error(`Error fetching user profile: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching user profile: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function createUserProfile(supabase, profile) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES).insert([
            profile
        ]).select().single();
        if (error) {
            console.error(`Error creating user profile: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data: Array.isArray(data) ? data[0] || null : data || null,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error creating user profile: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function checkIfCustomerProfileExists(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).maybeSingle();
        if (error) {
            console.error(`Error checking existing profile: ${error.message}`);
            return {
                exists: false,
                error: "Database error checking profile."
            };
        }
        return {
            exists: !!data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error checking profile: ${err}`);
        return {
            exists: false,
            error: "An unexpected error occurred."
        };
    }
}
async function updateUserProfile(supabase, userId, updates) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES).update(updates).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).select().single();
        if (error) {
            console.error(`Error updating user profile: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error updating user profile: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getCustomerProfileLocation(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY_SLUG}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY_SLUG}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE_SLUG}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error(`Error fetching customer profile location: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching customer profile location: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getCustomerProfilesByIds(supabase, userIds) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].EMAIL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].AVATAR_URL}`).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userIds);
        if (error) {
            console.error(`Error fetching customer profiles by IDs: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching customer profiles by IDs: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getPublicCustomerProfileById(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES_PUBLIC).select('*').eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).maybeSingle();
        if (error) {
            console.error(`Error fetching public customer profile: ${error.message}`);
            return {
                data: null,
                error: `Failed to fetch customer profile: ${error.message}`
            };
        }
        return {
            data,
            error: null
        };
    } catch (e) {
        console.error(`Exception in getPublicCustomerProfileById: ${e}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getPublicCustomerProfilesByIds(supabase, userIds) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].CUSTOMER_PROFILES_PUBLIC).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].AVATAR_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT}`).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userIds);
        if (error) {
            console.error(`Error fetching public customer profiles by IDs: ${error.message}`);
            return {
                data: null,
                error: `Failed to fetch customer profiles: ${error.message}`
            };
        }
        return {
            data,
            error: null
        };
    } catch (e) {
        console.error(`Exception in getPublicCustomerProfilesByIds: ${e}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
}}),
"[project]/lib/actions/secureCustomerProfiles.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"00b7242316cfe5621192e9a7fb199e41a1639be41e":"getAllSecureCustomerProfiles","4003cb6ac88ed8836fcdecc65cb471303e5fbf435a":"getSecureCustomerProfilesByIds","402503c38e9357a5bba18b5e5fb4fac5776decac90":"getUserProfilesForReviews","40851db4fcb02640ae0894959347b8d8773b99bdd3":"getSecureCustomerProfileById","40aa85f924b15066fa73f173a3866724f827412673":"checkUserCustomerProfileAccess","40cdeb48f922fb919d2af2c524b087c07fab79804c":"getUserProfileForReview"},"",""] */ __turbopack_context__.s({
    "checkUserCustomerProfileAccess": (()=>checkUserCustomerProfileAccess),
    "getAllSecureCustomerProfiles": (()=>getAllSecureCustomerProfiles),
    "getSecureCustomerProfileById": (()=>getSecureCustomerProfileById),
    "getSecureCustomerProfilesByIds": (()=>getSecureCustomerProfilesByIds),
    "getUserProfileForReview": (()=>getUserProfileForReview),
    "getUserProfilesForReviews": (()=>getUserProfilesForReviews)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/services/businessService.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$customerService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/services/customerService.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
async function getSecureCustomerProfileById(userId) {
    if (!userId) {
        return {
            error: "User ID is required."
        };
    }
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data, error } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$customerService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getPublicCustomerProfileById"])(supabase, userId);
        if (error) {
            return {
                error
            };
        }
        if (!data) {
            return {
                error: "Profile not found."
            };
        }
        // Data from public view is already safe
        const safeData = {
            id: data.id,
            name: data.name,
            email: null,
            avatar_url: data.avatar_url,
            created_at: data.created_at,
            updated_at: data.updated_at
        };
        return {
            data: safeData
        };
    } catch (e) {
        console.error("Exception in getSecureCustomerProfileById:", e);
        return {
            error: "An unexpected error occurred."
        };
    }
}
async function getSecureCustomerProfilesByIds(userIds) {
    if (!userIds || userIds.length === 0) {
        return {
            data: []
        };
    }
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data, error } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$customerService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getPublicCustomerProfilesByIds"])(supabase, userIds);
        if (error) {
            return {
                error
            };
        }
        // Map to expected format (email not available in public view)
        const safeData = data?.map((profile)=>({
                ...profile,
                email: null // Not available in public view
            })) || [];
        return {
            data: safeData
        };
    } catch (e) {
        console.error("Exception in getSecureCustomerProfilesByIds:", e);
        return {
            error: "An unexpected error occurred."
        };
    }
}
async function getAllSecureCustomerProfiles() {
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data, error } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$customerService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getPublicCustomerProfilesByIds"])(supabase, []);
        if (error) {
            return {
                error
            };
        }
        // Map to expected format (email not available in public view)
        const safeData = data?.map((profile)=>({
                ...profile,
                email: null // Not available in public view
            })) || [];
        return {
            data: safeData
        };
    } catch (e) {
        console.error("Exception in getAllSecureCustomerProfiles:", e);
        return {
            error: "An unexpected error occurred."
        };
    }
}
async function checkUserCustomerProfileAccess(userId) {
    if (!userId) {
        return {
            hasAccess: false,
            error: "User ID is required."
        };
    }
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        if (authError || !user) {
            return {
                hasAccess: false,
                error: "User not authenticated."
            };
        }
        if (user.id !== userId) {
            return {
                hasAccess: false,
                error: "Unauthorized access attempt."
            };
        }
        const { exists, error } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$customerService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["checkIfCustomerProfileExists"])(supabase, userId);
        if (error) {
            return {
                hasAccess: false,
                error
            };
        }
        return {
            hasAccess: exists
        };
    } catch (e) {
        console.error("Exception in checkUserCustomerProfileAccess:", e);
        return {
            hasAccess: false,
            error: "An unexpected error occurred."
        };
    }
}
async function getUserProfileForReview(userId) {
    if (!userId) {
        return {
            error: "User ID is required."
        };
    }
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: customerProfile, error: customerError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$customerService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getPublicCustomerProfileById"])(supabase, userId);
        if (customerError) {
            return {
                error: customerError
            };
        }
        if (customerProfile) {
            return {
                data: {
                    id: customerProfile.id,
                    name: customerProfile.name,
                    avatar_url: customerProfile.avatar_url,
                    is_business: false
                }
            };
        }
        const { data: businessProfile, error: businessError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessProfileById"])(supabase, userId);
        if (businessError) {
            return {
                error: businessError
            };
        }
        if (businessProfile) {
            return {
                data: {
                    id: businessProfile.id,
                    name: businessProfile.business_name,
                    avatar_url: businessProfile.logo_url,
                    is_business: true
                }
            };
        }
        return {
            error: "User profile not found in either customer or business profiles."
        };
    } catch (e) {
        console.error("Exception in getUserProfileForReview:", e);
        return {
            error: "An unexpected error occurred."
        };
    }
}
async function getUserProfilesForReviews(userIds) {
    if (!userIds || userIds.length === 0) {
        return {
            data: {}
        };
    }
    try {
        // Use the public view which only exposes safe data
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Fetch customer profiles from public view
        const { data: customerProfiles, error: customerError } = await supabase.from("customer_profiles_public").select("id, name, avatar_url").in("id", userIds);
        if (customerError) {
            console.error("Error fetching customer profiles:", customerError);
            return {
                error: `Failed to fetch customer profiles: ${customerError.message}`
            };
        }
        // Fetch business profiles
        const { data: businessProfiles, error: businessError } = await supabase.from("business_profiles").select("id, business_name, logo_url").in("id", userIds);
        if (businessError) {
            console.error("Error fetching business profiles:", businessError);
            return {
                error: `Failed to fetch business profiles: ${businessError.message}`
            };
        }
        // Combine the results into a map of user ID to profile data
        const profilesMap = {};
        // Add customer profiles to the map
        customerProfiles?.forEach((profile)=>{
            profilesMap[profile.id] = {
                id: profile.id,
                name: profile.name,
                avatar_url: profile.avatar_url,
                is_business: false
            };
        });
        // Add business profiles to the map
        businessProfiles?.forEach((profile)=>{
            // Only add if not already in the map (customer profiles take precedence)
            if (!profilesMap[profile.id]) {
                profilesMap[profile.id] = {
                    id: profile.id,
                    name: profile.business_name,
                    avatar_url: profile.logo_url,
                    is_business: true
                };
            }
        });
        return {
            data: profilesMap
        };
    } catch (e) {
        console.error("Exception in getUserProfilesForReviews:", e);
        return {
            error: "An unexpected error occurred."
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getSecureCustomerProfileById,
    getSecureCustomerProfilesByIds,
    getAllSecureCustomerProfiles,
    checkUserCustomerProfileAccess,
    getUserProfileForReview,
    getUserProfilesForReviews
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSecureCustomerProfileById, "40851db4fcb02640ae0894959347b8d8773b99bdd3", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSecureCustomerProfilesByIds, "4003cb6ac88ed8836fcdecc65cb471303e5fbf435a", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getAllSecureCustomerProfiles, "00b7242316cfe5621192e9a7fb199e41a1639be41e", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(checkUserCustomerProfileAccess, "40aa85f924b15066fa73f173a3866724f827412673", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getUserProfileForReview, "40cdeb48f922fb919d2af2c524b087c07fab79804c", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getUserProfilesForReviews, "402503c38e9357a5bba18b5e5fb4fac5776decac90", null);
}}),
"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7819fa25faa13e98e17d82eebb21cc9dc660cb6e9e":"fetchBusinessReviews"},"",""] */ __turbopack_context__.s({
    "fetchBusinessReviews": (()=>fetchBusinessReviews)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
// revalidatePath is imported but not used in this file
// import { revalidatePath } from 'next/cache';
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$secureCustomerProfiles$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/secureCustomerProfiles.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
async function fetchBusinessReviews(businessProfileId, page = 1, limit = 5, sortBy = "newest") {
    try {
        // Create admin client for secure operations
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // First, get the reviews with pagination and sorting
        let query = supabase.from("ratings_reviews").select("*", {
            count: "exact"
        }).eq("business_profile_id", businessProfileId)// Don't show reviews where the user is reviewing their own business
        .neq("user_id", businessProfileId);
        // Apply sorting
        switch(sortBy){
            case "oldest":
                query = query.order("created_at", {
                    ascending: true
                });
                break;
            case "highest_rating":
                query = query.order("rating", {
                    ascending: false
                });
                break;
            case "lowest_rating":
                query = query.order("rating", {
                    ascending: true
                });
                break;
            case "newest":
            default:
                query = query.order("created_at", {
                    ascending: false
                });
                break;
        }
        // Apply pagination
        const from = (page - 1) * limit;
        const to = from + limit - 1;
        query = query.range(from, to);
        // Execute the query
        const { data: reviewsData, error: reviewsError, count } = await query;
        if (reviewsError) {
            console.error("Error fetching reviews:", reviewsError);
            return {
                data: [],
                totalCount: 0,
                error: reviewsError.message
            };
        }
        // If no reviews, return empty array
        if (!reviewsData || reviewsData.length === 0) {
            return {
                data: [],
                totalCount: count || 0
            };
        }
        // Get all user IDs from the reviews
        const userIds = [
            ...new Set(reviewsData.map((review)=>review.user_id))
        ];
        // Use the secure method to fetch user profiles (both customer and business)
        const { data: profilesMap, error: profilesError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$secureCustomerProfiles$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getUserProfilesForReviews"])(userIds);
        if (profilesError) {
            console.error("Error fetching user profiles:", profilesError);
        // Continue without profiles
        }
        // Get business user IDs from the profiles
        const businessUserIds = userIds.filter((id)=>profilesMap?.[id]?.is_business);
        // Create a map of business IDs to their slugs
        let businessSlugMap = {};
        // Fetch business slugs for all business reviewers at once
        if (businessUserIds.length > 0) {
            const { data: businessSlugs } = await supabase.from("business_profiles").select("id, business_slug").in("id", businessUserIds);
            // Create a map of business IDs to their slugs
            if (businessSlugs) {
                businessSlugMap = businessSlugs.reduce((acc, business)=>{
                    acc[business.id] = business.business_slug;
                    return acc;
                }, {});
            }
        }
        // Process the reviews data with profile information
        const processedData = reviewsData.map((review)=>{
            const profile = profilesMap?.[review.user_id];
            const userProfile = profile ? {
                ...profile,
                business_slug: profile.is_business ? businessSlugMap[review.user_id] || null : null
            } : undefined;
            return {
                ...review,
                user_profile: userProfile
            };
        });
        return {
            data: processedData,
            totalCount: count || 0
        };
    } catch (error) {
        console.error("Unexpected error in fetchBusinessReviews:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        return {
            data: [],
            totalCount: 0,
            error: errorMessage
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    fetchBusinessReviews
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchBusinessReviews, "7819fa25faa13e98e17d82eebb21cc9dc660cb6e9e", null);
}}),
"[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
}}),
"[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => "[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/utils/supabase/server.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE4 => "[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "00c2d9624329cded09d08193b88cb56d91c74b75b9": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"]),
    "4004efaa33bf176b062db6f6fae76b568de8e96eb5": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessGalleryImages"]),
    "4007ef8feffe7c70e5e3985caa4dcb6cf89c22bff9": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unlikeBusiness"]),
    "402f24810a766d0d430e5836c3512992e6677ea96f": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessGalleryImagesForTab"]),
    "404e7fd84e2385eed21c771a2d81de71bc03c257c1": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsubscribeFromBusiness"]),
    "40582cec41753c809debb89593f731de59f975f5fa": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["deleteReview"]),
    "40796a449be22e4515d3e23b81252e0a149dac475e": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["likeBusiness"]),
    "4087fea01b98636856a1c9c1c75e269d43242ac8a2": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["recordCardVisit"]),
    "40d08ae16ea4ae523a49582b7f4802b4f201ad0ffd": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessGalleryImagesBySlug"]),
    "40f0adb120de9f3bad924fc8986f9159e19e49c98a": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["subscribeToBusiness"]),
    "40ffc7a7de1c66ef068d94ef44ea0c4dd1fe826a68": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getInteractionStatus"]),
    "7004905b104910f7a038a07205afd74fefbf03df0f": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["submitReview"]),
    "705db0368d449a63be0cdf8c9b55978983b5d2053c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessGalleryImagesPaginated"]),
    "7819fa25faa13e98e17d82eebb21cc9dc660cb6e9e": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchBusinessReviews"]),
    "7e7f42308ee715af64062c4a8c93140c99f6b10bcf": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchMoreProducts"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => "[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/utils/supabase/server.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE4 => "[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "00c2d9624329cded09d08193b88cb56d91c74b75b9": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["00c2d9624329cded09d08193b88cb56d91c74b75b9"]),
    "4004efaa33bf176b062db6f6fae76b568de8e96eb5": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["4004efaa33bf176b062db6f6fae76b568de8e96eb5"]),
    "4007ef8feffe7c70e5e3985caa4dcb6cf89c22bff9": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["4007ef8feffe7c70e5e3985caa4dcb6cf89c22bff9"]),
    "402f24810a766d0d430e5836c3512992e6677ea96f": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["402f24810a766d0d430e5836c3512992e6677ea96f"]),
    "404e7fd84e2385eed21c771a2d81de71bc03c257c1": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["404e7fd84e2385eed21c771a2d81de71bc03c257c1"]),
    "40582cec41753c809debb89593f731de59f975f5fa": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40582cec41753c809debb89593f731de59f975f5fa"]),
    "40796a449be22e4515d3e23b81252e0a149dac475e": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40796a449be22e4515d3e23b81252e0a149dac475e"]),
    "4087fea01b98636856a1c9c1c75e269d43242ac8a2": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["4087fea01b98636856a1c9c1c75e269d43242ac8a2"]),
    "40d08ae16ea4ae523a49582b7f4802b4f201ad0ffd": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40d08ae16ea4ae523a49582b7f4802b4f201ad0ffd"]),
    "40f0adb120de9f3bad924fc8986f9159e19e49c98a": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40f0adb120de9f3bad924fc8986f9159e19e49c98a"]),
    "40ffc7a7de1c66ef068d94ef44ea0c4dd1fe826a68": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40ffc7a7de1c66ef068d94ef44ea0c4dd1fe826a68"]),
    "7004905b104910f7a038a07205afd74fefbf03df0f": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7004905b104910f7a038a07205afd74fefbf03df0f"]),
    "705db0368d449a63be0cdf8c9b55978983b5d2053c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["705db0368d449a63be0cdf8c9b55978983b5d2053c"]),
    "7819fa25faa13e98e17d82eebb21cc9dc660cb6e9e": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7819fa25faa13e98e17d82eebb21cc9dc660cb6e9e"]),
    "7e7f42308ee715af64062c4a8c93140c99f6b10bcf": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7e7f42308ee715af64062c4a8c93140c99f6b10bcf"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => "[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/utils/supabase/server.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE4 => "[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <module evaluation>');
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => "[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/utils/supabase/server.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE4 => "[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <exports>');
}}),
"[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/[cardSlug]/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/[cardSlug]/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/[cardSlug]/loading.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/[cardSlug]/loading.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/lib/supabase/services/sharedService.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "fetchPincodeAddress": (()=>fetchPincodeAddress),
    "fetchUserSubscriptions": (()=>fetchUserSubscriptions),
    "getAuthenticatedUser": (()=>getAuthenticatedUser),
    "getPublicUrlFromStorage": (()=>getPublicUrlFromStorage),
    "getStateNameByCity": (()=>getStateNameByCity),
    "getUnifiedPosts": (()=>getUnifiedPosts),
    "listStorageFiles": (()=>listStorageFiles),
    "removeFileFromStorage": (()=>removeFileFromStorage),
    "signInWithOAuth": (()=>signInWithOAuth),
    "signInWithOtp": (()=>signInWithOtp),
    "signInWithPassword": (()=>signInWithPassword),
    "signOutUser": (()=>signOutUser),
    "subscribeToTableChanges": (()=>subscribeToTableChanges),
    "updateAuthUserPhone": (()=>updateAuthUserPhone),
    "uploadFileToStorage": (()=>uploadFileToStorage),
    "verifyOtp": (()=>verifyOtp)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/constants.ts [app-rsc] (ecmascript)");
;
async function getAuthenticatedUser(supabase) {
    try {
        const { data: { user }, error } = await supabase.auth.getUser();
        if (error) {
            console.error(`Error fetching authenticated user: ${error.message}`);
            return {
                user: null,
                error: "User not found or authentication error."
            };
        }
        return {
            user,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching authenticated user: ${err}`);
        return {
            user: null,
            error: "An unexpected error occurred."
        };
    }
}
async function uploadFileToStorage(supabase, bucketName, path, fileBuffer, contentType, upsert = true) {
    try {
        const { error } = await supabase.storage.from(bucketName).upload(path, fileBuffer, {
            contentType,
            upsert
        });
        if (error) {
            console.error(`Error uploading file to storage: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error uploading file to storage: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function getPublicUrlFromStorage(supabase, bucketName, path) {
    try {
        const { data } = supabase.storage.from(bucketName).getPublicUrl(path);
        if (!data?.publicUrl) {
            return {
                publicUrl: null,
                error: "Could not retrieve public URL."
            };
        }
        return {
            publicUrl: data.publicUrl,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error getting public URL: ${err}`);
        return {
            publicUrl: null,
            error: "An unexpected error occurred."
        };
    }
}
async function removeFileFromStorage(supabase, bucketName, paths) {
    try {
        const { error } = await supabase.storage.from(bucketName).remove(paths);
        if (error) {
            console.error(`Error removing file from storage: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error removing file from storage: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function updateAuthUserPhone(supabase, phone) {
    try {
        const { error } = await supabase.auth.updateUser({
            phone: `+91${phone}`
        });
        if (error) {
            console.error(`Error updating auth user phone: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error updating auth user phone: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function listStorageFiles(supabase, bucketName, path, options) {
    try {
        const { data, error } = await supabase.storage.from(bucketName).list(path, options);
        if (error) {
            console.error(`Error listing storage files: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error listing storage files: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function signOutUser(supabase) {
    try {
        const { error } = await supabase.auth.signOut();
        if (error) {
            console.error(`Error signing out user: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error signing out user: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function signInWithOtp(supabase, email, shouldCreateUser = true) {
    try {
        const { error } = await supabase.auth.signInWithOtp({
            email: email,
            options: {
                shouldCreateUser: shouldCreateUser,
                data: {
                    auth_type: "email"
                }
            }
        });
        if (error) {
            console.error(`Error sending OTP: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error sending OTP: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function verifyOtp(supabase, email, token) {
    try {
        const { data, error } = await supabase.auth.verifyOtp({
            email: email,
            token: token,
            type: 'email'
        });
        if (error) {
            console.error(`Error verifying OTP: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error verifying OTP: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function signInWithPassword(supabase, phone, password) {
    try {
        const { data, error } = await supabase.auth.signInWithPassword({
            phone: phone,
            password: password
        });
        if (error) {
            console.error(`Error signing in with password: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error signing in with password: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function signInWithOAuth(supabase, provider, redirectTo, queryParams) {
    try {
        const { data, error } = await supabase.auth.signInWithOAuth({
            provider: provider,
            options: {
                redirectTo: redirectTo,
                skipBrowserRedirect: true,
                queryParams: queryParams
            }
        });
        if (error) {
            console.error(`Error initiating OAuth sign-in: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error initiating OAuth sign-in: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
function subscribeToTableChanges(supabase, tableName, filter, callback) {
    const channel = supabase.channel(`public:${tableName}`).on("postgres_changes", {
        event: "*",
        schema: "public",
        table: tableName,
        filter: filter
    }, callback).subscribe();
    return ()=>{
        supabase.removeChannel(channel);
    };
}
async function fetchUserSubscriptions(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].SUBSCRIPTIONS).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID, userId);
        if (error) {
            console.error(`Error fetching user subscriptions: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching user subscriptions: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getStateNameByCity(supabase, city) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PINCODES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE_NAME).ilike(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DIVISION_NAME, `%${city}%`).limit(1);
        if (error) {
            console.error(`Error fetching state name for city ${city}: ${error.message}`);
            return {
                stateName: null,
                error: error.message
            };
        }
        return {
            stateName: data && data.length > 0 ? data[0].StateName : null,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching state name for city ${city}: ${err}`);
        return {
            stateName: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getUnifiedPosts(supabase, from, to, conditions = []) {
    let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].UNIFIED_POSTS).select('*', {
        count: 'exact'
    });
    if (conditions.length > 0) {
        query = query.or(conditions.join(','));
    }
    const { data, error, count } = await query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
        ascending: false
    }).range(from, to);
    if (error) {
        console.error("Error fetching unified posts:", error);
        return {
            data: null,
            error: error.message,
            count: null
        };
    }
    return {
        data,
        error: null,
        count
    };
}
async function fetchPincodeAddress(supabase, pincode, locality_slug, city_slug, state_slug) {
    try {
        let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PINCODES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].OFFICE_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DIVISION_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE}`);
        if (pincode) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE, pincode);
        }
        if (city_slug) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY_SLUG, city_slug);
        }
        if (state_slug) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE_SLUG, state_slug);
        }
        if (locality_slug) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY_SLUG, locality_slug);
        }
        const { data, error } = await query.limit(1);
        if (error) {
            console.error(`Error fetching pincode address: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data: data[0] || null,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching pincode address: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
}}),
"[project]/app/[cardSlug]/PublicCardPageClient.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/[cardSlug]/PublicCardPageClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/[cardSlug]/PublicCardPageClient.tsx <module evaluation>", "default");
}}),
"[project]/app/[cardSlug]/PublicCardPageClient.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/[cardSlug]/PublicCardPageClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/[cardSlug]/PublicCardPageClient.tsx", "default");
}}),
"[project]/app/[cardSlug]/PublicCardPageClient.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$PublicCardPageClient$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/[cardSlug]/PublicCardPageClient.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$PublicCardPageClient$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/app/[cardSlug]/PublicCardPageClient.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$PublicCardPageClient$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx <module evaluation>", "default");
}}),
"[project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx", "default");
}}),
"[project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$components$2f$OfflineBusinessMessage$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$components$2f$OfflineBusinessMessage$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$components$2f$OfflineBusinessMessage$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/app/[cardSlug]/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>PublicCardPage),
    "generateMetadata": (()=>generateMetadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$sharedService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/services/sharedService.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/services/businessService.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/constants.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$PublicCardPageClient$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/[cardSlug]/PublicCardPageClient.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$components$2f$OfflineBusinessMessage$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
const INITIAL_PRODUCTS_PAGE_SIZE = 20;
// Helper function to determine user plan
const getUserPlan = (profile)=>{
    // Simply return the plan_id from the subscription data
    switch(profile.plan_id){
        case "free":
            return "free";
        case "growth":
            return "growth";
        case "pro":
            return "pro";
        case "enterprise":
            return "enterprise";
        case "basic":
            return "basic";
        default:
            return "free"; // Default to free if no plan_id specified
    }
};
// Helper function to determine if platform ads should be shown
const shouldShowPlatformAds = ()=>{
    // Show platform ads for all plans - Pro/Enterprise users can override with their own custom ads
    return true; // Always show platform ads as fallback
};
async function PublicCardPage({ params }) {
    const { cardSlug } = await params;
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    // Use the secure method to fetch the business profile
    const { data: businessProfile, error: profileError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSecureBusinessProfileBySlug"])(supabase, cardSlug);
    if (profileError || !businessProfile) {
        console.error(`Error fetching profile for slug ${cardSlug}:`, profileError);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["notFound"])();
    }
    // Check if the profile is online
    if (businessProfile.status !== "online") {
        console.log(`Business profile ${cardSlug} is not online (status: ${businessProfile.status}).`);
        // Show offline message instead of 404
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$components$2f$OfflineBusinessMessage$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
            fileName: "[project]/app/[cardSlug]/page.tsx",
            lineNumber: 79,
            columnNumber: 12
        }, this);
    }
    // Check if required fields are missing but status is still online
    const requiredFields = [
        __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].MEMBER_NAME,
        __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TITLE,
        __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME,
        __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PHONE,
        __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ADDRESS_LINE,
        __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE,
        __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY,
        __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE,
        __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY,
        __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CONTACT_EMAIL
    ];
    const missingRequiredFields = requiredFields.filter((field)=>!businessProfile[field] || String(businessProfile[field]).trim() === "");
    if (missingRequiredFields.length > 0 && businessProfile.status === "online") {
        console.log(`Business profile ${cardSlug} is missing required fields but status is online, updating to offline. Missing fields: ${missingRequiredFields.join(", ")}`);
        const { error: updateError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["updateBusinessProfile"])(supabase, businessProfile.id, {
            status: "offline"
        });
        if (updateError) {
            console.error("Error forcing card offline:", updateError);
        }
        // Show offline message instead of 404
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$components$2f$OfflineBusinessMessage$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
            fileName: "[project]/app/[cardSlug]/page.tsx",
            lineNumber: 119,
            columnNumber: 12
        }, this);
    }
    // We no longer check subscription status, only if the business is online
    // The status field is the only thing that matters now
    // Determine user plan
    const userPlan = getUserPlan(businessProfile);
    let topAdData = null;
    if (shouldShowPlatformAds()) {
        try {
            const pincode = businessProfile.pincode || "999999";
            const { adData, error: adError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAdDataForPincode"])(supabase, pincode);
            if (adData) {
                topAdData = {
                    type: "custom",
                    imageUrl: adData.ad_image_url,
                    linkUrl: adData.ad_link_url
                };
            } else {
                if (adError) console.error(`Error fetching ad for pincode ${pincode}:`, adError);
                topAdData = null;
            }
        } catch (adFetchError) {
            console.error(`Error fetching custom ad:`, adFetchError);
            topAdData = null;
        }
    }
    const defaultSortPreference = "created_desc";
    const { products: initialProducts, count: totalProductCount, error: productsError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getProductsForBusiness"])(supabase, businessProfile.id, INITIAL_PRODUCTS_PAGE_SIZE);
    if (productsError) {
        console.error(`Error fetching initial products for business ${businessProfile.id}:`, productsError);
    }
    const { user } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$sharedService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAuthenticatedUser"])(supabase);
    const isAuthenticated = !!user;
    const currentUserId = user?.id || null;
    const { count: totalReviews, error: reviewsCountError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getReviewsCountForBusiness"])(supabase, businessProfile.id);
    if (reviewsCountError) {
        console.error(`Error fetching reviews count for business ${businessProfile.id}:`, reviewsCountError);
    }
    const { images: galleryImages, totalCount: galleryTotalCount, error: galleryError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessGalleryImagesForTab"])(cardSlug);
    if (galleryError) {
        console.error(`Error fetching gallery images for business ${businessProfile.id}:`, galleryError);
    }
    const businessProfileWithReviews = {
        ...businessProfile,
        total_reviews: totalReviews || 0,
        // Ensure all required fields are properly typed and present
        phone: businessProfile.phone || "",
        city: businessProfile.city || "",
        state: businessProfile.state || "",
        pincode: businessProfile.pincode || "",
        locality: businessProfile.locality || "",
        address_line: businessProfile.address_line || "",
        business_name: businessProfile.business_name || "",
        contact_email: businessProfile.contact_email || "",
        member_name: businessProfile.member_name || "",
        status: businessProfile.status,
        title: businessProfile.title || "",
        business_category: businessProfile.business_category || "",
        custom_branding: businessProfile.custom_branding,
        custom_ads: businessProfile.custom_ads,
        whatsapp_number: businessProfile.whatsapp_number || undefined,
        instagram_url: businessProfile.instagram_url || undefined,
        facebook_url: businessProfile.facebook_url || undefined,
        about_bio: businessProfile.about_bio || undefined,
        business_slug: businessProfile.business_slug || undefined,
        theme_color: businessProfile.theme_color || undefined,
        delivery_info: businessProfile.delivery_info || undefined,
        total_likes: businessProfile.total_likes || 0,
        total_subscriptions: businessProfile.total_subscriptions || 0,
        average_rating: businessProfile.average_rating || undefined,
        business_hours: businessProfile.business_hours || null,
        trial_end_date: businessProfile.trial_end_date || null,
        created_at: businessProfile.created_at || undefined,
        updated_at: businessProfile.updated_at || undefined,
        established_year: businessProfile.established_year || null
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen flex flex-col",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$PublicCardPageClient$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
            businessProfile: businessProfileWithReviews,
            initialProducts: initialProducts ?? [],
            totalProductCount: totalProductCount ?? 0,
            defaultSortPreference: defaultSortPreference,
            isAuthenticated: isAuthenticated,
            currentUserId: currentUserId,
            userPlan: userPlan,
            topAdData: topAdData,
            galleryImages: galleryImages ?? [],
            galleryTotalCount: galleryTotalCount ?? 0
        }, void 0, false, {
            fileName: "[project]/app/[cardSlug]/page.tsx",
            lineNumber: 228,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/[cardSlug]/page.tsx",
        lineNumber: 227,
        columnNumber: 5
    }, this);
}
async function generateMetadata({ params }) {
    const { cardSlug } = await params;
    const siteUrl = ("TURBOPACK compile-time value", "http://localhost:3000") || "https://dukancard.in";
    const pageUrl = `${siteUrl}/${cardSlug}`;
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: businessProfile, error: profileError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSecureBusinessProfileBySlug"])(supabase, cardSlug);
    if (profileError || !businessProfile) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["notFound"])();
    }
    const businessName = businessProfile.business_name || "Business";
    let baseTitle = businessName;
    let fullAddress = "";
    if (businessProfile.status === "online") {
        const addressComponents = [
            businessProfile.address_line,
            businessProfile.city,
            businessProfile.state,
            businessProfile.pincode
        ].filter(Boolean);
        fullAddress = addressComponents.join(", ");
        if (fullAddress) {
            baseTitle = `${businessName} - ${fullAddress}`;
        }
    }
    let description = "";
    if (businessProfile.status === "online") {
        description = `Visit ${businessName}'s digital business card on Dukancard. ${businessProfile.about_bio ? businessProfile.about_bio + " " : ""}Find products, services, contact info, and location${fullAddress ? ` at ${fullAddress}` : ""}.`.trim();
    } else {
        description = `${businessName}'s digital business card on Dukancard is currently offline. Check back later or discover other businesses nearby.`;
    }
    const ogImage = businessProfile.logo_url || `${siteUrl}/opengraph-image.png`;
    const keywords = [
        businessName,
        businessProfile?.business_category,
        businessProfile?.city,
        businessProfile?.state,
        "digital business card",
        "online storefront",
        "shop near me",
        "Dukancard"
    ].filter(Boolean);
    const schema = {
        "@context": "https://schema.org",
        "@type": "LocalBusiness",
        name: businessName,
        description: businessProfile.about_bio || description,
        url: pageUrl,
        image: businessProfile.logo_url,
        telephone: businessProfile?.phone,
        address: {
            "@type": "PostalAddress",
            streetAddress: businessProfile.address_line,
            addressLocality: businessProfile.city,
            addressRegion: businessProfile.state,
            postalCode: businessProfile.pincode,
            addressCountry: "IN"
        }
    };
    return {
        title: baseTitle,
        description,
        keywords: keywords.filter((k)=>k !== null),
        alternates: {
            canonical: `/${cardSlug}`
        },
        openGraph: {
            title: baseTitle,
            description,
            url: pageUrl,
            siteName: "Dukancard",
            type: "profile",
            locale: "en_IN",
            images: [
                {
                    url: ogImage,
                    alt: `${businessName} - Digital Business Card`
                }
            ]
        },
        twitter: {
            card: "summary_large_image",
            title: baseTitle,
            description,
            images: [
                ogImage
            ]
        },
        other: {
            "application-ld+json": JSON.stringify(schema)
        }
    };
}
}}),
"[project]/app/[cardSlug]/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/[cardSlug]/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__9d3aaf06._.js.map