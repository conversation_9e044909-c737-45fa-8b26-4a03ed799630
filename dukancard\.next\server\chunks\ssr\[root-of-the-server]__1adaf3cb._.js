module.exports = {

"[project]/lib/supabase/constants.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// lib/supabase/constants.ts
__turbopack_context__.s({
    "BUCKETS": (()=>BUCKETS),
    "COLUMNS": (()=>COLUMNS),
    "RPC_FUNCTIONS": (()=>RPC_FUNCTIONS),
    "RPC_PARAMS": (()=>RPC_PARAMS),
    "TABLES": (()=>TABLES)
});
const TABLES = {
    BLOGS: "blogs",
    BUSINESS_ACTIVITIES: "business_activities",
    BUSINESS_PROFILES: "business_profiles",
    CARD_VISITS: "card_visits",
    CUSTOM_ADS: "custom_ads",
    CUSTOM_AD_TARGETS: "custom_ad_targets",
    CUSTOMER_POSTS: "customer_posts",
    CUSTOMER_PROFILES: "customer_profiles",
    CUSTOMER_PROFILES_PUBLIC: "customer_profiles_public",
    LIKES: "likes",
    PAYMENT_SUBSCRIPTIONS: "payment_subscriptions",
    PINCODES: "pincodes",
    PRODUCTS_SERVICES: "products_services",
    PRODUCT_VARIANTS: "product_variants",
    PUBLIC_SUBSCRIPTION_STATUS: "public_subscription_status",
    STORAGE_CLEANUP_CONFIG: "storage_cleanup_config",
    STORAGE_CLEANUP_PROGRESS: "storage_cleanup_progress",
    SUBSCRIPTIONS: "subscriptions",
    SYSTEM_ALERTS: "system_alerts",
    UNIFIED_POSTS: "unified_posts",
    RATINGS_REVIEWS: "ratings_reviews"
};
const BUCKETS = {
    BUSINESS: "business",
    CUSTOMERS: "customers"
};
const COLUMNS = {
    ID: "id",
    CREATED_AT: "created_at",
    UPDATED_AT: "updated_at",
    NAME: "name",
    EMAIL: "email",
    PHONE: "phone",
    CITY: "city",
    STATE: "state",
    PINCODE: "pincode",
    PLAN_ID: "plan_id",
    LOCALITY: "locality",
    CITY_SLUG: "city_slug",
    STATE_SLUG: "state_slug",
    LOCALITY_SLUG: "locality_slug",
    OFFICE_NAME: "office_name",
    AVATAR_URL: "avatar_url",
    LOGO_URL: "logo_url",
    IMAGE_URL: "image_url",
    IMAGES: "images",
    SLUG: "slug",
    STATUS: "status",
    CONTENT: "content",
    GALLERY: "gallery",
    DESCRIPTION: "description",
    TITLE: "title",
    USER_ID: "user_id",
    BUSINESS_ID: "business_id",
    BUSINESS_NAME: "business_name",
    BUSINESS_SLUG: "business_slug",
    PRODUCT_ID: "product_id",
    LATITUDE: "latitude",
    LONGITUDE: "longitude",
    PRODUCT_TYPE: "product_type",
    BASE_PRICE: "base_price",
    DISCOUNTED_PRICE: "discounted_price",
    IS_AVAILABLE: "is_available",
    CUSTOM_AD_TARGETS: "custom_ad_targets",
    AD_IMAGE_URL: "ad_image_url",
    AD_LINK_URL: "ad_link_url",
    IS_ACTIVE: "is_active",
    TARGETING_LOCATIONS: "targeting_locations",
    RATINGS_REVIEWS: "ratings_reviews",
    BUSINESS_PROFILE_ID: "business_profile_id",
    RAZORPAY_SUBSCRIPTION_ID: "razorpay_subscription_id",
    SUBSCRIPTION_STATUS: "subscription_status",
    TOTAL_LIKES: "total_likes",
    TOTAL_SUBSCRIPTIONS: "total_subscriptions",
    AVERAGE_RATING: "average_rating",
    TOTAL_VISITS: "total_visits",
    TODAY_VISITS: "today_visits",
    YESTERDAY_VISITS: "yesterday_visits",
    VISITS_7_DAYS: "visits_7_days",
    VISITS_30_DAYS: "visits_30_days",
    CUSTOM_ADS: "custom_ads",
    CUSTOM_BRANDING: "custom_branding",
    CONTACT_EMAIL: "contact_email",
    HAS_ACTIVE_SUBSCRIPTION: "has_active_subscription",
    TRIAL_END_DATE: "trial_end_date",
    MEMBER_NAME: "member_name",
    ADDRESS_LINE: "address_line",
    INSTAGRAM_URL: "instagram_url",
    FACEBOOK_URL: "facebook_url",
    WHATSAPP_NUMBER: "whatsapp_number",
    ABOUT_BIO: "about_bio",
    THEME_COLOR: "theme_color",
    DELIVERY_INFO: "delivery_info",
    BUSINESS_HOURS: "business_hours",
    BUSINESS_CATEGORY: "business_category",
    ESTABLISHED_YEAR: "established_year",
    VARIANT_VALUES: "variant_values",
    VARIANT_NAME: "variant_name",
    FEATURED_IMAGE_INDEX: "featured_image_index",
    STATE_NAME: "StateName",
    DIVISION_NAME: "DivisionName"
};
const RPC_FUNCTIONS = {
    GET_DAILY_UNIQUE_VISIT_TREND: "get_daily_unique_visit_trend",
    GET_HOURLY_UNIQUE_VISIT_TREND: "get_hourly_unique_visit_trend",
    GET_MONTHLY_UNIQUE_VISITS: "get_monthly_unique_visits",
    GET_MONTHLY_UNIQUE_VISIT_TREND: "get_monthly_unique_visit_trend",
    GET_AVAILABLE_YEARS_FOR_MONTHLY_METRICS: "get_available_years_for_monthly_metrics",
    GET_TOTAL_UNIQUE_VISITS: "get_total_unique_visits",
    GET_AD_FOR_PINCODE: "get_ad_for_pincode",
    GET_PRODUCT_WITH_VARIANTS: "get_product_with_variants",
    GET_AVAILABLE_PRODUCT_VARIANTS: "get_available_product_variants",
    GET_BUSINESS_VARIANT_STATS: "get_business_variant_stats",
    IS_VARIANT_COMBINATION_UNIQUE: "is_variant_combination_unique"
};
const RPC_PARAMS = {
    BUSINESS_ID: "business_id",
    START_DATE: "start_date",
    END_DATE: "end_date",
    TARGET_DATE: "target_date",
    TARGET_YEAR: "target_year",
    TARGET_MONTH: "target_month",
    START_YEAR: "start_year",
    START_MONTH: "start_month",
    END_YEAR: "end_year",
    END_MONTH: "end_month",
    TARGET_PINCODE: "target_pincode"
};
}}),
"[project]/lib/supabase/services/businessService.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "bulkUpdateProductVariants": (()=>bulkUpdateProductVariants),
    "bulkUpdateProductVariantsData": (()=>bulkUpdateProductVariantsData),
    "checkBusinessSlugUniqueness": (()=>checkBusinessSlugUniqueness),
    "checkIfBusinessProfileExists": (()=>checkIfBusinessProfileExists),
    "createBusinessProfileAtomic": (()=>createBusinessProfileAtomic),
    "deleteProductByIdAndBusinessId": (()=>deleteProductByIdAndBusinessId),
    "deleteProductVariant": (()=>deleteProductVariant),
    "deleteProductVariantById": (()=>deleteProductVariantById),
    "deleteProductVariantsByProductId": (()=>deleteProductVariantsByProductId),
    "getAdDataForPincode": (()=>getAdDataForPincode),
    "getAvailableYearsForMonthlyMetrics": (()=>getAvailableYearsForMonthlyMetrics),
    "getBusinessIdsByCity": (()=>getBusinessIdsByCity),
    "getBusinessLikes": (()=>getBusinessLikes),
    "getBusinessLikesCount": (()=>getBusinessLikesCount),
    "getBusinessLogoUrl": (()=>getBusinessLogoUrl),
    "getBusinessProfileAnalyticsData": (()=>getBusinessProfileAnalyticsData),
    "getBusinessProfileById": (()=>getBusinessProfileById),
    "getBusinessProfileCustomAds": (()=>getBusinessProfileCustomAds),
    "getBusinessProfileCustomBranding": (()=>getBusinessProfileCustomBranding),
    "getBusinessProfileForOnboarding": (()=>getBusinessProfileForOnboarding),
    "getBusinessProfileGallery": (()=>getBusinessProfileGallery),
    "getBusinessProfileIdAndStatusBySlug": (()=>getBusinessProfileIdAndStatusBySlug),
    "getBusinessProfileLocation": (()=>getBusinessProfileLocation),
    "getBusinessProfilePhone": (()=>getBusinessProfilePhone),
    "getBusinessProfileStatus": (()=>getBusinessProfileStatus),
    "getBusinessProfileSubscriptionInfo": (()=>getBusinessProfileSubscriptionInfo),
    "getBusinessProfileWithAllDetails": (()=>getBusinessProfileWithAllDetails),
    "getBusinessProfileWithInteractionMetrics": (()=>getBusinessProfileWithInteractionMetrics),
    "getBusinessProfilesByCity": (()=>getBusinessProfilesByCity),
    "getBusinessProfilesByIds": (()=>getBusinessProfilesByIds),
    "getDailyUniqueVisitTrend": (()=>getDailyUniqueVisitTrend),
    "getExistingProductVariants": (()=>getExistingProductVariants),
    "getFilteredProductVariants": (()=>getFilteredProductVariants),
    "getHourlyUniqueVisitTrend": (()=>getHourlyUniqueVisitTrend),
    "getLatestSubscription": (()=>getLatestSubscription),
    "getLatestSubscriptionStatus": (()=>getLatestSubscriptionStatus),
    "getMonthlyUniqueVisitTrend": (()=>getMonthlyUniqueVisitTrend),
    "getMonthlyUniqueVisits": (()=>getMonthlyUniqueVisits),
    "getMyLikes": (()=>getMyLikes),
    "getMyLikesCount": (()=>getMyLikesCount),
    "getPaymentSubscriptionByBusinessProfileId": (()=>getPaymentSubscriptionByBusinessProfileId),
    "getProductBusinessId": (()=>getProductBusinessId),
    "getProductById": (()=>getProductById),
    "getProductByIdAndBusinessId": (()=>getProductByIdAndBusinessId),
    "getProductCountByBusinessIds": (()=>getProductCountByBusinessIds),
    "getProductDetailsByIdAndBusinessId": (()=>getProductDetailsByIdAndBusinessId),
    "getProductsByBusinessIds": (()=>getProductsByBusinessIds),
    "getProductsForBusiness": (()=>getProductsForBusiness),
    "getProductsWithFiltersAndPagination": (()=>getProductsWithFiltersAndPagination),
    "getProductsWithVariantInfo": (()=>getProductsWithVariantInfo),
    "getPublicSubscriptionStatus": (()=>getPublicSubscriptionStatus),
    "getReviewsCountForBusiness": (()=>getReviewsCountForBusiness),
    "getRpcAvailableProductVariants": (()=>getRpcAvailableProductVariants),
    "getRpcBusinessVariantStats": (()=>getRpcBusinessVariantStats),
    "getRpcIsVariantCombinationUnique": (()=>getRpcIsVariantCombinationUnique),
    "getRpcProductWithVariants": (()=>getRpcProductWithVariants),
    "getSecureBusinessProfileBySlug": (()=>getSecureBusinessProfileBySlug),
    "getSecureBusinessProfileWithProductsBySlug": (()=>getSecureBusinessProfileWithProductsBySlug),
    "getTotalUniqueVisits": (()=>getTotalUniqueVisits),
    "getVariantDetailsWithProductBusinessId": (()=>getVariantDetailsWithProductBusinessId),
    "getVariantsByProductId": (()=>getVariantsByProductId),
    "getVariantsWithProductBusinessId": (()=>getVariantsWithProductBusinessId),
    "insertCardVisit": (()=>insertCardVisit),
    "insertMultipleProductVariants": (()=>insertMultipleProductVariants),
    "insertProduct": (()=>insertProduct),
    "insertProductVariant": (()=>insertProductVariant),
    "updateBusinessLogoUrl": (()=>updateBusinessLogoUrl),
    "updateBusinessProfile": (()=>updateBusinessProfile),
    "updateProduct": (()=>updateProduct),
    "updateProductVariant": (()=>updateProductVariant),
    "updateProductVariantData": (()=>updateProductVariantData),
    "updateProductVariantImages": (()=>updateProductVariantImages)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/constants.ts [app-rsc] (ecmascript)");
;
async function checkIfBusinessProfileExists(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).maybeSingle();
        if (error) {
            console.error(`Error checking existing business profile: ${error.message}`);
            return {
                exists: false,
                error: "Database error checking business profile."
            };
        }
        return {
            exists: !!data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error checking business profile: ${err}`);
        return {
            exists: false,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileAnalyticsData(supabase, businessProfileId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_VISITS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TODAY_VISITS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].YESTERDAY_VISITS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].VISITS_7_DAYS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].VISITS_30_DAYS}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, businessProfileId).single();
        if (error) {
            console.error(`Error fetching business profile analytics data: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data: data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile analytics data: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getDailyUniqueVisitTrend(supabase, businessId, startDate, endDate) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_DAILY_UNIQUE_VISIT_TREND, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].BUSINESS_ID]: businessId,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].START_DATE]: startDate,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].END_DATE]: endDate
        });
        if (error) {
            console.error(`Error fetching daily unique visit trend: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching daily unique visit trend: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getHourlyUniqueVisitTrend(supabase, businessId, targetDate) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_HOURLY_UNIQUE_VISIT_TREND, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].BUSINESS_ID]: businessId,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].TARGET_DATE]: targetDate
        });
        if (error) {
            console.error(`Error fetching hourly unique visit trend: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching hourly unique visit trend: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getMonthlyUniqueVisits(supabase, businessId, targetYear, targetMonth) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_MONTHLY_UNIQUE_VISITS, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].BUSINESS_ID]: businessId,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].TARGET_YEAR]: targetYear,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].TARGET_MONTH]: targetMonth
        });
        if (error) {
            console.error(`Error fetching monthly unique visits: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching monthly unique visits: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getMonthlyUniqueVisitTrend(supabase, businessId, startYear, startMonth, endYear, endMonth) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_MONTHLY_UNIQUE_VISIT_TREND, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].BUSINESS_ID]: businessId,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].START_YEAR]: startYear,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].START_MONTH]: startMonth,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].END_YEAR]: endYear,
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].END_MONTH]: endMonth
        });
        if (error) {
            console.error(`Error fetching monthly unique visit trend: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching monthly unique visit trend: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getAvailableYearsForMonthlyMetrics(supabase, businessId) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_AVAILABLE_YEARS_FOR_MONTHLY_METRICS, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].BUSINESS_ID]: businessId
        });
        if (error) {
            console.error(`Error fetching available years for monthly metrics: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching available years for monthly metrics: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getTotalUniqueVisits(supabase, businessId) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_TOTAL_UNIQUE_VISITS, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].BUSINESS_ID]: businessId
        });
        if (error) {
            console.error(`Error fetching total unique visits: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching total unique visits: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileWithInteractionMetrics(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_LIKES}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_SUBSCRIPTIONS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].AVERAGE_RATING}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_VISITS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TODAY_VISITS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].YESTERDAY_VISITS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].VISITS_7_DAYS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].VISITS_30_DAYS}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error(`Error fetching business profile with interaction metrics: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile with interaction metrics: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getLatestSubscription(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PAYMENT_SUBSCRIPTIONS).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PLAN_ID).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, userId).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
            ascending: false
        }).limit(1).maybeSingle();
        if (error) {
            console.error(`Error fetching latest subscription: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching latest subscription: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileCustomAds(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CUSTOM_ADS).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error(`Error fetching business profile custom ads: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data: data.custom_ads,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile custom ads: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function updateBusinessProfile(supabase, userId, updates) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).update(updates).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).select().single();
        if (error) {
            console.error(`Error updating business profile: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error updating business profile: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileCustomBranding(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CUSTOM_BRANDING).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error(`Error fetching business profile custom branding: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data: data.custom_branding,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile custom branding: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfilePhone(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PHONE).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error(`Error fetching business profile phone: ${error.message}`);
            return {
                phone: null,
                error: error.message
            };
        }
        return {
            phone: data.phone,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile phone: ${err}`);
        return {
            phone: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileWithAllDetails(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CONTACT_EMAIL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].HAS_ACTIVE_SUBSCRIPTION},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TRIAL_END_DATE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].MEMBER_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TITLE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ADDRESS_LINE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PHONE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].INSTAGRAM_URL},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].FACEBOOK_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].WHATSAPP_NUMBER}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ABOUT_BIO}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_LIKES}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_SUBSCRIPTIONS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].AVERAGE_RATING}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].THEME_COLOR}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DELIVERY_INFO}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_HOURS},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_CATEGORY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CUSTOM_BRANDING}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CUSTOM_ADS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ESTABLISHED_YEAR}
      `).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error(`Error fetching business profile with all details: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile with all details: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getLatestSubscriptionStatus(supabase, userId) {
    try {
        const { data: subscription, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PAYMENT_SUBSCRIPTIONS).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PLAN_ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].SUBSCRIPTION_STATUS}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, userId).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
            ascending: false
        }).limit(1).maybeSingle();
        if (error) {
            console.error(`Error fetching latest subscription status: ${error.message}`);
            return {
                subscriptionStatus: null,
                error: error.message
            };
        }
        return {
            subscriptionStatus: subscription,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching latest subscription status: ${err}`);
        return {
            subscriptionStatus: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getSecureBusinessProfileBySlug(supabase, slug) {
    try {
        const { data: profileData, error: profileError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`
        *,
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PAYMENT_SUBSCRIPTIONS}!${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID} (
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PLAN_ID},
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].SUBSCRIPTION_STATUS}
        )
      `).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG, slug).maybeSingle();
        if (profileError) {
            console.error(`Error fetching secure business profile by slug: ${profileError.message}`);
            return {
                data: null,
                error: profileError.message
            };
        }
        if (!profileData) {
            return {
                data: null,
                error: "Profile not found."
            };
        }
        const safeData = {
            ...profileData,
            subscription_status: profileData[__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PAYMENT_SUBSCRIPTIONS]?.subscription_status || null,
            plan_id: profileData[__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PAYMENT_SUBSCRIPTIONS]?.plan_id || null
        };
        return {
            data: safeData,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching secure business profile by slug: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getSecureBusinessProfileWithProductsBySlug(supabase, slug) {
    try {
        const { data: profileData, error: profileError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`
        *,
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES} (
          ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DESCRIPTION}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGE_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE}
        )
      `).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG, slug).maybeSingle();
        if (profileError) {
            console.error(`Error fetching secure business profile with products by slug: ${profileError.message}`);
            return {
                data: null,
                error: profileError.message
            };
        }
        if (!profileData) {
            return {
                data: null,
                error: "Profile not found."
            };
        }
        const safeData = {
            ...profileData,
            products_services: profileData[__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES] || []
        };
        return {
            data: safeData,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching secure business profile with products by slug: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getAdDataForPincode(supabase, pincode) {
    try {
        // First, check if the custom_ad_targets table exists (for backward compatibility)
        const { count, error: tableCheckError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].CUSTOM_AD_TARGETS).select("*", {
            count: "exact",
            head: true
        });
        if (tableCheckError) {
            console.error(`Error checking custom_ad_targets table: ${tableCheckError.message}`);
            // Fallback to old approach if table check fails
            return {
                adData: null,
                error: tableCheckError.message
            };
        }
        // If the table exists and migration has been applied
        if (count !== null) {
            const { data: adData, error: adError } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_AD_FOR_PINCODE, {
                [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_PARAMS"].TARGET_PINCODE]: pincode
            });
            if (adError) {
                console.error(`Error fetching ad for pincode ${pincode}: ${adError.message}`);
                return {
                    adData: null,
                    error: adError.message
                };
            }
            return {
                adData: adData && adData.length > 0 ? adData[0] : null,
                error: null
            };
        } else {
            // Fallback to old approach if migration hasn't been applied yet
            const { data: customAd, error: customAdError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].CUSTOM_ADS).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].AD_IMAGE_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].AD_LINK_URL}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_ACTIVE, true).or(`targeting_locations.eq.'"global"',targeting_locations.cs.'["${pincode}"]'`).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                ascending: false
            }).limit(1).maybeSingle();
            if (customAdError) {
                console.error(`Error fetching custom ad (fallback): ${customAdError.message}`);
                return {
                    adData: null,
                    error: customAdError.message
                };
            }
            return {
                adData: customAd,
                error: null
            };
        }
    } catch (err) {
        console.error(`Unexpected error fetching ad data for pincode: ${err}`);
        return {
            adData: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductsForBusiness(supabase, businessId, limit) {
    try {
        const { data, error, count } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select("*", {
            count: "exact"
        }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessId).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE, true).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
            ascending: false
        }).limit(limit);
        if (error) {
            console.error(`Error fetching products for business ${businessId}: ${error.message}`);
            return {
                products: null,
                count: 0,
                error: error.message
            };
        }
        return {
            products: data,
            count: count || 0,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching products for business: ${err}`);
        return {
            products: null,
            count: 0,
            error: "An unexpected error occurred."
        };
    }
}
async function getReviewsCountForBusiness(supabase, businessId) {
    try {
        const { count, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].RATINGS_REVIEWS).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, {
            count: "exact",
            head: true
        }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, businessId).neq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID, businessId); // Don't count self-reviews
        if (error) {
            console.error(`Error fetching reviews count for business ${businessId}: ${error.message}`);
            return {
                count: 0,
                error: error.message
            };
        }
        return {
            count: count || 0,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching reviews count for business: ${err}`);
        return {
            count: 0,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileGallery(supabase, businessId) {
    try {
        const { data: profileData, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].GALLERY).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, businessId).single();
        if (error) {
            console.error(`Error fetching business profile gallery: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data: profileData.gallery
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile gallery: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileIdAndStatusBySlug(supabase, businessSlug) {
    try {
        const { data: business, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG, businessSlug).single();
        if (error) {
            console.error(`Error fetching business ID and status by slug: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data: business,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business ID and status by slug: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getPublicSubscriptionStatus(supabase, businessProfileId) {
    try {
        const { data: subscription, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PUBLIC_SUBSCRIPTION_STATUS).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PLAN_ID).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, businessProfileId).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
            ascending: false
        }).limit(1).maybeSingle();
        if (error) {
            console.error(`Error fetching public subscription status: ${error.message}`);
            return {
                planId: null,
                error: error.message
            };
        }
        return {
            planId: subscription?.plan_id || null,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching public subscription status: ${err}`);
        return {
            planId: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductsWithFiltersAndPagination(supabase, businessId, page = 1, sortBy = "created_desc", pageSize = 20, searchTerm, productType) {
    const offset = (page - 1) * pageSize;
    let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(`
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DESCRIPTION},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGE_URL},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].SLUG}
    `, {
        count: "exact"
    }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessId).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE, true);
    if (searchTerm && searchTerm.trim().length > 0) {
        query = query.ilike(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME, `%${searchTerm.trim()}%`);
    }
    if (productType && productType !== "all") {
        query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE, productType);
    }
    switch(sortBy){
        case "created_asc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                ascending: true
            });
            break;
        case "updated_desc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT, {
                ascending: false
            });
            break;
        case "price_asc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE, {
                ascending: true,
                nullsFirst: false
            }).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE, {
                ascending: true,
                nullsFirst: false
            });
            break;
        case "price_desc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE, {
                ascending: false,
                nullsFirst: false
            }).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE, {
                ascending: false,
                nullsFirst: false
            });
            break;
        case "name_asc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME, {
                ascending: true
            });
            break;
        case "name_desc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME, {
                ascending: false
            });
            break;
        case "created_desc":
        default:
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                ascending: false
            });
            break;
    }
    query = query.range(offset, offset + pageSize - 1);
    const { data, error, count } = await query;
    if (error) {
        console.error(`Error fetching products: ${error.message}`);
        return {
            data: null,
            error: error.message,
            totalCount: 0
        };
    }
    return {
        data: data,
        error: null,
        totalCount: count || 0
    };
}
async function getBusinessProfileStatus(supabase, businessProfileId) {
    try {
        const { data: profile, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, businessProfileId).single();
        if (error) {
            console.error(`Error fetching business profile status: ${error.message}`);
            return {
                status: null,
                error: error.message
            };
        }
        return {
            status: profile.status,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile status: ${err}`);
        return {
            status: null,
            error: "An unexpected error occurred."
        };
    }
}
async function insertCardVisit(supabase, visitData) {
    try {
        const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].CARD_VISITS).insert([
            visitData
        ]);
        if (error) {
            console.error(`Error inserting card visit: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error inserting card visit: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function updateBusinessLogoUrl(supabase, userId, logoUrl) {
    const { error: updateError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).update({
        [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL]: logoUrl,
        [__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT]: new Date().toISOString()
    }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId);
    if (updateError) {
        console.error("Update Business Logo URL Error:", updateError);
        return {
            success: false,
            error: `Failed to update business logo URL: ${updateError.message}`
        };
    }
    return {
        success: true
    };
}
async function getBusinessLogoUrl(supabase, userId) {
    const { data, error: fetchError } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
    if (fetchError) {
        console.error("Error fetching business logo URL:", fetchError);
        return {
            logoUrl: null,
            error: "Failed to fetch business logo URL."
        };
    }
    return {
        logoUrl: data?.logo_url || null
    };
}
async function checkBusinessSlugUniqueness(supabase, slug, excludeUserId = null) {
    try {
        let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID).ilike(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG, slug);
        if (excludeUserId) {
            query = query.neq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, excludeUserId);
        }
        const { data, error } = await query.maybeSingle();
        if (error) {
            console.error(`Error checking business slug uniqueness: ${error.message}`);
            return {
                available: false,
                error: error.message
            };
        }
        return {
            available: !data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error checking business slug uniqueness: ${err}`);
        return {
            available: false,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfilesByCity(supabase, city, status, category, page, limit, sortBy, ascending) {
    try {
        const offset = (page - 1) * limit;
        let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].MEMBER_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TITLE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ADDRESS_LINE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PHONE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_CATEGORY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].INSTAGRAM_URL},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].FACEBOOK_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].WHATSAPP_NUMBER}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ABOUT_BIO}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].THEME_COLOR},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DELIVERY_INFO}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_LIKES}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TOTAL_SUBSCRIPTIONS}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].AVERAGE_RATING}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_HOURS},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TRIAL_END_DATE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CONTACT_EMAIL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ESTABLISHED_YEAR}
        `, {
            count: "exact"
        }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY, city).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS, status);
        if (category && category.trim()) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_CATEGORY, category.trim());
        }
        const { data, count, error } = await query.range(offset, offset + limit - 1).order(sortBy, {
            ascending
        });
        if (error) {
            console.error(`Error fetching business profiles by city: ${error.message}`);
            return {
                data: null,
                count: null,
                error: error.message
            };
        }
        return {
            data,
            count,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profiles by city: ${err}`);
        return {
            data: null,
            count: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessIdsByCity(supabase, city, status, category) {
    try {
        let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY, city).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS, status);
        if (category && category.trim()) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_CATEGORY, category.trim());
        }
        const { data, error } = await query;
        if (error) {
            console.error(`Error fetching business IDs by city: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data: data.map((item)=>item.id),
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business IDs by city: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductCountByBusinessIds(supabase, businessIds, productType) {
    try {
        let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, {
            count: "exact"
        }).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessIds).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE, true);
        if (productType) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE, productType);
        }
        const { count, error } = await query;
        if (error) {
            console.error(`Error counting products by business IDs: ${error.message}`);
            return {
                count: null,
                error: error.message
            };
        }
        return {
            count,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error counting products by business IDs: ${err}`);
        return {
            count: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductsByBusinessIds(supabase, businessIds, page, limit, sortBy, ascending, productType) {
    try {
        const from = (page - 1) * limit;
        const to = from + limit - 1;
        let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(`
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DESCRIPTION}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGE_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].SLUG},
        business_profiles!${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID}(${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG})
        `).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessIds).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE, true);
        if (productType) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE, productType);
        }
        // Apply sorting based on the sortBy parameter
        if (sortBy === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE) {
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE, {
                ascending,
                nullsFirst: false
            }).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE, {
                ascending,
                nullsFirst: false
            });
        } else {
            query = query.order(sortBy, {
                ascending
            });
        }
        const { data, error } = await query.range(from, to);
        if (error) {
            console.error(`Error fetching products by business IDs: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching products by business IDs: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductByIdAndBusinessId(supabase, productId, businessId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, productId).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessId).single();
        if (error) {
            console.error(`Error fetching product by ID and business ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching product by ID and business ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getExistingProductVariants(supabase, productId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].VARIANT_VALUES}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_ID, productId);
        if (error) {
            console.error(`Error fetching existing product variants: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching existing product variants: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function insertProductVariant(supabase, variantData) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).insert(variantData).select().single();
        if (error) {
            console.error(`Error inserting product variant: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error inserting product variant: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function deleteProductVariant(supabase, variantId) {
    try {
        const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).delete().eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantId);
        if (error) {
            console.error(`Error deleting product variant: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error deleting product variant: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function updateProductVariantImages(supabase, variantId, imageUrls, featuredImageIndex) {
    try {
        const updateData = {
            images: imageUrls,
            featured_image_index: featuredImageIndex
        };
        const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).update(updateData).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantId);
        if (error) {
            console.error(`Error updating product variant images: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error updating product variant images: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function insertMultipleProductVariants(supabase, variantDataArray) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).insert(variantDataArray).select();
        if (error) {
            console.error(`Error inserting multiple product variants: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error inserting multiple product variants: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileLocation(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY_SLUG}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY_SLUG}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE_SLUG}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).single();
        if (error) {
            console.error(`Error fetching business profile location: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile location: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessLikesCount(supabase, businessId) {
    const { count, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].LIKES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, {
        count: 'exact',
        head: true
    }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, businessId);
    if (error) {
        console.error('Error fetching business likes count:', error);
        throw new Error('Failed to get total count');
    }
    return count || 0;
}
async function getBusinessLikes(supabase, businessId, page, limit) {
    const from = (page - 1) * limit;
    const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].LIKES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, businessId).range(from, from + limit - 1);
    if (error) {
        console.error('Error fetching business likes:', error);
        throw new Error('Failed to fetch likes');
    }
    return data;
}
async function getMyLikesCount(supabase, businessId, searchTerm) {
    let countQuery = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].LIKES).select(`
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}!inner (
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}
      )
    `, {
        count: 'exact',
        head: true
    }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID, businessId);
    if (searchTerm) {
        countQuery = countQuery.ilike(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}.${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}`, `%${searchTerm}%`);
    }
    const { count, error } = await countQuery;
    if (error) {
        console.error('Error fetching my likes count:', error);
        throw new Error('Failed to get total count');
    }
    return count || 0;
}
async function getMyLikes(supabase, businessId, page, limit, searchTerm) {
    const from = (page - 1) * limit;
    let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].LIKES).select(`
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}!inner (
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ADDRESS_LINE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY}
      )
    `).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].USER_ID, businessId);
    if (searchTerm) {
        query = query.ilike(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES}.${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}`, `%${searchTerm}%`);
    }
    const { data, error } = await query.range(from, from + limit - 1);
    if (error) {
        console.error('Error fetching my likes:', error);
        throw new Error('Failed to fetch likes');
    }
    return data;
}
async function getBusinessProfilesByIds(supabase, userIds) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ADDRESS_LINE}`).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userIds);
        if (error) {
            console.error(`Error fetching business profiles by IDs: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profiles by IDs: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function insertProduct(supabase, productData) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).insert(productData).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DESCRIPTION}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGE_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGES}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].FEATURED_IMAGE_INDEX}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].SLUG}`).single();
        if (error) {
            console.error(`Error inserting product: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error inserting product: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function updateProduct(supabase, productId, updates) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).update(updates).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, productId).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGE_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGES}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].FEATURED_IMAGE_INDEX}`).single();
        if (error) {
            console.error(`Error updating product: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error updating product: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductById(supabase, productId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DESCRIPTION}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGE_URL}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGES}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].FEATURED_IMAGE_INDEX}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].SLUG}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, productId).single();
        if (error) {
            console.error(`Error fetching product by ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching product by ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getVariantsWithProductBusinessId(supabase, variantIds) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).select(`
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_ID},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES}!inner(${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID})
      `).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantIds);
        if (error) {
            console.error(`Error fetching variants with product business ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching variants with product business ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileById(supabase, businessId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOGO_URL}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, businessId).single();
        if (error) {
            console.error(`Error fetching business profile by ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile by ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileSubscriptionInfo(supabase, businessId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].HAS_ACTIVE_SUBSCRIPTION}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TRIAL_END_DATE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, businessId).maybeSingle();
        if (error) {
            console.error(`Error fetching business profile subscription info: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile subscription info: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getPaymentSubscriptionByBusinessProfileId(supabase, businessProfileId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PAYMENT_SUBSCRIPTIONS).select("*").eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_PROFILE_ID, businessProfileId).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
            ascending: false
        }).limit(1).maybeSingle();
        if (error) {
            console.error(`Error fetching payment subscription: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching payment subscription: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function bulkUpdateProductVariants(supabase, variantIds, updateData) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).update(updateData).in(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantIds).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID);
        if (error) {
            console.error(`Error bulk updating product variants: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error bulk updating product variants: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function updateProductVariant(supabase, variantId, updateData) {
    try {
        const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).update(updateData).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantId);
        if (error) {
            console.error(`Error updating product variant: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error updating product variant: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductDetailsByIdAndBusinessId(supabase, productId, businessId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGES}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, productId).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessId).single();
        if (error) {
            console.error(`Error fetching product details by ID and business ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching product details by ID and business ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function deleteProductByIdAndBusinessId(supabase, productId, businessId) {
    try {
        const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).delete().eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, productId).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessId);
        if (error) {
            console.error(`Error deleting product by ID and business ID: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error deleting product by ID and business ID: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function deleteProductVariantsByProductId(supabase, productId) {
    try {
        const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).delete().eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_ID, productId);
        if (error) {
            console.error(`Error deleting product variants by product ID: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error deleting product variants by product ID: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function getRpcProductWithVariants(supabase, productId) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_PRODUCT_WITH_VARIANTS, {
            product_uuid: productId
        });
        if (error) {
            console.error(`Error calling get_product_with_variants: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error in getRpcProductWithVariants: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductBusinessId(supabase, productId, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, productId).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, userId).single();
        if (error) {
            console.error(`Error fetching product business ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching product business ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getRpcAvailableProductVariants(supabase, productId) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_AVAILABLE_PRODUCT_VARIANTS, {
            product_uuid: productId
        });
        if (error) {
            console.error(`Error calling get_available_product_variants: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error in getRpcAvailableProductVariants: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getFilteredProductVariants(supabase, productId, options = {}) {
    try {
        let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).select("*", {
            count: "exact"
        }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_ID, productId);
        if (!options.includeUnavailable) {
            query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE, true);
        }
        switch(options.sortBy){
            case "created_asc":
                query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                    ascending: true
                });
                break;
            case "created_desc":
                query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                    ascending: false
                });
                break;
            case "name_asc":
                query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].VARIANT_NAME, {
                    ascending: true
                });
                break;
            case "name_desc":
                query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].VARIANT_NAME, {
                    ascending: false
                });
                break;
            case "price_asc":
                query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE, {
                    ascending: true,
                    nullsFirst: false
                });
                break;
            case "price_desc":
                query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE, {
                    ascending: false,
                    nullsFirst: true
                });
                break;
            default:
                query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                    ascending: false
                });
        }
        if (options.limit) {
            query = query.limit(options.limit);
        }
        if (options.offset) {
            query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
        }
        const { data, error: queryError, count } = await query;
        if (queryError) {
            console.error(`Error fetching filtered product variants: ${queryError.message}`);
            return {
                data: null,
                error: queryError.message,
                count: null
            };
        }
        return {
            data,
            error: null,
            count
        };
    } catch (error) {
        console.error(`Unexpected error in getFilteredProductVariants: ${error}`);
        return {
            data: null,
            error: "An unexpected error occurred.",
            count: null
        };
    }
}
async function getRpcBusinessVariantStats(supabase, businessId) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].GET_BUSINESS_VARIANT_STATS, {
            business_uuid: businessId
        });
        if (error) {
            console.error(`Error calling get_business_variant_stats: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error in getRpcBusinessVariantStats: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getRpcIsVariantCombinationUnique(supabase, productId, variantValues, excludeVariantId) {
    try {
        const { data: result, error: functionError } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].IS_VARIANT_COMBINATION_UNIQUE, {
            product_uuid: productId,
            variant_vals: variantValues,
            exclude_variant_id: excludeVariantId || undefined
        });
        if (functionError) {
            console.error(`Error calling is_variant_combination_unique: ${functionError.message}`);
            return {
                isUnique: undefined,
                error: functionError.message
            };
        }
        return {
            isUnique: result || undefined,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error in getRpcIsVariantCombinationUnique: ${err}`);
        return {
            isUnique: undefined,
            error: "An unexpected error occurred."
        };
    }
}
async function getProductsWithVariantInfo(supabase, userId, page = 1, limit = 10, filters = {}, sortBy = "created_desc") {
    const offset = (page - 1) * limit;
    let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(`
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DESCRIPTION},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGE_URL},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGES},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].FEATURED_IMAGE_INDEX},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].UPDATED_AT},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].SLUG},
      ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS}(${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE})
    `, {
        count: "exact"
    }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, userId);
    // Apply Filters
    if (filters.searchTerm) query = query.or(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME}.ilike.%${filters.searchTerm}%,${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DESCRIPTION}.ilike.%${filters.searchTerm}%`);
    if (filters.hasVariants !== undefined) {
        if (filters.hasVariants) {
            // Only products that have variants
            query = query.not(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS, "is", null);
        } else {
            // Only products that don't have variants
            query = query.is(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS, null);
        }
    }
    if (filters.productType) query = query.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE, filters.productType);
    // Apply Sorting
    switch(sortBy){
        case "created_asc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                ascending: true
            });
            break;
        case "price_asc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE, {
                ascending: true,
                nullsFirst: false
            }).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE, {
                ascending: true,
                nullsFirst: false
            });
            break;
        case "price_desc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].DISCOUNTED_PRICE, {
                ascending: false,
                nullsFirst: false
            }).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BASE_PRICE, {
                ascending: false,
                nullsFirst: false
            });
            break;
        case "name_asc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME, {
                ascending: true
            });
            break;
        case "name_desc":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME, {
                ascending: false
            });
            break;
        case "available_first":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE, {
                ascending: false
            }).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                ascending: false
            });
            break;
        case "unavailable_first":
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE, {
                ascending: true
            }).order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                ascending: false
            });
            break;
        case "created_desc":
        case "variant_count_asc":
        case "variant_count_desc":
        default:
            query = query.order(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CREATED_AT, {
                ascending: false
            });
            break;
    }
    query = query.range(offset, offset + limit - 1);
    const { data, error, count } = await query;
    if (error) {
        console.error("Fetch Products Error:", error);
        return {
            data: null,
            error: error.message,
            count: null
        };
    }
    return {
        data,
        count,
        error: null
    };
}
async function getVariantDetailsWithProductBusinessId(supabase, variantId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).select(`
        *,
        products_services!inner(business_id)
      `).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantId).single();
        if (error) {
            console.error(`Error fetching variant details by ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching variant details by ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function updateProductVariantData(supabase, variantId, updateData) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).update(updateData).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantId).select().single();
        if (error) {
            console.error(`Error updating product variant data: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error updating product variant data: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function bulkUpdateProductVariantsData(supabase, updates) {
    try {
        const updatePromises = updates.map((update)=>supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).update(update.data).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, update.id));
        const results = await Promise.all(updatePromises);
        const errors = results.filter((result)=>result.error).map((result)=>result.error?.message);
        if (errors.length > 0) {
            console.error(`Errors during bulk update of product variants: ${errors.join(", ")}`);
            return {
                success: false,
                error: `Failed to update some variants: ${errors.join(", ")}`
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error during bulk update of product variants: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
async function getVariantsByProductId(supabase, productId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).select(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IS_AVAILABLE}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].VARIANT_NAME}, ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].IMAGES}, products_services!inner(${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID}))`).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_ID, productId);
        if (error) {
            console.error(`Error fetching variants by product ID: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        // Explicitly map the data to the correct type
        const typedData = data.map((item)=>({
                ...item,
                products_services: item.products_services
            }));
        return {
            data: typedData,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching variants by product ID: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function getBusinessProfileForOnboarding(supabase, userId) {
    try {
        const { data, error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].BUSINESS_PROFILES).select(`
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_NAME},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CONTACT_EMAIL},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].MEMBER_NAME},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].TITLE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PHONE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_CATEGORY},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_SLUG},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ADDRESS_LINE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PINCODE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].CITY},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATE},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].LOCALITY},
        ${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].STATUS}
      `).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, userId).maybeSingle();
        if (error) {
            console.error(`Error fetching business profile for onboarding: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error fetching business profile for onboarding: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function createBusinessProfileAtomic(supabase, businessData, subscriptionData) {
    try {
        const { data, error } = await supabase.rpc(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RPC_FUNCTIONS"].CREATE_BUSINESS_PROFILE_ATOMIC, {
            p_business_data: businessData,
            p_subscription_data: subscriptionData
        });
        if (error) {
            console.error(`Error calling create_business_profile_atomic: ${error.message}`);
            return {
                data: null,
                error: error.message
            };
        }
        return {
            data,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error in createBusinessProfileAtomic: ${err}`);
        return {
            data: null,
            error: "An unexpected error occurred."
        };
    }
}
async function deleteProductVariantById(supabase, variantId) {
    try {
        const { error } = await supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCT_VARIANTS).delete().eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, variantId);
        if (error) {
            console.error(`Error deleting product variant by ID: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: true,
            error: null
        };
    } catch (err) {
        console.error(`Unexpected error deleting product variant by ID: ${err}`);
        return {
            success: false,
            error: "An unexpected error occurred."
        };
    }
}
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createClient": (()=>createClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createServerClient.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-rsc] (ecmascript)");
"user server";
;
;
async function createClient() {
    const supabaseUrl = ("TURBOPACK compile-time value", "https://rnjolcoecogzgglnblqn.supabase.co");
    const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o");
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // Check if we're in a test environment
    const headersList = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["headers"])();
    const isTestEnvironment = ("TURBOPACK compile-time value", "development") === 'test' || process.env.PLAYWRIGHT_TESTING === 'true' || headersList.get('x-playwright-testing') === 'true';
    if (isTestEnvironment) {
        // Return a mocked Supabase client for testing
        return createMockSupabaseClient(headersList);
    }
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createServerClient"])(supabaseUrl, supabaseAnonKey, {
        cookies: {
            async getAll () {
                return await cookieStore.getAll();
            },
            async setAll (cookiesToSet) {
                try {
                    for (const { name, value, options } of cookiesToSet){
                        await cookieStore.set(name, value, options);
                    }
                } catch  {
                // The `setAll` method was called from a Server Component.
                // This can be ignored if you have middleware refreshing
                // user sessions.
                }
            }
        }
    });
}
function createMockSupabaseClient(headersList) {
    const testAuthState = headersList.get('x-test-auth-state');
    const testUserType = headersList.get('x-test-user-type');
    const testHasProfile = testUserType === 'customer' || testUserType === 'business';
    const testBusinessSlug = headersList.get('x-test-business-slug');
    const testPlanId = headersList.get('x-test-plan-id') || 'free';
    return {
        auth: {
            getUser: async ()=>{
                if (testAuthState === 'authenticated') {
                    return {
                        data: {
                            user: {
                                id: 'test-user-id',
                                email: '<EMAIL>'
                            }
                        },
                        error: null
                    };
                }
                return {
                    data: {
                        user: null
                    },
                    error: {
                        message: 'Unauthorized',
                        name: 'AuthApiError',
                        status: 401
                    }
                };
            },
            getSession: async ()=>{
                if (testAuthState === 'authenticated') {
                    return {
                        data: {
                            session: {
                                user: {
                                    id: 'test-user-id',
                                    email: '<EMAIL>'
                                }
                            }
                        },
                        error: null
                    };
                }
                return {
                    data: {
                        session: null
                    },
                    error: {
                        message: 'Unauthorized',
                        name: 'AuthApiError',
                        status: 401
                    }
                };
            },
            signInWithOtp: async ()=>({
                    data: {
                        user: null,
                        session: null
                    },
                    error: null
                }),
            signOut: async ()=>({
                    error: null
                })
        },
        from: (table)=>createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId)
    };
}
function createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId) {
    const getMockData = ()=>getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId);
    const createChainableMock = (data)=>({
            select: (_columns)=>createChainableMock(data),
            eq: (_column, _value)=>createChainableMock(data),
            neq: (_column, _value)=>createChainableMock(data),
            gt: (_column, _value)=>createChainableMock(data),
            gte: (_column, _value)=>createChainableMock(data),
            lt: (_column, _value)=>createChainableMock(data),
            lte: (_column, _value)=>createChainableMock(data),
            like: (_column, _pattern)=>createChainableMock(data),
            ilike: (_column, _pattern)=>createChainableMock(data),
            is: (_column, _value)=>createChainableMock(data),
            in: (_column, _values)=>createChainableMock(data),
            contains: (_column, _value)=>createChainableMock(data),
            containedBy: (_column, _value)=>createChainableMock(data),
            rangeGt: (_column, _value)=>createChainableMock(data),
            rangeGte: (_column, _value)=>createChainableMock(data),
            rangeLt: (_column, _value)=>createChainableMock(data),
            rangeLte: (_column, _value)=>createChainableMock(data),
            rangeAdjacent: (_column, _value)=>createChainableMock(data),
            overlaps: (_column, _value)=>createChainableMock(data),
            textSearch: (_column, _query)=>createChainableMock(data),
            match: (_query)=>createChainableMock(data),
            not: (_column, _operator, _value)=>createChainableMock(data),
            or: (_filters)=>createChainableMock(data),
            filter: (_column, _operator, _value)=>createChainableMock(data),
            order: (_column, _options)=>createChainableMock(data),
            limit: (_count, _options)=>createChainableMock(data),
            range: (_from, _to, _options)=>createChainableMock(data),
            abortSignal: (_signal)=>createChainableMock(data),
            single: async ()=>getMockData(),
            maybeSingle: async ()=>getMockData(),
            then: async (callback)=>{
                const result = getMockData();
                return callback ? callback(result) : result;
            },
            data: data || [],
            error: null,
            count: data ? data.length : 0,
            status: 200,
            statusText: 'OK'
        });
    return {
        select: (_columns)=>createChainableMock(),
        insert: (data)=>({
                select: (_columns)=>({
                        single: async ()=>({
                                data: Array.isArray(data) ? data[0] : data,
                                error: null
                            }),
                        maybeSingle: async ()=>({
                                data: Array.isArray(data) ? data[0] : data,
                                error: null
                            }),
                        then: async (_callback)=>{
                            const result = {
                                data: Array.isArray(data) ? data : [
                                    data
                                ],
                                error: null
                            };
                            return _callback ? _callback(result) : result;
                        }
                    }),
                then: async (_callback)=>{
                    const result = {
                        data: Array.isArray(data) ? data : [
                            data
                        ],
                        error: null
                    };
                    return _callback ? _callback(result) : result;
                }
            }),
        update: (data)=>createChainableMock(data),
        upsert: (data)=>createChainableMock(data),
        delete: ()=>createChainableMock(),
        rpc: (_functionName, _params)=>createChainableMock()
    };
}
/**
 * Helper function to get mock table data based on test state
 */ function getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId) {
    if (table === 'customer_profiles') {
        const hasCustomerProfile = testHasProfile && testUserType === 'customer';
        return {
            data: hasCustomerProfile ? {
                id: 'test-user-id',
                name: 'Test Customer',
                avatar_url: null,
                phone: '+1234567890',
                email: '<EMAIL>',
                address: 'Test Address',
                city: 'Test City',
                state: 'Test State',
                pincode: '123456'
            } : null,
            error: null
        };
    }
    if (table === 'business_profiles') {
        const hasBusinessProfile = testHasProfile && testUserType === 'business';
        return {
            data: hasBusinessProfile ? {
                id: 'test-user-id',
                business_slug: testBusinessSlug || null,
                trial_end_date: null,
                has_active_subscription: true,
                business_name: 'Test Business',
                city_slug: 'test-city',
                state_slug: 'test-state',
                locality_slug: 'test-locality',
                pincode: '123456',
                business_description: 'Test business description',
                business_category: 'retail',
                phone: '+1234567890',
                email: '<EMAIL>',
                website: 'https://testbusiness.com'
            } : null,
            error: null
        };
    }
    if (table === 'payment_subscriptions') {
        return {
            data: testUserType === 'business' ? {
                id: 'test-subscription-id',
                plan_id: testPlanId,
                business_profile_id: 'test-user-id',
                status: 'active',
                created_at: '2024-01-01T00:00:00Z'
            } : null,
            error: null
        };
    }
    if (table === 'products') {
        return {
            data: testUserType === 'business' ? [
                {
                    id: 'test-product-1',
                    name: 'Test Product 1',
                    price: 100,
                    business_profile_id: 'test-user-id',
                    available: true
                },
                {
                    id: 'test-product-2',
                    name: 'Test Product 2',
                    price: 200,
                    business_profile_id: 'test-user-id',
                    available: false
                }
            ] : [],
            error: null
        };
    }
    // Default return for unknown tables
    return {
        data: null,
        error: null
    };
}
}}),
"[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40039e5475159c6d152f6658179feab0b3344d8bcc":"getSecureBusinessProfileWithProductsBySlug","4090bf49bde90611f206004fcfecc276d0bd89ff93":"getSecureBusinessProfileBySlug"},"",""] */ __turbopack_context__.s({
    "getSecureBusinessProfileBySlug": (()=>getSecureBusinessProfileBySlug),
    "getSecureBusinessProfileWithProductsBySlug": (()=>getSecureBusinessProfileWithProductsBySlug)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/services/businessService.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
async function getSecureBusinessProfileBySlug(slug) {
    if (!slug) {
        return {
            error: "Business slug is required."
        };
    }
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: profileData, error: profileError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSecureBusinessProfileBySlug"])(supabase, slug);
        if (profileError) {
            console.error("Secure Fetch Error:", profileError);
            return {
                error: `Failed to fetch business profile: ${profileError}`
            };
        }
        if (!profileData) {
            return {
                error: "Profile not found."
            };
        }
        const safeData = {
            ...profileData,
            subscription_status: profileData.subscription_status || null,
            plan_id: profileData.plan_id || null
        };
        return {
            data: safeData
        };
    } catch (e) {
        console.error("Exception in getSecureBusinessProfileBySlug:", e);
        return {
            error: "An unexpected error occurred."
        };
    }
}
async function getSecureBusinessProfileWithProductsBySlug(slug) {
    if (!slug) {
        return {
            error: "Business slug is required."
        };
    }
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: profileData, error: profileError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$services$2f$businessService$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSecureBusinessProfileWithProductsBySlug"])(supabase, slug);
        if (profileError) {
            console.error("Secure Fetch Error:", profileError);
            return {
                error: `Failed to fetch business profile: ${profileError}`
            };
        }
        if (!profileData) {
            return {
                error: "Profile not found."
            };
        }
        const safeData = {
            ...profileData,
            products_services: profileData.products_services || []
        };
        return {
            data: safeData
        };
    } catch (e) {
        console.error("Exception in getSecureBusinessProfileWithProductsBySlug:", e);
        return {
            error: "An unexpected error occurred."
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getSecureBusinessProfileBySlug,
    getSecureBusinessProfileWithProductsBySlug
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSecureBusinessProfileBySlug, "4090bf49bde90611f206004fcfecc276d0bd89ff93", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSecureBusinessProfileWithProductsBySlug, "40039e5475159c6d152f6658179feab0b3344d8bcc", null);
}}),
"[project]/app/[cardSlug]/product/actions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"400897f4fcf08b6ff241e55ca68679f59b009f833f":"getBusinessWithContactInfo","604da278527a3b3ebc839ac04faa50dcf6b1fc354d":"getProductById","6086dbe3b7d9dde97a2afcbc0842cce29d93604d12":"getProductWithVariantsBySlug","60d4c90f1bb7d3950939f7f34a7e2c8dda1f2df44e":"getProductBySlug","60ecdd28a201e7e993d1c9af4cd41063d0f854d817":"getProductsFromOtherBusinesses","70a09fe73f3bf03ea5636420c66e2eb3cb262d219c":"getMoreProductsFromBusiness"},"",""] */ __turbopack_context__.s({
    "getBusinessWithContactInfo": (()=>getBusinessWithContactInfo),
    "getMoreProductsFromBusiness": (()=>getMoreProductsFromBusiness),
    "getProductById": (()=>getProductById),
    "getProductBySlug": (()=>getProductBySlug),
    "getProductWithVariantsBySlug": (()=>getProductWithVariantsBySlug),
    "getProductsFromOtherBusinesses": (()=>getProductsFromOtherBusinesses)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
async function getProductById(productId, businessId) {
    if (!productId || !businessId) {
        return {
            error: "Product ID and Business ID are required."
        };
    }
    try {
        // Use regular client - products_services has public read access for online businesses
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Fetch the product
        const { data, error } = await supabase.from("products_services").select(`
        id,
        business_id,
        name,
        description,
        base_price,
        discounted_price,
        product_type,
        is_available,
        image_url,
        images,
        featured_image_index,
        created_at,
        updated_at,
        slug
      `).eq("id", productId).eq("business_id", businessId).eq("is_available", true).single();
        if (error) {
            console.error("Error fetching product:", error);
            return {
                error: "Failed to fetch product details"
            };
        }
        if (!data) {
            return {
                error: "Product not found"
            };
        }
        // Transform the data to match ProductServiceData type
        const transformedData = {
            ...data,
            base_price: data.base_price || 0,
            product_type: data.product_type || "physical",
            created_at: data.created_at,
            updated_at: data.updated_at,
            slug: data.slug || undefined,
            description: data.description || undefined
        };
        return {
            data: transformedData
        };
    } catch (error) {
        console.error("Unexpected error in getProductById:", error);
        return {
            error: "An unexpected error occurred"
        };
    }
}
async function getProductBySlug(slug, businessId) {
    if (!slug || !businessId) {
        return {
            error: "Product slug and Business ID are required."
        };
    }
    try {
        // Use regular client - products_services has public read access for online businesses
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Fetch the product
        const { data, error } = await supabase.from("products_services").select(`
        id,
        business_id,
        name,
        description,
        base_price,
        discounted_price,
        product_type,
        is_available,
        image_url,
        images,
        featured_image_index,
        created_at,
        updated_at,
        slug
      `).eq("slug", slug).eq("business_id", businessId).eq("is_available", true).single();
        // Handle PGRST116 error (no rows returned) silently - this is an expected case
        if (error && error.code === "PGRST116") {
            return {
                data: undefined
            };
        }
        // Log other unexpected errors
        if (error) {
            console.error("Unexpected error fetching product by slug:", error);
            return {
                error: "Failed to fetch product details"
            };
        }
        if (!data) {
            return {
                data: undefined
            };
        }
        // Transform the data to match ProductServiceData type
        const transformedData = {
            ...data,
            base_price: data.base_price || 0,
            product_type: data.product_type || "physical",
            created_at: data.created_at,
            updated_at: data.updated_at,
            slug: data.slug || undefined,
            description: data.description || undefined
        };
        return {
            data: transformedData
        };
    } catch (error) {
        console.error("Unexpected error in getProductBySlug:", error);
        return {
            error: "An unexpected error occurred"
        };
    }
}
async function getProductWithVariantsBySlug(slug, businessId) {
    if (!slug || !businessId) {
        return {
            error: "Product slug and Business ID are required."
        };
    }
    try {
        // Use regular client - products_services has public read access for online businesses
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Fetch the product
        const { data: productData, error: productError } = await supabase.from("products_services").select(`
        id,
        business_id,
        name,
        description,
        base_price,
        discounted_price,
        product_type,
        is_available,
        image_url,
        images,
        featured_image_index,
        created_at,
        updated_at,
        slug
      `).eq("slug", slug).eq("business_id", businessId).eq("is_available", true).single();
        // Handle PGRST116 error (no rows returned) silently - this is an expected case
        if (productError && productError.code === "PGRST116") {
            return {
                data: undefined
            };
        }
        // Log other unexpected errors
        if (productError) {
            console.error("Unexpected error fetching product by slug:", productError);
            return {
                error: "Failed to fetch product details"
            };
        }
        if (!productData) {
            return {
                data: undefined
            };
        }
        // Fetch variants for this product
        const { data: variantsData, error: variantsError } = await supabase.from("product_variants").select(`
        id,
        product_id,
        variant_name,
        variant_values,
        base_price,
        discounted_price,
        is_available,
        images,
        featured_image_index,
        created_at,
        updated_at
      `).eq("product_id", productData.id).order("created_at", {
            ascending: true
        });
        if (variantsError) {
            console.error("Error fetching product variants:", variantsError);
        // Continue without variants rather than failing completely
        }
        // Transform variants data
        const variants = (variantsData || []).map((variant)=>({
                ...variant,
                variant_values: typeof variant.variant_values === "string" ? JSON.parse(variant.variant_values) : variant.variant_values,
                created_at: variant.created_at,
                updated_at: variant.updated_at
            }));
        // Create the product with variants
        const productWithVariants = {
            ...productData,
            variant_count: variants.length,
            has_variants: variants.length > 0,
            available_variant_count: variants.filter((v)=>v.is_available).length,
            variants
        };
        return {
            data: productWithVariants
        };
    } catch (error) {
        console.error("Unexpected error in getProductWithVariantsBySlug:", error);
        return {
            error: "An unexpected error occurred"
        };
    }
}
async function getMoreProductsFromBusiness(businessId, currentProductId, limit = 12) {
    if (!businessId || !currentProductId) {
        return {
            error: "Business ID and current product ID are required."
        };
    }
    try {
        // Use regular client - products_services has public read access for online businesses
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Fetch more products from the same business, excluding the current product
        const { data, error } = await supabase.from("products_services").select(`
        id,
        business_id,
        name,
        description,
        base_price,
        discounted_price,
        product_type,
        is_available,
        image_url,
        images,
        featured_image_index,
        created_at,
        updated_at,
        slug
      `).eq("business_id", businessId).eq("is_available", true).neq("id", currentProductId).order("created_at", {
            ascending: false
        }).limit(limit);
        if (error) {
            console.error("Error fetching more products:", error);
            return {
                error: "Failed to fetch more products"
            };
        }
        // Transform the data to match ProductServiceData type
        const transformedData = (data || []).map((item)=>({
                ...item,
                base_price: item.base_price || 0,
                product_type: item.product_type || "physical",
                created_at: item.created_at,
                updated_at: item.updated_at,
                slug: item.slug || undefined,
                description: item.description || undefined
            }));
        return {
            data: transformedData
        };
    } catch (error) {
        console.error("Unexpected error in getMoreProductsFromBusiness:", error);
        return {
            error: "An unexpected error occurred"
        };
    }
}
async function getProductsFromOtherBusinesses(businessId, limit = 12) {
    if (!businessId) {
        return {
            error: "Business ID is required."
        };
    }
    try {
        // Use regular client - business_profiles has public read access
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Get all online business IDs
        const { data: validBusinesses, error: businessError } = await supabase.from("business_profiles").select("id").neq("id", businessId).eq("status", "online");
        if (businessError) {
            console.error("Error filtering valid businesses:", businessError);
            return {
                error: "Failed to filter valid businesses"
            };
        }
        // If no valid businesses found, return empty result
        if (!validBusinesses || validBusinesses.length === 0) {
            return {
                data: []
            };
        }
        // Get the IDs of valid businesses
        const validBusinessIds = validBusinesses.map((b)=>b.id);
        // Fetch products from valid businesses
        const { data, error } = await supabase.from("products_services").select(`
        id,
        business_id,
        name,
        description,
        base_price,
        discounted_price,
        product_type,
        is_available,
        image_url,
        images,
        featured_image_index,
        created_at,
        updated_at,
        slug,
        business_profiles!business_id(business_slug)
      `).in("business_id", validBusinessIds).eq("is_available", true).order("created_at", {
            ascending: false
        }).limit(limit);
        if (error) {
            console.error("Error fetching products from other businesses:", error);
            return {
                error: "Failed to fetch products from other businesses"
            };
        }
        // Transform the data to include business_slug directly and add required fields
        const transformedData = data.map((item)=>{
            // Extract business_slug from the nested business_profiles object
            // Handle different response formats from Supabase
            let businessSlug = null;
            if (item.business_profiles) {
                if (Array.isArray(item.business_profiles)) {
                    businessSlug = item.business_profiles[0]?.business_slug || null;
                } else {
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    businessSlug = item.business_profiles.business_slug || null;
                }
            }
            // Create a new object without the nested business_profiles property
            // to avoid issues with serialization and type conflicts
            const { ...productData } = item;
            // Create a properly structured product with all required fields
            return {
                id: productData.id,
                business_id: productData.business_id,
                name: productData.name || "",
                description: productData.description || "",
                base_price: Number(productData.base_price) || 0,
                discounted_price: productData.discounted_price ? Number(productData.discounted_price) : undefined,
                product_type: productData.product_type || "physical",
                is_available: Boolean(productData.is_available) || false,
                image_url: productData.image_url,
                images: productData.images || [],
                featured_image_index: typeof productData.featured_image_index === "number" ? productData.featured_image_index : 0,
                created_at: productData.created_at ? new Date(productData.created_at) : undefined,
                updated_at: productData.updated_at ? new Date(productData.updated_at) : undefined,
                slug: productData.slug,
                business_slug: businessSlug
            };
        });
        return {
            data: transformedData
        };
    } catch (error) {
        console.error("Unexpected error in getProductsFromOtherBusinesses:", error);
        return {
            error: "An unexpected error occurred"
        };
    }
}
async function getBusinessWithContactInfo(businessId) {
    if (!businessId) {
        return {
            error: "Business ID is required."
        };
    }
    try {
        // Use regular client - business_profiles has public read access
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Fetch the business profile with contact information
        const { data, error } = await supabase.from("business_profiles").select(`
        business_name,
        whatsapp_number,
        phone,
        business_slug
      `).eq("id", businessId).single();
        if (error) {
            console.error("Error fetching business profile:", error);
            return {
                error: "Failed to fetch business details"
            };
        }
        if (!data) {
            return {
                error: "Business not found"
            };
        }
        // Transform data to ensure business_slug is not null
        const transformedData = {
            business_name: data.business_name || "",
            whatsapp_number: data.whatsapp_number,
            phone: data.phone,
            business_slug: data.business_slug || ""
        };
        return {
            data: transformedData
        };
    } catch (error) {
        console.error("Unexpected error in getBusinessWithContactInfo:", error);
        return {
            error: "An unexpected error occurred"
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getProductById,
    getProductBySlug,
    getProductWithVariantsBySlug,
    getMoreProductsFromBusiness,
    getProductsFromOtherBusinesses,
    getBusinessWithContactInfo
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getProductById, "604da278527a3b3ebc839ac04faa50dcf6b1fc354d", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getProductBySlug, "60d4c90f1bb7d3950939f7f34a7e2c8dda1f2df44e", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getProductWithVariantsBySlug, "6086dbe3b7d9dde97a2afcbc0842cce29d93604d12", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getMoreProductsFromBusiness, "70a09fe73f3bf03ea5636420c66e2eb3cb262d219c", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getProductsFromOtherBusinesses, "60ecdd28a201e7e993d1c9af4cd41063d0f854d817", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getBusinessWithContactInfo, "400897f4fcf08b6ff241e55ca68679f59b009f833f", null);
}}),
"[project]/.next-internal/server/app/[cardSlug]/product/[productSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/[cardSlug]/product/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/[cardSlug]/product/actions.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
}}),
"[project]/.next-internal/server/app/[cardSlug]/product/[productSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/[cardSlug]/product/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/[cardSlug]/product/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$product$2f5b$productSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/[cardSlug]/product/[productSlug]/page/actions.js { ACTIONS_MODULE0 => "[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/app/[cardSlug]/product/actions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/[cardSlug]/product/[productSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/[cardSlug]/product/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "40039e5475159c6d152f6658179feab0b3344d8bcc": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSecureBusinessProfileWithProductsBySlug"]),
    "400897f4fcf08b6ff241e55ca68679f59b009f833f": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessWithContactInfo"]),
    "4090bf49bde90611f206004fcfecc276d0bd89ff93": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSecureBusinessProfileBySlug"]),
    "604da278527a3b3ebc839ac04faa50dcf6b1fc354d": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getProductById"]),
    "6086dbe3b7d9dde97a2afcbc0842cce29d93604d12": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getProductWithVariantsBySlug"]),
    "60d4c90f1bb7d3950939f7f34a7e2c8dda1f2df44e": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getProductBySlug"]),
    "60ecdd28a201e7e993d1c9af4cd41063d0f854d817": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getProductsFromOtherBusinesses"]),
    "70a09fe73f3bf03ea5636420c66e2eb3cb262d219c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getMoreProductsFromBusiness"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/[cardSlug]/product/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$product$2f5b$productSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/[cardSlug]/product/[productSlug]/page/actions.js { ACTIONS_MODULE0 => "[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/app/[cardSlug]/product/actions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/[cardSlug]/product/[productSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/[cardSlug]/product/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "40039e5475159c6d152f6658179feab0b3344d8bcc": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$product$2f5b$productSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40039e5475159c6d152f6658179feab0b3344d8bcc"]),
    "400897f4fcf08b6ff241e55ca68679f59b009f833f": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$product$2f5b$productSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["400897f4fcf08b6ff241e55ca68679f59b009f833f"]),
    "4090bf49bde90611f206004fcfecc276d0bd89ff93": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$product$2f5b$productSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["4090bf49bde90611f206004fcfecc276d0bd89ff93"]),
    "604da278527a3b3ebc839ac04faa50dcf6b1fc354d": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$product$2f5b$productSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["604da278527a3b3ebc839ac04faa50dcf6b1fc354d"]),
    "6086dbe3b7d9dde97a2afcbc0842cce29d93604d12": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$product$2f5b$productSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["6086dbe3b7d9dde97a2afcbc0842cce29d93604d12"]),
    "60d4c90f1bb7d3950939f7f34a7e2c8dda1f2df44e": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$product$2f5b$productSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60d4c90f1bb7d3950939f7f34a7e2c8dda1f2df44e"]),
    "60ecdd28a201e7e993d1c9af4cd41063d0f854d817": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$product$2f5b$productSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60ecdd28a201e7e993d1c9af4cd41063d0f854d817"]),
    "70a09fe73f3bf03ea5636420c66e2eb3cb262d219c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$product$2f5b$productSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["70a09fe73f3bf03ea5636420c66e2eb3cb262d219c"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$product$2f5b$productSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/[cardSlug]/product/[productSlug]/page/actions.js { ACTIONS_MODULE0 => "[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/app/[cardSlug]/product/actions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <module evaluation>');
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$product$2f5b$productSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/[cardSlug]/product/[productSlug]/page/actions.js { ACTIONS_MODULE0 => "[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/app/[cardSlug]/product/actions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <exports>');
}}),
"[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/[cardSlug]/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/[cardSlug]/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/[cardSlug]/loading.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/[cardSlug]/loading.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/[cardSlug]/product/[productSlug]/ProductDetailClient.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/[cardSlug]/product/[productSlug]/ProductDetailClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/[cardSlug]/product/[productSlug]/ProductDetailClient.tsx <module evaluation>", "default");
}}),
"[project]/app/[cardSlug]/product/[productSlug]/ProductDetailClient.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/[cardSlug]/product/[productSlug]/ProductDetailClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/[cardSlug]/product/[productSlug]/ProductDetailClient.tsx", "default");
}}),
"[project]/app/[cardSlug]/product/[productSlug]/ProductDetailClient.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f5b$productSlug$5d2f$ProductDetailClient$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/[cardSlug]/product/[productSlug]/ProductDetailClient.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f5b$productSlug$5d2f$ProductDetailClient$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/app/[cardSlug]/product/[productSlug]/ProductDetailClient.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f5b$productSlug$5d2f$ProductDetailClient$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/app/[cardSlug]/product/components/OfflineProductMessage.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/[cardSlug]/product/components/OfflineProductMessage.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/[cardSlug]/product/components/OfflineProductMessage.tsx <module evaluation>", "default");
}}),
"[project]/app/[cardSlug]/product/components/OfflineProductMessage.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/[cardSlug]/product/components/OfflineProductMessage.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/[cardSlug]/product/components/OfflineProductMessage.tsx", "default");
}}),
"[project]/app/[cardSlug]/product/components/OfflineProductMessage.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$components$2f$OfflineProductMessage$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/[cardSlug]/product/components/OfflineProductMessage.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$components$2f$OfflineProductMessage$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/app/[cardSlug]/product/components/OfflineProductMessage.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$components$2f$OfflineProductMessage$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/app/[cardSlug]/product/[productSlug]/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ProductPage),
    "generateMetadata": (()=>generateMetadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/[cardSlug]/product/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f5b$productSlug$5d2f$ProductDetailClient$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/[cardSlug]/product/[productSlug]/ProductDetailClient.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$components$2f$OfflineProductMessage$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/[cardSlug]/product/components/OfflineProductMessage.tsx [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
// Helper function to determine user plan
const getUserPlan = (profile)=>{
    // Simply return the plan_id from the subscription data
    switch(profile.plan_id){
        case "free":
            return "free";
        case "growth":
            return "growth";
        case "pro":
            return "pro";
        case "enterprise":
            return "enterprise";
        case "basic":
            return "basic";
        default:
            return "free"; // Default to free if no plan_id specified
    }
};
// Helper function to determine if platform ads should be shown
const shouldShowPlatformAds = ()=>{
    // Show platform ads for all plans - Pro/Enterprise users can override with their own custom ads
    return true; // Always show platform ads as fallback
};
async function ProductPage({ params }) {
    const { cardSlug, productSlug } = await params;
    // Fetch the business profile
    const { data: businessProfile, error: profileError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSecureBusinessProfileBySlug"])(cardSlug);
    if (profileError || !businessProfile) {
        console.error(`Error fetching profile for slug ${cardSlug}:`, profileError);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["notFound"])();
    }
    // Check if the profile is online
    if (businessProfile.status !== "online") {
        console.log(`Business profile ${cardSlug} is not online (status: ${businessProfile.status}).`);
        // Show offline message instead of 404
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$components$2f$OfflineProductMessage$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
            fileName: "[project]/app/[cardSlug]/product/[productSlug]/page.tsx",
            lineNumber: 66,
            columnNumber: 12
        }, this);
    }
    // Fetch the product with variants by slug
    const { data: productWithVariants } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getProductWithVariantsBySlug"])(productSlug, businessProfile.id);
    if (!productWithVariants) {
        // For non-existent products, just use notFound() without logging an error
        // This is an expected case, not an error condition
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["notFound"])();
    }
    // Ensure productWithVariants is not null before accessing its properties
    const product = productWithVariants;
    // Fetch business details with contact information
    const { data: businessDetails } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessWithContactInfo"])(businessProfile.id);
    // Fetch more products from the same business
    const { data: moreBusinessProducts } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getMoreProductsFromBusiness"])(businessProfile.id, product.id);
    // Fetch products from other businesses
    const { data: otherBusinessProducts } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getProductsFromOtherBusinesses"])(businessProfile.id);
    // Ensure arrays are properly initialized to prevent "Cannot read properties of undefined (reading 'length')" error
    const safeBusinessProducts = moreBusinessProducts || [];
    const safeOtherBusinessProducts = otherBusinessProducts || [];
    // Determine user plan
    const _userPlan = getUserPlan(businessProfile);
    let topAdData = null;
    // Fetch platform ads for all businesses (Pro/Enterprise can override with their own custom ads)
    if (shouldShowPlatformAds()) {
        try {
            const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
            // First, check if the custom_ad_targets table exists (for backward compatibility)
            const { count, error: tableCheckError } = await supabase.from("custom_ad_targets").select("*", {
                count: "exact",
                head: true
            });
            // If the table exists and migration has been applied
            if (count !== null && !tableCheckError) {
                // Use the get_ad_for_pincode function to find the appropriate ad
                const pincode = businessProfile.pincode || "999999"; // Use a dummy pincode if none provided
                const { data: adData, error: adError } = await supabase.rpc("get_ad_for_pincode", {
                    target_pincode: pincode
                });
                if (adData && adData.length > 0) {
                    // Found an ad (either pincode-specific or global)
                    topAdData = {
                        type: "custom",
                        imageUrl: adData[0].ad_image_url,
                        linkUrl: adData[0].ad_link_url
                    };
                } else {
                    // No custom ads found or error occurred
                    if (adError) console.error(`Error fetching ad for pincode ${pincode}:`, adError);
                    topAdData = null; // Show placeholder when no custom ads are available
                }
            } else {
                // Fallback to old approach if migration hasn't been applied yet
                if (businessProfile.pincode) {
                    const { data: customAd } = await supabase.from("custom_ads").select("ad_image_url, ad_link_url").eq("is_active", true).or(`targeting_locations.eq.'"global"',targeting_locations.cs.'["${businessProfile.pincode}"]'`).order("created_at", {
                        ascending: false
                    }).limit(1).maybeSingle();
                    if (customAd) {
                        topAdData = {
                            type: "custom",
                            imageUrl: customAd.ad_image_url,
                            linkUrl: customAd.ad_link_url
                        };
                    } else {
                        // No matching custom ad found
                        topAdData = null;
                    }
                } else {
                    // If business has no pincode, try to find global ads
                    const { data: globalAd } = await supabase.from("custom_ads").select("ad_image_url, ad_link_url").eq("is_active", true).eq("targeting_locations", '"global"').order("created_at", {
                        ascending: false
                    }).limit(1).maybeSingle();
                    if (globalAd) {
                        topAdData = {
                            type: "custom",
                            imageUrl: globalAd.ad_image_url,
                            linkUrl: globalAd.ad_link_url
                        };
                    } else {
                        topAdData = null;
                    }
                }
            }
        } catch (adFetchError) {
            console.error(`Error fetching custom ad:`, adFetchError);
            topAdData = null; // fallback on error
        }
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f5b$productSlug$5d2f$ProductDetailClient$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
        product: productWithVariants,
        variants: productWithVariants.variants || [],
        businessSlug: cardSlug,
        businessName: businessDetails?.business_name || businessProfile.business_name || "",
        whatsappNumber: businessDetails?.whatsapp_number || null,
        phoneNumber: businessDetails?.phone || null,
        businessProducts: safeBusinessProducts,
        otherBusinessProducts: safeOtherBusinessProducts,
        topAdData: topAdData,
        businessCustomAd: businessProfile.custom_ads,
        userPlan: _userPlan
    }, void 0, false, {
        fileName: "[project]/app/[cardSlug]/product/[productSlug]/page.tsx",
        lineNumber: 193,
        columnNumber: 5
    }, this);
}
async function generateMetadata({ params }) {
    const { cardSlug, productSlug } = await params;
    const siteUrl = ("TURBOPACK compile-time value", "http://localhost:3000") || "https://dukancard.in";
    const pageUrl = `${siteUrl}/${cardSlug}/product/${productSlug}`;
    // Fetch the business profile
    const { data: businessProfile, error: profileError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSecureBusinessProfileBySlug"])(cardSlug);
    if (profileError || !businessProfile) {
        // Use notFound() to trigger the 404 page for non-existent business slugs
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["notFound"])();
    }
    // Fetch the product with variants for better SEO
    const { data: productWithVariants } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getProductWithVariantsBySlug"])(productSlug, businessProfile.id);
    // Use the product with variants data
    const product = productWithVariants;
    // Fetch business details with contact information
    const { data: businessDetails } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$product$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessWithContactInfo"])(businessProfile.id);
    if (!product) {
        // Use notFound() to trigger the 404 page for non-existent product slugs
        // This is an expected case, not an error condition
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["notFound"])();
    }
    const businessName = businessDetails?.business_name || businessProfile.business_name || "Business";
    const productName = product.name || "Product";
    // Enhanced description with variant information
    let productDescription = product.description || `${productName} by ${businessName}`;
    if (product && product.variants && product.variants.length > 0) {
        const variantTypes = new Set();
        product.variants.forEach((variant)=>{
            Object.keys(variant.variant_values || {}).forEach((type)=>variantTypes.add(type));
        });
        if (variantTypes.size > 0) {
            const variantTypesText = Array.from(variantTypes).join(", ");
            productDescription += ` Available in multiple ${variantTypesText} options.`;
        }
    }
    return {
        title: `${productName} | ${businessName}`,
        description: productDescription.substring(0, 160),
        openGraph: {
            title: `${productName} | ${businessName}`,
            description: productDescription.substring(0, 160),
            url: pageUrl,
            images: product.image_url ? [
                {
                    url: product.image_url,
                    width: 1200,
                    height: 630,
                    alt: productName
                }
            ] : undefined,
            type: "website"
        },
        twitter: {
            card: "summary_large_image",
            title: `${productName} | ${businessName}`,
            description: productDescription.substring(0, 160),
            images: product.image_url ? [
                product.image_url
            ] : undefined
        }
    };
}
}}),
"[project]/app/[cardSlug]/product/[productSlug]/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/[cardSlug]/product/[productSlug]/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__1adaf3cb._.js.map