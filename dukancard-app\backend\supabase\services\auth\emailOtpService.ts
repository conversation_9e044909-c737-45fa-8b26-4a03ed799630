import { supabase } from '@/lib/supabase';
import { sendEmailOTP as sendOTP, verifyEmailOTP as verifyOTP } from '@/src/config/supabase/services/sharedService';

export interface OtpResponse {
  success: boolean;
  message: string;
  error?: any;
}

/**
 * Send OTP to email address
 */
export async function sendEmailOTP(email: string): Promise<OtpResponse> {
  try {
    const result = await sendOTP(email);

    if (!result.success) {
      // Handle specific error cases
      if (result.message && result.message.toLowerCase().includes('rate limit')) {
        return {
          success: false,
          message: 'Too many requests. Please wait a moment before trying again.',
        };
      }
      if (result.message && result.message.toLowerCase().includes('invalid email')) {
        return {
          success: false,
          message: 'Please enter a valid email address.',
        };
      }
      return {
        success: false,
        message: result.message || 'Unable to send verification code. Please try again.',
      };
    }

    return {
      success: true,
      message: 'OTP sent to your email address. Please check your inbox.',
    };
  } catch (err) {
    console.error('Unexpected error sending OTP:', err);
    return {
      success: false,
      message: 'An unexpected error occurred. Please try again.',
    };
  }
}

/**
 * Verify OTP and sign in
 */
export async function verifyEmailOTP(email: string, otp: string): Promise<OtpResponse> {
  try {
    const result = await verifyOTP(email, otp);

    if (!result.success) {
      // Handle specific error cases
      if (result.message && (result.message.toLowerCase().includes('invalid') || result.message.toLowerCase().includes('expired'))) {
        return {
          success: false,
          message: 'Invalid or expired OTP. Please try again.',
        };
      }
      if (result.message && result.message.toLowerCase().includes('too many')) {
        return {
          success: false,
          message: 'Too many verification attempts. Please wait before trying again.',
        };
      }
      return {
        success: false,
        message: result.message || 'Unable to verify code. Please try again.',
      };
    }

    return {
      success: true,
      message: 'Successfully signed in!',
    };
  } catch (err) {
    if (err instanceof Error && err.message.includes('Network')) {
      return {
        success: false,
        message: 'A network error occurred. Please check your connection and try again.',
      };
    }
  }
  return {
    success: false,
    message: 'An unexpected error occurred. Please try again.',
  };
}

/**
 * Validate email format
 */
export function validateEmail(email: string): { isValid: boolean; message?: string } {
  if (!email) {
    return { isValid: false, message: 'Email is required' };
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return { isValid: false, message: 'Please enter a valid email address' };
  }
  
  return { isValid: true };
}

/**
 * Validate OTP format
 */
export function validateOTP(otp: string): { isValid: boolean; message?: string } {
  if (!otp) {
    return { isValid: false, message: 'OTP is required' };
  }
  
  if (otp.length !== 6) {
    return { isValid: false, message: 'OTP must be 6 digits' };
  }
  
  if (!/^\d{6}$/.test(otp)) {
    return { isValid: false, message: 'OTP must contain only numbers' };
  }
  
  return { isValid: true };
}
