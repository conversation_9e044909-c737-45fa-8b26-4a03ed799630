
import { supabase } from "@/src/config/supabase";
import { Database, Tables } from "@/src/types/supabase";
import { COLUMNS, TABLES } from "../constants";

/**
 * Updates the user's avatar URL in the customer_profiles table.
 * @param userId The ID of the user.
 * @param avatarUrl The public URL of the uploaded avatar.
 * @returns Promise<void>
 */
export const updateCustomerAvatar = async (userId: string, avatarUrl: string): Promise<void> => {
  const { error } = await supabase
    .from(TABLES.CUSTOMER_PROFILES)
    .update({ [COLUMNS.AVATAR_URL]: avatarUrl })
    .eq(COLUMNS.ID, userId);

  if (error) {
    throw error;
  }
};

/**
 * Fetches the customer profile for a given user ID.
 * @param userId The ID of the user.
 * @returns Promise<{ data: Tables<'customer_profiles'> | null, error: Error | null }> - The customer profile data or an error.
 */
export const getCustomerProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from(TABLES.CUSTOMER_PROFILES)
    .select("*")
    .eq(COLUMNS.ID, userId)
    .single();

  return { data, error };
};

/**
 * Upserts a customer profile.
 * @param profileData The profile data to upsert.
 * @returns Promise<{ data: Tables<'customer_profiles'>[] | null, error: Error | null }>
 */
export const upsertCustomerProfile = async (profileData: Database['public']['Tables']['customer_profiles']['Insert']) => {
  const { data, error } = await supabase
    .from(TABLES.CUSTOMER_PROFILES)
    .upsert(profileData)
    .select();

  return { data, error };
};

/**
 * Fetches location data from the customer_profiles table for a given user ID.
 * @param userId The ID of the user.
 * @returns Promise<{ data: Pick<Tables<'customer_profiles'>, 'city_slug' | 'state_slug' | 'locality_slug' | 'pincode'> | null, error: Error | null }>
 */
export const getCustomerProfileLocation = async (userId: string) => {
  const { data, error } = await supabase
    .from(TABLES.CUSTOMER_PROFILES)
    .select(`${COLUMNS.CITY_SLUG}, ${COLUMNS.STATE_SLUG}, ${COLUMNS.LOCALITY_SLUG}, ${COLUMNS.PINCODE}`)
    .eq(COLUMNS.ID, userId)
    .single();

  return { data, error };
};

/**
 * Checks if a customer profile exists for a given user ID.
 * @param userId The ID of the user.
 * @returns Promise<boolean> - True if a customer profile exists, false otherwise.
 */
export const hasCustomerProfile = async (userId: string): Promise<boolean> => {
  const { data, error } = await supabase
    .from(TABLES.CUSTOMER_PROFILES)
    .select(COLUMNS.ID)
    .eq(COLUMNS.ID, userId)
    .single();
  return !error && !!data;
};

/**
 * Fetches customer profile data for validation purposes.
 * @param userId The ID of the user.
 * @returns Promise<{ data: Pick<Tables<'customer_profiles'>, 'name' | 'pincode' | 'state' | 'city' | 'locality' | 'address' | 'latitude' | 'longitude'> | null, error: Error | null }>
 */
export const getCustomerProfileForValidation = async (userId: string) => {
  return await supabase
    .from(TABLES.CUSTOMER_PROFILES)
    .select(`${COLUMNS.NAME}, ${COLUMNS.PINCODE}, ${COLUMNS.STATE}, ${COLUMNS.CITY}, ${COLUMNS.LOCALITY}, ${COLUMNS.ADDRESS_LINE}, ${COLUMNS.LATITUDE}, ${COLUMNS.LONGITUDE}`)
    .eq(COLUMNS.ID, userId)
    .single();
};

/**
 * Fetches customer activities from the 'business_activities' table.
 * @param userId The ID of the user whose activities are to be fetched.
 * @param from The starting index for pagination.
 * @param to The ending index for pagination.
 * @returns A Promise that resolves to an object containing the activities data and count, or an error.
 */
export const fetchCustomerActivities = async (userId: string, from: number, to: number) => {
  return await supabase
    .from(TABLES.BUSINESS_ACTIVITIES)
    .select('*', { count: 'exact' })
    .eq(COLUMNS.USER_ID, userId)
    .order(COLUMNS.CREATED_AT, { ascending: false })
    .range(from, to);
};

/**
 * Fetches business profiles for a given list of IDs.
 * @param businessProfileIds An array of business profile IDs.
 * @returns A Promise that resolves to an object containing the business profiles data or an error.
 */
export const fetchBusinessProfilesByIds = async (businessProfileIds: string[]) => {
  return await supabase
    .from(TABLES.BUSINESS_PROFILES)
    .select(`${COLUMNS.ID}, ${COLUMNS.BUSINESS_NAME}, ${COLUMNS.BUSINESS_SLUG}, ${COLUMNS.LOGO_URL}`)
    .in(COLUMNS.ID, businessProfileIds);
};

/**
 * Fetches multiple user profiles (customer and/or business) in a batch.
 * This function is designed for efficient retrieval of profile data for a list of user IDs,
 * typically used for enriching activity feeds or other displays.
 * It leverages public Row Level Security (RLS) policies for read access.
 * @param userIds An array of user IDs for whom to fetch profiles.
 * @param types An array specifying which types of profiles to fetch ('customer' and/or 'business').
 * @returns A Promise that resolves to a ServiceResult object containing:
 *   - `success`: A boolean indicating if the operation was successful.
 *   - `data`: An object containing `customerProfiles` and `businessProfiles` arrays, or undefined if `success` is false.
 *   - `error`: A string containing an error message if the operation failed, otherwise undefined.
 */
export async function fetchBatchProfiles(
  userIds: string[],
  types: ('customer' | 'business')[]
): Promise<{ success: boolean; data?: { customerProfiles: any[]; businessProfiles: any[] }; error?: string }> {
  try {
    // Validate input
    if (!Array.isArray(userIds) || userIds.length === 0) {
      return { 
        success: true, 
        data: { customerProfiles: [], businessProfiles: [] } 
      };
    }

    if (!Array.isArray(types) || types.length === 0) {
      return { 
        success: true, 
        data: { customerProfiles: [], businessProfiles: [] } 
      };
    }

    const response: { customerProfiles: any[]; businessProfiles: any[] } = {
      customerProfiles: [],
      businessProfiles: [],
    };

    // Fetch customer profiles if requested
    if (types.includes('customer')) {
      const { data: customerProfiles, error: customerError } = await supabase
        .from(TABLES.CUSTOMER_PROFILES_PUBLIC)
        .select(`${COLUMNS.ID}, ${COLUMNS.NAME}, ${COLUMNS.AVATAR_URL}`)
        .in(COLUMNS.ID, userIds);

      if (customerError) {
        console.error('[BATCH_PROFILE_SERVICE] Error fetching customer profiles:', customerError);
        return { success: false, error: 'Failed to fetch customer profiles' };
      }

      response.customerProfiles = customerProfiles || [];
    }

    // Fetch business profiles if requested
    if (types.includes('business')) {
      const { data: businessProfiles, error: businessError } = await supabase
        .from(TABLES.BUSINESS_PROFILES)
        .select(`${COLUMNS.ID}, ${COLUMNS.BUSINESS_NAME}, ${COLUMNS.BUSINESS_SLUG}, ${COLUMNS.LOGO_URL}`)
        .in(COLUMNS.ID, userIds);

      if (businessError) {
        console.error('[BATCH_PROFILE_SERVICE] Error fetching business profiles:', businessError);
        return { success: false, error: 'Failed to fetch business profiles' };
      }

      response.businessProfiles = businessProfiles || [];
    }

    return { success: true, data: response };
  } catch (error) {
    console.error('[BATCH_PROFILE_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Fetches customer profiles by their IDs.
 * This function retrieves public customer profile information.
 * @param userIds An array of user IDs for whom to fetch customer profiles.
 * @returns A Promise that resolves to a ServiceResult object containing:
 *   - `success`: A boolean indicating if the operation was successful.
 *   - `data`: An array of customer profile objects, or undefined if `success` is false.
 *   - `error`: A string containing an error message if the operation failed, otherwise undefined.
 */
export async function fetchCustomerProfiles(
  userIds: string[]
): Promise<{ success: boolean; data?: any[]; error?: string }> {
  try {
    if (!Array.isArray(userIds) || userIds.length === 0) {
      return { success: true, data: [] };
    }

    const { data: profiles, error } = await supabase
      .from(TABLES.CUSTOMER_PROFILES_PUBLIC)
      .select(`${COLUMNS.ID}, ${COLUMNS.NAME}, ${COLUMNS.AVATAR_URL}`)
      .in(COLUMNS.ID, userIds);

    if (error) {
      console.error('[BATCH_PROFILE_SERVICE] Error fetching customer profiles:', error);
      return { success: false, error: 'Failed to fetch customer profiles' };
    }

    return { success: true, data: profiles || [] };
  } catch (error) {
    console.error('[BATCH_PROFILE_SERVICE] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}