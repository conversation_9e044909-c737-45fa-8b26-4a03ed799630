# Supabase Direct Calls Analysis Summary

## Overview
This analysis identifies all files in both `dukancard` and `dukancard-app` projects that contain **direct Supabase calls** (using `supabase.` pattern), excluding the centralized service files where you're consolidating Supabase functionality.

## Refined Search Pattern
The script now searches for:
- `supabase.from()`, `supabase.rpc()`, `supabase.auth`, `supabase.storage`, etc.
- Multi-line patterns where `supabase` is on one line and `.from()` etc. is on the next line
- This provides a much more accurate list of files that need refactoring

## Results

### dukancard Project
- **Total files with direct Supabase calls**: 139 files (refined from 246)
- **Exclusions applied**:
  - `lib/supabase/services/businessService.ts`
  - `lib/supabase/services/customerService.ts`
  - `lib/supabase/services/sharedService.ts`
  - Commented lines (starting with //, /*, *)

### dukancard-app Project
- **Total files with direct Supabase calls**: 81 files (refined from 192)
- **Exclusions applied**:
  - `src/config/supabase/services/businessService.ts`
  - `src/config/supabase/services/customerService.ts`
  - `src/config/supabase/services/sharedService.ts`
  - Commented lines (starting with //, /*, *)

## File Outputs
- `dukancard-direct-supabase-calls.txt` - Detailed list for dukancard project (1,112 lines)
- `dukancard-app-direct-supabase-calls.txt` - Detailed list for dukancard-app project (883 lines)

## Sample Findings

### dukancard Project Examples:
- `supabase.auth.getUser()` calls in dashboard pages
- `supabase.from("business_profiles")` queries in components
- `supabase.from("payment_subscriptions")` calls in plan pages
- `supabase.from("products_services")` queries in product management

### dukancard-app Project Examples:
- `supabase.from('subscriptions')` calls in customer dashboard
- `supabase.auth.signOut()` in onboarding layout
- `supabase.from("products_services")` queries in product screens
- `supabase.rpc()` calls in backend services

## Common Patterns Found:
- **Auth calls**: `supabase.auth.getUser()`, `supabase.auth.signOut()`
- **Database queries**: `supabase.from(table_name).select()/.insert()/.update()/.delete()`
- **RPC calls**: `supabase.rpc(function_name, params)`
- **Multi-line queries**: Where `supabase` and `.from()` are on separate lines

## Verification
The script has been verified to:
✅ Correctly exclude the specified service files
✅ Skip commented Supabase references
✅ Find direct Supabase calls using `supabase.` pattern
✅ Handle multi-line patterns (supabase on one line, .from() on next)
✅ Capture line numbers and content for each reference
✅ Generate comprehensive reports for both projects

## Next Steps
These **139 + 81 = 220 files** represent the exact files that need refactoring to complete the Supabase centralization. Each file contains direct Supabase calls that should be migrated to use the centralized service functions instead.
