{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/node_modules_@supabase_auth-js_dist_module_17bbb6b5._.js", "server/edge/chunks/node_modules_@upstash_redis_b3b75fae._.js", "server/edge/chunks/node_modules_a5b8fa46._.js", "server/edge/chunks/[root-of-the-server]__c2258e89._.js", "server/edge/chunks/edge-wrapper_3918d6b0.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "1TCz2iHGXTF9z/Vm+N6MDej5HZtAZG+srmqCH3mHLPg=", "__NEXT_PREVIEW_MODE_ID": "1c6c9590b411201617e77b382a8af7da", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1d8bbc27e442f2bbd8af1ad5ff48a9dd3da644d68110422ea77695446968734d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "82d2beb944305ab631b8ac4e88d1b66b38076c43e0cd1eeaafd109373902f60d"}}}, "sortedMiddleware": ["/"], "functions": {}}