### Story 6.1: Refactor All Supabase Calls in Batch 6 Files (dukancard-app)

**Status:** Draft

**Story:**
As a developer,
I want to identify and refactor all direct Supabase calls within files 46-54 from `dukancard-app-direct-supabase-calls.txt` into the appropriate centralized service files (`customerService.ts`, `businessService.ts`, or `sharedService.ts` located in `@dukancard-app/src/config/supabase/services/`),
so that all Supabase interaction logic is centralized, consistent, and easier to maintain across these files, without interfering with existing service file functions.

**Acceptance Criteria:**
1.  For each of the following files, all direct `supabase.*` calls are identified:
    *   `backend\supabase\services\storage\imageUploadService.ts`
    *   `backend\supabase\services\storage\storageService.ts`
    *   `backend\supabase\utils\businessSlugValidation.ts`
    *   `dependency-graph.json`
    *   `docs\auth-docs\GOOGLE_OAUTH_SETUP.md`
    *   `docs\auth-docs\KEYSTORE_CREDENTIALS.md`
    *   `docs\brownfield-architecture-app.md`
    *   `lib\actions\businessPosts.ts`
    *   `lib\actions\customer\profileActions.ts`
2.  Each identified `supabase.*` call is categorized as belonging to the customer domain, business domain, or a shared/general domain.
3.  For each categorized call, a corresponding function is identified or appended to the appropriate service file (`customerService.ts`, `businessService.ts`, or `sharedService.ts`).
    *   **Before appending a new function:** Confirm that no existing function within the target service file already serves the purpose of encapsulating the identified Supabase call.
    *   **If a suitable existing function is found:** Utilize that function.
    *   **If no suitable existing function is found:** A new function will be appended to the target service file. This new function will not modify any existing functions in the service file.
4.  All instances of direct `supabase.*` calls in the listed files are replaced with calls to the identified or newly appended service functions.
5.  The new or utilized service functions correctly handle potential errors from Supabase calls and return standardized responses.
6.  The refactored files correctly import and use the appropriate service functions.

**Dev Notes:**
*   Refer to `dukancard-app-direct-supabase-calls.txt` for the exact lines and files to refactor.
*   Prioritize using existing service functions if they match the required functionality.
*   Ensure proper error handling and standardized responses from the new/modified service functions.

**Testing:**
*   No specific testing is required for this refactoring effort as per PRD.

**Dev Agent Record:**
*   **Agent Model Used:** Gemini
*   **Debug Log References:**
*   **Completion Notes List:**
*   **File List:**
*   **Change Log:**
