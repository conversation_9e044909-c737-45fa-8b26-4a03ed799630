import * as Linking from 'expo-linking';
import * as WebBrowser from 'expo-web-browser';
import { signInWithGoogleOAuth, setAuthSession } from '@/src/config/supabase/services/sharedService';

export interface GoogleAuthResponse {
  success: boolean;
  message: string;
  error?: any;
}

/**
 * Enhanced Google authentication with in-app browser
 * This provides a better user experience than redirecting to external browser
 */
export async function signInWithGoogle(): Promise<GoogleAuthResponse> {
  try {
    // Configure WebBrowser for better UX
    WebBrowser.maybeCompleteAuthSession();

    // Get the OAuth URL from Supabase
    const { data, error } = await signInWithGoogleOAuth(
      Linking.createURL('/auth/callback'),
      {
        access_type: 'offline',
        prompt: 'select_account', // Force account selection modal
        approval_prompt: 'force', // Force fresh consent
      }
    );

    if (error) {
      return {
        success: false,
        message: error.message || 'Failed to initialize Google sign-in',
        error,
      };
    }

    if (!data?.url) {
      return {
        success: false,
        message: 'Failed to get Google sign-in URL',
      };
    }

    // Open the OAuth URL in an in-app browser with better styling
    const result = await WebBrowser.openAuthSessionAsync(
      data.url,
      Linking.createURL('/auth/callback'),
      {
        showTitle: true,
        showInRecents: false,
        enableBarCollapsing: true,
        // Add custom styling for better UX
        controlsColor: '#C29D5B',
        toolbarColor: '#ffffff',
        // Force fresh authentication by clearing any cached sessions
        createTask: false, // This helps prevent cached sessions
      }
    );

    if (result.type === 'success') {
      // Parse the URL to extract the session
      const url = result.url;
      const urlParams = new URLSearchParams(url.split('#')[1] || url.split('?')[1]);

      const accessToken = urlParams.get('access_token');
      const refreshToken = urlParams.get('refresh_token');

      if (accessToken) {
        // Set the session in Supabase
        const { error: sessionError } = await setAuthSession(accessToken, refreshToken || '');

        if (sessionError) {
          return {
            success: false,
            message: 'Failed to establish session',
            error: sessionError,
          };
        }

        return {
          success: true,
          message: 'Successfully signed in with Google',
        };
      }
    }

    if (result.type === 'cancel') {
      return {
        success: false,
        message: 'Google sign-in was cancelled',
      };
    }

    return {
      success: false,
      message: 'Google sign-in failed',
    };
  } catch (err) {
    console.error('Google sign-in error:', err);
    return {
      success: false,
      message: 'An unexpected error occurred during Google sign-in',
      error: err,
    };
  }
}

/**
 * Alternative Google sign-in method using deep linking
 * This is a fallback if the in-app browser method doesn't work
 */
export async function signInWithGoogleDeepLink(): Promise<GoogleAuthResponse> {
  try {
    const { error } = await signInWithGoogleOAuth(
      'dukancardapp://auth/callback',
      {
        access_type: 'offline',
        prompt: 'select_account',
      }
    );

    if (error) {
      return {
        success: false,
        message: error.message || 'Failed to initialize Google sign-in',
        error,
      };
    }

    return {
      success: true,
      message: 'Google sign-in initiated',
    };
  } catch (err) {
    console.error('Google deep link sign-in error:', err);
    return {
      success: false,
      message: 'An unexpected error occurred during Google sign-in',
      error: err,
    };
  }
}
