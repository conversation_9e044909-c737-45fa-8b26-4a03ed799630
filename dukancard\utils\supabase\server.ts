"use server"

import { createServerClient } from '@supabase/ssr';
import { SupabaseClient } from '@supabase/supabase-js';
import { cookies, headers } from 'next/headers';
import { Database } from '@/types/supabase';

export async function createClient(): Promise<SupabaseClient<Database>> {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Supabase environment variables are not set.');
  }

  // Check if we're in a test environment
  const headersList = await headers();
  const isTestEnvironment =
    process.env.NODE_ENV === 'test' ||
    process.env.PLAYWRIGHT_TESTING === 'true' ||
    headersList.get('x-playwright-testing') === 'true';

  if (isTestEnvironment) {
    // Return a mocked Supabase client for testing
    return createMockSupabaseClient(headersList) as unknown as SupabaseClient<Database>;
  }

  const cookieStore = await cookies();

  return createServerClient(
    supabaseUrl,
    supabaseAnonKey,
    {
      cookies: {
        async getAll() {
          return await cookieStore.getAll();
        },
        async setAll(cookiesToSet: any[]) {
          try {
            for (const { name, value, options } of cookiesToSet) {
              await cookieStore.set(name, value, options);
            }
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  ) as unknown as SupabaseClient<Database>;
}

type MockQueryBuilder = {
  select: (columns?: string) => MockQueryBuilder;
  eq: (column: string, value: any) => MockQueryBuilder;
  neq: (column: string, value: any) => MockQueryBuilder;
  gt: (column: string, value: any) => MockQueryBuilder;
  gte: (column: string, value: any) => MockQueryBuilder;
  lt: (column: string, value: any) => MockQueryBuilder;
  lte: (column: string, value: any) => MockQueryBuilder;
  like: (column: string, pattern: string) => MockQueryBuilder;
  ilike: (column: string, pattern: string) => MockQueryBuilder;
  is: (column: string, value: any) => MockQueryBuilder;
  in: (column: string, values: any[]) => MockQueryBuilder;
  contains: (column: string, value: any) => MockQueryBuilder;
  containedBy: (column: string, value: any) => MockQueryBuilder;
  rangeGt: (column: string, value: any) => MockQueryBuilder;
  rangeGte: (column: string, value: any) => MockQueryBuilder;
  rangeLt: (column: string, value: any) => MockQueryBuilder;
  rangeLte: (column: string, value: any) => MockQueryBuilder;
  rangeAdjacent: (column: string, value: any) => MockQueryBuilder;
  overlaps: (column: string, value: any) => MockQueryBuilder;
  textSearch: (column: string, query: string) => MockQueryBuilder;
  match: (query: Record<string, any>) => MockQueryBuilder;
  not: (column: string, operator: string, value: any) => MockQueryBuilder;
  or: (filters: string) => MockQueryBuilder;
  filter: (column: string, operator: string, value: any) => MockQueryBuilder;
  order: (column: string, options?: { ascending?: boolean; nullsFirst?: boolean }) => MockQueryBuilder;
  limit: (count: number, options?: { foreignTable?: string }) => MockQueryBuilder;
  range: (from: number, to: number, options?: { foreignTable?: string }) => MockQueryBuilder;
  abortSignal: (signal: AbortSignal) => MockQueryBuilder;
  single: () => Promise<any>;
  maybeSingle: () => Promise<any>;
  then: (callback?: any) => Promise<any>;
  data: any;
  error: any;
  count: number;
  status: number;
  statusText: string;
};

function createMockSupabaseClient(headersList: Headers) {
  const testAuthState = headersList.get('x-test-auth-state');
  const testUserType = headersList.get('x-test-user-type');
  const testHasProfile = testUserType === 'customer' || testUserType === 'business';
  const testBusinessSlug = headersList.get('x-test-business-slug');
  const testPlanId = headersList.get('x-test-plan-id') || 'free';

  return {
    auth: {
      getUser: async () => {
        if (testAuthState === 'authenticated') {
          return { data: { user: { id: 'test-user-id', email: '<EMAIL>' } }, error: null };
        }
        return { data: { user: null }, error: { message: 'Unauthorized', name: 'AuthApiError', status: 401 } };
      },
      getSession: async () => {
        if (testAuthState === 'authenticated') {
          return { data: { session: { user: { id: 'test-user-id', email: '<EMAIL>' } } }, error: null };
        }
        return { data: { session: null }, error: { message: 'Unauthorized', name: 'AuthApiError', status: 401 } };
      },
      signInWithOtp: async () => ({ data: { user: null, session: null }, error: null }),
      signOut: async () => ({ error: null }),
    },
    from: (table: string) => createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId),
  };
}

function createMockQueryBuilder(
  table: string,
  testUserType: string | null,
  testHasProfile: boolean,
  testBusinessSlug: string | null,
  testPlanId: string
): any {
  const getMockData = () => getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId);

  const createChainableMock = (data?: any): MockQueryBuilder => ({
    select: (_columns?: string) => createChainableMock(data),
    eq: (_column: string, _value: any) => createChainableMock(data),
    neq: (_column: string, _value: any) => createChainableMock(data),
    gt: (_column: string, _value: any) => createChainableMock(data),
    gte: (_column: string, _value: any) => createChainableMock(data),
    lt: (_column: string, _value: any) => createChainableMock(data),
    lte: (_column: string, _value: any) => createChainableMock(data),
    like: (_column: string, _pattern: string) => createChainableMock(data),
    ilike: (_column: string, _pattern: string) => createChainableMock(data),
    is: (_column: string, _value: any) => createChainableMock(data),
    in: (_column: string, _values: any[]) => createChainableMock(data),
    contains: (_column: string, _value: any) => createChainableMock(data),
    containedBy: (_column: string, _value: any) => createChainableMock(data),
    rangeGt: (_column: string, _value: any) => createChainableMock(data),
    rangeGte: (_column: string, _value: any) => createChainableMock(data),
    rangeLt: (_column: string, _value: any) => createChainableMock(data),
    rangeLte: (_column: string, _value: any) => createChainableMock(data),
    rangeAdjacent: (_column: string, _value: any) => createChainableMock(data),
    overlaps: (_column: string, _value: any) => createChainableMock(data),
    textSearch: (_column: string, _query: string) => createChainableMock(data),
    match: (_query: Record<string, any>) => createChainableMock(data),
    not: (_column: string, _operator: string, _value: any) => createChainableMock(data),
    or: (_filters: string) => createChainableMock(data),
    filter: (_column: string, _operator: string, _value: any) => createChainableMock(data),
    order: (_column: string, _options?: { ascending?: boolean; nullsFirst?: boolean }) => createChainableMock(data),
    limit: (_count: number, _options?: { foreignTable?: string }) => createChainableMock(data),
    range: (_from: number, _to: number, _options?: { foreignTable?: string }) => createChainableMock(data),
    abortSignal: (_signal: AbortSignal) => createChainableMock(data),
    single: async () => getMockData(),
    maybeSingle: async () => getMockData(),
    then: async (callback?: any) => {
      const result = getMockData();
      return callback ? callback(result) : result;
    },
    data: data || [],
    error: null,
    count: data ? data.length : 0,
    status: 200,
    statusText: 'OK',
  });

  return {
    select: (_columns?: string) => createChainableMock(),
    insert: (data: any | any[]) => ({
      select: (_columns?: string) => ({
        single: async () => ({
          data: Array.isArray(data) ? data[0] : data,
          error: null,
        }),
        maybeSingle: async () => ({
          data: Array.isArray(data) ? data[0] : data,
          error: null,
        }),
        then: async (_callback?: any) => {
          const result = { data: Array.isArray(data) ? data : [data], error: null };
          return _callback ? _callback(result) : result;
        },
      }),
      then: async (_callback?: any) => {
        const result = { data: Array.isArray(data) ? data : [data], error: null };
        return _callback ? _callback(result) : result;
      },
    }),
    update: (data: any) => createChainableMock(data),
    upsert: (data: any | any[]) => createChainableMock(data),
    delete: () => createChainableMock(),
    rpc: (_functionName: string, _params?: any) => createChainableMock(),
  };
}

/**
 * Helper function to get mock table data based on test state
 */
function getMockTableData(
  table: string,
  testUserType: string | null,
  testHasProfile: boolean,
  testBusinessSlug: string | null,
  testPlanId: string
) {
  if (table === 'customer_profiles') {
    const hasCustomerProfile = testHasProfile && testUserType === 'customer';
    return {
      data: hasCustomerProfile ? {
        id: 'test-user-id',
        name: 'Test Customer',
        avatar_url: null,
        phone: '+1234567890',
        email: '<EMAIL>',
        address: 'Test Address',
        city: 'Test City',
        state: 'Test State',
        pincode: '123456'
      } : null,
      error: null
    };
  }

  if (table === 'business_profiles') {
    const hasBusinessProfile = testHasProfile && testUserType === 'business';
    return {
      data: hasBusinessProfile ? {
        id: 'test-user-id',
        business_slug: testBusinessSlug || null,
        trial_end_date: null,
        has_active_subscription: true,
        business_name: 'Test Business',
        city_slug: 'test-city',
        state_slug: 'test-state',
        locality_slug: 'test-locality',
        pincode: '123456',
        business_description: 'Test business description',
        business_category: 'retail',
        phone: '+1234567890',
        email: '<EMAIL>',
        website: 'https://testbusiness.com'
      } : null,
      error: null
    };
  }

  if (table === 'payment_subscriptions') {
    return {
      data: testUserType === 'business' ? {
        id: 'test-subscription-id',
        plan_id: testPlanId,
        business_profile_id: 'test-user-id',
        status: 'active',
        created_at: '2024-01-01T00:00:00Z'
      } : null,
      error: null
    };
  }

  if (table === 'products') {
    return {
      data: testUserType === 'business' ? [
        {
          id: 'test-product-1',
          name: 'Test Product 1',
          price: 100,
          business_profile_id: 'test-user-id',
          available: true
        },
        {
          id: 'test-product-2',
          name: 'Test Product 2',
          price: 200,
          business_profile_id: 'test-user-id',
          available: false
        }
      ] : [],
      error: null
    };
  }

  // Default return for unknown tables
  return { data: null, error: null };
}

