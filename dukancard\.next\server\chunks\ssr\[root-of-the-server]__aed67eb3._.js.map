{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/supabase/constants.ts"], "sourcesContent": ["// lib/supabase/constants.ts\r\n\r\nexport const TABLES = {\r\n  BLOGS: \"blogs\",\r\n  BUSINESS_ACTIVITIES: \"business_activities\",\r\n  BUSINESS_PROFILES: \"business_profiles\",\r\n  CARD_VISITS: \"card_visits\",\r\n  CUSTOM_ADS: \"custom_ads\",\r\n  CUSTOM_AD_TARGETS: \"custom_ad_targets\",\r\n  CUSTOMER_POSTS: \"customer_posts\",\r\n  CUSTOMER_PROFILES: \"customer_profiles\",\r\n  CUSTOMER_PROFILES_PUBLIC: \"customer_profiles_public\",\r\n  LIKES: \"likes\",\r\n  PAYMENT_SUBSCRIPTIONS: \"payment_subscriptions\",\r\n  PINCODES: \"pincodes\",\r\n  PRODUCTS_SERVICES: \"products_services\",\r\n  PRODUCT_VARIANTS: \"product_variants\",\r\n  PUBLIC_SUBSCRIPTION_STATUS: \"public_subscription_status\",\r\n  STORAGE_CLEANUP_CONFIG: \"storage_cleanup_config\",\r\n  STORAGE_CLEANUP_PROGRESS: \"storage_cleanup_progress\",\r\n  SUBSCRIPTIONS: \"subscriptions\",\r\n  SYSTEM_ALERTS: \"system_alerts\",\r\n  UNIFIED_POSTS: \"unified_posts\",\r\n  RATINGS_REVIEWS: \"ratings_reviews\",\r\n} as const;\r\n\r\nexport const BUCKETS = {\r\n  BUSINESS: \"business\",\r\n  CUSTOMERS: \"customers\",\r\n} as const;\r\n\r\nexport const COLUMNS = {\r\n  ID: \"id\",\r\n  CREATED_AT: \"created_at\",\r\n  UPDATED_AT: \"updated_at\",\r\n  NAME: \"name\",\r\n  EMAIL: \"email\",\r\n  PHONE: \"phone\",\r\n  CITY: \"city\",\r\n  STATE: \"state\",\r\n  PINCODE: \"pincode\",\r\n  PLAN_ID: \"plan_id\",\r\n  LOCALITY: \"locality\",\r\n  CITY_SLUG: \"city_slug\",\r\n  STATE_SLUG: \"state_slug\",\r\n  LOCALITY_SLUG: \"locality_slug\",\r\n  OFFICE_NAME: \"office_name\",\r\n  AVATAR_URL: \"avatar_url\",\r\n  LOGO_URL: \"logo_url\",\r\n  IMAGE_URL: \"image_url\",\r\n  IMAGES: \"images\",\r\n  SLUG: \"slug\",\r\n  STATUS: \"status\",\r\n  CONTENT: \"content\",\r\n  GALLERY: \"gallery\",\r\n  DESCRIPTION: \"description\",\r\n  TITLE: \"title\",\r\n  USER_ID: \"user_id\",\r\n  BUSINESS_ID: \"business_id\",\r\n  BUSINESS_NAME: \"business_name\",\r\n  BUSINESS_SLUG: \"business_slug\",\r\n  PRODUCT_ID: \"product_id\",\r\n  LATITUDE: \"latitude\",\r\n  LONGITUDE: \"longitude\",\r\n  PRODUCT_TYPE: \"product_type\",\r\n  BASE_PRICE: \"base_price\",\r\n  DISCOUNTED_PRICE: \"discounted_price\",\r\n  IS_AVAILABLE: \"is_available\",\r\n  CUSTOM_AD_TARGETS: \"custom_ad_targets\",\r\n  AD_IMAGE_URL: \"ad_image_url\",\r\n  AD_LINK_URL: \"ad_link_url\",\r\n  IS_ACTIVE: \"is_active\",\r\n  TARGETING_LOCATIONS: \"targeting_locations\",\r\n  RATINGS_REVIEWS: \"ratings_reviews\",\r\n  BUSINESS_PROFILE_ID: \"business_profile_id\",\r\n  RAZORPAY_SUBSCRIPTION_ID: \"razorpay_subscription_id\",\r\n  SUBSCRIPTION_STATUS: \"subscription_status\",\r\n  TOTAL_LIKES: \"total_likes\",\r\n  TOTAL_SUBSCRIPTIONS: \"total_subscriptions\",\r\n  AVERAGE_RATING: \"average_rating\",\r\n  TOTAL_VISITS: \"total_visits\",\r\n  TODAY_VISITS: \"today_visits\",\r\n  YESTERDAY_VISITS: \"yesterday_visits\",\r\n  VISITS_7_DAYS: \"visits_7_days\",\r\n  VISITS_30_DAYS: \"visits_30_days\",\r\n  CUSTOM_ADS: \"custom_ads\",\r\n  CUSTOM_BRANDING: \"custom_branding\",\r\n  CONTACT_EMAIL: \"contact_email\",\r\n  HAS_ACTIVE_SUBSCRIPTION: \"has_active_subscription\",\r\n  TRIAL_END_DATE: \"trial_end_date\",\r\n  MEMBER_NAME: \"member_name\",\r\n  ADDRESS_LINE: \"address_line\",\r\n  INSTAGRAM_URL: \"instagram_url\",\r\n  FACEBOOK_URL: \"facebook_url\",\r\n  WHATSAPP_NUMBER: \"whatsapp_number\",\r\n  ABOUT_BIO: \"about_bio\",\r\n  THEME_COLOR: \"theme_color\",\r\n  DELIVERY_INFO: \"delivery_info\",\r\n  BUSINESS_HOURS: \"business_hours\",\r\n  BUSINESS_CATEGORY: \"business_category\",\r\n  ESTABLISHED_YEAR: \"established_year\",\r\n  VARIANT_VALUES: \"variant_values\",\r\n  VARIANT_NAME: \"variant_name\",\r\n  FEATURED_IMAGE_INDEX: \"featured_image_index\",\r\n  STATE_NAME: \"StateName\",\r\n  DIVISION_NAME: \"DivisionName\",\r\n} as const;\r\n\r\nexport const RPC_FUNCTIONS = {\r\n  GET_DAILY_UNIQUE_VISIT_TREND: \"get_daily_unique_visit_trend\",\r\n  GET_HOURLY_UNIQUE_VISIT_TREND: \"get_hourly_unique_visit_trend\",\r\n  GET_MONTHLY_UNIQUE_VISITS: \"get_monthly_unique_visits\",\r\n  GET_MONTHLY_UNIQUE_VISIT_TREND: \"get_monthly_unique_visit_trend\",\r\n  GET_AVAILABLE_YEARS_FOR_MONTHLY_METRICS: \"get_available_years_for_monthly_metrics\",\r\n  GET_TOTAL_UNIQUE_VISITS: \"get_total_unique_visits\",\r\n  GET_AD_FOR_PINCODE: \"get_ad_for_pincode\",\r\n  GET_PRODUCT_WITH_VARIANTS: \"get_product_with_variants\",\r\n  GET_AVAILABLE_PRODUCT_VARIANTS: \"get_available_product_variants\",\r\n  GET_BUSINESS_VARIANT_STATS: \"get_business_variant_stats\",\r\n  IS_VARIANT_COMBINATION_UNIQUE: \"is_variant_combination_unique\",\r\n} as const;\r\n\r\nexport const RPC_PARAMS = {\r\n    BUSINESS_ID: \"business_id\",\r\n    START_DATE: \"start_date\",\r\n    END_DATE: \"end_date\",\r\n    TARGET_DATE: \"target_date\",\r\n    TARGET_YEAR: \"target_year\",\r\n    TARGET_MONTH: \"target_month\",\r\n    START_YEAR: \"start_year\",\r\n    START_MONTH: \"start_month\",\r\n    END_YEAR: \"end_year\",\r\n    END_MONTH: \"end_month\",\r\n    TARGET_PINCODE: \"target_pincode\",\r\n} as const;\r\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;;;;;AAErB,MAAM,SAAS;IACpB,OAAO;IACP,qBAAqB;IACrB,mBAAmB;IACnB,aAAa;IACb,YAAY;IACZ,mBAAmB;IACnB,gBAAgB;IAChB,mBAAmB;IACnB,0BAA0B;IAC1B,OAAO;IACP,uBAAuB;IACvB,UAAU;IACV,mBAAmB;IACnB,kBAAkB;IAClB,4BAA4B;IAC5B,wBAAwB;IACxB,0BAA0B;IAC1B,eAAe;IACf,eAAe;IACf,eAAe;IACf,iBAAiB;AACnB;AAEO,MAAM,UAAU;IACrB,UAAU;IACV,WAAW;AACb;AAEO,MAAM,UAAU;IACrB,IAAI;IACJ,YAAY;IACZ,YAAY;IACZ,MAAM;IACN,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,SAAS;IACT,SAAS;IACT,UAAU;IACV,WAAW;IACX,YAAY;IACZ,eAAe;IACf,aAAa;IACb,YAAY;IACZ,UAAU;IACV,WAAW;IACX,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,SAAS;IACT,SAAS;IACT,aAAa;IACb,OAAO;IACP,SAAS;IACT,aAAa;IACb,eAAe;IACf,eAAe;IACf,YAAY;IACZ,UAAU;IACV,WAAW;IACX,cAAc;IACd,YAAY;IACZ,kBAAkB;IAClB,cAAc;IACd,mBAAmB;IACnB,cAAc;IACd,aAAa;IACb,WAAW;IACX,qBAAqB;IACrB,iBAAiB;IACjB,qBAAqB;IACrB,0BAA0B;IAC1B,qBAAqB;IACrB,aAAa;IACb,qBAAqB;IACrB,gBAAgB;IAChB,cAAc;IACd,cAAc;IACd,kBAAkB;IAClB,eAAe;IACf,gBAAgB;IAChB,YAAY;IACZ,iBAAiB;IACjB,eAAe;IACf,yBAAyB;IACzB,gBAAgB;IAChB,aAAa;IACb,cAAc;IACd,eAAe;IACf,cAAc;IACd,iBAAiB;IACjB,WAAW;IACX,aAAa;IACb,eAAe;IACf,gBAAgB;IAChB,mBAAmB;IACnB,kBAAkB;IAClB,gBAAgB;IAChB,cAAc;IACd,sBAAsB;IACtB,YAAY;IACZ,eAAe;AACjB;AAEO,MAAM,gBAAgB;IAC3B,8BAA8B;IAC9B,+BAA+B;IAC/B,2BAA2B;IAC3B,gCAAgC;IAChC,yCAAyC;IACzC,yBAAyB;IACzB,oBAAoB;IACpB,2BAA2B;IAC3B,gCAAgC;IAChC,4BAA4B;IAC5B,+BAA+B;AACjC;AAEO,MAAM,aAAa;IACtB,aAAa;IACb,YAAY;IACZ,UAAU;IACV,aAAa;IACb,aAAa;IACb,cAAc;IACd,YAAY;IACZ,aAAa;IACb,UAAU;IACV,WAAW;IACX,gBAAgB;AACpB", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/supabase/services/sharedService.ts"], "sourcesContent": ["import { SupabaseClient, RealtimePostgresChangesPayload } from \"@supabase/supabase-js\";\r\nimport { TABLES, COLUMNS } from \"../constants\";\r\nimport { Tables } from \"../../../types/supabase\";\r\n\r\n/**\r\n * Fetches the currently authenticated user.\r\n * @param supabase The Supabase client.\r\n * @returns An object containing the user data or an error.\r\n */\r\nexport async function getAuthenticatedUser(supabase: SupabaseClient) {\r\n  try {\r\n    const { data: { user }, error } = await supabase.auth.getUser();\r\n    if (error) {\r\n      console.error(`Error fetching authenticated user: ${error.message}`);\r\n      return { user: null, error: \"User not found or authentication error.\" };\r\n    }\r\n    return { user, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching authenticated user: ${err}`);\r\n    return { user: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Uploads a file to Supabase Storage.\r\n * @param supabase The Supabase client.\r\n * @param bucketName The name of the storage bucket.\r\n * @param path The path where the file will be stored in the bucket.\r\n * @param fileBuffer The file content as a Buffer.\r\n * @param contentType The content type of the file (e.g., 'image/jpeg').\r\n * @param upsert Whether to upsert the file if it already exists.\r\n * @returns An object indicating success or an error.\r\n */\r\nexport async function uploadFileToStorage(\r\n  supabase: SupabaseClient,\r\n  bucketName: string,\r\n  path: string,\r\n  fileBuffer: Buffer,\r\n  contentType: string,\r\n  upsert: boolean = true\r\n) {\r\n  try {\r\n    const { error } = await supabase.storage\r\n      .from(bucketName)\r\n      .upload(path, fileBuffer, {\r\n        contentType,\r\n        upsert,\r\n      });\r\n\r\n    if (error) {\r\n      console.error(`Error uploading file to storage: ${error.message}`);\r\n      return { success: false, error: error.message };\r\n    }\r\n    return { success: true, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error uploading file to storage: ${err}`);\r\n    return { success: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Retrieves the public URL for a file in Supabase Storage.\r\n * @param supabase The Supabase client.\r\n * @param bucketName The name of the storage bucket.\r\n * @param path The path of the file in the bucket.\r\n * @returns An object containing the public URL or an error.\r\n */\r\nexport async function getPublicUrlFromStorage(supabase: SupabaseClient, bucketName: string, path: string) {\r\n  try {\r\n    const { data } = supabase.storage.from(bucketName).getPublicUrl(path);\r\n    if (!data?.publicUrl) {\r\n      return { publicUrl: null, error: \"Could not retrieve public URL.\" };\r\n    }\r\n    return { publicUrl: data.publicUrl, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error getting public URL: ${err}`);\r\n    return { publicUrl: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Removes files from Supabase Storage.\r\n * @param supabase The Supabase client.\r\n * @param bucketName The name of the storage bucket.\r\n * @param paths An array of file paths to remove from the bucket.\r\n * @returns An object indicating success or an error.\r\n */\r\nexport async function removeFileFromStorage(supabase: SupabaseClient, bucketName: string, paths: string[]) {\r\n  try {\r\n    const { error } = await supabase.storage.from(bucketName).remove(paths);\r\n    if (error) {\r\n      console.error(`Error removing file from storage: ${error.message}`);\r\n      return { success: false, error: error.message };\r\n    }\r\n    return { success: true, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error removing file from storage: ${err}`);\r\n    return { success: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Updates the authenticated user's phone number in Supabase Auth.\r\n * @param supabase The Supabase client.\r\n * @param phone The new phone number.\r\n * @returns An object indicating success or an error.\r\n */\r\nexport async function updateAuthUserPhone(supabase: SupabaseClient, phone: string) {\r\n  try {\r\n    const { error } = await supabase.auth.updateUser({\r\n      phone: `+91${phone}`,\r\n    });\r\n\r\n    if (error) {\r\n      console.error(`Error updating auth user phone: ${error.message}`);\r\n      return { success: false, error: error.message };\r\n    }\r\n    return { success: true, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error updating auth user phone: ${err}`);\r\n    return { success: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Lists files in a Supabase Storage bucket.\r\n * @param supabase The Supabase client.\r\n * @param bucketName The name of the storage bucket.\r\n * @param path The path within the bucket to list files from.\r\n * @param options Options for listing files (e.g., limit).\r\n * @returns An object containing the list of files or an error.\r\n */\r\nexport async function listStorageFiles(\r\n  supabase: SupabaseClient,\r\n  bucketName: string,\r\n  path: string,\r\n  options?: { limit?: number; offset?: number; search?: string }\r\n) {\r\n  try {\r\n    const { data, error } = await supabase.storage\r\n      .from(bucketName)\r\n      .list(path, options);\r\n\r\n    if (error) {\r\n      console.error(`Error listing storage files: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error listing storage files: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Signs out the currently authenticated user.\r\n * @param supabase The Supabase client.\r\n * @returns An object indicating success or an error.\r\n */\r\n/**\r\n * Signs out the currently authenticated user.\r\n * @param supabase The Supabase client.\r\n * @returns An object indicating success or an error.\r\n */\r\nexport async function signOutUser(supabase: SupabaseClient) {\r\n  try {\r\n    const { error } = await supabase.auth.signOut();\r\n    if (error) {\r\n      console.error(`Error signing out user: ${error.message}`);\r\n      return { success: false, error: error.message };\r\n    }\r\n    return { success: true, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error signing out user: ${err}`);\r\n    return { success: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Sends an OTP to the provided email address for authentication.\r\n * @param supabase The Supabase client.\r\n * @param email The email address to send the OTP to.\r\n * @param shouldCreateUser Whether to create a new user if the email doesn't exist.\r\n * @returns An object indicating success or an error.\r\n */\r\nexport async function signInWithOtp(supabase: SupabaseClient, email: string, shouldCreateUser: boolean = true) {\r\n  try {\r\n    const { error } = await supabase.auth.signInWithOtp({\r\n      email: email,\r\n      options: {\r\n        shouldCreateUser: shouldCreateUser,\r\n        data: {\r\n          auth_type: \"email\",\r\n        },\r\n      },\r\n    });\r\n    if (error) {\r\n      console.error(`Error sending OTP: ${error.message}`);\r\n      return { success: false, error: error.message };\r\n    }\r\n    return { success: true, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error sending OTP: ${err}`);\r\n    return { success: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Verifies an OTP for email authentication.\r\n * @param supabase The Supabase client.\r\n * @param email The email address associated with the OTP.\r\n * @param token The OTP token to verify.\r\n * @returns An object containing user data on success or an error.\r\n */\r\nexport async function verifyOtp(supabase: SupabaseClient, email: string, token: string) {\r\n  try {\r\n    const { data, error } = await supabase.auth.verifyOtp({\r\n      email: email,\r\n      token: token,\r\n      type: 'email',\r\n    });\r\n    if (error) {\r\n      console.error(`Error verifying OTP: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error verifying OTP: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Signs in a user with a mobile number and password.\r\n * @param supabase The Supabase client.\r\n * @param phone The user's phone number (should include country code, e.g., +91).\r\n * @param password The user's password.\r\n * @returns An object containing user data on success or an error.\r\n */\r\nexport async function signInWithPassword(supabase: SupabaseClient, phone: string, password: string) {\r\n  try {\r\n    const { data, error } = await supabase.auth.signInWithPassword({\r\n      phone: phone,\r\n      password: password,\r\n    });\r\n    if (error) {\r\n      console.error(`Error signing in with password: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error signing in with password: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Initiates an OAuth sign-in flow with a specified provider.\r\n * @param supabase The Supabase client.\r\n * @param provider The OAuth provider (e.g., 'google').\r\n * @param redirectTo The URL to redirect to after successful authentication.\r\n * @param queryParams Optional query parameters for the OAuth flow.\r\n * @returns An object containing the authorization URL or an error.\r\n */\r\nexport async function signInWithOAuth(supabase: SupabaseClient, provider: \"google\", redirectTo: string, queryParams?: { [key: string]: string }) {\r\n  try {\r\n    const { data, error } = await supabase.auth.signInWithOAuth({\r\n      provider: provider,\r\n      options: {\r\n        redirectTo: redirectTo,\r\n        skipBrowserRedirect: true,\r\n        queryParams: queryParams,\r\n      },\r\n    });\r\n    if (error) {\r\n      console.error(`Error initiating OAuth sign-in: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error initiating OAuth sign-in: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Subscribes to real-time changes in a Supabase table.\r\n * @param supabase The Supabase client.\r\n * @param tableName The name of the table to subscribe to.\r\n * @param filter The filter to apply to the subscription (e.g., `id=eq.some_id`).\r\n * @param callback The callback function to execute when changes are received.\r\n * @returns A cleanup function to unsubscribe from the channel.\r\n */\r\nexport function subscribeToTableChanges<T extends { [key: string]: any }>(\r\n  supabase: SupabaseClient,\r\n  tableName: string,\r\n  filter: string,\r\n  callback: (_payload: RealtimePostgresChangesPayload<T>) => void\r\n): () => void {\r\n\r\n  const channel = supabase\r\n    .channel(`public:${tableName}`)\r\n    .on<T>(\r\n      \"postgres_changes\",\r\n      {\r\n        event: \"*\",\r\n        schema: \"public\",\r\n        table: tableName,\r\n        filter: filter,\r\n      },\r\n      callback\r\n    )\r\n    .subscribe();\r\n\r\n  return () => {\r\n    supabase.removeChannel(channel);\r\n  };\r\n}\r\n\r\n/**\r\n * Fetches user subscriptions.\r\n * @param supabase The Supabase client.\r\n * @param userId The ID of the user.\r\n * @returns An object containing the subscriptions data or an error.\r\n */\r\nexport async function fetchUserSubscriptions(supabase: SupabaseClient, userId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.SUBSCRIPTIONS)\r\n      .select(COLUMNS.BUSINESS_PROFILE_ID)\r\n      .eq(COLUMNS.USER_ID, userId);\r\n\r\n    if (error) {\r\n      console.error(`Error fetching user subscriptions: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching user subscriptions: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches the state name for a given city from the pincodes table.\r\n * @param supabase The Supabase client.\r\n * @param city The city name to search for.\r\n * @returns An object containing the state name or an error.\r\n */\r\nexport async function getStateNameByCity(supabase: SupabaseClient, city: string): Promise<{ stateName: string | null; error: string | null }> {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.PINCODES)\r\n      .select(COLUMNS.STATE_NAME)\r\n      .ilike(COLUMNS.DIVISION_NAME, `%${city}%`)\r\n      .limit(1);\r\n\r\n    if (error) {\r\n      console.error(`Error fetching state name for city ${city}: ${error.message}`);\r\n      return { stateName: null, error: error.message };\r\n    }\r\n\r\n    return { stateName: data && data.length > 0 ? data[0].StateName : null, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching state name for city ${city}: ${err}`);\r\n    return { stateName: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches unified posts with optional filters and pagination.\r\n * @param supabase The Supabase client.\r\n * @param from The starting index for pagination.\r\n * @param to The ending index for pagination.\r\n * @param conditions Optional array of conditions for filtering.\r\n * @returns An object containing the unified posts data, count, or an error.\r\n */\r\nexport async function getUnifiedPosts(\r\n  supabase: SupabaseClient,\r\n  from: number,\r\n  to: number,\r\n  conditions: string[] = []\r\n) {\r\n  let query = supabase.from(TABLES.UNIFIED_POSTS).select('*', { count: 'exact' });\r\n\r\n  if (conditions.length > 0) {\r\n    query = query.or(conditions.join(','));\r\n  }\r\n\r\n  const { data, error, count } = await query\r\n    .order(COLUMNS.CREATED_AT, { ascending: false })\r\n    .range(from, to);\r\n\r\n  if (error) {\r\n    console.error(\"Error fetching unified posts:\", error);\r\n    return { data: null, error: error.message, count: null };\r\n  }\r\n  return { data, error: null, count };\r\n}\r\n\r\n/**\r\n * Fetches address data from the pincodes table.\r\n * @param supabase The Supabase client.\r\n * @param pincode The pincode to search for.\r\n * @param locality_slug Optional. The locality slug to filter by.\r\n * @param city_slug Optional. The city slug to filter by.\r\n * @param state_slug Optional. The state slug to filter by.\r\n * @returns An object containing the address data or an error.\r\n */\r\nexport async function fetchPincodeAddress(\r\n  supabase: SupabaseClient,\r\n  pincode: string,\r\n  locality_slug?: string | null,\r\n  city_slug?: string | null,\r\n  state_slug?: string | null\r\n) {\r\n  try {\r\n    let query = supabase\r\n      .from(TABLES.PINCODES)\r\n      .select(`${COLUMNS.OFFICE_NAME}, ${COLUMNS.DIVISION_NAME}, ${COLUMNS.STATE_NAME}, ${COLUMNS.PINCODE}`);\r\n\r\n    if (pincode) {\r\n      query = query.eq(COLUMNS.PINCODE, pincode);\r\n    }\r\n    if (city_slug) {\r\n      query = query.eq(COLUMNS.CITY_SLUG, city_slug);\r\n    }\r\n    if (state_slug) {\r\n      query = query.eq(COLUMNS.STATE_SLUG, state_slug);\r\n    }\r\n    if (locality_slug) {\r\n      query = query.eq(COLUMNS.LOCALITY_SLUG, locality_slug);\r\n    }\r\n\r\n    const { data, error } = await query.limit(1);\r\n\r\n    if (error) {\r\n      console.error(`Error fetching pincode address: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data: data[0] || null, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching pincode address: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AACA;;AAQO,eAAe,qBAAqB,QAAwB;IACjE,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7D,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,MAAM,OAAO,EAAE;YACnE,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAA0C;QACxE;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,8CAA8C,EAAE,KAAK;QACpE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAYO,eAAe,oBACpB,QAAwB,EACxB,UAAkB,EAClB,IAAY,EACZ,UAAkB,EAClB,WAAmB,EACnB,SAAkB,IAAI;IAEtB,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,OAAO,CACrC,IAAI,CAAC,YACL,MAAM,CAAC,MAAM,YAAY;YACxB;YACA;QACF;QAEF,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,MAAM,OAAO,EAAE;YACjE,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QAChD;QACA,OAAO;YAAE,SAAS;YAAM,OAAO;QAAK;IACtC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,4CAA4C,EAAE,KAAK;QAClE,OAAO;YAAE,SAAS;YAAO,OAAO;QAAgC;IAClE;AACF;AASO,eAAe,wBAAwB,QAAwB,EAAE,UAAkB,EAAE,IAAY;IACtG,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,SAAS,OAAO,CAAC,IAAI,CAAC,YAAY,YAAY,CAAC;QAChE,IAAI,CAAC,MAAM,WAAW;YACpB,OAAO;gBAAE,WAAW;gBAAM,OAAO;YAAiC;QACpE;QACA,OAAO;YAAE,WAAW,KAAK,SAAS;YAAE,OAAO;QAAK;IAClD,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,qCAAqC,EAAE,KAAK;QAC3D,OAAO;YAAE,WAAW;YAAM,OAAO;QAAgC;IACnE;AACF;AASO,eAAe,sBAAsB,QAAwB,EAAE,UAAkB,EAAE,KAAe;IACvG,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,OAAO,CAAC,IAAI,CAAC,YAAY,MAAM,CAAC;QACjE,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,MAAM,OAAO,EAAE;YAClE,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QAChD;QACA,OAAO;YAAE,SAAS;YAAM,OAAO;QAAK;IACtC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,6CAA6C,EAAE,KAAK;QACnE,OAAO;YAAE,SAAS;YAAO,OAAO;QAAgC;IAClE;AACF;AAQO,eAAe,oBAAoB,QAAwB,EAAE,KAAa;IAC/E,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU,CAAC;YAC/C,OAAO,CAAC,GAAG,EAAE,OAAO;QACtB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,MAAM,OAAO,EAAE;YAChE,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QAChD;QACA,OAAO;YAAE,SAAS;YAAM,OAAO;QAAK;IACtC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,KAAK;QACjE,OAAO;YAAE,SAAS;YAAO,OAAO;QAAgC;IAClE;AACF;AAUO,eAAe,iBACpB,QAAwB,EACxB,UAAkB,EAClB,IAAY,EACZ,OAA8D;IAE9D,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,OAAO,CAC3C,IAAI,CAAC,YACL,IAAI,CAAC,MAAM;QAEd,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,MAAM,OAAO,EAAE;YAC7D,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,wCAAwC,EAAE,KAAK;QAC9D,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAYO,eAAe,YAAY,QAAwB;IACxD,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,MAAM,OAAO,EAAE;YACxD,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QAChD;QACA,OAAO;YAAE,SAAS;YAAM,OAAO;QAAK;IACtC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,KAAK;QACzD,OAAO;YAAE,SAAS;YAAO,OAAO;QAAgC;IAClE;AACF;AASO,eAAe,cAAc,QAAwB,EAAE,KAAa,EAAE,mBAA4B,IAAI;IAC3G,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC;YAClD,OAAO;YACP,SAAS;gBACP,kBAAkB;gBAClB,MAAM;oBACJ,WAAW;gBACb;YACF;QACF;QACA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;YACnD,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QAChD;QACA,OAAO;YAAE,SAAS;YAAM,OAAO;QAAK;IACtC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,8BAA8B,EAAE,KAAK;QACpD,OAAO;YAAE,SAAS;YAAO,OAAO;QAAgC;IAClE;AACF;AASO,eAAe,UAAU,QAAwB,EAAE,KAAa,EAAE,KAAa;IACpF,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,SAAS,CAAC;YACpD,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,MAAM,OAAO,EAAE;YACrD,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,KAAK;QACtD,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AASO,eAAe,mBAAmB,QAAwB,EAAE,KAAa,EAAE,QAAgB;IAChG,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YAC7D,OAAO;YACP,UAAU;QACZ;QACA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,MAAM,OAAO,EAAE;YAChE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,KAAK;QACjE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAUO,eAAe,gBAAgB,QAAwB,EAAE,QAAkB,EAAE,UAAkB,EAAE,WAAuC;IAC7I,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,eAAe,CAAC;YAC1D,UAAU;YACV,SAAS;gBACP,YAAY;gBACZ,qBAAqB;gBACrB,aAAa;YACf;QACF;QACA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,MAAM,OAAO,EAAE;YAChE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,KAAK;QACjE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAUO,SAAS,wBACd,QAAwB,EACxB,SAAiB,EACjB,MAAc,EACd,QAA+D;IAG/D,MAAM,UAAU,SACb,OAAO,CAAC,CAAC,OAAO,EAAE,WAAW,EAC7B,EAAE,CACD,oBACA;QACE,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV,GACA,UAED,SAAS;IAEZ,OAAO;QACL,SAAS,aAAa,CAAC;IACzB;AACF;AAQO,eAAe,uBAAuB,QAAwB,EAAE,MAAc;IACnF,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,aAAa,EACzB,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,mBAAmB,EAClC,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;QAEvB,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,MAAM,OAAO,EAAE;YACnE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,8CAA8C,EAAE,KAAK;QACpE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAQO,eAAe,mBAAmB,QAAwB,EAAE,IAAY;IAC7E,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,QAAQ,EACpB,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EACzB,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EACxC,KAAK,CAAC;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,KAAK,EAAE,EAAE,MAAM,OAAO,EAAE;YAC5E,OAAO;gBAAE,WAAW;gBAAM,OAAO,MAAM,OAAO;YAAC;QACjD;QAEA,OAAO;YAAE,WAAW,QAAQ,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG;YAAM,OAAO;QAAK;IACtF,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,8CAA8C,EAAE,KAAK,EAAE,EAAE,KAAK;QAC7E,OAAO;YAAE,WAAW;YAAM,OAAO;QAAgC;IACnE;AACF;AAUO,eAAe,gBACpB,QAAwB,EACxB,IAAY,EACZ,EAAU,EACV,aAAuB,EAAE;IAEzB,IAAI,QAAQ,SAAS,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,aAAa,EAAE,MAAM,CAAC,KAAK;QAAE,OAAO;IAAQ;IAE7E,IAAI,WAAW,MAAM,GAAG,GAAG;QACzB,QAAQ,MAAM,EAAE,CAAC,WAAW,IAAI,CAAC;IACnC;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,MAClC,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;QAAE,WAAW;IAAM,GAC7C,KAAK,CAAC,MAAM;IAEf,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YAAE,MAAM;YAAM,OAAO,MAAM,OAAO;YAAE,OAAO;QAAK;IACzD;IACA,OAAO;QAAE;QAAM,OAAO;QAAM;IAAM;AACpC;AAWO,eAAe,oBACpB,QAAwB,EACxB,OAAe,EACf,aAA6B,EAC7B,SAAyB,EACzB,UAA0B;IAE1B,IAAI;QACF,IAAI,QAAQ,SACT,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,QAAQ,EACpB,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;QAEvG,IAAI,SAAS;YACX,QAAQ,MAAM,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;QACpC;QACA,IAAI,WAAW;YACb,QAAQ,MAAM,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,SAAS,EAAE;QACtC;QACA,IAAI,YAAY;YACd,QAAQ,MAAM,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;QACvC;QACA,IAAI,eAAe;YACjB,QAAQ,MAAM,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE;QAC1C;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,KAAK,CAAC;QAE1C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,MAAM,OAAO,EAAE;YAChE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE,MAAM,IAAI,CAAC,EAAE,IAAI;YAAM,OAAO;QAAK;IAC9C,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,KAAK;QACjE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF", "debugId": null}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/server.ts"], "sourcesContent": ["import { createServerClient, type CookieOptions } from '@supabase/ssr';\r\nimport { SupabaseClient } from '@supabase/supabase-js';\r\nimport { cookies, headers } from 'next/headers';\r\nimport { Database } from '@/types/supabase';\r\n\r\nexport async function createClient(): Promise<SupabaseClient<Database>> {\r\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\r\n  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\r\n\r\n  if (!supabaseUrl || !supabaseAnonKey) {\r\n    throw new Error('Supabase environment variables are not set.');\r\n  }\r\n\r\n  // Check if we're in a test environment\r\n  const headersList = await headers();\r\n  const isTestEnvironment =\r\n    process.env.NODE_ENV === 'test' ||\r\n    process.env.PLAYWRIGHT_TESTING === 'true' ||\r\n    headersList.get('x-playwright-testing') === 'true';\r\n\r\n  if (isTestEnvironment) {\r\n    // Return a mocked Supabase client for testing\r\n    return createMockSupabaseClient(headersList) as unknown as SupabaseClient<Database>;\r\n  }\r\n\r\n  const cookieStore = await cookies();\r\n\r\n  return createServerClient(\r\n    supabaseUrl,\r\n    supabaseAnonKey,\r\n    {\r\n      cookies: {\r\n        async getAll() {\r\n          return await cookieStore.getAll();\r\n        },\r\n        async setAll(cookiesToSet: any[]) {\r\n          try {\r\n            for (const { name, value, options } of cookiesToSet) {\r\n              await cookieStore.set(name, value, options);\r\n            }\r\n          } catch {\r\n            // The `setAll` method was called from a Server Component.\r\n            // This can be ignored if you have middleware refreshing\r\n            // user sessions.\r\n          }\r\n        },\r\n      },\r\n    }\r\n  ) as unknown as SupabaseClient<Database>;\r\n}\r\n\r\ntype MockQueryBuilder = {\r\n  select: (columns?: string) => MockQueryBuilder;\r\n  eq: (column: string, value: any) => MockQueryBuilder;\r\n  neq: (column: string, value: any) => MockQueryBuilder;\r\n  gt: (column: string, value: any) => MockQueryBuilder;\r\n  gte: (column: string, value: any) => MockQueryBuilder;\r\n  lt: (column: string, value: any) => MockQueryBuilder;\r\n  lte: (column: string, value: any) => MockQueryBuilder;\r\n  like: (column: string, pattern: string) => MockQueryBuilder;\r\n  ilike: (column: string, pattern: string) => MockQueryBuilder;\r\n  is: (column: string, value: any) => MockQueryBuilder;\r\n  in: (column: string, values: any[]) => MockQueryBuilder;\r\n  contains: (column: string, value: any) => MockQueryBuilder;\r\n  containedBy: (column: string, value: any) => MockQueryBuilder;\r\n  rangeGt: (column: string, value: any) => MockQueryBuilder;\r\n  rangeGte: (column: string, value: any) => MockQueryBuilder;\r\n  rangeLt: (column: string, value: any) => MockQueryBuilder;\r\n  rangeLte: (column: string, value: any) => MockQueryBuilder;\r\n  rangeAdjacent: (column: string, value: any) => MockQueryBuilder;\r\n  overlaps: (column: string, value: any) => MockQueryBuilder;\r\n  textSearch: (column: string, query: string) => MockQueryBuilder;\r\n  match: (query: Record<string, any>) => MockQueryBuilder;\r\n  not: (column: string, operator: string, value: any) => MockQueryBuilder;\r\n  or: (filters: string) => MockQueryBuilder;\r\n  filter: (column: string, operator: string, value: any) => MockQueryBuilder;\r\n  order: (column: string, options?: { ascending?: boolean; nullsFirst?: boolean }) => MockQueryBuilder;\r\n  limit: (count: number, options?: { foreignTable?: string }) => MockQueryBuilder;\r\n  range: (from: number, to: number, options?: { foreignTable?: string }) => MockQueryBuilder;\r\n  abortSignal: (signal: AbortSignal) => MockQueryBuilder;\r\n  single: () => Promise<any>;\r\n  maybeSingle: () => Promise<any>;\r\n  then: (callback?: any) => Promise<any>;\r\n  data: any;\r\n  error: any;\r\n  count: number;\r\n  status: number;\r\n  statusText: string;\r\n};\r\n\r\nfunction createMockSupabaseClient(headersList: Headers) {\r\n  const testAuthState = headersList.get('x-test-auth-state');\r\n  const testUserType = headersList.get('x-test-user-type');\r\n  const testHasProfile = testUserType === 'customer' || testUserType === 'business';\r\n  const testBusinessSlug = headersList.get('x-test-business-slug');\r\n  const testPlanId = headersList.get('x-test-plan-id') || 'free';\r\n\r\n  return {\r\n    auth: {\r\n      getUser: async () => {\r\n        if (testAuthState === 'authenticated') {\r\n          return { data: { user: { id: 'test-user-id', email: '<EMAIL>' } }, error: null };\r\n        }\r\n        return { data: { user: null }, error: { message: 'Unauthorized', name: 'AuthApiError', status: 401 } };\r\n      },\r\n      getSession: async () => {\r\n        if (testAuthState === 'authenticated') {\r\n          return { data: { session: { user: { id: 'test-user-id', email: '<EMAIL>' } } }, error: null };\r\n        }\r\n        return { data: { session: null }, error: { message: 'Unauthorized', name: 'AuthApiError', status: 401 } };\r\n      },\r\n      signInWithOtp: async () => ({ data: { user: null, session: null }, error: null }),\r\n      signOut: async () => ({ error: null }),\r\n    },\r\n    from: (table: string) => createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId),\r\n  };\r\n}\r\n\r\nfunction createMockQueryBuilder(\r\n  table: string,\r\n  testUserType: string | null,\r\n  testHasProfile: boolean,\r\n  testBusinessSlug: string | null,\r\n  testPlanId: string\r\n): any {\r\n  const getMockData = () => getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId);\r\n\r\n  const createChainableMock = (data?: any): MockQueryBuilder => ({\r\n    select: (_columns?: string) => createChainableMock(data),\r\n    eq: (_column: string, _value: any) => createChainableMock(data),\r\n    neq: (_column: string, _value: any) => createChainableMock(data),\r\n    gt: (_column: string, _value: any) => createChainableMock(data),\r\n    gte: (_column: string, _value: any) => createChainableMock(data),\r\n    lt: (_column: string, _value: any) => createChainableMock(data),\r\n    lte: (_column: string, _value: any) => createChainableMock(data),\r\n    like: (_column: string, _pattern: string) => createChainableMock(data),\r\n    ilike: (_column: string, _pattern: string) => createChainableMock(data),\r\n    is: (_column: string, _value: any) => createChainableMock(data),\r\n    in: (_column: string, _values: any[]) => createChainableMock(data),\r\n    contains: (_column: string, _value: any) => createChainableMock(data),\r\n    containedBy: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeGt: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeGte: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeLt: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeLte: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeAdjacent: (_column: string, _value: any) => createChainableMock(data),\r\n    overlaps: (_column: string, _value: any) => createChainableMock(data),\r\n    textSearch: (_column: string, _query: string) => createChainableMock(data),\r\n    match: (_query: Record<string, any>) => createChainableMock(data),\r\n    not: (_column: string, _operator: string, _value: any) => createChainableMock(data),\r\n    or: (_filters: string) => createChainableMock(data),\r\n    filter: (_column: string, _operator: string, _value: any) => createChainableMock(data),\r\n    order: (_column: string, _options?: { ascending?: boolean; nullsFirst?: boolean }) => createChainableMock(data),\r\n    limit: (_count: number, _options?: { foreignTable?: string }) => createChainableMock(data),\r\n    range: (_from: number, _to: number, _options?: { foreignTable?: string }) => createChainableMock(data),\r\n    abortSignal: (_signal: AbortSignal) => createChainableMock(data),\r\n    single: async () => getMockData(),\r\n    maybeSingle: async () => getMockData(),\r\n    then: async (callback?: any) => {\r\n      const result = getMockData();\r\n      return callback ? callback(result) : result;\r\n    },\r\n    data: data || [],\r\n    error: null,\r\n    count: data ? data.length : 0,\r\n    status: 200,\r\n    statusText: 'OK',\r\n  });\r\n\r\n  return {\r\n    select: (_columns?: string) => createChainableMock(),\r\n    insert: (data: any | any[]) => ({\r\n      select: (_columns?: string) => ({\r\n        single: async () => ({\r\n          data: Array.isArray(data) ? data[0] : data,\r\n          error: null,\r\n        }),\r\n        maybeSingle: async () => ({\r\n          data: Array.isArray(data) ? data[0] : data,\r\n          error: null,\r\n        }),\r\n        then: async (_callback?: any) => {\r\n          const result = { data: Array.isArray(data) ? data : [data], error: null };\r\n          return _callback ? _callback(result) : result;\r\n        },\r\n      }),\r\n      then: async (_callback?: any) => {\r\n        const result = { data: Array.isArray(data) ? data : [data], error: null };\r\n        return _callback ? _callback(result) : result;\r\n      },\r\n    }),\r\n    update: (data: any) => createChainableMock(data),\r\n    upsert: (data: any | any[]) => createChainableMock(data),\r\n    delete: () => createChainableMock(),\r\n    rpc: (_functionName: string, _params?: any) => createChainableMock(),\r\n  };\r\n}\r\n\r\n/**\r\n * Helper function to get mock table data based on test state\r\n */\r\nfunction getMockTableData(\r\n  table: string,\r\n  testUserType: string | null,\r\n  testHasProfile: boolean,\r\n  testBusinessSlug: string | null,\r\n  testPlanId: string\r\n) {\r\n  if (table === 'customer_profiles') {\r\n    const hasCustomerProfile = testHasProfile && testUserType === 'customer';\r\n    return {\r\n      data: hasCustomerProfile ? {\r\n        id: 'test-user-id',\r\n        name: 'Test Customer',\r\n        avatar_url: null,\r\n        phone: '+1234567890',\r\n        email: '<EMAIL>',\r\n        address: 'Test Address',\r\n        city: 'Test City',\r\n        state: 'Test State',\r\n        pincode: '123456'\r\n      } : null,\r\n      error: null\r\n    };\r\n  }\r\n\r\n  if (table === 'business_profiles') {\r\n    const hasBusinessProfile = testHasProfile && testUserType === 'business';\r\n    return {\r\n      data: hasBusinessProfile ? {\r\n        id: 'test-user-id',\r\n        business_slug: testBusinessSlug || null,\r\n        trial_end_date: null,\r\n        has_active_subscription: true,\r\n        business_name: 'Test Business',\r\n        city_slug: 'test-city',\r\n        state_slug: 'test-state',\r\n        locality_slug: 'test-locality',\r\n        pincode: '123456',\r\n        business_description: 'Test business description',\r\n        business_category: 'retail',\r\n        phone: '+1234567890',\r\n        email: '<EMAIL>',\r\n        website: 'https://testbusiness.com'\r\n      } : null,\r\n      error: null\r\n    };\r\n  }\r\n\r\n  if (table === 'payment_subscriptions') {\r\n    return {\r\n      data: testUserType === 'business' ? {\r\n        id: 'test-subscription-id',\r\n        plan_id: testPlanId,\r\n        business_profile_id: 'test-user-id',\r\n        status: 'active',\r\n        created_at: '2024-01-01T00:00:00Z'\r\n      } : null,\r\n      error: null\r\n    };\r\n  }\r\n\r\n  if (table === 'products') {\r\n    return {\r\n      data: testUserType === 'business' ? [\r\n        {\r\n          id: 'test-product-1',\r\n          name: 'Test Product 1',\r\n          price: 100,\r\n          business_profile_id: 'test-user-id',\r\n          available: true\r\n        },\r\n        {\r\n          id: 'test-product-2',\r\n          name: 'Test Product 2',\r\n          price: 200,\r\n          business_profile_id: 'test-user-id',\r\n          available: false\r\n        }\r\n      ] : [],\r\n      error: null\r\n    };\r\n  }\r\n\r\n  // Default return for unknown tables\r\n  return { data: null, error: null };\r\n}\r\n\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAEA;;;AAGO,eAAe;IACpB,MAAM;IACN,MAAM;IAEN,uCAAsC;;IAEtC;IAEA,uCAAuC;IACvC,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,oBACJ,oDAAyB,UACzB,QAAQ,GAAG,CAAC,kBAAkB,KAAK,UACnC,YAAY,GAAG,CAAC,4BAA4B;IAE9C,IAAI,mBAAmB;QACrB,8CAA8C;QAC9C,OAAO,yBAAyB;IAClC;IAEA,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,EACtB,aACA,iBACA;QACE,SAAS;YACP,MAAM;gBACJ,OAAO,MAAM,YAAY,MAAM;YACjC;YACA,MAAM,QAAO,YAAmB;gBAC9B,IAAI;oBACF,KAAK,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,aAAc;wBACnD,MAAM,YAAY,GAAG,CAAC,MAAM,OAAO;oBACrC;gBACF,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ;AAyCA,SAAS,yBAAyB,WAAoB;IACpD,MAAM,gBAAgB,YAAY,GAAG,CAAC;IACtC,MAAM,eAAe,YAAY,GAAG,CAAC;IACrC,MAAM,iBAAiB,iBAAiB,cAAc,iBAAiB;IACvE,MAAM,mBAAmB,YAAY,GAAG,CAAC;IACzC,MAAM,aAAa,YAAY,GAAG,CAAC,qBAAqB;IAExD,OAAO;QACL,MAAM;YACJ,SAAS;gBACP,IAAI,kBAAkB,iBAAiB;oBACrC,OAAO;wBAAE,MAAM;4BAAE,MAAM;gCAAE,IAAI;gCAAgB,OAAO;4BAAmB;wBAAE;wBAAG,OAAO;oBAAK;gBAC1F;gBACA,OAAO;oBAAE,MAAM;wBAAE,MAAM;oBAAK;oBAAG,OAAO;wBAAE,SAAS;wBAAgB,MAAM;wBAAgB,QAAQ;oBAAI;gBAAE;YACvG;YACA,YAAY;gBACV,IAAI,kBAAkB,iBAAiB;oBACrC,OAAO;wBAAE,MAAM;4BAAE,SAAS;gCAAE,MAAM;oCAAE,IAAI;oCAAgB,OAAO;gCAAmB;4BAAE;wBAAE;wBAAG,OAAO;oBAAK;gBACvG;gBACA,OAAO;oBAAE,MAAM;wBAAE,SAAS;oBAAK;oBAAG,OAAO;wBAAE,SAAS;wBAAgB,MAAM;wBAAgB,QAAQ;oBAAI;gBAAE;YAC1G;YACA,eAAe,UAAY,CAAC;oBAAE,MAAM;wBAAE,MAAM;wBAAM,SAAS;oBAAK;oBAAG,OAAO;gBAAK,CAAC;YAChF,SAAS,UAAY,CAAC;oBAAE,OAAO;gBAAK,CAAC;QACvC;QACA,MAAM,CAAC,QAAkB,uBAAuB,OAAO,cAAc,gBAAgB,kBAAkB;IACzG;AACF;AAEA,SAAS,uBACP,KAAa,EACb,YAA2B,EAC3B,cAAuB,EACvB,gBAA+B,EAC/B,UAAkB;IAElB,MAAM,cAAc,IAAM,iBAAiB,OAAO,cAAc,gBAAgB,kBAAkB;IAElG,MAAM,sBAAsB,CAAC,OAAiC,CAAC;YAC7D,QAAQ,CAAC,WAAsB,oBAAoB;YACnD,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,KAAK,CAAC,SAAiB,SAAgB,oBAAoB;YAC3D,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,KAAK,CAAC,SAAiB,SAAgB,oBAAoB;YAC3D,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,KAAK,CAAC,SAAiB,SAAgB,oBAAoB;YAC3D,MAAM,CAAC,SAAiB,WAAqB,oBAAoB;YACjE,OAAO,CAAC,SAAiB,WAAqB,oBAAoB;YAClE,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,IAAI,CAAC,SAAiB,UAAmB,oBAAoB;YAC7D,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,aAAa,CAAC,SAAiB,SAAgB,oBAAoB;YACnE,SAAS,CAAC,SAAiB,SAAgB,oBAAoB;YAC/D,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,SAAS,CAAC,SAAiB,SAAgB,oBAAoB;YAC/D,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,eAAe,CAAC,SAAiB,SAAgB,oBAAoB;YACrE,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,YAAY,CAAC,SAAiB,SAAmB,oBAAoB;YACrE,OAAO,CAAC,SAAgC,oBAAoB;YAC5D,KAAK,CAAC,SAAiB,WAAmB,SAAgB,oBAAoB;YAC9E,IAAI,CAAC,WAAqB,oBAAoB;YAC9C,QAAQ,CAAC,SAAiB,WAAmB,SAAgB,oBAAoB;YACjF,OAAO,CAAC,SAAiB,WAA6D,oBAAoB;YAC1G,OAAO,CAAC,QAAgB,WAAyC,oBAAoB;YACrF,OAAO,CAAC,OAAe,KAAa,WAAyC,oBAAoB;YACjG,aAAa,CAAC,UAAyB,oBAAoB;YAC3D,QAAQ,UAAY;YACpB,aAAa,UAAY;YACzB,MAAM,OAAO;gBACX,MAAM,SAAS;gBACf,OAAO,WAAW,SAAS,UAAU;YACvC;YACA,MAAM,QAAQ,EAAE;YAChB,OAAO;YACP,OAAO,OAAO,KAAK,MAAM,GAAG;YAC5B,QAAQ;YACR,YAAY;QACd,CAAC;IAED,OAAO;QACL,QAAQ,CAAC,WAAsB;QAC/B,QAAQ,CAAC,OAAsB,CAAC;gBAC9B,QAAQ,CAAC,WAAsB,CAAC;wBAC9B,QAAQ,UAAY,CAAC;gCACnB,MAAM,MAAM,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,GAAG;gCACtC,OAAO;4BACT,CAAC;wBACD,aAAa,UAAY,CAAC;gCACxB,MAAM,MAAM,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,GAAG;gCACtC,OAAO;4BACT,CAAC;wBACD,MAAM,OAAO;4BACX,MAAM,SAAS;gCAAE,MAAM,MAAM,OAAO,CAAC,QAAQ,OAAO;oCAAC;iCAAK;gCAAE,OAAO;4BAAK;4BACxE,OAAO,YAAY,UAAU,UAAU;wBACzC;oBACF,CAAC;gBACD,MAAM,OAAO;oBACX,MAAM,SAAS;wBAAE,MAAM,MAAM,OAAO,CAAC,QAAQ,OAAO;4BAAC;yBAAK;wBAAE,OAAO;oBAAK;oBACxE,OAAO,YAAY,UAAU,UAAU;gBACzC;YACF,CAAC;QACD,QAAQ,CAAC,OAAc,oBAAoB;QAC3C,QAAQ,CAAC,OAAsB,oBAAoB;QACnD,QAAQ,IAAM;QACd,KAAK,CAAC,eAAuB,UAAkB;IACjD;AACF;AAEA;;CAEC,GACD,SAAS,iBACP,KAAa,EACb,YAA2B,EAC3B,cAAuB,EACvB,gBAA+B,EAC/B,UAAkB;IAElB,IAAI,UAAU,qBAAqB;QACjC,MAAM,qBAAqB,kBAAkB,iBAAiB;QAC9D,OAAO;YACL,MAAM,qBAAqB;gBACzB,IAAI;gBACJ,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,OAAO;gBACP,SAAS;gBACT,MAAM;gBACN,OAAO;gBACP,SAAS;YACX,IAAI;YACJ,OAAO;QACT;IACF;IAEA,IAAI,UAAU,qBAAqB;QACjC,MAAM,qBAAqB,kBAAkB,iBAAiB;QAC9D,OAAO;YACL,MAAM,qBAAqB;gBACzB,IAAI;gBACJ,eAAe,oBAAoB;gBACnC,gBAAgB;gBAChB,yBAAyB;gBACzB,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,eAAe;gBACf,SAAS;gBACT,sBAAsB;gBACtB,mBAAmB;gBACnB,OAAO;gBACP,OAAO;gBACP,SAAS;YACX,IAAI;YACJ,OAAO;QACT;IACF;IAEA,IAAI,UAAU,yBAAyB;QACrC,OAAO;YACL,MAAM,iBAAiB,aAAa;gBAClC,IAAI;gBACJ,SAAS;gBACT,qBAAqB;gBACrB,QAAQ;gBACR,YAAY;YACd,IAAI;YACJ,OAAO;QACT;IACF;IAEA,IAAI,UAAU,YAAY;QACxB,OAAO;YACL,MAAM,iBAAiB,aAAa;gBAClC;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,qBAAqB;oBACrB,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,qBAAqB;oBACrB,WAAW;gBACb;aACD,GAAG,EAAE;YACN,OAAO;QACT;IACF;IAEA,oCAAoC;IACpC,OAAO;QAAE,MAAM;QAAM,OAAO;IAAK;AACnC", "debugId": null}}, {"offset": {"line": 917, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/schemas/authSchemas.ts"], "sourcesContent": ["import { z } from \"zod\";\n\nexport const IndianMobileSchema = z\n  .string()\n  .trim()\n  .min(10, { message: \"Mobile number must be 10 digits\" })\n  .max(10, { message: \"Mobile number must be 10 digits\" })\n  .regex(/^[6-9]\\d{9}$/, { message: \"Please enter a valid Indian mobile number\" });\n\nexport const EmailOTPSchema = z.object({\n  email: z\n    .string()\n    .trim()\n    .min(1, { message: \"Email is required\" })\n    .email({ message: \"Please enter a valid email address\" }),\n});\n\nexport const VerifyOTPSchema = z.object({\n  email: z\n    .string()\n    .trim()\n    .min(1, { message: \"Email is required\" })\n    .email({ message: \"Please enter a valid email address\" }),\n  otp: z\n    .string()\n    .trim()\n    .min(6, { message: \"OTP must be 6 digits\" })\n    .max(6, { message: \"OTP must be 6 digits\" })\n    .regex(/^\\d{6}$/, { message: \"OTP must be 6 digits\" }),\n});\n\nexport const PasswordComplexitySchema = z\n  .string()\n  .min(6, \"Password must be at least 6 characters long\")\n  .regex(/[A-Z]/, \"Password must contain at least one uppercase letter\")\n  .regex(/[a-z]/, \"Password must contain at least one lowercase letter\")\n  .regex(/\\d/, \"Password must contain at least one number\")\n  .regex(/[^a-zA-Z0-9]/, \"Password must contain at least one special character\");\n\nexport const MobilePasswordLoginSchema = z.object({\n  mobile: IndianMobileSchema,\n  password: z.string().trim().min(1, { message: \"Password is required\" }),\n});"], "names": [], "mappings": ";;;;;;;AAAA;;AAEO,MAAM,qBAAqB,oIAAA,CAAA,IAAC,CAChC,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,IAAI;IAAE,SAAS;AAAkC,GACrD,GAAG,CAAC,IAAI;IAAE,SAAS;AAAkC,GACrD,KAAK,CAAC,gBAAgB;IAAE,SAAS;AAA4C;AAEzE,MAAM,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,OAAO,oIAAA,CAAA,IAAC,CACL,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoB,GACtC,KAAK,CAAC;QAAE,SAAS;IAAqC;AAC3D;AAEO,MAAM,kBAAkB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,OAAO,oIAAA,CAAA,IAAC,CACL,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoB,GACtC,KAAK,CAAC;QAAE,SAAS;IAAqC;IACzD,KAAK,oIAAA,CAAA,IAAC,CACH,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB,GACzC,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB,GACzC,KAAK,CAAC,WAAW;QAAE,SAAS;IAAuB;AACxD;AAEO,MAAM,2BAA2B,oIAAA,CAAA,IAAC,CACtC,MAAM,GACN,GAAG,CAAC,GAAG,+CACP,KAAK,CAAC,SAAS,uDACf,KAAK,CAAC,SAAS,uDACf,KAAK,CAAC,MAAM,6CACZ,KAAK,CAAC,gBAAgB;AAElB,MAAM,4BAA4B,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChD,QAAQ;IACR,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB;AACvE", "debugId": null}}, {"offset": {"line": 967, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/supabaseErrorHandler.ts"], "sourcesContent": ["import { AuthError } from \"@supabase/supabase-js\";\n\n/**\n * User-friendly error messages for Supabase auth error codes\n */\nconst AUTH_ERROR_MESSAGES: Record<string, string> = {\n  // Rate limiting errors\n  over_email_send_rate_limit: \"Email rate limit exceeded. Please wait before requesting another OTP.\",\n  over_request_rate_limit: \"Too many requests. Please wait a few minutes before trying again.\",\n  over_sms_send_rate_limit: \"Too many SMS messages sent. Please wait before requesting another OTP.\",\n\n  // OTP related errors\n  otp_expired: \"OTP has expired. Please request a new one.\",\n  otp_disabled: \"OTP authentication is currently disabled.\",\n\n  // Token/JWT errors\n  bad_jwt: \"Invalid authentication token. Please sign in again.\",\n  session_expired: \"Your session has expired. Please sign in again.\",\n  session_not_found: \"Session not found. Please sign in again.\",\n  refresh_token_not_found: \"Authentication expired. Please sign in again.\",\n  refresh_token_already_used: \"Authentication expired. Please sign in again.\",\n\n  // User/account errors\n  user_not_found: \"User account not found.\",\n  user_banned: \"Your account has been temporarily suspended.\",\n  email_not_confirmed: \"Please verify your email address before signing in.\",\n  phone_not_confirmed: \"Please verify your phone number before signing in.\",\n  invalid_credentials: \"Invalid email or password.\",\n\n  // Signup/registration errors\n  signup_disabled: \"New account registration is currently disabled.\",\n  email_exists: \"An account with this email already exists.\",\n  phone_exists: \"An account with this phone number already exists.\",\n  weak_password: \"Password does not meet security requirements.\",\n  email_address_invalid: \"Please enter a valid email address.\",\n  email_address_not_authorized: \"This email address is not authorized for registration.\",\n\n  // Provider/OAuth errors\n  provider_disabled: \"This sign-in method is currently disabled.\",\n  oauth_provider_not_supported: \"This sign-in provider is not supported.\",\n  provider_email_needs_verification: \"Please verify your email address to complete sign-in.\",\n\n  // Validation errors\n  validation_failed: \"Please check your input and try again.\",\n  bad_json: \"Invalid request format. Please try again.\",\n\n  // MFA errors\n  mfa_challenge_expired: \"MFA challenge expired. Please try again.\",\n  mfa_verification_failed: \"Invalid MFA code. Please try again.\",\n  insufficient_aal: \"Additional authentication required.\",\n\n  // CAPTCHA errors\n  captcha_failed: \"CAPTCHA verification failed. Please try again.\",\n\n  // General errors\n  conflict: \"A conflict occurred. Please try again.\",\n  request_timeout: \"Request timed out. Please try again.\",\n  unexpected_failure: \"An unexpected error occurred. Please try again.\",\n  same_password: \"New password must be different from your current password.\",\n\n  // Flow state errors\n  flow_state_expired: \"Authentication session expired. Please start over.\",\n  flow_state_not_found: \"Authentication session not found. Please start over.\",\n\n  // Reauthentication errors\n  reauthentication_needed: \"Please verify your identity to continue.\",\n  reauthentication_not_valid: \"Identity verification failed. Please try again.\",\n};\n\n/**\n * Default error message for unknown error codes\n */\nconst DEFAULT_ERROR_MESSAGE = \"An error occurred. Please try again.\";\n\n/**\n * Handles Supabase auth errors and returns user-friendly messages\n * @param error - The error object from Supabase\n * @returns User-friendly error message\n */\nexport function handleSupabaseAuthError(error: AuthError | Error | null): string {\n  if (!error) {\n    return DEFAULT_ERROR_MESSAGE;\n  }\n\n  // Check if it's an AuthError with a code property\n  if ('code' in error && error.code) {\n    const userMessage = AUTH_ERROR_MESSAGES[error.code];\n    if (userMessage) {\n      return userMessage;\n    }\n  }\n\n  // Check if it's an AuthError with a message we can parse for specific cases\n  if (error.message) {\n      const message = error.message.toLowerCase();\n    \n    // Handle common network-related messages first\n    if (message.includes('failed to fetch') || message.includes('network request failed') || message.includes('network error')) {\n      return \"Network error. Please check your internet connection and try again.\";\n    }\n\n    // Handle other common message patterns that might not have specific codes\n    if (message.includes('token has expired') || message.includes('expired')) {\n      return \"Your session has expired. Please sign in again.\";\n    }\n    \n    if (message.includes('invalid token') || message.includes('invalid otp')) {\n      return \"Invalid code. Please check and try again.\";\n    }\n    \n    if (message.includes('rate limit') || message.includes('too many')) {\n      return \"Too many attempts. Please wait before trying again.\";\n    }\n    \n    if (message.includes('email already exists') || message.includes('user already exists')) {\n      return \"An account with this email already exists.\";\n    }\n    \n    if (message.includes('invalid email')) {\n      return \"Please enter a valid email address.\";\n    }\n    \n    if (message.includes('weak password')) {\n      return \"Password does not meet security requirements.\";\n    }\n  }\n\n  // Return default message for unknown errors\n  return DEFAULT_ERROR_MESSAGE;\n}\n\n/**\n * Checks if an error is a rate limit error\n * @param error - The error object from Supabase\n * @returns True if it's a rate limit error\n */\nexport function isRateLimitError(error: AuthError | Error | null): boolean {\n  if (!error) return false;\n\n  if ('code' in error && error.code) {\n    return [\n      'over_email_send_rate_limit',\n      'over_request_rate_limit',\n      'over_sms_send_rate_limit'\n    ].includes(error.code);\n  }\n\n  return false;\n}\n\n/**\n * Checks if an error is specifically an email rate limit error\n * This indicates Supabase is trying to send verification emails instead of OTP\n * @param error - The error object from Supabase\n * @returns True if it's an email rate limit error\n */\nexport function isEmailRateLimitError(error: AuthError | Error | null): boolean {\n  if (!error) return false;\n\n  if ('code' in error && error.code) {\n    return error.code === 'over_email_send_rate_limit';\n  }\n\n  return false;\n}\n\n/**\n * Checks if an error is a temporary/retryable error\n * @param error - The error object from Supabase\n * @returns True if the error is temporary and user should retry\n */\nexport function isTemporaryError(error: AuthError | Error | null): boolean {\n  if (!error) return false;\n  \n  if ('code' in error && error.code) {\n    return [\n      'over_email_send_rate_limit',\n      'over_request_rate_limit',\n      'over_sms_send_rate_limit',\n      'request_timeout',\n      'conflict',\n      'unexpected_failure'\n    ].includes(error.code);\n  }\n  \n  return false;\n}\n\n/**\n * Gets the retry delay in seconds for rate limit errors\n * @param error - The error object from Supabase\n * @returns Suggested retry delay in seconds, or null if not applicable\n */\nexport function getRetryDelay(error: AuthError | Error | null): number | null {\n  if (!error || !isRateLimitError(error)) return null;\n  \n  if ('code' in error && error.code) {\n    switch (error.code) {\n      case 'over_email_send_rate_limit':\n        return 60; // 1 minute for email rate limits\n      case 'over_sms_send_rate_limit':\n        return 60; // 1 minute for SMS rate limits\n      case 'over_request_rate_limit':\n        return 300; // 5 minutes for general rate limits\n      default:\n        return 60;\n    }\n  }\n  \n  return null;\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;;CAEC,GACD,MAAM,sBAA8C;IAClD,uBAAuB;IACvB,4BAA4B;IAC5B,yBAAyB;IACzB,0BAA0B;IAE1B,qBAAqB;IACrB,aAAa;IACb,cAAc;IAEd,mBAAmB;IACnB,SAAS;IACT,iBAAiB;IACjB,mBAAmB;IACnB,yBAAyB;IACzB,4BAA4B;IAE5B,sBAAsB;IACtB,gBAAgB;IAChB,aAAa;IACb,qBAAqB;IACrB,qBAAqB;IACrB,qBAAqB;IAErB,6BAA6B;IAC7B,iBAAiB;IACjB,cAAc;IACd,cAAc;IACd,eAAe;IACf,uBAAuB;IACvB,8BAA8B;IAE9B,wBAAwB;IACxB,mBAAmB;IACnB,8BAA8B;IAC9B,mCAAmC;IAEnC,oBAAoB;IACpB,mBAAmB;IACnB,UAAU;IAEV,aAAa;IACb,uBAAuB;IACvB,yBAAyB;IACzB,kBAAkB;IAElB,iBAAiB;IACjB,gBAAgB;IAEhB,iBAAiB;IACjB,UAAU;IACV,iBAAiB;IACjB,oBAAoB;IACpB,eAAe;IAEf,oBAAoB;IACpB,oBAAoB;IACpB,sBAAsB;IAEtB,0BAA0B;IAC1B,yBAAyB;IACzB,4BAA4B;AAC9B;AAEA;;CAEC,GACD,MAAM,wBAAwB;AAOvB,SAAS,wBAAwB,KAA+B;IACrE,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,kDAAkD;IAClD,IAAI,UAAU,SAAS,MAAM,IAAI,EAAE;QACjC,MAAM,cAAc,mBAAmB,CAAC,MAAM,IAAI,CAAC;QACnD,IAAI,aAAa;YACf,OAAO;QACT;IACF;IAEA,4EAA4E;IAC5E,IAAI,MAAM,OAAO,EAAE;QACf,MAAM,UAAU,MAAM,OAAO,CAAC,WAAW;QAE3C,+CAA+C;QAC/C,IAAI,QAAQ,QAAQ,CAAC,sBAAsB,QAAQ,QAAQ,CAAC,6BAA6B,QAAQ,QAAQ,CAAC,kBAAkB;YAC1H,OAAO;QACT;QAEA,0EAA0E;QAC1E,IAAI,QAAQ,QAAQ,CAAC,wBAAwB,QAAQ,QAAQ,CAAC,YAAY;YACxE,OAAO;QACT;QAEA,IAAI,QAAQ,QAAQ,CAAC,oBAAoB,QAAQ,QAAQ,CAAC,gBAAgB;YACxE,OAAO;QACT;QAEA,IAAI,QAAQ,QAAQ,CAAC,iBAAiB,QAAQ,QAAQ,CAAC,aAAa;YAClE,OAAO;QACT;QAEA,IAAI,QAAQ,QAAQ,CAAC,2BAA2B,QAAQ,QAAQ,CAAC,wBAAwB;YACvF,OAAO;QACT;QAEA,IAAI,QAAQ,QAAQ,CAAC,kBAAkB;YACrC,OAAO;QACT;QAEA,IAAI,QAAQ,QAAQ,CAAC,kBAAkB;YACrC,OAAO;QACT;IACF;IAEA,4CAA4C;IAC5C,OAAO;AACT;AAOO,SAAS,iBAAiB,KAA+B;IAC9D,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI,UAAU,SAAS,MAAM,IAAI,EAAE;QACjC,OAAO;YACL;YACA;YACA;SACD,CAAC,QAAQ,CAAC,MAAM,IAAI;IACvB;IAEA,OAAO;AACT;AAQO,SAAS,sBAAsB,KAA+B;IACnE,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI,UAAU,SAAS,MAAM,IAAI,EAAE;QACjC,OAAO,MAAM,IAAI,KAAK;IACxB;IAEA,OAAO;AACT;AAOO,SAAS,iBAAiB,KAA+B;IAC9D,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI,UAAU,SAAS,MAAM,IAAI,EAAE;QACjC,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;SACD,CAAC,QAAQ,CAAC,MAAM,IAAI;IACvB;IAEA,OAAO;AACT;AAOO,SAAS,cAAc,KAA+B;IAC3D,IAAI,CAAC,SAAS,CAAC,iBAAiB,QAAQ,OAAO;IAE/C,IAAI,UAAU,SAAS,MAAM,IAAI,EAAE;QACjC,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,OAAO,IAAI,iCAAiC;YAC9C,KAAK;gBACH,OAAO,IAAI,+BAA+B;YAC5C,KAAK;gBACH,OAAO,KAAK,oCAAoC;YAClD;gBACE,OAAO;QACX;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1126, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { signInWithOtp, verifyOtp, signInWithPassword } from \"@/lib/supabase/services/sharedService\";\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { z } from \"zod\";\r\nimport { EmailOTPSchema, VerifyOTPSchema, MobilePasswordLoginSchema } from \"@/lib/schemas/authSchemas\";\r\nimport { handleSupabaseAuthError, isEmailRateLimitError } from \"@/lib/utils/supabaseErrorHandler\";\r\n\r\n// Validate email format\r\nfunction validateEmail(email: string): { isValid: boolean; message?: string } {\r\n  if (!email) {\r\n    return { isValid: false, message: 'Email is required' };\r\n  }\r\n  \r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  if (!emailRegex.test(email)) {\r\n    return { isValid: false, message: 'Please enter a valid email address' };\r\n  }\r\n  \r\n  return { isValid: true };\r\n}\r\n\r\n// Validate OTP format\r\nfunction validateOTP(otp: string): { isValid: boolean; message?: string } {\r\n  if (!otp) {\r\n    return { isValid: false, message: 'OTP is required' };\r\n  }\r\n  \r\n  if (otp.length !== 6) {\r\n    return { isValid: false, message: 'OTP must be 6 digits' };\r\n  }\r\n  \r\n  if (!/^\\d{6}$/.test(otp)) {\r\n    return { isValid: false, message: 'OTP must contain only numbers' };\r\n  }\r\n  \r\n  return { isValid: true };\r\n}\r\n\r\n// Send OTP to email\r\nexport async function sendOTP(values: z.infer<typeof EmailOTPSchema>) {\r\n  const { email } = values;\r\n  const emailValidation = validateEmail(email);\r\n  if (!emailValidation.isValid) {\r\n    return {\r\n      success: false,\r\n      error: emailValidation.message,\r\n    };\r\n  }\r\n\r\n  try {\r\n    const supabase = await createClient();\r\n    const { error } = await signInWithOtp(supabase, email, true);\r\n\r\n    if (error) {\r\n      if (isEmailRateLimitError(error)) {\r\n        return {\r\n          success: false,\r\n          error: \"Email rate limit exceeded. This indicates a configuration issue with OTP authentication. Please contact support.\",\r\n          isConfigurationError: true,\r\n        };\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        error: handleSupabaseAuthError(error),\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      message: \"OTP sent to your email address. Please check your inbox.\",\r\n    };\r\n  } catch (error) {\r\n    if (isEmailRateLimitError(error as Error)) {\r\n      return {\r\n        success: false,\r\n        error: \"Email rate limit exceeded. This indicates a configuration issue with OTP authentication. Please contact support.\",\r\n        isConfigurationError: true,\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: handleSupabaseAuthError(error as Error),\r\n    };\r\n  }\r\n}\r\n\r\n// Verify OTP and sign in\r\nexport async function verifyOTP(values: z.infer<typeof VerifyOTPSchema>) {\r\n  const { email, otp } = values;\r\n  const otpValidation = validateOTP(otp);\r\n  if (!otpValidation.isValid) {\r\n    return {\r\n      success: false,\r\n      error: otpValidation.message,\r\n    };\r\n  }\r\n  \r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { data, error } = await verifyOtp(supabase, email, otp);\r\n\r\n    if (error) {\r\n      return {\r\n        success: false,\r\n        error: handleSupabaseAuthError(error),\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data,\r\n      message: \"Successfully signed in!\",\r\n    };\r\n  } catch (error) {\r\n    return {\r\n      success: false,\r\n      error: handleSupabaseAuthError(error as Error),\r\n    };\r\n  }\r\n}\r\n\r\n// Mobile + password login\r\nexport async function loginWithMobilePassword(values: z.infer<typeof MobilePasswordLoginSchema>) {\r\n  const validatedFields = MobilePasswordLoginSchema.safeParse(values);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      success: false,\r\n      error: \"Invalid mobile number or password format\",\r\n    };\r\n  }\r\n\r\n  const { mobile, password } = validatedFields.data;\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    // Format mobile number with +91 prefix\r\n    const phoneNumber = `+91${mobile}`;\r\n\r\n    const { data, error } = await signInWithPassword(supabase, phoneNumber, password);\r\n\r\n    if (error) {\r\n      return {\r\n        success: false,\r\n        error: handleSupabaseAuthError(error),\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data,\r\n      message: \"Successfully signed in!\",\r\n    };\r\n  } catch (error) {\r\n    return {\r\n      success: false,\r\n      error: handleSupabaseAuthError(error as Error),\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AACA;;;;;;;;AAEA,wBAAwB;AACxB,SAAS,cAAc,KAAa;IAClC,IAAI,CAAC,OAAO;QACV,OAAO;YAAE,SAAS;YAAO,SAAS;QAAoB;IACxD;IAEA,MAAM,aAAa;IACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;QAC3B,OAAO;YAAE,SAAS;YAAO,SAAS;QAAqC;IACzE;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAEA,sBAAsB;AACtB,SAAS,YAAY,GAAW;IAC9B,IAAI,CAAC,KAAK;QACR,OAAO;YAAE,SAAS;YAAO,SAAS;QAAkB;IACtD;IAEA,IAAI,IAAI,MAAM,KAAK,GAAG;QACpB,OAAO;YAAE,SAAS;YAAO,SAAS;QAAuB;IAC3D;IAEA,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM;QACxB,OAAO;YAAE,SAAS;YAAO,SAAS;QAAgC;IACpE;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,eAAe,QAAQ,MAAsC;IAClE,MAAM,EAAE,KAAK,EAAE,GAAG;IAClB,MAAM,kBAAkB,cAAc;IACtC,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;YACL,SAAS;YACT,OAAO,gBAAgB,OAAO;QAChC;IACF;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,4IAAA,CAAA,gBAAa,AAAD,EAAE,UAAU,OAAO;QAEvD,IAAI,OAAO;YACT,IAAI,CAAA,GAAA,oIAAA,CAAA,wBAAqB,AAAD,EAAE,QAAQ;gBAChC,OAAO;oBACL,SAAS;oBACT,OAAO;oBACP,sBAAsB;gBACxB;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,OAAO,CAAA,GAAA,oIAAA,CAAA,0BAAuB,AAAD,EAAE;YACjC;QACF;QAEA,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,IAAI,CAAA,GAAA,oIAAA,CAAA,wBAAqB,AAAD,EAAE,QAAiB;YACzC,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,sBAAsB;YACxB;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO,CAAA,GAAA,oIAAA,CAAA,0BAAuB,AAAD,EAAE;QACjC;IACF;AACF;AAGO,eAAe,UAAU,MAAuC;IACrE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG;IACvB,MAAM,gBAAgB,YAAY;IAClC,IAAI,CAAC,cAAc,OAAO,EAAE;QAC1B,OAAO;YACL,SAAS;YACT,OAAO,cAAc,OAAO;QAC9B;IACF;IAEA,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,4IAAA,CAAA,YAAS,AAAD,EAAE,UAAU,OAAO;QAEzD,IAAI,OAAO;YACT,OAAO;gBACL,SAAS;gBACT,OAAO,CAAA,GAAA,oIAAA,CAAA,0BAAuB,AAAD,EAAE;YACjC;QACF;QAEA,OAAO;YACL,SAAS;YACT,MAAM;YACN,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,SAAS;YACT,OAAO,CAAA,GAAA,oIAAA,CAAA,0BAAuB,AAAD,EAAE;QACjC;IACF;AACF;AAGO,eAAe,wBAAwB,MAAiD;IAC7F,MAAM,kBAAkB,6HAAA,CAAA,4BAAyB,CAAC,SAAS,CAAC;IAE5D,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,gBAAgB,IAAI;IACjD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,uCAAuC;QACvC,MAAM,cAAc,CAAC,GAAG,EAAE,QAAQ;QAElC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,4IAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,aAAa;QAExE,IAAI,OAAO;YACT,OAAO;gBACL,SAAS;gBACT,OAAO,CAAA,GAAA,oIAAA,CAAA,0BAAuB,AAAD,EAAE;YACjC;QACF;QAEA,OAAO;YACL,SAAS;YACT,MAAM;YACN,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,SAAS;YACT,OAAO,CAAA,GAAA,oIAAA,CAAA,0BAAuB,AAAD,EAAE;QACjC;IACF;AACF;;;IA3HsB;IAkDA;IAoCA;;AAtFA,+OAAA;AAkDA,+OAAA;AAoCA,+OAAA", "debugId": null}}, {"offset": {"line": 1307, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/.next-internal/server/app/%28main%29/login/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {sendOTP as '4039928e7548d26b67a82d94754953987e64086b42'} from 'ACTIONS_MODULE0'\nexport {verifyOTP as '408a3fc0038eb1b725d89d6b69dcc55b2b1cd06dd3'} from 'ACTIONS_MODULE0'\nexport {loginWithMobilePassword as '404769c266f0c071880e95e6a0dfe29621565ed871'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 1377, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/LoginForm.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LoginForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call LoginForm() from the server but LoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(main)/login/LoginForm.tsx <module evaluation>\",\n    \"LoginForm\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,gEACA", "debugId": null}}, {"offset": {"line": 1391, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/LoginForm.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LoginForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call LoginForm() from the server but LoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(main)/login/LoginForm.tsx\",\n    \"LoginForm\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,4CACA", "debugId": null}}, {"offset": {"line": 1405, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1415, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/auth/AuthPageBackground.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(main)/components/auth/AuthPageBackground.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(main)/components/auth/AuthPageBackground.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqT,GAClV,mFACA", "debugId": null}}, {"offset": {"line": 1429, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/auth/AuthPageBackground.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(main)/components/auth/AuthPageBackground.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(main)/components/auth/AuthPageBackground.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "debugId": null}}, {"offset": {"line": 1443, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1453, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/page.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport { LoginForm } from \"./LoginForm\";\nimport { Suspense } from \"react\";\nimport AuthPageBackground from \"../components/auth/AuthPageBackground\";\n\nexport async function generateMetadata(): Promise<Metadata> {\n  const title = \"Sign In\";\n  const description =\n    \"Sign in to your Dukancard account or create a new account with just your email address.\";\n\n  return {\n    title, // Uses template: \"Sign In - Dukancard\"\n    description,\n    robots: \"noindex, follow\", // Prevent indexing\n    // Keywords are generally not needed for noindex pages\n  };\n}\n\nexport default function LoginPage() {\n  return (\n    // Use semantic background and add top padding\n    <div className=\"w-full min-h-[calc(100vh-80px)] md:min-h-[calc(100vh-64px)] flex items-center justify-center bg-neutral-50 dark:bg-neutral-950 p-2 sm:p-4 pt-6 pb-20 md:pb-6 relative overflow-hidden\">\n      {/* Add animated background */}\n      <AuthPageBackground />\n\n      <Suspense\n        fallback={\n          <div className=\"flex flex-col justify-center items-center min-h-screen gap-2 relative z-10\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[var(--brand-gold)]\"></div>\n            <p className=\"text-muted-foreground\">Loading sign in form...</p>\n          </div>\n        }\n      >\n        <LoginForm />\n      </Suspense>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;;;;;AAEO,eAAe;IACpB,MAAM,QAAQ;IACd,MAAM,cACJ;IAEF,OAAO;QACL;QACA;QACA,QAAQ;IAEV;AACF;AAEe,SAAS;IACtB,OACE,8CAA8C;kBAC9C,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,4JAAA,CAAA,UAAkB;;;;;0BAEnB,8OAAC,qMAAA,CAAA,WAAQ;gBACP,wBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;0BAIzC,cAAA,8OAAC,sIAAA,CAAA,YAAS;;;;;;;;;;;;;;;;AAIlB", "debugId": null}}]}