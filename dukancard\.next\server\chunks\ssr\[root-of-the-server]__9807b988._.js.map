{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { signInWithOtp, verifyOtp, signInWithPassword } from \"@/lib/supabase/services/sharedService\";\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { z } from \"zod\";\r\nimport { EmailOTPSchema, VerifyOTPSchema, MobilePasswordLoginSchema } from \"@/lib/schemas/authSchemas\";\r\nimport { handleSupabaseAuthError, isEmailRateLimitError } from \"@/lib/utils/supabaseErrorHandler\";\r\n\r\n// Validate email format\r\nfunction validateEmail(email: string): { isValid: boolean; message?: string } {\r\n  if (!email) {\r\n    return { isValid: false, message: 'Email is required' };\r\n  }\r\n  \r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  if (!emailRegex.test(email)) {\r\n    return { isValid: false, message: 'Please enter a valid email address' };\r\n  }\r\n  \r\n  return { isValid: true };\r\n}\r\n\r\n// Validate OTP format\r\nfunction validateOTP(otp: string): { isValid: boolean; message?: string } {\r\n  if (!otp) {\r\n    return { isValid: false, message: 'OTP is required' };\r\n  }\r\n  \r\n  if (otp.length !== 6) {\r\n    return { isValid: false, message: 'OTP must be 6 digits' };\r\n  }\r\n  \r\n  if (!/^\\d{6}$/.test(otp)) {\r\n    return { isValid: false, message: 'OTP must contain only numbers' };\r\n  }\r\n  \r\n  return { isValid: true };\r\n}\r\n\r\n// Send OTP to email\r\nexport async function sendOTP(values: z.infer<typeof EmailOTPSchema>) {\r\n  const { email } = values;\r\n  const emailValidation = validateEmail(email);\r\n  if (!emailValidation.isValid) {\r\n    return {\r\n      success: false,\r\n      error: emailValidation.message,\r\n    };\r\n  }\r\n\r\n  try {\r\n    const supabase = await createClient();\r\n    const { error } = await signInWithOtp(supabase, email, true);\r\n\r\n    if (error) {\r\n      if (isEmailRateLimitError(error)) {\r\n        return {\r\n          success: false,\r\n          error: \"Email rate limit exceeded. This indicates a configuration issue with OTP authentication. Please contact support.\",\r\n          isConfigurationError: true,\r\n        };\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        error: handleSupabaseAuthError(error),\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      message: \"OTP sent to your email address. Please check your inbox.\",\r\n    };\r\n  } catch (error) {\r\n    if (isEmailRateLimitError(error as Error)) {\r\n      return {\r\n        success: false,\r\n        error: \"Email rate limit exceeded. This indicates a configuration issue with OTP authentication. Please contact support.\",\r\n        isConfigurationError: true,\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: handleSupabaseAuthError(error as Error),\r\n    };\r\n  }\r\n}\r\n\r\n// Verify OTP and sign in\r\nexport async function verifyOTP(values: z.infer<typeof VerifyOTPSchema>) {\r\n  const { email, otp } = values;\r\n  const otpValidation = validateOTP(otp);\r\n  if (!otpValidation.isValid) {\r\n    return {\r\n      success: false,\r\n      error: otpValidation.message,\r\n    };\r\n  }\r\n  \r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { data, error } = await verifyOtp(supabase, email, otp);\r\n\r\n    if (error) {\r\n      return {\r\n        success: false,\r\n        error: handleSupabaseAuthError(error),\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data,\r\n      message: \"Successfully signed in!\",\r\n    };\r\n  } catch (error) {\r\n    return {\r\n      success: false,\r\n      error: handleSupabaseAuthError(error as Error),\r\n    };\r\n  }\r\n}\r\n\r\n// Mobile + password login\r\nexport async function loginWithMobilePassword(values: z.infer<typeof MobilePasswordLoginSchema>) {\r\n  const validatedFields = MobilePasswordLoginSchema.safeParse(values);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      success: false,\r\n      error: \"Invalid mobile number or password format\",\r\n    };\r\n  }\r\n\r\n  const { mobile, password } = validatedFields.data;\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    // Format mobile number with +91 prefix\r\n    const phoneNumber = `+91${mobile}`;\r\n\r\n    const { data, error } = await signInWithPassword(supabase, phoneNumber, password);\r\n\r\n    if (error) {\r\n      return {\r\n        success: false,\r\n        error: handleSupabaseAuthError(error),\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data,\r\n      message: \"Successfully signed in!\",\r\n    };\r\n  } catch (error) {\r\n    return {\r\n      success: false,\r\n      error: handleSupabaseAuthError(error as Error),\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAwCsB,UAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { signInWithOtp, verifyOtp, signInWithPassword } from \"@/lib/supabase/services/sharedService\";\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { z } from \"zod\";\r\nimport { EmailOTPSchema, VerifyOTPSchema, MobilePasswordLoginSchema } from \"@/lib/schemas/authSchemas\";\r\nimport { handleSupabaseAuthError, isEmailRateLimitError } from \"@/lib/utils/supabaseErrorHandler\";\r\n\r\n// Validate email format\r\nfunction validateEmail(email: string): { isValid: boolean; message?: string } {\r\n  if (!email) {\r\n    return { isValid: false, message: 'Email is required' };\r\n  }\r\n  \r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  if (!emailRegex.test(email)) {\r\n    return { isValid: false, message: 'Please enter a valid email address' };\r\n  }\r\n  \r\n  return { isValid: true };\r\n}\r\n\r\n// Validate OTP format\r\nfunction validateOTP(otp: string): { isValid: boolean; message?: string } {\r\n  if (!otp) {\r\n    return { isValid: false, message: 'OTP is required' };\r\n  }\r\n  \r\n  if (otp.length !== 6) {\r\n    return { isValid: false, message: 'OTP must be 6 digits' };\r\n  }\r\n  \r\n  if (!/^\\d{6}$/.test(otp)) {\r\n    return { isValid: false, message: 'OTP must contain only numbers' };\r\n  }\r\n  \r\n  return { isValid: true };\r\n}\r\n\r\n// Send OTP to email\r\nexport async function sendOTP(values: z.infer<typeof EmailOTPSchema>) {\r\n  const { email } = values;\r\n  const emailValidation = validateEmail(email);\r\n  if (!emailValidation.isValid) {\r\n    return {\r\n      success: false,\r\n      error: emailValidation.message,\r\n    };\r\n  }\r\n\r\n  try {\r\n    const supabase = await createClient();\r\n    const { error } = await signInWithOtp(supabase, email, true);\r\n\r\n    if (error) {\r\n      if (isEmailRateLimitError(error)) {\r\n        return {\r\n          success: false,\r\n          error: \"Email rate limit exceeded. This indicates a configuration issue with OTP authentication. Please contact support.\",\r\n          isConfigurationError: true,\r\n        };\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        error: handleSupabaseAuthError(error),\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      message: \"OTP sent to your email address. Please check your inbox.\",\r\n    };\r\n  } catch (error) {\r\n    if (isEmailRateLimitError(error as Error)) {\r\n      return {\r\n        success: false,\r\n        error: \"Email rate limit exceeded. This indicates a configuration issue with OTP authentication. Please contact support.\",\r\n        isConfigurationError: true,\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: handleSupabaseAuthError(error as Error),\r\n    };\r\n  }\r\n}\r\n\r\n// Verify OTP and sign in\r\nexport async function verifyOTP(values: z.infer<typeof VerifyOTPSchema>) {\r\n  const { email, otp } = values;\r\n  const otpValidation = validateOTP(otp);\r\n  if (!otpValidation.isValid) {\r\n    return {\r\n      success: false,\r\n      error: otpValidation.message,\r\n    };\r\n  }\r\n  \r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { data, error } = await verifyOtp(supabase, email, otp);\r\n\r\n    if (error) {\r\n      return {\r\n        success: false,\r\n        error: handleSupabaseAuthError(error),\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data,\r\n      message: \"Successfully signed in!\",\r\n    };\r\n  } catch (error) {\r\n    return {\r\n      success: false,\r\n      error: handleSupabaseAuthError(error as Error),\r\n    };\r\n  }\r\n}\r\n\r\n// Mobile + password login\r\nexport async function loginWithMobilePassword(values: z.infer<typeof MobilePasswordLoginSchema>) {\r\n  const validatedFields = MobilePasswordLoginSchema.safeParse(values);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      success: false,\r\n      error: \"Invalid mobile number or password format\",\r\n    };\r\n  }\r\n\r\n  const { mobile, password } = validatedFields.data;\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    // Format mobile number with +91 prefix\r\n    const phoneNumber = `+91${mobile}`;\r\n\r\n    const { data, error } = await signInWithPassword(supabase, phoneNumber, password);\r\n\r\n    if (error) {\r\n      return {\r\n        success: false,\r\n        error: handleSupabaseAuthError(error),\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data,\r\n      message: \"Successfully signed in!\",\r\n    };\r\n  } catch (error) {\r\n    return {\r\n      success: false,\r\n      error: handleSupabaseAuthError(error as Error),\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA0FsB,YAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { signInWithOtp, verifyOtp, signInWithPassword } from \"@/lib/supabase/services/sharedService\";\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { z } from \"zod\";\r\nimport { EmailOTPSchema, VerifyOTPSchema, MobilePasswordLoginSchema } from \"@/lib/schemas/authSchemas\";\r\nimport { handleSupabaseAuthError, isEmailRateLimitError } from \"@/lib/utils/supabaseErrorHandler\";\r\n\r\n// Validate email format\r\nfunction validateEmail(email: string): { isValid: boolean; message?: string } {\r\n  if (!email) {\r\n    return { isValid: false, message: 'Email is required' };\r\n  }\r\n  \r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  if (!emailRegex.test(email)) {\r\n    return { isValid: false, message: 'Please enter a valid email address' };\r\n  }\r\n  \r\n  return { isValid: true };\r\n}\r\n\r\n// Validate OTP format\r\nfunction validateOTP(otp: string): { isValid: boolean; message?: string } {\r\n  if (!otp) {\r\n    return { isValid: false, message: 'OTP is required' };\r\n  }\r\n  \r\n  if (otp.length !== 6) {\r\n    return { isValid: false, message: 'OTP must be 6 digits' };\r\n  }\r\n  \r\n  if (!/^\\d{6}$/.test(otp)) {\r\n    return { isValid: false, message: 'OTP must contain only numbers' };\r\n  }\r\n  \r\n  return { isValid: true };\r\n}\r\n\r\n// Send OTP to email\r\nexport async function sendOTP(values: z.infer<typeof EmailOTPSchema>) {\r\n  const { email } = values;\r\n  const emailValidation = validateEmail(email);\r\n  if (!emailValidation.isValid) {\r\n    return {\r\n      success: false,\r\n      error: emailValidation.message,\r\n    };\r\n  }\r\n\r\n  try {\r\n    const supabase = await createClient();\r\n    const { error } = await signInWithOtp(supabase, email, true);\r\n\r\n    if (error) {\r\n      if (isEmailRateLimitError(error)) {\r\n        return {\r\n          success: false,\r\n          error: \"Email rate limit exceeded. This indicates a configuration issue with OTP authentication. Please contact support.\",\r\n          isConfigurationError: true,\r\n        };\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        error: handleSupabaseAuthError(error),\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      message: \"OTP sent to your email address. Please check your inbox.\",\r\n    };\r\n  } catch (error) {\r\n    if (isEmailRateLimitError(error as Error)) {\r\n      return {\r\n        success: false,\r\n        error: \"Email rate limit exceeded. This indicates a configuration issue with OTP authentication. Please contact support.\",\r\n        isConfigurationError: true,\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: handleSupabaseAuthError(error as Error),\r\n    };\r\n  }\r\n}\r\n\r\n// Verify OTP and sign in\r\nexport async function verifyOTP(values: z.infer<typeof VerifyOTPSchema>) {\r\n  const { email, otp } = values;\r\n  const otpValidation = validateOTP(otp);\r\n  if (!otpValidation.isValid) {\r\n    return {\r\n      success: false,\r\n      error: otpValidation.message,\r\n    };\r\n  }\r\n  \r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { data, error } = await verifyOtp(supabase, email, otp);\r\n\r\n    if (error) {\r\n      return {\r\n        success: false,\r\n        error: handleSupabaseAuthError(error),\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data,\r\n      message: \"Successfully signed in!\",\r\n    };\r\n  } catch (error) {\r\n    return {\r\n      success: false,\r\n      error: handleSupabaseAuthError(error as Error),\r\n    };\r\n  }\r\n}\r\n\r\n// Mobile + password login\r\nexport async function loginWithMobilePassword(values: z.infer<typeof MobilePasswordLoginSchema>) {\r\n  const validatedFields = MobilePasswordLoginSchema.safeParse(values);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      success: false,\r\n      error: \"Invalid mobile number or password format\",\r\n    };\r\n  }\r\n\r\n  const { mobile, password } = validatedFields.data;\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    // Format mobile number with +91 prefix\r\n    const phoneNumber = `+91${mobile}`;\r\n\r\n    const { data, error } = await signInWithPassword(supabase, phoneNumber, password);\r\n\r\n    if (error) {\r\n      return {\r\n        success: false,\r\n        error: handleSupabaseAuthError(error),\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data,\r\n      message: \"Successfully signed in!\",\r\n    };\r\n  } catch (error) {\r\n    return {\r\n      success: false,\r\n      error: handleSupabaseAuthError(error as Error),\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA8HsB,0BAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport {\r\n  Controller,\r\n  FormProvider,\r\n  useFormContext,\r\n  useFormState,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues,\r\n} from \"react-hook-form\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Label } from \"@/components/ui/label\"\r\n\r\nconst Form = FormProvider\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n> = {\r\n  name: TName\r\n}\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\r\n  {} as FormFieldContextValue\r\n)\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  )\r\n}\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext)\r\n  const itemContext = React.useContext(FormItemContext)\r\n  const { getFieldState } = useFormContext()\r\n  const formState = useFormState({ name: fieldContext.name })\r\n  const fieldState = getFieldState(fieldContext.name, formState)\r\n\r\n  if (!fieldContext) {\r\n    throw new Error(\"useFormField should be used within <FormField>\")\r\n  }\r\n\r\n  const { id } = itemContext\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  }\r\n}\r\n\r\ntype FormItemContextValue = {\r\n  id: string\r\n}\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>(\r\n  {} as FormItemContextValue\r\n)\r\n\r\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  const id = React.useId()\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div\r\n        data-slot=\"form-item\"\r\n        className={cn(\"grid gap-2\", className)}\r\n        {...props}\r\n      />\r\n    </FormItemContext.Provider>\r\n  )\r\n}\r\n\r\nfunction FormLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  const { error, formItemId } = useFormField()\r\n\r\n  return (\r\n    <Label\r\n      data-slot=\"form-label\"\r\n      data-error={!!error}\r\n      className={cn(\"data-[error=true]:text-destructive\", className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\r\n\r\n  return (\r\n    <Slot\r\n      data-slot=\"form-control\"\r\n      id={formItemId}\r\n      aria-describedby={\r\n        !error\r\n          ? `${formDescriptionId}`\r\n          : `${formDescriptionId} ${formMessageId}`\r\n      }\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\r\n  const { formDescriptionId } = useFormField()\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-description\"\r\n      id={formDescriptionId}\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\r\n  const { error, formMessageId } = useFormField()\r\n  const body = error ? String(error?.message ?? \"\") : props.children\r\n\r\n  if (!body) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-message\"\r\n      id={formMessageId}\r\n      className={cn(\"text-destructive text-sm\", className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  )\r\n}\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;AAhBA;;;;;;;AAkBA,MAAM,OAAO,8JAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,8OAAC,8JAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,8JAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAErB,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC,0HAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,GAAG,OAA0C;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,8OAAC,gKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/input-otp.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { OTPInput, OTPInputContext } from \"input-otp\"\r\nimport { MinusIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction InputOTP({\r\n  className,\r\n  containerClassName,\r\n  ...props\r\n}: React.ComponentProps<typeof OTPInput> & {\r\n  containerClassName?: string\r\n}) {\r\n  return (\r\n    <OTPInput\r\n      data-slot=\"input-otp\"\r\n      containerClassName={cn(\r\n        \"flex items-center gap-2 has-disabled:opacity-50\",\r\n        containerClassName\r\n      )}\r\n      className={cn(\"disabled:cursor-not-allowed\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction InputOTPGroup({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"input-otp-group\"\r\n      className={cn(\"flex items-center\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction InputOTPSlot({\r\n  index,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  index: number\r\n}) {\r\n  const inputOTPContext = React.useContext(OTPInputContext)\r\n  const { char, hasFakeCaret, isActive } = inputOTPContext?.slots[index] ?? {}\r\n\r\n  return (\r\n    <div\r\n      data-slot=\"input-otp-slot\"\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"data-[active=true]:border-ring data-[active=true]:ring-ring/50 data-[active=true]:aria-invalid:ring-destructive/20 dark:data-[active=true]:aria-invalid:ring-destructive/40 aria-invalid:border-destructive data-[active=true]:aria-invalid:border-destructive dark:bg-input/30 border-input relative flex h-9 w-9 items-center justify-center border-y border-r text-sm shadow-xs transition-all outline-none first:rounded-l-md first:border-l last:rounded-r-md data-[active=true]:z-10 data-[active=true]:ring-[3px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {char}\r\n      {hasFakeCaret && (\r\n        <div className=\"pointer-events-none absolute inset-0 flex items-center justify-center\">\r\n          <div className=\"animate-caret-blink bg-foreground h-4 w-px duration-1000\" />\r\n        </div>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction InputOTPSeparator({ ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div data-slot=\"input-otp-separator\" role=\"separator\" {...props}>\r\n      <MinusIcon />\r\n    </div>\r\n  )\r\n}\r\n\r\nexport { InputOTP, InputOTPGroup, InputOTPSlot, InputOTPSeparator }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,kBAAkB,EAClB,GAAG,OAGJ;IACC,qBACE,8OAAC,8IAAA,CAAA,WAAQ;QACP,aAAU;QACV,oBAAoB,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACnB,mDACA;QAEF,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB;QAClC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,KAAK,EACL,SAAS,EACT,GAAG,OAGJ;IACC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,8IAAA,CAAA,kBAAe;IACxD,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG,iBAAiB,KAAK,CAAC,MAAM,IAAI,CAAC;IAE3E,qBACE,8OAAC;QACC,aAAU;QACV,eAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4fACA;QAED,GAAG,KAAK;;YAER;YACA,8BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAKzB;AAEA,SAAS,kBAAkB,EAAE,GAAG,OAAoC;IAClE,qBACE,8OAAC;QAAI,aAAU;QAAsB,MAAK;QAAa,GAAG,KAAK;kBAC7D,cAAA,8OAAC,wMAAA,CAAA,YAAS;;;;;;;;;;AAGhB", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/components/EmailOTPForm.tsx"], "sourcesContent": ["\"use client\"\nimport { zod<PERSON><PERSON>olver } from \"@hookform/resolvers/zod\"\nimport { useForm } from \"react-hook-form\"\nimport { z } from \"zod\"\nimport { Button } from \"@/components/ui/button\"\nimport {\n  Form,\n  FormControl,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage\n} from \"@/components/ui/form\"\nimport { Input } from \"@/components/ui/input\"\nimport {\n  InputOTP,\n  InputOTPGroup,\n  InputOTPSlot\n} from \"@/components/ui/input-otp\"\nimport { ArrowRight, Loader2 } from \"lucide-react\"\n// Email schema for step \nconst emailSchema = z.object({\n  email: z\n    .string()\n    .min(1, { message: \"Email is required\" })\n    .email({ message: \"Please enter a valid email address\" })\n})\n// OTP schema for step \nconst otpSchema = z.object({\n  otp: z\n    .string()\n    .min(6, { message: \"OTP must be 6 digits\" })\n    .max(6, { message: \"OTP must be 6 digits\" })\n    .regex(/^\\d{6}$/, { message: \"OTP must be 6 digits\" })\n})\ninterface EmailOTPFormProps {\n  step: 'email' | 'otp'\n  email: string\n  countdown: number\n  isPending: boolean\n  onEmailSubmit: (_values: z.infer<typeof emailSchema>) => void\n  onOTPSubmit: (_values: { email: string; otp: string }) => void\n  onResendOTP: () => void\n  onBackToEmail: () => void\n}\n\nexport function EmailOTPForm({\n  step,\n  email,\n  countdown,\n  isPending,\n  onEmailSubmit,\n  onOTPSubmit,\n  onResendOTP,\n  onBackToEmail,\n}: EmailOTPFormProps) {\n  const emailForm = useForm<z.infer<typeof emailSchema>>({\n    resolver: zodResolver(emailSchema),\n    defaultValues: {\n      email: \"\"\n    }\n  })\n  const otpForm = useForm<z.infer<typeof otpSchema>>({\n    resolver: zodResolver(otpSchema),\n    defaultValues: {\n      otp: \"\"\n    }\n  })\n  if (step === 'email') {\n    return (\n      <Form {...emailForm}>\n        <form\n          onSubmit={emailForm.handleSubmit(onEmailSubmit)}\n          className=\"space-y-4 sm:space-y-6\"\n        >\n          <FormField\n            control={emailForm.control}\n            name=\"email\"\n            render={({ field }) => (\n              <FormItem>\n                <FormLabel className=\"text-foreground text-sm sm:text-base\">\n                  Email Address\n                </FormLabel>\n                <FormControl>\n                  <Input\n                    id=\"email-login-field\"\n                    placeholder=\"<EMAIL>\"\n                    type=\"email\"\n                    {...field}\n                    className=\"bg-background border-border focus:border-primary dark:bg-neutral-800 dark:border-neutral-700 dark:focus:border-[var(--brand-gold)] h-10 sm:h-11 text-sm sm:text-base\"\n                  />\n                </FormControl>\n                <FormMessage />\n              </FormItem>\n            )}\n          />\n          <Button\n            type=\"submit\"\n            className=\"cursor-pointer w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-sm sm:text-base\"\n            disabled={isPending}\n          >\n            {isPending ? (\n              <>\n                <Loader2 className=\"w-5 h-5 mr-2 animate-spin\" />\n                Sending OTP...\n              </>\n            ) : (\n              <>\n                Continue <ArrowRight className=\"w-5 h-5 ml-2\" />\n              </>\n            )}\n          </Button>\n          <div className=\"text-center mt-4 text-xs sm:text-sm\">\n            <span className=\"text-muted-foreground\">\n              New to Dukancard? No worries! We&apos;ll create your account automatically.\n            </span>\n          </div>\n        </form>\n      </Form>\n    )\n  }\n  return (\n    <Form {...otpForm}>\n      <form\n        onSubmit={otpForm.handleSubmit((values) => {\n          onOTPSubmit({ email, otp: values.otp });\n        })}\n        className=\"space-y-4 sm:space-y-6\"\n      >\n        <div className=\"text-center mb-4\">\n          <p className=\"text-sm text-muted-foreground\">\n            We&apos;ve sent a 6-digit code to\n          </p>\n          <p className=\"text-sm font-medium text-foreground\">{email}</p>\n          <p className=\"text-xs text-muted-foreground mt-2\">\n            Code expires in 24 hours\n          </p>\n        </div>\n        <FormField\n          control={otpForm.control}\n          name=\"otp\"\n          render={({ field }) => (\n            <FormItem>\n              <FormLabel className=\"text-foreground text-sm sm:text-base text-center block\">\n                Enter Verification Code\n              </FormLabel>\n              <FormControl>\n                <div className=\"flex justify-center\">\n                  <InputOTP\n                    id=\"otp-login-field\"\n                    maxLength={6}\n                    value={field.value} // Ensure value is controlled\n                    onChange={(value) => field.onChange(value)} // Pass value directly\n                    className=\"gap-2\"\n                  >\n                    <InputOTPGroup>\n                      <InputOTPSlot\n                        index={0}\n                        className=\"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]\"\n                      />\n                      <InputOTPSlot\n                        index={1}\n                        className=\"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]\"\n                      />\n                      <InputOTPSlot\n                        index={2}\n                        className=\"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]\"\n                      />\n                      <InputOTPSlot\n                        index={3}\n                        className=\"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]\"\n                      />\n                      <InputOTPSlot\n                        index={4}\n                        className=\"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]\"\n                      />\n                      <InputOTPSlot\n                        index={5}\n                        className=\"w-12 h-12 text-lg font-semibold border-2 border-border focus:border-[var(--brand-gold)] dark:border-neutral-700 dark:focus:border-[var(--brand-gold)]\"\n                      />\n                    </InputOTPGroup>\n                  </InputOTP>\n                </div>\n              </FormControl>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n        <Button\n          type=\"submit\"\n          className=\"cursor-pointer w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-sm sm:text-base\"\n          disabled={isPending}\n        >\n          {isPending ? (\n            <>\n              <Loader2 className=\"w-5 h-5 mr-2 animate-spin\" />\n              Verifying...\n            </>\n          ) : (\n            <>\n              Verify & Sign In <ArrowRight className=\"w-5 h-5 ml-2\" />\n            </>\n          )}\n        </Button>\n        <div className=\"flex flex-col items-center space-y-3 mt-4 text-xs sm:text-sm\">\n          <button\n            type=\"button\"\n            onClick={onResendOTP}\n            disabled={countdown > 0}\n            className={`${\n              countdown > 0\n                ? \"text-muted-foreground cursor-not-allowed\"\n                : \"text-primary dark:text-[var(--brand-gold)] hover:underline cursor-pointer\"\n            }`}\n          >\n            {countdown > 0 ? `Resend OTP in ${countdown}s` : \"Resend OTP\"}\n          </button>\n          <button\n            type=\"button\"\n            onClick={onBackToEmail}\n            className=\"text-muted-foreground hover:text-foreground cursor-pointer\"\n          >\n            ← Change email address\n          </button>\n        </div>\n      </form>\n    </Form>\n  )\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AAKA;AAAA;AAnBA;;;;;;;;;;AAoBA,yBAAyB;AACzB,MAAM,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,OAAO,oIAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoB,GACtC,KAAK,CAAC;QAAE,SAAS;IAAqC;AAC3D;AACA,uBAAuB;AACvB,MAAM,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzB,KAAK,oIAAA,CAAA,IAAC,CACH,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB,GACzC,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB,GACzC,KAAK,CAAC,WAAW;QAAE,SAAS;IAAuB;AACxD;AAYO,SAAS,aAAa,EAC3B,IAAI,EACJ,KAAK,EACL,SAAS,EACT,SAAS,EACT,aAAa,EACb,WAAW,EACX,WAAW,EACX,aAAa,EACK;IAClB,MAAM,YAAY,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAA+B;QACrD,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,OAAO;QACT;IACF;IACA,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAA6B;QACjD,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,KAAK;QACP;IACF;IACA,IAAI,SAAS,SAAS;QACpB,qBACE,8OAAC,yHAAA,CAAA,OAAI;YAAE,GAAG,SAAS;sBACjB,cAAA,8OAAC;gBACC,UAAU,UAAU,YAAY,CAAC;gBACjC,WAAU;;kCAEV,8OAAC,yHAAA,CAAA,YAAS;wBACR,SAAS,UAAU,OAAO;wBAC1B,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;kDACP,8OAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAuC;;;;;;kDAG5D,8OAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,0HAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,aAAY;4CACZ,MAAK;4CACJ,GAAG,KAAK;4CACT,WAAU;;;;;;;;;;;kDAGd,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kCAIlB,8OAAC,2HAAA,CAAA,SAAM;wBACL,MAAK;wBACL,WAAU;wBACV,UAAU;kCAET,0BACC;;8CACE,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAA8B;;yDAInD;;gCAAE;8CACS,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;kCAIrC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;IAOlD;IACA,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAE,GAAG,OAAO;kBACf,cAAA,8OAAC;YACC,UAAU,QAAQ,YAAY,CAAC,CAAC;gBAC9B,YAAY;oBAAE;oBAAO,KAAK,OAAO,GAAG;gBAAC;YACvC;YACA,WAAU;;8BAEV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;sCAG7C,8OAAC;4BAAE,WAAU;sCAAuC;;;;;;sCACpD,8OAAC;4BAAE,WAAU;sCAAqC;;;;;;;;;;;;8BAIpD,8OAAC,yHAAA,CAAA,YAAS;oBACR,SAAS,QAAQ,OAAO;oBACxB,MAAK;oBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;8CACP,8OAAC,yHAAA,CAAA,YAAS;oCAAC,WAAU;8CAAyD;;;;;;8CAG9E,8OAAC,yHAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,iIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,WAAW;4CACX,OAAO,MAAM,KAAK;4CAClB,UAAU,CAAC,QAAU,MAAM,QAAQ,CAAC;4CACpC,WAAU;sDAEV,cAAA,8OAAC,iIAAA,CAAA,gBAAa;;kEACZ,8OAAC,iIAAA,CAAA,eAAY;wDACX,OAAO;wDACP,WAAU;;;;;;kEAEZ,8OAAC,iIAAA,CAAA,eAAY;wDACX,OAAO;wDACP,WAAU;;;;;;kEAEZ,8OAAC,iIAAA,CAAA,eAAY;wDACX,OAAO;wDACP,WAAU;;;;;;kEAEZ,8OAAC,iIAAA,CAAA,eAAY;wDACX,OAAO;wDACP,WAAU;;;;;;kEAEZ,8OAAC,iIAAA,CAAA,eAAY;wDACX,OAAO;wDACP,WAAU;;;;;;kEAEZ,8OAAC,iIAAA,CAAA,eAAY;wDACX,OAAO;wDACP,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMpB,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8BAIlB,8OAAC,2HAAA,CAAA,SAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;8BAET,0BACC;;0CACE,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAA8B;;qDAInD;;4BAAE;0CACiB,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;8BAI7C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,MAAK;4BACL,SAAS;4BACT,UAAU,YAAY;4BACtB,WAAW,GACT,YAAY,IACR,6CACA,6EACJ;sCAED,YAAY,IAAI,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,GAAG;;;;;;sCAEnD,8OAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 842, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/schemas/authSchemas.ts"], "sourcesContent": ["import { z } from \"zod\";\n\nexport const IndianMobileSchema = z\n  .string()\n  .trim()\n  .min(10, { message: \"Mobile number must be 10 digits\" })\n  .max(10, { message: \"Mobile number must be 10 digits\" })\n  .regex(/^[6-9]\\d{9}$/, { message: \"Please enter a valid Indian mobile number\" });\n\nexport const EmailOTPSchema = z.object({\n  email: z\n    .string()\n    .trim()\n    .min(1, { message: \"Email is required\" })\n    .email({ message: \"Please enter a valid email address\" }),\n});\n\nexport const VerifyOTPSchema = z.object({\n  email: z\n    .string()\n    .trim()\n    .min(1, { message: \"Email is required\" })\n    .email({ message: \"Please enter a valid email address\" }),\n  otp: z\n    .string()\n    .trim()\n    .min(6, { message: \"OTP must be 6 digits\" })\n    .max(6, { message: \"OTP must be 6 digits\" })\n    .regex(/^\\d{6}$/, { message: \"OTP must be 6 digits\" }),\n});\n\nexport const PasswordComplexitySchema = z\n  .string()\n  .min(6, \"Password must be at least 6 characters long\")\n  .regex(/[A-Z]/, \"Password must contain at least one uppercase letter\")\n  .regex(/[a-z]/, \"Password must contain at least one lowercase letter\")\n  .regex(/\\d/, \"Password must contain at least one number\")\n  .regex(/[^a-zA-Z0-9]/, \"Password must contain at least one special character\");\n\nexport const MobilePasswordLoginSchema = z.object({\n  mobile: IndianMobileSchema,\n  password: z.string().trim().min(1, { message: \"Password is required\" }),\n});"], "names": [], "mappings": ";;;;;;;AAAA;;AAEO,MAAM,qBAAqB,oIAAA,CAAA,IAAC,CAChC,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,IAAI;IAAE,SAAS;AAAkC,GACrD,GAAG,CAAC,IAAI;IAAE,SAAS;AAAkC,GACrD,KAAK,CAAC,gBAAgB;IAAE,SAAS;AAA4C;AAEzE,MAAM,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,OAAO,oIAAA,CAAA,IAAC,CACL,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoB,GACtC,KAAK,CAAC;QAAE,SAAS;IAAqC;AAC3D;AAEO,MAAM,kBAAkB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,OAAO,oIAAA,CAAA,IAAC,CACL,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoB,GACtC,KAAK,CAAC;QAAE,SAAS;IAAqC;IACzD,KAAK,oIAAA,CAAA,IAAC,CACH,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB,GACzC,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB,GACzC,KAAK,CAAC,WAAW;QAAE,SAAS;IAAuB;AACxD;AAEO,MAAM,2BAA2B,oIAAA,CAAA,IAAC,CACtC,MAAM,GACN,GAAG,CAAC,GAAG,+CACP,KAAK,CAAC,SAAS,uDACf,KAAK,CAAC,SAAS,uDACf,KAAK,CAAC,MAAM,6CACZ,KAAK,CAAC,gBAAgB;AAElB,MAAM,4BAA4B,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChD,QAAQ;IACR,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB;AACvE", "debugId": null}}, {"offset": {"line": 892, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/components/MobilePasswordForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport { z } from \"zod\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { MobilePasswordLoginSchema } from \"@/lib/schemas/authSchemas\";\nimport { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport { Loader2, ArrowRight } from \"lucide-react\";\nimport Link from \"next/link\";\n\ninterface MobilePasswordFormProps {\n  isPending: boolean;\n  onSubmit: (_values: z.infer<typeof MobilePasswordLoginSchema>) => void;\n}\n\nexport function MobilePasswordForm({ isPending, onSubmit }: MobilePasswordFormProps) {\n  const form = useForm<z.infer<typeof MobilePasswordLoginSchema>>({\n    resolver: zodResolver(MobilePasswordLoginSchema),\n    defaultValues: {\n      mobile: \"\",\n      password: \"\",\n    },\n  });\n\n  return (\n    <Form {...form}>\n      <form\n        onSubmit={form.handleSubmit(onSubmit)}\n        className=\"space-y-4 sm:space-y-6\"\n      >\n        <FormField\n          control={form.control}\n          name=\"mobile\"\n          render={({ field }) => (\n            <FormItem>\n              <FormLabel className=\"text-foreground text-sm sm:text-base\">\n                Mobile Number\n              </FormLabel>\n              <FormControl>\n                <div className=\"relative\">\n                  <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-sm text-muted-foreground\">\n                    +91\n                  </div>\n                  <Input\n                    placeholder=\"9876543210\"\n                    type=\"tel\"\n                    {...field}\n                    onChange={(e) => {\n                      let value = e.target.value;\n                      // Remove any +91 prefix if user enters it\n                      value = value.replace(/^\\+91/, '');\n                      // Only allow numeric input\n                      value = value.replace(/\\D/g, '');\n                      // Limit to 10 digits for mobile numbers\n                      if (value.length > 10) {\n                        value = value.slice(0, 10);\n                      }\n                      field.onChange(value);\n                    }}\n                    onKeyDown={(e) => {\n                      const isNumeric = /^[0-9]$/.test(e.key);\n                      const isControl = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key);\n                      if (!isNumeric && !isControl) {\n                        e.preventDefault();\n                      }\n                    }}\n                    className=\"pl-12 bg-background border-border focus:border-primary dark:bg-neutral-800 dark:border-neutral-700 dark:focus:border-[var(--brand-gold)] h-10 sm:h-11 text-sm sm:text-base\"\n                    maxLength={10}\n                  />\n                </div>\n              </FormControl>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n\n        <FormField\n          control={form.control}\n          name=\"password\"\n          render={({ field }) => (\n            <FormItem>\n              <FormLabel className=\"text-foreground text-sm sm:text-base\">\n                Password\n              </FormLabel>\n              <FormControl>\n                <Input\n                  placeholder=\"••••••••\"\n                  type=\"password\"\n                  {...field}\n                  className=\"bg-background border-border focus:border-primary dark:bg-neutral-800 dark:border-neutral-700 dark:focus:border-[var(--brand-gold)] h-10 sm:h-11 text-sm sm:text-base\"\n                />\n              </FormControl>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n\n        <Button\n          type=\"submit\"\n          className=\"cursor-pointer w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-sm sm:text-base\"\n          disabled={isPending}\n        >\n          {isPending ? (\n            <>\n              <Loader2 className=\"w-5 h-5 mr-2 animate-spin\" />\n              Signing in...\n            </>\n          ) : (\n            <>\n              Sign In <ArrowRight className=\"w-5 h-5 ml-2\" />\n            </>\n          )}\n        </Button>\n\n        <div className=\"text-center mt-4 text-xs sm:text-sm\">\n          <span className=\"text-muted-foreground\">\n            Don&apos;t have an account?{\" \"}\n            <Link\n              href=\"/register\"\n              className=\"text-primary dark:text-[var(--brand-gold)] hover:underline font-medium\"\n            >\n              Register here\n            </Link>\n          </span>\n        </div>\n      </form>\n    </Form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAVA;;;;;;;;;;AAiBO,SAAS,mBAAmB,EAAE,SAAS,EAAE,QAAQ,EAA2B;IACjF,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAA6C;QAC9D,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,6HAAA,CAAA,4BAAyB;QAC/C,eAAe;YACb,QAAQ;YACR,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,UAAU,KAAK,YAAY,CAAC;YAC5B,WAAU;;8BAEV,8OAAC,yHAAA,CAAA,YAAS;oBACR,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;8CACP,8OAAC,yHAAA,CAAA,YAAS;oCAAC,WAAU;8CAAuC;;;;;;8CAG5D,8OAAC,yHAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAmF;;;;;;0DAGlG,8OAAC,0HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,MAAK;gDACJ,GAAG,KAAK;gDACT,UAAU,CAAC;oDACT,IAAI,QAAQ,EAAE,MAAM,CAAC,KAAK;oDAC1B,0CAA0C;oDAC1C,QAAQ,MAAM,OAAO,CAAC,SAAS;oDAC/B,2BAA2B;oDAC3B,QAAQ,MAAM,OAAO,CAAC,OAAO;oDAC7B,wCAAwC;oDACxC,IAAI,MAAM,MAAM,GAAG,IAAI;wDACrB,QAAQ,MAAM,KAAK,CAAC,GAAG;oDACzB;oDACA,MAAM,QAAQ,CAAC;gDACjB;gDACA,WAAW,CAAC;oDACV,MAAM,YAAY,UAAU,IAAI,CAAC,EAAE,GAAG;oDACtC,MAAM,YAAY;wDAAC;wDAAa;wDAAU;wDAAa;wDAAc;qDAAM,CAAC,QAAQ,CAAC,EAAE,GAAG;oDAC1F,IAAI,CAAC,aAAa,CAAC,WAAW;wDAC5B,EAAE,cAAc;oDAClB;gDACF;gDACA,WAAU;gDACV,WAAW;;;;;;;;;;;;;;;;;8CAIjB,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8BAKlB,8OAAC,yHAAA,CAAA,YAAS;oBACR,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yHAAA,CAAA,WAAQ;;8CACP,8OAAC,yHAAA,CAAA,YAAS;oCAAC,WAAU;8CAAuC;;;;;;8CAG5D,8OAAC,yHAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,0HAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,MAAK;wCACJ,GAAG,KAAK;wCACT,WAAU;;;;;;;;;;;8CAGd,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8BAKlB,8OAAC,2HAAA,CAAA,SAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;8BAET,0BACC;;0CACE,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAA8B;;qDAInD;;4BAAE;0CACQ,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;8BAKpC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;;4BAAwB;4BACV;0CAC5B,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 1139, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/components/AuthMethodToggle.tsx"], "sourcesContent": ["\"use client\";\r\n\r\ninterface AuthMethodToggleProps {\r\n  authMethod: 'email-otp' | 'mobile-password';\r\n  step: 'email' | 'otp';\r\n  onMethodChange: (_method: 'email-otp' | 'mobile-password') => void;\r\n}\r\n\r\nexport function AuthMethodToggle({ authMethod, step, onMethodChange }: AuthMethodToggleProps) {\r\n  // Only show toggle when on email step or mobile-password method\r\n  if (!(authMethod === 'email-otp' && step === 'email') && authMethod !== 'mobile-password') {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex rounded-lg bg-muted p-1 mb-6\">\r\n      <button\r\n        type=\"button\"\r\n        onClick={() => onMethodChange('email-otp')}\r\n        className={`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${\r\n          authMethod === 'email-otp'\r\n            ? 'bg-background text-foreground shadow-sm'\r\n            : 'text-muted-foreground hover:text-foreground'\r\n        }`}\r\n      >\r\n        Email OTP\r\n      </button>\r\n      <button\r\n        type=\"button\"\r\n        onClick={() => onMethodChange('mobile-password')}\r\n        className={`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${\r\n          authMethod === 'mobile-password'\r\n            ? 'bg-background text-foreground shadow-sm'\r\n            : 'text-muted-foreground hover:text-foreground'\r\n        }`}\r\n      >\r\n        Mobile + Password\r\n      </button>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAQO,SAAS,iBAAiB,EAAE,UAAU,EAAE,IAAI,EAAE,cAAc,EAAyB;IAC1F,gEAAgE;IAChE,IAAI,CAAC,CAAC,eAAe,eAAe,SAAS,OAAO,KAAK,eAAe,mBAAmB;QACzF,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,MAAK;gBACL,SAAS,IAAM,eAAe;gBAC9B,WAAW,CAAC,kEAAkE,EAC5E,eAAe,cACX,4CACA,+CACJ;0BACH;;;;;;0BAGD,8OAAC;gBACC,MAAK;gBACL,SAAS,IAAM,eAAe;gBAC9B,WAAW,CAAC,kEAAkE,EAC5E,eAAe,oBACX,4CACA,+CACJ;0BACH;;;;;;;;;;;;AAKP", "debugId": null}}, {"offset": {"line": 1186, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/supabase/constants.ts"], "sourcesContent": ["// lib/supabase/constants.ts\r\n\r\nexport const TABLES = {\r\n  BLOGS: \"blogs\",\r\n  BUSINESS_ACTIVITIES: \"business_activities\",\r\n  BUSINESS_PROFILES: \"business_profiles\",\r\n  CARD_VISITS: \"card_visits\",\r\n  CUSTOM_ADS: \"custom_ads\",\r\n  CUSTOM_AD_TARGETS: \"custom_ad_targets\",\r\n  CUSTOMER_POSTS: \"customer_posts\",\r\n  CUSTOMER_PROFILES: \"customer_profiles\",\r\n  CUSTOMER_PROFILES_PUBLIC: \"customer_profiles_public\",\r\n  LIKES: \"likes\",\r\n  PAYMENT_SUBSCRIPTIONS: \"payment_subscriptions\",\r\n  PINCODES: \"pincodes\",\r\n  PRODUCTS_SERVICES: \"products_services\",\r\n  PRODUCT_VARIANTS: \"product_variants\",\r\n  PUBLIC_SUBSCRIPTION_STATUS: \"public_subscription_status\",\r\n  STORAGE_CLEANUP_CONFIG: \"storage_cleanup_config\",\r\n  STORAGE_CLEANUP_PROGRESS: \"storage_cleanup_progress\",\r\n  SUBSCRIPTIONS: \"subscriptions\",\r\n  SYSTEM_ALERTS: \"system_alerts\",\r\n  UNIFIED_POSTS: \"unified_posts\",\r\n  RATINGS_REVIEWS: \"ratings_reviews\",\r\n} as const;\r\n\r\nexport const BUCKETS = {\r\n  BUSINESS: \"business\",\r\n  CUSTOMERS: \"customers\",\r\n} as const;\r\n\r\nexport const COLUMNS = {\r\n  ID: \"id\",\r\n  CREATED_AT: \"created_at\",\r\n  UPDATED_AT: \"updated_at\",\r\n  NAME: \"name\",\r\n  EMAIL: \"email\",\r\n  PHONE: \"phone\",\r\n  CITY: \"city\",\r\n  STATE: \"state\",\r\n  PINCODE: \"pincode\",\r\n  PLAN_ID: \"plan_id\",\r\n  LOCALITY: \"locality\",\r\n  CITY_SLUG: \"city_slug\",\r\n  STATE_SLUG: \"state_slug\",\r\n  LOCALITY_SLUG: \"locality_slug\",\r\n  OFFICE_NAME: \"office_name\",\r\n  AVATAR_URL: \"avatar_url\",\r\n  LOGO_URL: \"logo_url\",\r\n  IMAGE_URL: \"image_url\",\r\n  IMAGES: \"images\",\r\n  SLUG: \"slug\",\r\n  STATUS: \"status\",\r\n  CONTENT: \"content\",\r\n  GALLERY: \"gallery\",\r\n  DESCRIPTION: \"description\",\r\n  TITLE: \"title\",\r\n  USER_ID: \"user_id\",\r\n  BUSINESS_ID: \"business_id\",\r\n  BUSINESS_NAME: \"business_name\",\r\n  BUSINESS_SLUG: \"business_slug\",\r\n  PRODUCT_ID: \"product_id\",\r\n  LATITUDE: \"latitude\",\r\n  LONGITUDE: \"longitude\",\r\n  PRODUCT_TYPE: \"product_type\",\r\n  BASE_PRICE: \"base_price\",\r\n  DISCOUNTED_PRICE: \"discounted_price\",\r\n  IS_AVAILABLE: \"is_available\",\r\n  CUSTOM_AD_TARGETS: \"custom_ad_targets\",\r\n  AD_IMAGE_URL: \"ad_image_url\",\r\n  AD_LINK_URL: \"ad_link_url\",\r\n  IS_ACTIVE: \"is_active\",\r\n  TARGETING_LOCATIONS: \"targeting_locations\",\r\n  RATINGS_REVIEWS: \"ratings_reviews\",\r\n  BUSINESS_PROFILE_ID: \"business_profile_id\",\r\n  RAZORPAY_SUBSCRIPTION_ID: \"razorpay_subscription_id\",\r\n  SUBSCRIPTION_STATUS: \"subscription_status\",\r\n  TOTAL_LIKES: \"total_likes\",\r\n  TOTAL_SUBSCRIPTIONS: \"total_subscriptions\",\r\n  AVERAGE_RATING: \"average_rating\",\r\n  TOTAL_VISITS: \"total_visits\",\r\n  TODAY_VISITS: \"today_visits\",\r\n  YESTERDAY_VISITS: \"yesterday_visits\",\r\n  VISITS_7_DAYS: \"visits_7_days\",\r\n  VISITS_30_DAYS: \"visits_30_days\",\r\n  CUSTOM_ADS: \"custom_ads\",\r\n  CUSTOM_BRANDING: \"custom_branding\",\r\n  CONTACT_EMAIL: \"contact_email\",\r\n  HAS_ACTIVE_SUBSCRIPTION: \"has_active_subscription\",\r\n  TRIAL_END_DATE: \"trial_end_date\",\r\n  MEMBER_NAME: \"member_name\",\r\n  ADDRESS_LINE: \"address_line\",\r\n  INSTAGRAM_URL: \"instagram_url\",\r\n  FACEBOOK_URL: \"facebook_url\",\r\n  WHATSAPP_NUMBER: \"whatsapp_number\",\r\n  ABOUT_BIO: \"about_bio\",\r\n  THEME_COLOR: \"theme_color\",\r\n  DELIVERY_INFO: \"delivery_info\",\r\n  BUSINESS_HOURS: \"business_hours\",\r\n  BUSINESS_CATEGORY: \"business_category\",\r\n  ESTABLISHED_YEAR: \"established_year\",\r\n  VARIANT_VALUES: \"variant_values\",\r\n  VARIANT_NAME: \"variant_name\",\r\n  FEATURED_IMAGE_INDEX: \"featured_image_index\",\r\n  STATE_NAME: \"StateName\",\r\n  DIVISION_NAME: \"DivisionName\",\r\n} as const;\r\n\r\nexport const RPC_FUNCTIONS = {\r\n  GET_DAILY_UNIQUE_VISIT_TREND: \"get_daily_unique_visit_trend\",\r\n  GET_HOURLY_UNIQUE_VISIT_TREND: \"get_hourly_unique_visit_trend\",\r\n  GET_MONTHLY_UNIQUE_VISITS: \"get_monthly_unique_visits\",\r\n  GET_MONTHLY_UNIQUE_VISIT_TREND: \"get_monthly_unique_visit_trend\",\r\n  GET_AVAILABLE_YEARS_FOR_MONTHLY_METRICS: \"get_available_years_for_monthly_metrics\",\r\n  GET_TOTAL_UNIQUE_VISITS: \"get_total_unique_visits\",\r\n  GET_AD_FOR_PINCODE: \"get_ad_for_pincode\",\r\n  GET_PRODUCT_WITH_VARIANTS: \"get_product_with_variants\",\r\n  GET_AVAILABLE_PRODUCT_VARIANTS: \"get_available_product_variants\",\r\n  GET_BUSINESS_VARIANT_STATS: \"get_business_variant_stats\",\r\n  IS_VARIANT_COMBINATION_UNIQUE: \"is_variant_combination_unique\",\r\n} as const;\r\n\r\nexport const RPC_PARAMS = {\r\n    BUSINESS_ID: \"business_id\",\r\n    START_DATE: \"start_date\",\r\n    END_DATE: \"end_date\",\r\n    TARGET_DATE: \"target_date\",\r\n    TARGET_YEAR: \"target_year\",\r\n    TARGET_MONTH: \"target_month\",\r\n    START_YEAR: \"start_year\",\r\n    START_MONTH: \"start_month\",\r\n    END_YEAR: \"end_year\",\r\n    END_MONTH: \"end_month\",\r\n    TARGET_PINCODE: \"target_pincode\",\r\n} as const;\r\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;;;;;AAErB,MAAM,SAAS;IACpB,OAAO;IACP,qBAAqB;IACrB,mBAAmB;IACnB,aAAa;IACb,YAAY;IACZ,mBAAmB;IACnB,gBAAgB;IAChB,mBAAmB;IACnB,0BAA0B;IAC1B,OAAO;IACP,uBAAuB;IACvB,UAAU;IACV,mBAAmB;IACnB,kBAAkB;IAClB,4BAA4B;IAC5B,wBAAwB;IACxB,0BAA0B;IAC1B,eAAe;IACf,eAAe;IACf,eAAe;IACf,iBAAiB;AACnB;AAEO,MAAM,UAAU;IACrB,UAAU;IACV,WAAW;AACb;AAEO,MAAM,UAAU;IACrB,IAAI;IACJ,YAAY;IACZ,YAAY;IACZ,MAAM;IACN,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,SAAS;IACT,SAAS;IACT,UAAU;IACV,WAAW;IACX,YAAY;IACZ,eAAe;IACf,aAAa;IACb,YAAY;IACZ,UAAU;IACV,WAAW;IACX,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,SAAS;IACT,SAAS;IACT,aAAa;IACb,OAAO;IACP,SAAS;IACT,aAAa;IACb,eAAe;IACf,eAAe;IACf,YAAY;IACZ,UAAU;IACV,WAAW;IACX,cAAc;IACd,YAAY;IACZ,kBAAkB;IAClB,cAAc;IACd,mBAAmB;IACnB,cAAc;IACd,aAAa;IACb,WAAW;IACX,qBAAqB;IACrB,iBAAiB;IACjB,qBAAqB;IACrB,0BAA0B;IAC1B,qBAAqB;IACrB,aAAa;IACb,qBAAqB;IACrB,gBAAgB;IAChB,cAAc;IACd,cAAc;IACd,kBAAkB;IAClB,eAAe;IACf,gBAAgB;IAChB,YAAY;IACZ,iBAAiB;IACjB,eAAe;IACf,yBAAyB;IACzB,gBAAgB;IAChB,aAAa;IACb,cAAc;IACd,eAAe;IACf,cAAc;IACd,iBAAiB;IACjB,WAAW;IACX,aAAa;IACb,eAAe;IACf,gBAAgB;IAChB,mBAAmB;IACnB,kBAAkB;IAClB,gBAAgB;IAChB,cAAc;IACd,sBAAsB;IACtB,YAAY;IACZ,eAAe;AACjB;AAEO,MAAM,gBAAgB;IAC3B,8BAA8B;IAC9B,+BAA+B;IAC/B,2BAA2B;IAC3B,gCAAgC;IAChC,yCAAyC;IACzC,yBAAyB;IACzB,oBAAoB;IACpB,2BAA2B;IAC3B,gCAAgC;IAChC,4BAA4B;IAC5B,+BAA+B;AACjC;AAEO,MAAM,aAAa;IACtB,aAAa;IACb,YAAY;IACZ,UAAU;IACV,aAAa;IACb,aAAa;IACb,cAAc;IACd,YAAY;IACZ,aAAa;IACb,UAAU;IACV,WAAW;IACX,gBAAgB;AACpB", "debugId": null}}, {"offset": {"line": 1329, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/supabase/services/sharedService.ts"], "sourcesContent": ["import { SupabaseClient, RealtimePostgresChangesPayload } from \"@supabase/supabase-js\";\r\nimport { TABLES, COLUMNS } from \"../constants\";\r\nimport { Tables } from \"../../../types/supabase\";\r\n\r\n/**\r\n * Fetches the currently authenticated user.\r\n * @param supabase The Supabase client.\r\n * @returns An object containing the user data or an error.\r\n */\r\nexport async function getAuthenticatedUser(supabase: SupabaseClient) {\r\n  try {\r\n    const { data: { user }, error } = await supabase.auth.getUser();\r\n    if (error) {\r\n      console.error(`Error fetching authenticated user: ${error.message}`);\r\n      return { user: null, error: \"User not found or authentication error.\" };\r\n    }\r\n    return { user, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching authenticated user: ${err}`);\r\n    return { user: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Uploads a file to Supabase Storage.\r\n * @param supabase The Supabase client.\r\n * @param bucketName The name of the storage bucket.\r\n * @param path The path where the file will be stored in the bucket.\r\n * @param fileBuffer The file content as a Buffer.\r\n * @param contentType The content type of the file (e.g., 'image/jpeg').\r\n * @param upsert Whether to upsert the file if it already exists.\r\n * @returns An object indicating success or an error.\r\n */\r\nexport async function uploadFileToStorage(\r\n  supabase: SupabaseClient,\r\n  bucketName: string,\r\n  path: string,\r\n  fileBuffer: Buffer,\r\n  contentType: string,\r\n  upsert: boolean = true\r\n) {\r\n  try {\r\n    const { error } = await supabase.storage\r\n      .from(bucketName)\r\n      .upload(path, fileBuffer, {\r\n        contentType,\r\n        upsert,\r\n      });\r\n\r\n    if (error) {\r\n      console.error(`Error uploading file to storage: ${error.message}`);\r\n      return { success: false, error: error.message };\r\n    }\r\n    return { success: true, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error uploading file to storage: ${err}`);\r\n    return { success: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Retrieves the public URL for a file in Supabase Storage.\r\n * @param supabase The Supabase client.\r\n * @param bucketName The name of the storage bucket.\r\n * @param path The path of the file in the bucket.\r\n * @returns An object containing the public URL or an error.\r\n */\r\nexport async function getPublicUrlFromStorage(supabase: SupabaseClient, bucketName: string, path: string) {\r\n  try {\r\n    const { data } = supabase.storage.from(bucketName).getPublicUrl(path);\r\n    if (!data?.publicUrl) {\r\n      return { publicUrl: null, error: \"Could not retrieve public URL.\" };\r\n    }\r\n    return { publicUrl: data.publicUrl, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error getting public URL: ${err}`);\r\n    return { publicUrl: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Removes files from Supabase Storage.\r\n * @param supabase The Supabase client.\r\n * @param bucketName The name of the storage bucket.\r\n * @param paths An array of file paths to remove from the bucket.\r\n * @returns An object indicating success or an error.\r\n */\r\nexport async function removeFileFromStorage(supabase: SupabaseClient, bucketName: string, paths: string[]) {\r\n  try {\r\n    const { error } = await supabase.storage.from(bucketName).remove(paths);\r\n    if (error) {\r\n      console.error(`Error removing file from storage: ${error.message}`);\r\n      return { success: false, error: error.message };\r\n    }\r\n    return { success: true, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error removing file from storage: ${err}`);\r\n    return { success: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Updates the authenticated user's phone number in Supabase Auth.\r\n * @param supabase The Supabase client.\r\n * @param phone The new phone number.\r\n * @returns An object indicating success or an error.\r\n */\r\nexport async function updateAuthUserPhone(supabase: SupabaseClient, phone: string) {\r\n  try {\r\n    const { error } = await supabase.auth.updateUser({\r\n      phone: `+91${phone}`,\r\n    });\r\n\r\n    if (error) {\r\n      console.error(`Error updating auth user phone: ${error.message}`);\r\n      return { success: false, error: error.message };\r\n    }\r\n    return { success: true, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error updating auth user phone: ${err}`);\r\n    return { success: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Lists files in a Supabase Storage bucket.\r\n * @param supabase The Supabase client.\r\n * @param bucketName The name of the storage bucket.\r\n * @param path The path within the bucket to list files from.\r\n * @param options Options for listing files (e.g., limit).\r\n * @returns An object containing the list of files or an error.\r\n */\r\nexport async function listStorageFiles(\r\n  supabase: SupabaseClient,\r\n  bucketName: string,\r\n  path: string,\r\n  options?: { limit?: number; offset?: number; search?: string }\r\n) {\r\n  try {\r\n    const { data, error } = await supabase.storage\r\n      .from(bucketName)\r\n      .list(path, options);\r\n\r\n    if (error) {\r\n      console.error(`Error listing storage files: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error listing storage files: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Signs out the currently authenticated user.\r\n * @param supabase The Supabase client.\r\n * @returns An object indicating success or an error.\r\n */\r\n/**\r\n * Signs out the currently authenticated user.\r\n * @param supabase The Supabase client.\r\n * @returns An object indicating success or an error.\r\n */\r\nexport async function signOutUser(supabase: SupabaseClient) {\r\n  try {\r\n    const { error } = await supabase.auth.signOut();\r\n    if (error) {\r\n      console.error(`Error signing out user: ${error.message}`);\r\n      return { success: false, error: error.message };\r\n    }\r\n    return { success: true, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error signing out user: ${err}`);\r\n    return { success: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Sends an OTP to the provided email address for authentication.\r\n * @param supabase The Supabase client.\r\n * @param email The email address to send the OTP to.\r\n * @param shouldCreateUser Whether to create a new user if the email doesn't exist.\r\n * @returns An object indicating success or an error.\r\n */\r\nexport async function signInWithOtp(supabase: SupabaseClient, email: string, shouldCreateUser: boolean = true) {\r\n  try {\r\n    const { error } = await supabase.auth.signInWithOtp({\r\n      email: email,\r\n      options: {\r\n        shouldCreateUser: shouldCreateUser,\r\n        data: {\r\n          auth_type: \"email\",\r\n        },\r\n      },\r\n    });\r\n    if (error) {\r\n      console.error(`Error sending OTP: ${error.message}`);\r\n      return { success: false, error: error.message };\r\n    }\r\n    return { success: true, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error sending OTP: ${err}`);\r\n    return { success: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Verifies an OTP for email authentication.\r\n * @param supabase The Supabase client.\r\n * @param email The email address associated with the OTP.\r\n * @param token The OTP token to verify.\r\n * @returns An object containing user data on success or an error.\r\n */\r\nexport async function verifyOtp(supabase: SupabaseClient, email: string, token: string) {\r\n  try {\r\n    const { data, error } = await supabase.auth.verifyOtp({\r\n      email: email,\r\n      token: token,\r\n      type: 'email',\r\n    });\r\n    if (error) {\r\n      console.error(`Error verifying OTP: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error verifying OTP: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Signs in a user with a mobile number and password.\r\n * @param supabase The Supabase client.\r\n * @param phone The user's phone number (should include country code, e.g., +91).\r\n * @param password The user's password.\r\n * @returns An object containing user data on success or an error.\r\n */\r\nexport async function signInWithPassword(supabase: SupabaseClient, phone: string, password: string) {\r\n  try {\r\n    const { data, error } = await supabase.auth.signInWithPassword({\r\n      phone: phone,\r\n      password: password,\r\n    });\r\n    if (error) {\r\n      console.error(`Error signing in with password: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error signing in with password: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Initiates an OAuth sign-in flow with a specified provider.\r\n * @param supabase The Supabase client.\r\n * @param provider The OAuth provider (e.g., 'google').\r\n * @param redirectTo The URL to redirect to after successful authentication.\r\n * @param queryParams Optional query parameters for the OAuth flow.\r\n * @returns An object containing the authorization URL or an error.\r\n */\r\nexport async function signInWithOAuth(supabase: SupabaseClient, provider: \"google\", redirectTo: string, queryParams?: { [key: string]: string }) {\r\n  try {\r\n    const { data, error } = await supabase.auth.signInWithOAuth({\r\n      provider: provider,\r\n      options: {\r\n        redirectTo: redirectTo,\r\n        skipBrowserRedirect: true,\r\n        queryParams: queryParams,\r\n      },\r\n    });\r\n    if (error) {\r\n      console.error(`Error initiating OAuth sign-in: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error initiating OAuth sign-in: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Subscribes to real-time changes in a Supabase table.\r\n * @param supabase The Supabase client.\r\n * @param tableName The name of the table to subscribe to.\r\n * @param filter The filter to apply to the subscription (e.g., `id=eq.some_id`).\r\n * @param callback The callback function to execute when changes are received.\r\n * @returns A cleanup function to unsubscribe from the channel.\r\n */\r\nexport function subscribeToTableChanges<T extends { [key: string]: any }>(\r\n  supabase: SupabaseClient,\r\n  tableName: string,\r\n  filter: string,\r\n  callback: (_payload: RealtimePostgresChangesPayload<T>) => void\r\n): () => void {\r\n\r\n  const channel = supabase\r\n    .channel(`public:${tableName}`)\r\n    .on<T>(\r\n      \"postgres_changes\",\r\n      {\r\n        event: \"*\",\r\n        schema: \"public\",\r\n        table: tableName,\r\n        filter: filter,\r\n      },\r\n      callback\r\n    )\r\n    .subscribe();\r\n\r\n  return () => {\r\n    supabase.removeChannel(channel);\r\n  };\r\n}\r\n\r\n/**\r\n * Fetches user subscriptions.\r\n * @param supabase The Supabase client.\r\n * @param userId The ID of the user.\r\n * @returns An object containing the subscriptions data or an error.\r\n */\r\nexport async function fetchUserSubscriptions(supabase: SupabaseClient, userId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.SUBSCRIPTIONS)\r\n      .select(COLUMNS.BUSINESS_PROFILE_ID)\r\n      .eq(COLUMNS.USER_ID, userId);\r\n\r\n    if (error) {\r\n      console.error(`Error fetching user subscriptions: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching user subscriptions: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches the state name for a given city from the pincodes table.\r\n * @param supabase The Supabase client.\r\n * @param city The city name to search for.\r\n * @returns An object containing the state name or an error.\r\n */\r\nexport async function getStateNameByCity(supabase: SupabaseClient, city: string): Promise<{ stateName: string | null; error: string | null }> {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.PINCODES)\r\n      .select(COLUMNS.STATE_NAME)\r\n      .ilike(COLUMNS.DIVISION_NAME, `%${city}%`)\r\n      .limit(1);\r\n\r\n    if (error) {\r\n      console.error(`Error fetching state name for city ${city}: ${error.message}`);\r\n      return { stateName: null, error: error.message };\r\n    }\r\n\r\n    return { stateName: data && data.length > 0 ? data[0].StateName : null, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching state name for city ${city}: ${err}`);\r\n    return { stateName: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches unified posts with optional filters and pagination.\r\n * @param supabase The Supabase client.\r\n * @param from The starting index for pagination.\r\n * @param to The ending index for pagination.\r\n * @param conditions Optional array of conditions for filtering.\r\n * @returns An object containing the unified posts data, count, or an error.\r\n */\r\nexport async function getUnifiedPosts(\r\n  supabase: SupabaseClient,\r\n  from: number,\r\n  to: number,\r\n  conditions: string[] = []\r\n) {\r\n  let query = supabase.from(TABLES.UNIFIED_POSTS).select('*', { count: 'exact' });\r\n\r\n  if (conditions.length > 0) {\r\n    query = query.or(conditions.join(','));\r\n  }\r\n\r\n  const { data, error, count } = await query\r\n    .order(COLUMNS.CREATED_AT, { ascending: false })\r\n    .range(from, to);\r\n\r\n  if (error) {\r\n    console.error(\"Error fetching unified posts:\", error);\r\n    return { data: null, error: error.message, count: null };\r\n  }\r\n  return { data, error: null, count };\r\n}\r\n\r\n/**\r\n * Fetches address data from the pincodes table.\r\n * @param supabase The Supabase client.\r\n * @param pincode The pincode to search for.\r\n * @param locality_slug Optional. The locality slug to filter by.\r\n * @param city_slug Optional. The city slug to filter by.\r\n * @param state_slug Optional. The state slug to filter by.\r\n * @returns An object containing the address data or an error.\r\n */\r\nexport async function fetchPincodeAddress(\r\n  supabase: SupabaseClient,\r\n  pincode: string,\r\n  locality_slug?: string | null,\r\n  city_slug?: string | null,\r\n  state_slug?: string | null\r\n) {\r\n  try {\r\n    let query = supabase\r\n      .from(TABLES.PINCODES)\r\n      .select(`${COLUMNS.OFFICE_NAME}, ${COLUMNS.DIVISION_NAME}, ${COLUMNS.STATE_NAME}, ${COLUMNS.PINCODE}`);\r\n\r\n    if (pincode) {\r\n      query = query.eq(COLUMNS.PINCODE, pincode);\r\n    }\r\n    if (city_slug) {\r\n      query = query.eq(COLUMNS.CITY_SLUG, city_slug);\r\n    }\r\n    if (state_slug) {\r\n      query = query.eq(COLUMNS.STATE_SLUG, state_slug);\r\n    }\r\n    if (locality_slug) {\r\n      query = query.eq(COLUMNS.LOCALITY_SLUG, locality_slug);\r\n    }\r\n\r\n    const { data, error } = await query.limit(1);\r\n\r\n    if (error) {\r\n      console.error(`Error fetching pincode address: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data: data[0] || null, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching pincode address: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AACA;;AAQO,eAAe,qBAAqB,QAAwB;IACjE,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7D,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,MAAM,OAAO,EAAE;YACnE,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAA0C;QACxE;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,8CAA8C,EAAE,KAAK;QACpE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAYO,eAAe,oBACpB,QAAwB,EACxB,UAAkB,EAClB,IAAY,EACZ,UAAkB,EAClB,WAAmB,EACnB,SAAkB,IAAI;IAEtB,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,OAAO,CACrC,IAAI,CAAC,YACL,MAAM,CAAC,MAAM,YAAY;YACxB;YACA;QACF;QAEF,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,MAAM,OAAO,EAAE;YACjE,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QAChD;QACA,OAAO;YAAE,SAAS;YAAM,OAAO;QAAK;IACtC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,4CAA4C,EAAE,KAAK;QAClE,OAAO;YAAE,SAAS;YAAO,OAAO;QAAgC;IAClE;AACF;AASO,eAAe,wBAAwB,QAAwB,EAAE,UAAkB,EAAE,IAAY;IACtG,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,SAAS,OAAO,CAAC,IAAI,CAAC,YAAY,YAAY,CAAC;QAChE,IAAI,CAAC,MAAM,WAAW;YACpB,OAAO;gBAAE,WAAW;gBAAM,OAAO;YAAiC;QACpE;QACA,OAAO;YAAE,WAAW,KAAK,SAAS;YAAE,OAAO;QAAK;IAClD,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,qCAAqC,EAAE,KAAK;QAC3D,OAAO;YAAE,WAAW;YAAM,OAAO;QAAgC;IACnE;AACF;AASO,eAAe,sBAAsB,QAAwB,EAAE,UAAkB,EAAE,KAAe;IACvG,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,OAAO,CAAC,IAAI,CAAC,YAAY,MAAM,CAAC;QACjE,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,MAAM,OAAO,EAAE;YAClE,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QAChD;QACA,OAAO;YAAE,SAAS;YAAM,OAAO;QAAK;IACtC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,6CAA6C,EAAE,KAAK;QACnE,OAAO;YAAE,SAAS;YAAO,OAAO;QAAgC;IAClE;AACF;AAQO,eAAe,oBAAoB,QAAwB,EAAE,KAAa;IAC/E,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU,CAAC;YAC/C,OAAO,CAAC,GAAG,EAAE,OAAO;QACtB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,MAAM,OAAO,EAAE;YAChE,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QAChD;QACA,OAAO;YAAE,SAAS;YAAM,OAAO;QAAK;IACtC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,KAAK;QACjE,OAAO;YAAE,SAAS;YAAO,OAAO;QAAgC;IAClE;AACF;AAUO,eAAe,iBACpB,QAAwB,EACxB,UAAkB,EAClB,IAAY,EACZ,OAA8D;IAE9D,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,OAAO,CAC3C,IAAI,CAAC,YACL,IAAI,CAAC,MAAM;QAEd,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,MAAM,OAAO,EAAE;YAC7D,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,wCAAwC,EAAE,KAAK;QAC9D,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAYO,eAAe,YAAY,QAAwB;IACxD,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,MAAM,OAAO,EAAE;YACxD,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QAChD;QACA,OAAO;YAAE,SAAS;YAAM,OAAO;QAAK;IACtC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,KAAK;QACzD,OAAO;YAAE,SAAS;YAAO,OAAO;QAAgC;IAClE;AACF;AASO,eAAe,cAAc,QAAwB,EAAE,KAAa,EAAE,mBAA4B,IAAI;IAC3G,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC;YAClD,OAAO;YACP,SAAS;gBACP,kBAAkB;gBAClB,MAAM;oBACJ,WAAW;gBACb;YACF;QACF;QACA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;YACnD,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QAChD;QACA,OAAO;YAAE,SAAS;YAAM,OAAO;QAAK;IACtC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,8BAA8B,EAAE,KAAK;QACpD,OAAO;YAAE,SAAS;YAAO,OAAO;QAAgC;IAClE;AACF;AASO,eAAe,UAAU,QAAwB,EAAE,KAAa,EAAE,KAAa;IACpF,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,SAAS,CAAC;YACpD,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,MAAM,OAAO,EAAE;YACrD,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,KAAK;QACtD,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AASO,eAAe,mBAAmB,QAAwB,EAAE,KAAa,EAAE,QAAgB;IAChG,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YAC7D,OAAO;YACP,UAAU;QACZ;QACA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,MAAM,OAAO,EAAE;YAChE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,KAAK;QACjE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAUO,eAAe,gBAAgB,QAAwB,EAAE,QAAkB,EAAE,UAAkB,EAAE,WAAuC;IAC7I,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,eAAe,CAAC;YAC1D,UAAU;YACV,SAAS;gBACP,YAAY;gBACZ,qBAAqB;gBACrB,aAAa;YACf;QACF;QACA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,MAAM,OAAO,EAAE;YAChE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,KAAK;QACjE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAUO,SAAS,wBACd,QAAwB,EACxB,SAAiB,EACjB,MAAc,EACd,QAA+D;IAG/D,MAAM,UAAU,SACb,OAAO,CAAC,CAAC,OAAO,EAAE,WAAW,EAC7B,EAAE,CACD,oBACA;QACE,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV,GACA,UAED,SAAS;IAEZ,OAAO;QACL,SAAS,aAAa,CAAC;IACzB;AACF;AAQO,eAAe,uBAAuB,QAAwB,EAAE,MAAc;IACnF,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,aAAa,EACzB,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,mBAAmB,EAClC,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;QAEvB,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,MAAM,OAAO,EAAE;YACnE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,8CAA8C,EAAE,KAAK;QACpE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAQO,eAAe,mBAAmB,QAAwB,EAAE,IAAY;IAC7E,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,QAAQ,EACpB,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EACzB,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EACxC,KAAK,CAAC;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,KAAK,EAAE,EAAE,MAAM,OAAO,EAAE;YAC5E,OAAO;gBAAE,WAAW;gBAAM,OAAO,MAAM,OAAO;YAAC;QACjD;QAEA,OAAO;YAAE,WAAW,QAAQ,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG;YAAM,OAAO;QAAK;IACtF,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,8CAA8C,EAAE,KAAK,EAAE,EAAE,KAAK;QAC7E,OAAO;YAAE,WAAW;YAAM,OAAO;QAAgC;IACnE;AACF;AAUO,eAAe,gBACpB,QAAwB,EACxB,IAAY,EACZ,EAAU,EACV,aAAuB,EAAE;IAEzB,IAAI,QAAQ,SAAS,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,aAAa,EAAE,MAAM,CAAC,KAAK;QAAE,OAAO;IAAQ;IAE7E,IAAI,WAAW,MAAM,GAAG,GAAG;QACzB,QAAQ,MAAM,EAAE,CAAC,WAAW,IAAI,CAAC;IACnC;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,MAClC,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;QAAE,WAAW;IAAM,GAC7C,KAAK,CAAC,MAAM;IAEf,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YAAE,MAAM;YAAM,OAAO,MAAM,OAAO;YAAE,OAAO;QAAK;IACzD;IACA,OAAO;QAAE;QAAM,OAAO;QAAM;IAAM;AACpC;AAWO,eAAe,oBACpB,QAAwB,EACxB,OAAe,EACf,aAA6B,EAC7B,SAAyB,EACzB,UAA0B;IAE1B,IAAI;QACF,IAAI,QAAQ,SACT,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,QAAQ,EACpB,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;QAEvG,IAAI,SAAS;YACX,QAAQ,MAAM,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;QACpC;QACA,IAAI,WAAW;YACb,QAAQ,MAAM,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,SAAS,EAAE;QACtC;QACA,IAAI,YAAY;YACd,QAAQ,MAAM,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;QACvC;QACA,IAAI,eAAe;YACjB,QAAQ,MAAM,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE;QAC1C;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,KAAK,CAAC;QAE1C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,MAAM,OAAO,EAAE;YAChE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE,MAAM,IAAI,CAAC,EAAE,IAAI;YAAM,OAAO;QAAK;IAC9C,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,KAAK;QACjE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF", "debugId": null}}, {"offset": {"line": 1737, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/components/SocialLoginButton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Loader2 } from \"lucide-react\";\r\nimport { toast } from \"sonner\";\r\nimport { signInWithOAuth } from \"@/lib/supabase/services/sharedService\";\r\n\r\ninterface SocialLoginButtonProps {\r\n  redirectSlug?: string | null;\r\n  message?: string | null;\r\n  disabled?: boolean;\r\n}\r\n\r\nexport function SocialLoginButton({ redirectSlug, message, disabled }: SocialLoginButtonProps) {\r\n  const [isSocialLoading, setIsSocialLoading] = useState<\"google\" | null>(null);\r\n\r\n  // Initiates the Supabase OAuth flow for social login.\r\n  // This function gets the authorization URL and opens it in a new tab.\r\n  // After successful authentication, Supabase will redirect to /auth/callback,\r\n  // where the unified post-login redirection logic is executed.\r\n  async function handleSocialLogin(provider: \"google\") {\r\n    try {\r\n      setIsSocialLoading(provider);\r\n      const supabase = createClient();\r\n\r\n      // Construct the callback URL with the redirect and message parameters if available\r\n      // Add closeWindow=true to indicate this window should close after auth\r\n      let callbackUrl = `${window.location.origin}/auth/callback?closeWindow=true`;\r\n\r\n      // Add redirect parameter if available\r\n      if (redirectSlug) {\r\n        callbackUrl += `&redirect=${encodeURIComponent(redirectSlug)}`;\r\n      }\r\n\r\n      // Add message parameter if available\r\n      if (message) {\r\n        callbackUrl += `&message=${encodeURIComponent(message)}`;\r\n      }\r\n\r\n      // Get the authorization URL but don't redirect automatically\r\n      const { data, error } = await signInWithOAuth(supabase, provider, callbackUrl, {\r\n        access_type: \"offline\",\r\n        prompt: \"select_account\",\r\n      });\r\n\r\n      if (error) {\r\n        toast.error(\"Login failed\", {\r\n          description: error.message,\r\n        });\r\n        setIsSocialLoading(null);\r\n        return;\r\n      }\r\n\r\n      // If we have the URL, open it in a new tab\r\n      if (data?.url) {\r\n        // Open the authorization URL in a new tab\r\n        window.open(data.url, \"_blank\");\r\n\r\n        // Show a toast to guide the user\r\n        toast.info(\"Google sign-in opened in a new tab\", {\r\n          description: \"Please complete the sign-in process in the new tab.\",\r\n          duration: 5000,\r\n        });\r\n\r\n        // Reset loading state after a short delay\r\n        setTimeout(() => {\r\n          setIsSocialLoading(null);\r\n        }, 1000);\r\n      } else {\r\n        toast.error(\"Failed to start Google sign-in\", {\r\n          description: \"Please try again or use email login.\",\r\n        });\r\n        setIsSocialLoading(null);\r\n      }\r\n    } catch (error: unknown) {\r\n      const errorMessage =\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"An unexpected error occurred. Please try again.\";\r\n      toast.error(\"Login failed\", {\r\n        description: errorMessage,\r\n      });\r\n      setIsSocialLoading(null);\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex justify-center mb-5 sm:mb-6\">\r\n      <Button\r\n        variant=\"outline\"\r\n        className=\"cursor-pointer bg-background hover:bg-muted border-border dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:border-neutral-800 w-full py-5 sm:py-6 rounded-lg sm:rounded-xl font-medium text-foreground\"\r\n        onClick={() => handleSocialLogin(\"google\")}\r\n        disabled={!!isSocialLoading || disabled}\r\n      >\r\n        {isSocialLoading === \"google\" ? (\r\n          <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\r\n        ) : (\r\n          <>\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              viewBox=\"0 0 24 24\"\r\n              className=\"w-4 h-4 mr-2\"\r\n            >\r\n              <path\r\n                fill=\"#4285F4\"\r\n                d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\r\n              />\r\n              <path\r\n                fill=\"#34A853\"\r\n                d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\r\n              />\r\n              <path\r\n                fill=\"#FBBC05\"\r\n                d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\r\n              />\r\n              <path\r\n                fill=\"#EA4335\"\r\n                d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\r\n              />\r\n            </svg>\r\n            Login with Google\r\n          </>\r\n        )}\r\n      </Button>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAcO,SAAS,kBAAkB,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAA0B;IAC3F,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAExE,sDAAsD;IACtD,sEAAsE;IACtE,6EAA6E;IAC7E,8DAA8D;IAC9D,eAAe,kBAAkB,QAAkB;QACjD,IAAI;YACF,mBAAmB;YACnB,MAAM,WAAW;YAEjB,mFAAmF;YACnF,uEAAuE;YACvE,IAAI,cAAc,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,+BAA+B,CAAC;YAE5E,sCAAsC;YACtC,IAAI,cAAc;gBAChB,eAAe,CAAC,UAAU,EAAE,mBAAmB,eAAe;YAChE;YAEA,qCAAqC;YACrC,IAAI,SAAS;gBACX,eAAe,CAAC,SAAS,EAAE,mBAAmB,UAAU;YAC1D;YAEA,6DAA6D;YAC7D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,4IAAA,CAAA,kBAAe,AAAD,EAAE,UAAU,UAAU,aAAa;gBAC7E,aAAa;gBACb,QAAQ;YACV;YAEA,IAAI,OAAO;gBACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gBAAgB;oBAC1B,aAAa,MAAM,OAAO;gBAC5B;gBACA,mBAAmB;gBACnB;YACF;YAEA,2CAA2C;YAC3C,IAAI,MAAM,KAAK;gBACb,0CAA0C;gBAC1C,OAAO,IAAI,CAAC,KAAK,GAAG,EAAE;gBAEtB,iCAAiC;gBACjC,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC,sCAAsC;oBAC/C,aAAa;oBACb,UAAU;gBACZ;gBAEA,0CAA0C;gBAC1C,WAAW;oBACT,mBAAmB;gBACrB,GAAG;YACL,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,kCAAkC;oBAC5C,aAAa;gBACf;gBACA,mBAAmB;YACrB;QACF,EAAE,OAAO,OAAgB;YACvB,MAAM,eACJ,iBAAiB,QACb,MAAM,OAAO,GACb;YACN,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gBAAgB;gBAC1B,aAAa;YACf;YACA,mBAAmB;QACrB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;YACL,SAAQ;YACR,WAAU;YACV,SAAS,IAAM,kBAAkB;YACjC,UAAU,CAAC,CAAC,mBAAmB;sBAE9B,oBAAoB,yBACnB,8OAAC,iNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;qCAEnB;;kCACE,8OAAC;wBACC,OAAM;wBACN,SAAQ;wBACR,WAAU;;0CAEV,8OAAC;gCACC,MAAK;gCACL,GAAE;;;;;;0CAEJ,8OAAC;gCACC,MAAK;gCACL,GAAE;;;;;;0CAEJ,8OAAC;gCACC,MAAK;gCACL,GAAE;;;;;;0CAEJ,8OAAC;gCACC,MAAK;gCACL,GAAE;;;;;;;;;;;;oBAEA;;;;;;;;;;;;;AAOlB", "debugId": null}}, {"offset": {"line": 1891, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/supabase/services/customerService.ts"], "sourcesContent": ["import { SupabaseClient } from \"@supabase/supabase-js\";\r\nimport { TABLES, COLUMNS } from \"../constants\";\r\nimport { TablesInsert, TablesUpdate } from \"../../../types/supabase\";\r\nimport { getPublicUrlFromStorage, uploadFileToStorage, removeFileFromStorage } from \"./sharedService\";\r\n\r\n/**\r\n * Fetches a user's customer profile.\r\n * @param supabase The Supabase client.\r\n * @param userId The ID of the user to fetch the profile for.\r\n * @returns An object containing the user's profile data or an error.\r\n */\r\nexport async function getUserProfile(supabase: SupabaseClient, userId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.CUSTOMER_PROFILES)\r\n      .select(\"*\")\r\n      .eq(COLUMNS.ID, userId)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching user profile: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching user profile: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Creates a new customer profile.\r\n * @param supabase The Supabase client.\r\n * @param profile The profile data to insert.\r\n * @returns An object containing the newly created profile data or an error.\r\n */\r\nexport async function createUserProfile(supabase: SupabaseClient, profile: TablesInsert<'customer_profiles'>) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.CUSTOMER_PROFILES)\r\n      .insert([profile])\r\n      .select()\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error creating user profile: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data: Array.isArray(data) ? data[0] || null : data || null, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error creating user profile: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Checks if a customer profile exists for a given user ID.\r\n * @param supabase The Supabase client.\r\n * @param userId The ID of the user to check.\r\n * @returns An object containing a boolean indicating if the profile exists and an error if one occurred.\r\n */\r\nexport async function checkIfCustomerProfileExists(supabase: SupabaseClient, userId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.CUSTOMER_PROFILES)\r\n      .select(COLUMNS.ID)\r\n      .eq(COLUMNS.ID, userId)\r\n      .maybeSingle();\r\n\r\n    if (error) {\r\n      console.error(`Error checking existing profile: ${error.message}`);\r\n      return { exists: false, error: \"Database error checking profile.\" };\r\n    }\r\n\r\n    return { exists: !!data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error checking profile: ${err}`);\r\n    return { exists: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Updates a user's profile.\r\n * @param supabase The Supabase client.\r\n * @param userId The ID of the user to update.\r\n * @param updates The profile data to update.\r\n * @returns An object containing the updated profile data or an error.\r\n */\r\nexport async function updateUserProfile(supabase: SupabaseClient, userId: string, updates: TablesUpdate<'customer_profiles'>) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.CUSTOMER_PROFILES)\r\n      .update(updates)\r\n      .eq(COLUMNS.ID, userId)\r\n      .select()\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error updating user profile: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error updating user profile: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches the locality, pincode, and city slug for a customer profile.\r\n * @param supabase The Supabase client.\r\n * @param userId The ID of the user.\r\n * @returns An object containing the location data or an error.\r\n */\r\nexport async function getCustomerProfileLocation(supabase: SupabaseClient, userId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.CUSTOMER_PROFILES)\r\n      .select(`${COLUMNS.LOCALITY_SLUG}, ${COLUMNS.PINCODE}, ${COLUMNS.CITY_SLUG}, ${COLUMNS.STATE_SLUG}`)\r\n      .eq(COLUMNS.ID, userId)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching customer profile location: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching customer profile location: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches customer profiles by their IDs.\r\n * @param supabase The Supabase client.\r\n * @param userIds An array of user IDs.\r\n * @returns An object containing the customer profiles data or an error.\r\n */\r\nexport async function getCustomerProfilesByIds(supabase: SupabaseClient, userIds: string[]) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.CUSTOMER_PROFILES)\r\n      .select(`${COLUMNS.ID}, ${COLUMNS.NAME}, ${COLUMNS.EMAIL}, ${COLUMNS.AVATAR_URL}`)\r\n      .in(COLUMNS.ID, userIds);\r\n\r\n    if (error) {\r\n      console.error(`Error fetching customer profiles by IDs: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching customer profiles by IDs: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches a public customer profile by ID.\r\n * @param supabase The Supabase client.\r\n * @param userId The ID of the user to fetch the profile for.\r\n * @returns An object containing the public customer profile data or an error.\r\n */\r\nexport async function getPublicCustomerProfileById(supabase: SupabaseClient, userId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.CUSTOMER_PROFILES_PUBLIC)\r\n      .select('*')\r\n      .eq(COLUMNS.ID, userId)\r\n      .maybeSingle();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching public customer profile: ${error.message}`);\r\n      return { data: null, error: `Failed to fetch customer profile: ${error.message}` };\r\n    }\r\n\r\n    return { data, error: null };\r\n  } catch (e) {\r\n    console.error(`Exception in getPublicCustomerProfileById: ${e}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches public customer profiles by their IDs.\r\n * @param supabase The Supabase client.\r\n * @param userIds An array of user IDs.\r\n * @returns An object containing the public customer profiles data or an error.\r\n */\r\nexport async function getPublicCustomerProfilesByIds(supabase: SupabaseClient, userIds: string[]) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.CUSTOMER_PROFILES_PUBLIC)\r\n      .select(`${COLUMNS.ID}, ${COLUMNS.NAME}, ${COLUMNS.AVATAR_URL}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}`)\r\n      .in(COLUMNS.ID, userIds);\r\n\r\n    if (error) {\r\n      console.error(`Error fetching public customer profiles by IDs: ${error.message}`);\r\n      return { data: null, error: `Failed to fetch customer profiles: ${error.message}` };\r\n    }\r\n\r\n    return { data, error: null };\r\n  } catch (e) {\r\n    console.error(`Exception in getPublicCustomerProfilesByIds: ${e}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;;AAUO,eAAe,eAAe,QAAwB,EAAE,MAAc;IAC3E,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,KACP,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,QACf,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,MAAM,OAAO,EAAE;YAC7D,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,wCAAwC,EAAE,KAAK;QAC9D,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAQO,eAAe,kBAAkB,QAAwB,EAAE,OAA0C;IAC1G,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC;YAAC;SAAQ,EAChB,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,MAAM,OAAO,EAAE;YAC7D,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE,MAAM,MAAM,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,IAAI,OAAO,QAAQ;YAAM,OAAO;QAAK;IACnF,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,wCAAwC,EAAE,KAAK;QAC9D,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAQO,eAAe,6BAA6B,QAAwB,EAAE,MAAc;IACzF,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EACjB,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,QACf,WAAW;QAEd,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,MAAM,OAAO,EAAE;YACjE,OAAO;gBAAE,QAAQ;gBAAO,OAAO;YAAmC;QACpE;QAEA,OAAO;YAAE,QAAQ,CAAC,CAAC;YAAM,OAAO;QAAK;IACvC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,KAAK;QACzD,OAAO;YAAE,QAAQ;YAAO,OAAO;QAAgC;IACjE;AACF;AASO,eAAe,kBAAkB,QAAwB,EAAE,MAAc,EAAE,OAA0C;IAC1H,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,SACP,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,QACf,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,MAAM,OAAO,EAAE;YAC7D,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,wCAAwC,EAAE,KAAK;QAC9D,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAQO,eAAe,2BAA2B,QAAwB,EAAE,MAAc;IACvF,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,SAAS,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE,EAClG,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,QACf,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,0CAA0C,EAAE,MAAM,OAAO,EAAE;YAC1E,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,qDAAqD,EAAE,KAAK;QAC3E,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAQO,eAAe,yBAAyB,QAAwB,EAAE,OAAiB;IACxF,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,KAAK,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE,EAChF,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;QAElB,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,yCAAyC,EAAE,MAAM,OAAO,EAAE;YACzE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,oDAAoD,EAAE,KAAK;QAC1E,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAQO,eAAe,6BAA6B,QAAwB,EAAE,MAAc;IACzF,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,wBAAwB,EACpC,MAAM,CAAC,KACP,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,QACf,WAAW;QAEd,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,wCAAwC,EAAE,MAAM,OAAO,EAAE;YACxE,OAAO;gBAAE,MAAM;gBAAM,OAAO,CAAC,kCAAkC,EAAE,MAAM,OAAO,EAAE;YAAC;QACnF;QAEA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,GAAG;QAC/D,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAQO,eAAe,+BAA+B,QAAwB,EAAE,OAAiB;IAC9F,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,wBAAwB,EACpC,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE,EAC5G,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;QAElB,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,gDAAgD,EAAE,MAAM,OAAO,EAAE;YAChF,OAAO;gBAAE,MAAM;gBAAM,OAAO,CAAC,mCAAmC,EAAE,MAAM,OAAO,EAAE;YAAC;QACpF;QAEA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,CAAC,6CAA6C,EAAE,GAAG;QACjE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF", "debugId": null}}, {"offset": {"line": 2087, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/supabase/services/businessService.ts"], "sourcesContent": ["import { SupabaseClient } from \"@supabase/supabase-js\";\r\nimport { TABLES, COLUMNS, RPC_FUNCTIONS, RPC_PARAMS } from \"../constants\";\r\nimport { Tables, TablesInsert, TablesUpdate, Json } from \"../../../types/supabase\";\r\n\r\ntype BusinessProfileAnalyticsRow = Pick<Tables<'business_profiles'>, 'total_visits' | 'today_visits' | 'yesterday_visits' | 'visits_7_days' | 'visits_30_days'>;\r\n\r\n/**\r\n * Checks if a business profile exists for a given user ID.\r\n * @param userId The ID of the user to check.\r\n * @returns An object containing a boolean indicating if the profile exists and an error if one occurred.\r\n */\r\nexport async function checkIfBusinessProfileExists(supabase: SupabaseClient, userId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.BUSINESS_PROFILES)\r\n      .select(COLUMNS.ID)\r\n      .eq(COLUMNS.ID, userId)\r\n      .maybeSingle();\r\n\r\n    if (error) {\r\n      console.error(`Error checking existing business profile: ${error.message}`);\r\n      return { exists: false, error: \"Database error checking business profile.\" };\r\n    }\r\n\r\n    return { exists: !!data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error checking business profile: ${err}`);\r\n    return { exists: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches analytics data for a business profile.\r\n * @param businessProfileId The ID of the business profile.\r\n * @returns An object containing the business profile analytics data or an error.\r\n */\r\nexport async function getBusinessProfileAnalyticsData(supabase: SupabaseClient, businessProfileId: string): Promise<{ data: BusinessProfileAnalyticsRow | null; error: string | null }> {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.BUSINESS_PROFILES)\r\n      .select(`${COLUMNS.TOTAL_VISITS}, ${COLUMNS.TODAY_VISITS}, ${COLUMNS.YESTERDAY_VISITS}, ${COLUMNS.VISITS_7_DAYS}, ${COLUMNS.VISITS_30_DAYS}`)\r\n      .eq(COLUMNS.ID, businessProfileId)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching business profile analytics data: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data: data as BusinessProfileAnalyticsRow, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching business profile analytics data: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Calls the 'get_daily_unique_visit_trend' RPC function.\r\n * @param businessId The ID of the business.\r\n * @param startDate The start date for the trend (YYYY-MM-DD).\r\n * @param endDate The end date for the trend (YYYY-MM-DD).\r\n * @returns An object containing the daily unique visit trend data or an error.\r\n */\r\nexport async function getDailyUniqueVisitTrend(supabase: SupabaseClient, businessId: string, startDate: string, endDate: string) {\r\n  try {\r\n    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.GET_DAILY_UNIQUE_VISIT_TREND, {\r\n      [RPC_PARAMS.BUSINESS_ID]: businessId,\r\n      [RPC_PARAMS.START_DATE]: startDate,\r\n      [RPC_PARAMS.END_DATE]: endDate,\r\n    });\r\n    if (error) {\r\n      console.error(`Error fetching daily unique visit trend: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching daily unique visit trend: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Calls the 'get_hourly_unique_visit_trend' RPC function.\r\n * @param businessId The ID of the business.\r\n * @param targetDate The target date for the hourly trend (YYYY-MM-DD).\r\n * @returns An object containing the hourly unique visit trend data or an error.\r\n */\r\nexport async function getHourlyUniqueVisitTrend(supabase: SupabaseClient, businessId: string, targetDate: string) {\r\n  try {\r\n    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.GET_HOURLY_UNIQUE_VISIT_TREND, {\r\n      [RPC_PARAMS.BUSINESS_ID]: businessId,\r\n      [RPC_PARAMS.TARGET_DATE]: targetDate,\r\n    });\r\n    if (error) {\r\n      console.error(`Error fetching hourly unique visit trend: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching hourly unique visit trend: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Calls the 'get_monthly_unique_visits' RPC function.\r\n * @param businessId The ID of the business.\r\n * @param targetYear The target year.\r\n * @param targetMonth The target month.\r\n * @returns An object containing the monthly unique visits count or an error.\r\n */\r\nexport async function getMonthlyUniqueVisits(supabase: SupabaseClient, businessId: string, targetYear: number, targetMonth: number) {\r\n  try {\r\n    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.GET_MONTHLY_UNIQUE_VISITS, {\r\n      [RPC_PARAMS.BUSINESS_ID]: businessId,\r\n      [RPC_PARAMS.TARGET_YEAR]: targetYear,\r\n      [RPC_PARAMS.TARGET_MONTH]: targetMonth,\r\n    });\r\n    if (error) {\r\n      console.error(`Error fetching monthly unique visits: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching monthly unique visits: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Calls the 'get_monthly_unique_visit_trend' RPC function.\r\n * @param businessId The ID of the business.\r\n * @param startYear The start year for the trend.\r\n * @param startMonth The start month for the trend.\r\n * @param endYear The end year for the trend.\r\n * @param endMonth The end month for the trend.\r\n * @returns An object containing the monthly unique visit trend data or an error.\r\n */\r\nexport async function getMonthlyUniqueVisitTrend(supabase: SupabaseClient, businessId: string, startYear: number, startMonth: number, endYear: number, endMonth: number) {\r\n  try {\r\n    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.GET_MONTHLY_UNIQUE_VISIT_TREND, {\r\n      [RPC_PARAMS.BUSINESS_ID]: businessId,\r\n      [RPC_PARAMS.START_YEAR]: startYear,\r\n      [RPC_PARAMS.START_MONTH]: startMonth,\r\n      [RPC_PARAMS.END_YEAR]: endYear,\r\n      [RPC_PARAMS.END_MONTH]: endMonth,\r\n    });\r\n    if (error) {\r\n      console.error(`Error fetching monthly unique visit trend: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching monthly unique visit trend: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Calls the 'get_available_years_for_monthly_metrics' RPC function.\r\n * @param businessId The ID of the business.\r\n * @returns An object containing the available years for monthly metrics or an error.\r\n */\r\nexport async function getAvailableYearsForMonthlyMetrics(supabase: SupabaseClient, businessId: string) {\r\n  try {\r\n    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.GET_AVAILABLE_YEARS_FOR_MONTHLY_METRICS, {\r\n      [RPC_PARAMS.BUSINESS_ID]: businessId,\r\n    });\r\n    if (error) {\r\n      console.error(`Error fetching available years for monthly metrics: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching available years for monthly metrics: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Calls the 'get_total_unique_visits' RPC function.\r\n * @param businessId The ID of the business.\r\n * @returns An object containing the total unique visits count or an error.\r\n */\r\nexport async function getTotalUniqueVisits(supabase: SupabaseClient, businessId: string) {\r\n  try {\r\n    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.GET_TOTAL_UNIQUE_VISITS, {\r\n      [RPC_PARAMS.BUSINESS_ID]: businessId,\r\n    });\r\n    if (error) {\r\n      console.error(`Error fetching total unique visits: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching total unique visits: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches a business profile along with its interaction metrics.\r\n * @param userId The ID of the user to fetch the profile for.\r\n * @returns An object containing the business profile data or an error.\r\n */\r\nexport async function getBusinessProfileWithInteractionMetrics(supabase: SupabaseClient, userId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.BUSINESS_PROFILES)\r\n      .select(`${COLUMNS.TOTAL_LIKES}, ${COLUMNS.TOTAL_SUBSCRIPTIONS}, ${COLUMNS.AVERAGE_RATING}, ${COLUMNS.TOTAL_VISITS}, ${COLUMNS.TODAY_VISITS}, ${COLUMNS.YESTERDAY_VISITS}, ${COLUMNS.VISITS_7_DAYS}, ${COLUMNS.VISITS_30_DAYS}`)\r\n      .eq(COLUMNS.ID, userId)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching business profile with interaction metrics: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching business profile with interaction metrics: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches the latest subscription for a business.\r\n * @param userId The ID of the user to fetch the subscription for.\r\n * @returns An object containing the latest subscription data or an error.\r\n */\r\nexport async function getLatestSubscription(supabase: SupabaseClient, userId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.PAYMENT_SUBSCRIPTIONS)\r\n      .select(COLUMNS.PLAN_ID)\r\n      .eq(COLUMNS.BUSINESS_PROFILE_ID, userId)\r\n      .order(COLUMNS.CREATED_AT, { ascending: false })\r\n      .limit(1)\r\n      .maybeSingle();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching latest subscription: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching latest subscription: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches the custom_ads data for a business profile.\r\n * @param userId The ID of the business profile.\r\n * @returns An object containing the custom_ads data or an error.\r\n */\r\nexport async function getBusinessProfileCustomAds(supabase: SupabaseClient, userId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.BUSINESS_PROFILES)\r\n      .select(COLUMNS.CUSTOM_ADS)\r\n      .eq(COLUMNS.ID, userId)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching business profile custom ads: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data: data.custom_ads, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching business profile custom ads: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Updates a business profile.\r\n * @param userId The ID of the business profile to update.\r\n * @param updates The data to update.\r\n * @returns An object containing the updated data or an error.\r\n */\r\nexport async function updateBusinessProfile(supabase: SupabaseClient, userId: string, updates: TablesUpdate<'business_profiles'>) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.BUSINESS_PROFILES)\r\n      .update(updates)\r\n      .eq(COLUMNS.ID, userId)\r\n      .select()\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error updating business profile: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error updating business profile: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches the custom_branding data for a business profile.\r\n * @param userId The ID of the business profile.\r\n * @returns An object containing the custom_branding data or an error.\r\n */\r\nexport async function getBusinessProfileCustomBranding(supabase: SupabaseClient, userId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.BUSINESS_PROFILES)\r\n      .select(COLUMNS.CUSTOM_BRANDING)\r\n      .eq(COLUMNS.ID, userId)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching business profile custom branding: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data: data.custom_branding as Tables<'business_profiles'>['custom_branding'], error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching business profile custom branding: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches the phone number from a business profile.\r\n * @param userId The ID of the business profile.\r\n * @returns An object containing the phone number or an error.\r\n */\r\nexport async function getBusinessProfilePhone(supabase: SupabaseClient, userId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.BUSINESS_PROFILES)\r\n      .select(COLUMNS.PHONE)\r\n      .eq(COLUMNS.ID, userId)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching business profile phone: ${error.message}`);\r\n      return { phone: null, error: error.message };\r\n    }\r\n    return { phone: data.phone, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching business profile phone: ${err}`);\r\n    return { phone: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches a business profile with all relevant details for the business card.\r\n * @param userId The ID of the user to fetch the profile for.\r\n * @returns An object containing the business profile data or an error.\r\n */\r\nexport async function getBusinessProfileWithAllDetails(supabase: SupabaseClient, userId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.BUSINESS_PROFILES)\r\n      .select(\r\n        `\r\n        ${COLUMNS.ID}, ${COLUMNS.BUSINESS_NAME}, ${COLUMNS.CONTACT_EMAIL}, ${COLUMNS.HAS_ACTIVE_SUBSCRIPTION},\r\n        ${COLUMNS.TRIAL_END_DATE}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}, ${COLUMNS.LOGO_URL}, ${COLUMNS.MEMBER_NAME}, ${COLUMNS.TITLE},\r\n        ${COLUMNS.ADDRESS_LINE}, ${COLUMNS.CITY}, ${COLUMNS.STATE}, ${COLUMNS.PINCODE}, ${COLUMNS.LOCALITY}, ${COLUMNS.PHONE}, ${COLUMNS.INSTAGRAM_URL},\r\n        ${COLUMNS.FACEBOOK_URL}, ${COLUMNS.WHATSAPP_NUMBER}, ${COLUMNS.ABOUT_BIO}, ${COLUMNS.STATUS}, ${COLUMNS.BUSINESS_SLUG},\r\n        ${COLUMNS.TOTAL_LIKES}, ${COLUMNS.TOTAL_SUBSCRIPTIONS}, ${COLUMNS.AVERAGE_RATING}, ${COLUMNS.THEME_COLOR}, ${COLUMNS.DELIVERY_INFO}, ${COLUMNS.BUSINESS_HOURS},\r\n        ${COLUMNS.BUSINESS_CATEGORY}, ${COLUMNS.CUSTOM_BRANDING}, ${COLUMNS.CUSTOM_ADS}, ${COLUMNS.ESTABLISHED_YEAR}\r\n      `\r\n      )\r\n      .eq(COLUMNS.ID, userId)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching business profile with all details: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching business profile with all details: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches the latest subscription status for a given user ID.\r\n * @param userId The ID of the user to fetch the subscription status for.\r\n * @returns An object containing the subscription status (plan_id and subscription_status) or an error.\r\n */\r\nexport async function getLatestSubscriptionStatus(supabase: SupabaseClient, userId: string): Promise<{ subscriptionStatus: { plan_id: string | null; subscription_status: string | null } | null; error: string | null }> {\r\n  try {\r\n    const { data: subscription, error } = await supabase\r\n      .from(TABLES.PAYMENT_SUBSCRIPTIONS)\r\n      .select(`${COLUMNS.PLAN_ID}, ${COLUMNS.SUBSCRIPTION_STATUS}`)\r\n      .eq(COLUMNS.BUSINESS_PROFILE_ID, userId)\r\n      .order(COLUMNS.CREATED_AT, { ascending: false })\r\n      .limit(1)\r\n      .maybeSingle();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching latest subscription status: ${error.message}`);\r\n      return { subscriptionStatus: null, error: error.message };\r\n    }\r\n\r\n    return { subscriptionStatus: subscription, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching latest subscription status: ${err}`);\r\n    return { subscriptionStatus: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Securely fetches a business profile by slug, including subscription data.\r\n * This function uses the service role key to bypass RLS.\r\n * @param slug The slug of the business profile to fetch.\r\n * @returns An object containing the business profile data or an error.\r\n */\r\nexport async function getSecureBusinessProfileBySlug(supabase: SupabaseClient, slug: string) {\r\n  try {\r\n    const { data: profileData, error: profileError } = await supabase\r\n      .from(TABLES.BUSINESS_PROFILES)\r\n      .select(\r\n        `\r\n        *,\r\n        ${TABLES.PAYMENT_SUBSCRIPTIONS}!${COLUMNS.BUSINESS_PROFILE_ID} (\r\n          ${COLUMNS.PLAN_ID},\r\n          ${COLUMNS.SUBSCRIPTION_STATUS}\r\n        )\r\n      `\r\n      )\r\n      .eq(COLUMNS.BUSINESS_SLUG, slug)\r\n      .maybeSingle();\r\n\r\n    if (profileError) {\r\n      console.error(`Error fetching secure business profile by slug: ${profileError.message}`);\r\n      return { data: null, error: profileError.message };\r\n    }\r\n\r\n    if (!profileData) {\r\n      return { data: null, error: \"Profile not found.\" };\r\n    }\r\n\r\n    const safeData = {\r\n      ...profileData,\r\n      subscription_status:\r\n        (profileData[TABLES.PAYMENT_SUBSCRIPTIONS] as Tables<'payment_subscriptions'> | undefined)?.subscription_status || null,\r\n      plan_id: (profileData[TABLES.PAYMENT_SUBSCRIPTIONS] as Tables<'payment_subscriptions'> | undefined)?.plan_id || null,\r\n    };\r\n\r\n    return { data: safeData, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching secure business profile by slug: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Securely fetches a business profile with products by slug.\r\n * This function uses the service role key to bypass RLS.\r\n * @param slug The slug of the business profile to fetch.\r\n * @returns An object containing the business profile data with products or an error.\r\n */\r\nexport async function getSecureBusinessProfileWithProductsBySlug(supabase: SupabaseClient, slug: string) {\r\n  try {\r\n    const { data: profileData, error: profileError } = await supabase\r\n      .from(TABLES.BUSINESS_PROFILES)\r\n      .select(\r\n        `\r\n        *,\r\n        ${TABLES.PRODUCTS_SERVICES} (\r\n          ${COLUMNS.ID}, ${COLUMNS.NAME}, ${COLUMNS.DESCRIPTION}, ${COLUMNS.BASE_PRICE}, ${COLUMNS.DISCOUNTED_PRICE}, ${COLUMNS.IS_AVAILABLE}, ${COLUMNS.IMAGE_URL}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}, ${COLUMNS.PRODUCT_TYPE}\r\n        )\r\n      `\r\n      )\r\n      .eq(COLUMNS.BUSINESS_SLUG, slug)\r\n      .maybeSingle();\r\n\r\n    if (profileError) {\r\n      console.error(`Error fetching secure business profile with products by slug: ${profileError.message}`);\r\n      return { data: null, error: profileError.message };\r\n    }\r\n\r\n    if (!profileData) {\r\n      return { data: null, error: \"Profile not found.\" };\r\n    }\r\n\r\n    const safeData = {\r\n      ...profileData,\r\n      products_services: (profileData[TABLES.PRODUCTS_SERVICES] as Tables<'products_services'>[]) || [],\r\n    };\r\n\r\n    return { data: safeData, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching secure business profile with products by slug: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches ad data for a given pincode.\r\n * @param pincode The pincode to fetch ads for.\r\n * @returns An object containing the ad data or an error.\r\n */\r\nexport async function getAdDataForPincode(supabase: SupabaseClient, pincode: string) {\r\n  try {\r\n    // First, check if the custom_ad_targets table exists (for backward compatibility)\r\n    const { count, error: tableCheckError } = await supabase\r\n      .from(TABLES.CUSTOM_AD_TARGETS)\r\n      .select(\"*\", { count: \"exact\", head: true });\r\n\r\n    if (tableCheckError) {\r\n      console.error(`Error checking custom_ad_targets table: ${tableCheckError.message}`);\r\n      // Fallback to old approach if table check fails\r\n      return { adData: null, error: tableCheckError.message };\r\n    }\r\n\r\n    // If the table exists and migration has been applied\r\n    if (count !== null) {\r\n      const { data: adData, error: adError } = await supabase.rpc(\r\n        RPC_FUNCTIONS.GET_AD_FOR_PINCODE,\r\n        { [RPC_PARAMS.TARGET_PINCODE]: pincode }\r\n      );\r\n\r\n      if (adError) {\r\n        console.error(`Error fetching ad for pincode ${pincode}: ${adError.message}`);\r\n        return { adData: null, error: adError.message };\r\n      }\r\n      return { adData: adData && adData.length > 0 ? adData[0] : null, error: null };\r\n    } else {\r\n      // Fallback to old approach if migration hasn't been applied yet\r\n      const { data: customAd, error: customAdError } = await supabase\r\n        .from(TABLES.CUSTOM_ADS)\r\n        .select(`${COLUMNS.AD_IMAGE_URL}, ${COLUMNS.AD_LINK_URL}`)\r\n        .eq(COLUMNS.IS_ACTIVE, true)\r\n        .or(\r\n          `targeting_locations.eq.'\"global\"',targeting_locations.cs.'[\"${pincode}\"]'`\r\n        )\r\n        .order(COLUMNS.CREATED_AT, { ascending: false })\r\n        .limit(1)\r\n        .maybeSingle();\r\n\r\n      if (customAdError) {\r\n        console.error(`Error fetching custom ad (fallback): ${customAdError.message}`);\r\n        return { adData: null, error: customAdError.message };\r\n      }\r\n      return { adData: customAd, error: null };\r\n    }\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching ad data for pincode: ${err}`);\r\n    return { adData: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches products for a given business ID.\r\n * @param businessId The ID of the business.\r\n * @param limit The maximum number of products to return.\r\n * @returns An object containing the products data or an error.\r\n */\r\nexport async function getProductsForBusiness(supabase: SupabaseClient, businessId: string, limit: number) {\r\n  try {\r\n    const { data, error, count } = await supabase\r\n      .from(TABLES.PRODUCTS_SERVICES)\r\n      .select(\"*\", { count: \"exact\" })\r\n      .eq(COLUMNS.BUSINESS_ID, businessId)\r\n      .eq(COLUMNS.IS_AVAILABLE, true)\r\n      .order(COLUMNS.CREATED_AT, { ascending: false })\r\n      .limit(limit);\r\n\r\n    if (error) {\r\n      console.error(`Error fetching products for business ${businessId}: ${error.message}`);\r\n      return { products: null, count: 0, error: error.message };\r\n    }\r\n    return { products: data, count: count || 0, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching products for business: ${err}`);\r\n    return { products: null, count: 0, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches the count of reviews for a given business ID, excluding self-reviews.\r\n * @param businessId The ID of the business.\r\n * @returns An object containing the reviews count or an error.\r\n */\r\nexport async function getReviewsCountForBusiness(supabase: SupabaseClient, businessId: string) {\r\n  try {\r\n    const { count, error } = await supabase\r\n      .from(TABLES.RATINGS_REVIEWS)\r\n      .select(COLUMNS.ID, { count: \"exact\", head: true })\r\n      .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId)\r\n      .neq(COLUMNS.USER_ID, businessId); // Don't count self-reviews\r\n\r\n    if (error) {\r\n      console.error(`Error fetching reviews count for business ${businessId}: ${error.message}`);\r\n      return { count: 0, error: error.message };\r\n    }\r\n    return { count: count || 0, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching reviews count for business: ${err}`);\r\n    return { count: 0, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches the gallery data for a business profile.\r\n * @param businessId The ID of the business profile.\r\n * @returns An object containing the gallery data or an error.\r\n */\r\nexport async function getBusinessProfileGallery(supabase: SupabaseClient, businessId: string): Promise<{ data: Json | null; error?: string }> {\r\n  try {\r\n    const { data: profileData, error } = await supabase\r\n      .from(TABLES.BUSINESS_PROFILES)\r\n      .select(COLUMNS.GALLERY)\r\n      .eq(COLUMNS.ID, businessId)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching business profile gallery: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data: profileData.gallery };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching business profile gallery: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches the business ID and status for a given business slug.\r\n * @param businessSlug The slug of the business.\r\n * @returns An object containing the business ID and status or an error.\r\n */\r\nexport async function getBusinessProfileIdAndStatusBySlug(supabase: SupabaseClient, businessSlug: string) {\r\n  try {\r\n    const { data: business, error } = await supabase\r\n      .from(TABLES.BUSINESS_PROFILES)\r\n      .select(`${COLUMNS.ID}, ${COLUMNS.STATUS}`)\r\n      .eq(COLUMNS.BUSINESS_SLUG, businessSlug)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching business ID and status by slug: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data: business, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching business ID and status by slug: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches the public subscription status for a given business profile ID.\r\n * @param businessProfileId The ID of the business profile.\r\n * @returns An object containing the plan ID or an error.\r\n */\r\nexport async function getPublicSubscriptionStatus(supabase: SupabaseClient, businessProfileId: string) {\r\n  try {\r\n    const { data: subscription, error } = await supabase\r\n      .from(TABLES.PUBLIC_SUBSCRIPTION_STATUS)\r\n      .select(COLUMNS.PLAN_ID)\r\n      .eq(COLUMNS.BUSINESS_PROFILE_ID, businessProfileId)\r\n      .order(COLUMNS.CREATED_AT, { ascending: false })\r\n      .limit(1)\r\n      .maybeSingle();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching public subscription status: ${error.message}`);\r\n      return { planId: null, error: error.message };\r\n    }\r\n    return { planId: subscription?.plan_id || null, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching public subscription status: ${err}`);\r\n    return { planId: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches products for a given business ID with filters, sorting, and pagination.\r\n * @param businessId The ID of the business.\r\n * @param page The page number (1-based).\r\n * @param sortBy The sorting criteria.\r\n * @param pageSize The number of items per page.\r\n * @param searchTerm Optional search term for product names.\r\n * @param productType Optional product type filter.\r\n * @returns An object containing the products data, total count, or an error.\r\n */\r\nexport async function getProductsWithFiltersAndPagination(\r\n  supabase: SupabaseClient,\r\n  businessId: string,\r\n  page: number = 1,\r\n  sortBy: string = \"created_desc\",\r\n  pageSize: number = 20,\r\n  searchTerm?: string | null,\r\n  productType?: string | null\r\n) {\r\n  const offset = (page - 1) * pageSize;\r\n\r\n  let query = supabase\r\n    .from(TABLES.PRODUCTS_SERVICES)\r\n    .select(\r\n      `\r\n      ${COLUMNS.ID},\r\n      ${COLUMNS.BUSINESS_ID},\r\n      ${COLUMNS.NAME},\r\n      ${COLUMNS.DESCRIPTION},\r\n      ${COLUMNS.BASE_PRICE},\r\n      ${COLUMNS.DISCOUNTED_PRICE},\r\n      ${COLUMNS.PRODUCT_TYPE},\r\n      ${COLUMNS.IS_AVAILABLE},\r\n      ${COLUMNS.IMAGE_URL},\r\n      ${COLUMNS.CREATED_AT},\r\n      ${COLUMNS.UPDATED_AT},\r\n      ${COLUMNS.SLUG}\r\n    `,\r\n      { count: \"exact\" }\r\n    )\r\n    .eq(COLUMNS.BUSINESS_ID, businessId)\r\n    .eq(COLUMNS.IS_AVAILABLE, true);\r\n\r\n  if (searchTerm && searchTerm.trim().length > 0) {\r\n    query = query.ilike(COLUMNS.NAME, `%${searchTerm.trim()}%`);\r\n  }\r\n\r\n  if (productType && productType !== \"all\") {\r\n    query = query.eq(COLUMNS.PRODUCT_TYPE, productType);\r\n  }\r\n\r\n  switch (sortBy) {\r\n    case \"created_asc\":\r\n      query = query.order(COLUMNS.CREATED_AT, { ascending: true });\r\n      break;\r\n    case \"updated_desc\":\r\n      query = query.order(COLUMNS.UPDATED_AT, { ascending: false });\r\n      break;\r\n    case \"price_asc\":\r\n      query = query\r\n        .order(COLUMNS.DISCOUNTED_PRICE, { ascending: true, nullsFirst: false })\r\n        .order(COLUMNS.BASE_PRICE, { ascending: true, nullsFirst: false });\r\n      break;\r\n    case \"price_desc\":\r\n      query = query\r\n        .order(COLUMNS.DISCOUNTED_PRICE, { ascending: false, nullsFirst: false })\r\n        .order(COLUMNS.BASE_PRICE, { ascending: false, nullsFirst: false });\r\n      break;\r\n    case \"name_asc\":\r\n      query = query.order(COLUMNS.NAME, { ascending: true });\r\n      break;\r\n    case \"name_desc\":\r\n      query = query.order(COLUMNS.NAME, { ascending: false });\r\n      break;\r\n    case \"created_desc\":\r\n    default:\r\n      query = query.order(COLUMNS.CREATED_AT, { ascending: false });\r\n      break;\r\n  }\r\n\r\n  query = query.range(offset, offset + pageSize - 1);\r\n\r\n  const { data, error, count } = await query;\r\n\r\n  if (error) {\r\n    console.error(`Error fetching products: ${error.message}`);\r\n    return { data: null, error: error.message, totalCount: 0 };\r\n  }\r\n\r\n  return { data: data as Tables<'products_services'>[], error: null, totalCount: count || 0 };\r\n}\r\n\r\n/**\r\n * Fetches the status of a business profile.\r\n * @param businessProfileId The ID of the business profile.\r\n * @returns An object containing the status or an error.\r\n */\r\nexport async function getBusinessProfileStatus(supabase: SupabaseClient, businessProfileId: string) {\r\n  try {\r\n    const { data: profile, error } = await supabase\r\n      .from(TABLES.BUSINESS_PROFILES)\r\n      .select(COLUMNS.STATUS)\r\n      .eq(COLUMNS.ID, businessProfileId)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching business profile status: ${error.message}`);\r\n      return { status: null, error: error.message };\r\n    }\r\n    return { status: profile.status, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching business profile status: ${err}`);\r\n    return { status: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Inserts a new card visit record.\r\n * @param visitData The data for the card visit.\r\n * @returns An object indicating success or an error.\r\n */\r\nexport async function insertCardVisit(supabase: SupabaseClient, visitData: TablesInsert<'card_visits'>) {\r\n  try {\r\n    const { error } = await supabase.from(TABLES.CARD_VISITS).insert([visitData]);\r\n\r\n    if (error) {\r\n      console.error(`Error inserting card visit: ${error.message}`);\r\n      return { success: false, error: error.message };\r\n    }\r\n    return { success: true, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error inserting card visit: ${err}`);\r\n    return { success: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Updates the logo URL for a business profile in the database.\r\n * @param userId - The ID of the user whose business profile is being updated.\r\n * @param logoUrl - The new logo URL to set.\r\n * @returns A Promise that resolves to an object indicating success or containing an error message.\r\n */\r\nexport async function updateBusinessLogoUrl(\r\n  supabase: SupabaseClient,\r\n  userId: string,\r\n  logoUrl: string | null\r\n): Promise<{ success: boolean; error?: string }> {\r\n  const { error: updateError } = await supabase\r\n    .from(TABLES.BUSINESS_PROFILES)\r\n    .update({ [COLUMNS.LOGO_URL]: logoUrl as string | null | undefined, [COLUMNS.UPDATED_AT]: new Date().toISOString() })\r\n    .eq(COLUMNS.ID, userId);\r\n\r\n  if (updateError) {\r\n    console.error(\"Update Business Logo URL Error:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update business logo URL: ${updateError.message}`,\r\n    };\r\n  }\r\n  return { success: true };\r\n}\r\n\r\n/**\r\n * Fetches the current logo URL for a business profile.\r\n * @param userId - The ID of the user whose business profile logo URL is being fetched.\r\n * @returns A Promise that resolves to an object containing the logo URL or an error message.\r\n */\r\nexport async function getBusinessLogoUrl(\r\n  supabase: SupabaseClient,\r\n  userId: string\r\n): Promise<{ logoUrl: string | null; error?: string }> {\r\n  const { data, error: fetchError } = await supabase\r\n    .from(TABLES.BUSINESS_PROFILES)\r\n    .select(COLUMNS.LOGO_URL)\r\n    .eq(COLUMNS.ID, userId)\r\n    .single();\r\n\r\n  if (fetchError) {\r\n    console.error(\"Error fetching business logo URL:\", fetchError);\r\n    return { logoUrl: null, error: \"Failed to fetch business logo URL.\" };\r\n  }\r\n\r\n  return { logoUrl: data?.logo_url || null };\r\n}\r\n\r\n/**\r\n * Checks if a business slug is unique and not currently in use by another business profile.\r\n * @param slug The business slug to check for availability.\r\n * @param excludeUserId Optional. A user ID to exclude from the check (e.g., the current user's profile).\r\n * @returns An object indicating whether the slug is available and an error message if any.\r\n */\r\nexport async function checkBusinessSlugUniqueness(supabase: SupabaseClient, slug: string, excludeUserId: string | null = null) {\r\n  try {\r\n    let query = supabase\r\n      .from(TABLES.BUSINESS_PROFILES)\r\n      .select(COLUMNS.ID)\r\n      .ilike(COLUMNS.BUSINESS_SLUG, slug);\r\n\r\n    if (excludeUserId) {\r\n      query = query.neq(COLUMNS.ID, excludeUserId);\r\n    }\r\n\r\n    const { data, error } = await query.maybeSingle();\r\n\r\n    if (error) {\r\n      console.error(`Error checking business slug uniqueness: ${error.message}`);\r\n      return { available: false, error: error.message };\r\n    }\r\n\r\n    return { available: !data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error checking business slug uniqueness: ${err}`);\r\n    return { available: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches business profiles based on city, status, category, with pagination and sorting.\r\n * @param city The city to filter by.\r\n * @param status The status of the business profile (e.g., 'online').\r\n * @param category Optional. The business category to filter by.\r\n * @param page The page number for pagination (1-based).\r\n * @param limit The number of results per page.\r\n * @param sortBy The column to sort by.\r\n * @param ascending Whether to sort in ascending order.\r\n * @returns An object containing the business profiles data and count, or an error.\r\n */\r\nexport async function getBusinessProfilesByCity(\r\n  supabase: SupabaseClient,\r\n  city: string,\r\n  status: string,\r\n  category: string | null,\r\n  page: number,\r\n  limit: number,\r\n  sortBy: string,\r\n  ascending: boolean\r\n): Promise<{ data: any[] | null; count: number | null; error: string | null }> {\r\n  try {\r\n    const offset = (page - 1) * limit;\r\n\r\n    let query = supabase\r\n      .from(TABLES.BUSINESS_PROFILES)\r\n      .select(\r\n        `\r\n        ${COLUMNS.ID}, ${COLUMNS.BUSINESS_NAME}, ${COLUMNS.LOGO_URL}, ${COLUMNS.MEMBER_NAME}, ${COLUMNS.TITLE},\r\n        ${COLUMNS.ADDRESS_LINE}, ${COLUMNS.CITY}, ${COLUMNS.STATE}, ${COLUMNS.PINCODE}, ${COLUMNS.LOCALITY}, ${COLUMNS.PHONE}, ${COLUMNS.BUSINESS_CATEGORY}, ${COLUMNS.INSTAGRAM_URL},\r\n        ${COLUMNS.FACEBOOK_URL}, ${COLUMNS.WHATSAPP_NUMBER}, ${COLUMNS.ABOUT_BIO}, ${COLUMNS.STATUS}, ${COLUMNS.BUSINESS_SLUG}, ${COLUMNS.THEME_COLOR},\r\n        ${COLUMNS.DELIVERY_INFO}, ${COLUMNS.TOTAL_LIKES}, ${COLUMNS.TOTAL_SUBSCRIPTIONS}, ${COLUMNS.AVERAGE_RATING}, ${COLUMNS.BUSINESS_HOURS},\r\n        ${COLUMNS.TRIAL_END_DATE}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}, ${COLUMNS.CONTACT_EMAIL}, ${COLUMNS.ESTABLISHED_YEAR}\r\n        `,\r\n        { count: \"exact\" }\r\n      )\r\n      .eq(COLUMNS.CITY, city)\r\n      .eq(COLUMNS.STATUS, status);\r\n\r\n    if (category && category.trim()) {\r\n      query = query.eq(COLUMNS.BUSINESS_CATEGORY, category.trim());\r\n    }\r\n\r\n    const { data, count, error } = await query\r\n      .range(offset, offset + limit - 1)\r\n      .order(sortBy, { ascending });\r\n\r\n    if (error) {\r\n      console.error(`Error fetching business profiles by city: ${error.message}`);\r\n      return { data: null, count: null, error: error.message };\r\n    }\r\n\r\n    return { data, count, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching business profiles by city: ${err}`);\r\n    return { data: null, count: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches business IDs based on city, status, and category.\r\n * @param city The city to filter by.\r\n * @param status The status of the business profile (e.g., 'online').\r\n * @param category Optional. The business category to filter by.\r\n * @returns An object containing an array of business IDs or an error.\r\n */\r\nexport async function getBusinessIdsByCity(\r\n  supabase: SupabaseClient,\r\n  city: string,\r\n  status: string,\r\n  category: string | null\r\n): Promise<{ data: string[] | null; error: string | null }> {\r\n  try {\r\n    let query = supabase\r\n      .from(TABLES.BUSINESS_PROFILES)\r\n      .select(COLUMNS.ID)\r\n      .eq(COLUMNS.CITY, city)\r\n      .eq(COLUMNS.STATUS, status);\r\n\r\n    if (category && category.trim()) {\r\n      query = query.eq(COLUMNS.BUSINESS_CATEGORY, category.trim());\r\n    }\r\n\r\n    const { data, error } = await query;\r\n\r\n    if (error) {\r\n      console.error(`Error fetching business IDs by city: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n\r\n    return { data: data.map((item) => item.id), error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching business IDs by city: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Gets the count of products for a given set of business IDs.\r\n * @param businessIds An array of business IDs.\r\n * @param productType Optional. The product type to filter by.\r\n * @returns An object containing the product count or an error.\r\n */\r\nexport async function getProductCountByBusinessIds(\r\n  supabase: SupabaseClient,\r\n  businessIds: string[],\r\n  productType: string | null\r\n): Promise<{ count: number | null; error: string | null }> {\r\n  try {\r\n    let query = supabase\r\n      .from(TABLES.PRODUCTS_SERVICES)\r\n      .select(COLUMNS.ID, { count: \"exact\" })\r\n      .in(COLUMNS.BUSINESS_ID, businessIds)\r\n      .eq(COLUMNS.IS_AVAILABLE, true);\r\n\r\n    if (productType) {\r\n      query = query.eq(COLUMNS.PRODUCT_TYPE, productType);\r\n    }\r\n\r\n    const { count, error } = await query;\r\n\r\n    if (error) {\r\n      console.error(`Error counting products by business IDs: ${error.message}`);\r\n      return { count: null, error: error.message };\r\n    }\r\n\r\n    return { count, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error counting products by business IDs: ${err}`);\r\n    return { count: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches products for a given set of business IDs with pagination and sorting.\r\n * @param businessIds An array of business IDs.\r\n * @param page The page number for pagination (1-based).\r\n * @param limit The number of results per page.\r\n * @param sortBy The column to sort by.\r\n * @param ascending Whether to sort in ascending order.\r\n * @param productType Optional. The product type to filter by.\r\n * @returns An object containing the products data or an error.\r\n */\r\nexport async function getProductsByBusinessIds(\r\n  supabase: SupabaseClient,\r\n  businessIds: string[],\r\n  page: number,\r\n  limit: number,\r\n  sortBy: string,\r\n  ascending: boolean,\r\n  productType: string | null\r\n): Promise<{ data: any[] | null; error: string | null }> {\r\n  try {\r\n    const from = (page - 1) * limit;\r\n    const to = from + limit - 1;\r\n\r\n    let query = supabase\r\n      .from(TABLES.PRODUCTS_SERVICES)\r\n      .select(\r\n        `\r\n        ${COLUMNS.ID}, ${COLUMNS.BUSINESS_ID}, ${COLUMNS.NAME}, ${COLUMNS.DESCRIPTION}, ${COLUMNS.BASE_PRICE}, ${COLUMNS.DISCOUNTED_PRICE}, ${COLUMNS.PRODUCT_TYPE},\r\n        ${COLUMNS.IS_AVAILABLE}, ${COLUMNS.IMAGE_URL}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}, ${COLUMNS.SLUG},\r\n        business_profiles!${COLUMNS.BUSINESS_ID}(${COLUMNS.BUSINESS_SLUG})\r\n        `\r\n      )\r\n      .in(COLUMNS.BUSINESS_ID, businessIds)\r\n      .eq(COLUMNS.IS_AVAILABLE, true);\r\n\r\n    if (productType) {\r\n      query = query.eq(COLUMNS.PRODUCT_TYPE, productType);\r\n    }\r\n\r\n    // Apply sorting based on the sortBy parameter\r\n    if (sortBy === COLUMNS.DISCOUNTED_PRICE) {\r\n      query = query\r\n        .order(COLUMNS.DISCOUNTED_PRICE, { ascending, nullsFirst: false })\r\n        .order(COLUMNS.BASE_PRICE, { ascending, nullsFirst: false });\r\n    } else {\r\n      query = query.order(sortBy, { ascending });\r\n    }\r\n\r\n    const { data, error } = await query.range(from, to);\r\n\r\n    if (error) {\r\n      console.error(`Error fetching products by business IDs: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching products by business IDs: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches a product by its ID and business ID.\r\n * @param productId The ID of the product.\r\n * @param businessId The ID of the business.\r\n * @returns An object containing the product data or an error.\r\n */\r\nexport async function getProductByIdAndBusinessId(supabase: SupabaseClient, productId: string, businessId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.PRODUCTS_SERVICES)\r\n      .select(`${COLUMNS.ID}, ${COLUMNS.BUSINESS_ID}`)\r\n      .eq(COLUMNS.ID, productId)\r\n      .eq(COLUMNS.BUSINESS_ID, businessId)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching product by ID and business ID: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching product by ID and business ID: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches existing product variants for a given product ID.\r\n * @param productId The ID of the product.\r\n * @returns An object containing the existing product variants or an error.\r\n */\r\nexport async function getExistingProductVariants(supabase: SupabaseClient, productId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.PRODUCT_VARIANTS)\r\n      .select(`${COLUMNS.ID}, ${COLUMNS.VARIANT_VALUES}`)\r\n      .eq(COLUMNS.PRODUCT_ID, productId);\r\n\r\n    if (error) {\r\n      console.error(`Error fetching existing product variants: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching existing product variants: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Inserts a new product variant.\r\n * @param variantData The data for the new product variant.\r\n * @returns An object containing the newly inserted product variant data or an error.\r\n */\r\nexport async function insertProductVariant(supabase: SupabaseClient, variantData: TablesInsert<'product_variants'>) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.PRODUCT_VARIANTS)\r\n      .insert(variantData)\r\n      .select()\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error inserting product variant: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error inserting product variant: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Deletes a product variant by its ID.\r\n * @param variantId The ID of the product variant to delete.\r\n * @returns An object indicating success or an error.\r\n */\r\nexport async function deleteProductVariant(supabase: SupabaseClient, variantId: string) {\r\n  try {\r\n    const { error } = await supabase\r\n      .from(TABLES.PRODUCT_VARIANTS)\r\n      .delete()\r\n      .eq(COLUMNS.ID, variantId);\r\n\r\n    if (error) {\r\n      console.error(`Error deleting product variant: ${error.message}`);\r\n      return { success: false, error: error.message };\r\n    }\r\n    return { success: true, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error deleting product variant: ${err}`);\r\n    return { success: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Updates a product variant with new image URLs.\r\n * @param variantId The ID of the product variant to update.\r\n * @param imageUrls An array of new image URLs.\r\n * @param featuredImageIndex The index of the featured image.\r\n * @returns An object indicating success or an error.\r\n */\r\nexport async function updateProductVariantImages(supabase: SupabaseClient, variantId: string, imageUrls: string[], featuredImageIndex: number) {\r\n  try {\r\n    const updateData = {\r\n      images: imageUrls,\r\n      featured_image_index: featuredImageIndex,\r\n    };\r\n    const { error } = await supabase\r\n      .from(TABLES.PRODUCT_VARIANTS)\r\n      .update(updateData)\r\n      .eq(COLUMNS.ID, variantId);\r\n\r\n    if (error) {\r\n      console.error(`Error updating product variant images: ${error.message}`);\r\n      return { success: false, error: error.message };\r\n    }\r\n    return { success: true, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error updating product variant images: ${err}`);\r\n    return { success: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Inserts multiple product variants in a single operation.\r\n * @param variantDataArray An array of product variant data to insert.\r\n * @returns An object containing the newly inserted product variant data or an error.\r\n */\r\nexport async function insertMultipleProductVariants(supabase: SupabaseClient, variantDataArray: TablesInsert<'product_variants'>[]) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.PRODUCT_VARIANTS)\r\n      .insert(variantDataArray)\r\n      .select();\r\n\r\n    if (error) {\r\n      console.error(`Error inserting multiple product variants: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error inserting multiple product variants: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches the locality, pincode, and city slug for a business profile.\r\n * @param userId The ID of the user.\r\n * @returns An object containing the location data or an error.\r\n */\r\nexport async function getBusinessProfileLocation(supabase: SupabaseClient, userId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.BUSINESS_PROFILES)\r\n      .select(`${COLUMNS.LOCALITY_SLUG}, ${COLUMNS.PINCODE}, ${COLUMNS.CITY_SLUG}, ${COLUMNS.STATE_SLUG}`)\r\n      .eq(COLUMNS.ID, userId)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching business profile location: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching business profile location: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches the total count of likes for a business.\r\n * @param businessId The ID of the business.\r\n * @returns The total count of likes.\r\n */\r\nexport async function getBusinessLikesCount(supabase: SupabaseClient, businessId: string): Promise<number> {\r\n  const { count, error } = await supabase\r\n    .from(TABLES.LIKES)\r\n    .select(COLUMNS.ID, { count: 'exact', head: true })\r\n    .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId);\r\n\r\n  if (error) {\r\n    console.error('Error fetching business likes count:', error);\r\n    throw new Error('Failed to get total count');\r\n  }\r\n\r\n  return count || 0;\r\n}\r\n\r\n/**\r\n * Fetches likes for a business with pagination.\r\n * @param businessId The ID of the business.\r\n * @param page The page number.\r\n * @param limit The number of items per page.\r\n * @returns A list of likes.\r\n */\r\nexport async function getBusinessLikes(supabase: SupabaseClient, businessId: string, page: number, limit: number) {\r\n  const from = (page - 1) * limit;\r\n  const { data, error } = await supabase\r\n    .from(TABLES.LIKES)\r\n    .select(`${COLUMNS.ID}, ${COLUMNS.USER_ID}`)\r\n    .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId)\r\n    .range(from, from + limit - 1);\r\n\r\n  if (error) {\r\n    console.error('Error fetching business likes:', error);\r\n    throw new Error('Failed to fetch likes');\r\n  }\r\n\r\n  return data;\r\n}\r\n\r\n/**\r\n * Fetches the total count of businesses liked by a business.\r\n * @param businessId The ID of the business.\r\n * @param searchTerm Optional search term for business names.\r\n * @returns The total count of liked businesses.\r\n */\r\nexport async function getMyLikesCount(supabase: SupabaseClient, businessId: string, searchTerm?: string): Promise<number> {\r\n  let countQuery = supabase\r\n    .from(TABLES.LIKES)\r\n    .select(`\r\n      ${COLUMNS.ID},\r\n      ${TABLES.BUSINESS_PROFILES}!inner (\r\n        ${COLUMNS.ID},\r\n        ${COLUMNS.BUSINESS_NAME}\r\n      )\r\n    `, { count: 'exact', head: true })\r\n    .eq(COLUMNS.USER_ID, businessId);\r\n\r\n  if (searchTerm) {\r\n    countQuery = countQuery.ilike(`${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`, `%${searchTerm}%`);\r\n  }\r\n\r\n  const { count, error } = await countQuery;\r\n\r\n  if (error) {\r\n    console.error('Error fetching my likes count:', error);\r\n    throw new Error('Failed to get total count');\r\n  }\r\n\r\n  return count || 0;\r\n}\r\n\r\n/**\r\n * Fetches businesses liked by a business with pagination and search.\r\n * @param businessId The ID of the business.\r\n * @param page The page number.\r\n * @param limit The number of items per page.\r\n * @param searchTerm Optional search term for business names.\r\n * @returns A list of liked businesses.\r\n */\r\nexport async function getMyLikes(supabase: SupabaseClient, businessId: string, page: number, limit: number, searchTerm?: string) {\r\n  const from = (page - 1) * limit;\r\n  let query = supabase\r\n    .from(TABLES.LIKES)\r\n    .select(`\r\n      ${COLUMNS.ID},\r\n      ${TABLES.BUSINESS_PROFILES}!inner (\r\n        ${COLUMNS.ID},\r\n        ${COLUMNS.BUSINESS_NAME},\r\n        ${COLUMNS.BUSINESS_SLUG},\r\n        ${COLUMNS.LOGO_URL},\r\n        ${COLUMNS.CITY},\r\n        ${COLUMNS.STATE},\r\n        ${COLUMNS.PINCODE},\r\n        ${COLUMNS.ADDRESS_LINE},\r\n        ${COLUMNS.LOCALITY}\r\n      )\r\n    `)\r\n    .eq(COLUMNS.USER_ID, businessId);\r\n\r\n  if (searchTerm) {\r\n    query = query.ilike(`${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`, `%${searchTerm}%`);\r\n  }\r\n\r\n  const { data, error } = await query.range(from, from + limit - 1);\r\n\r\n  if (error) {\r\n    console.error('Error fetching my likes:', error);\r\n    throw new Error('Failed to fetch likes');\r\n  }\r\n\r\n  return data;\r\n}\r\n\r\n/**\r\n * Fetches business profiles by their IDs.\r\n * @param userIds An array of user IDs.\r\n * @returns An object containing the business profiles data or an error.\r\n */\r\nexport async function getBusinessProfilesByIds(supabase: SupabaseClient, userIds: string[]) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.BUSINESS_PROFILES)\r\n      .select(`${COLUMNS.ID}, ${COLUMNS.BUSINESS_NAME}, ${COLUMNS.BUSINESS_SLUG}, ${COLUMNS.LOGO_URL}, ${COLUMNS.CITY}, ${COLUMNS.STATE}, ${COLUMNS.PINCODE}, ${COLUMNS.ADDRESS_LINE}`)\r\n      .in(COLUMNS.ID, userIds);\r\n\r\n    if (error) {\r\n      console.error(`Error fetching business profiles by IDs: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching business profiles by IDs: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Inserts a new product into the database.\r\n * @param productData The data for the new product.\r\n * @returns An object containing the newly inserted product data or an error.\r\n */\r\nexport async function insertProduct(supabase: SupabaseClient, productData: TablesInsert<'products_services'>) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.PRODUCTS_SERVICES)\r\n      .insert(productData)\r\n      .select(`${COLUMNS.ID}, ${COLUMNS.PRODUCT_TYPE}, ${COLUMNS.NAME}, ${COLUMNS.DESCRIPTION}, ${COLUMNS.BASE_PRICE}, ${COLUMNS.DISCOUNTED_PRICE}, ${COLUMNS.IS_AVAILABLE}, ${COLUMNS.IMAGE_URL}, ${COLUMNS.IMAGES}, ${COLUMNS.FEATURED_IMAGE_INDEX}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}, ${COLUMNS.SLUG}`)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error inserting product: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error inserting product: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Updates an existing product in the database.\r\n * @param productId The ID of the product to update.\r\n * @param updates The data to update.\r\n * @returns An object containing the updated product data or an error.\r\n */\r\nexport async function updateProduct(supabase: SupabaseClient, productId: string, updates: TablesUpdate<'products_services'>) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.PRODUCTS_SERVICES)\r\n      .update(updates)\r\n      .eq(COLUMNS.ID, productId)\r\n      .select(`${COLUMNS.ID}, ${COLUMNS.IMAGE_URL}, ${COLUMNS.IMAGES}, ${COLUMNS.FEATURED_IMAGE_INDEX}`)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error updating product: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error updating product: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches a product by its ID.\r\n * @param productId The ID of the product to fetch.\r\n * @returns An object containing the product data or an error.\r\n */\r\nexport async function getProductById(supabase: SupabaseClient, productId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.PRODUCTS_SERVICES)\r\n      .select(`${COLUMNS.ID}, ${COLUMNS.PRODUCT_TYPE}, ${COLUMNS.NAME}, ${COLUMNS.DESCRIPTION}, ${COLUMNS.BASE_PRICE}, ${COLUMNS.DISCOUNTED_PRICE}, ${COLUMNS.IS_AVAILABLE}, ${COLUMNS.IMAGE_URL}, ${COLUMNS.IMAGES}, ${COLUMNS.FEATURED_IMAGE_INDEX}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}, ${COLUMNS.SLUG}`)\r\n      .eq(COLUMNS.ID, productId)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching product by ID: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching product by ID: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches product variants along with their associated product's business ID.\r\n * @param variantIds An array of variant IDs.\r\n * @returns An object containing the variant data or an error.\r\n */\r\nexport async function getVariantsWithProductBusinessId(supabase: SupabaseClient, variantIds: string[]) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.PRODUCT_VARIANTS)\r\n      .select(`\r\n        ${COLUMNS.ID},\r\n        ${COLUMNS.PRODUCT_ID},\r\n        ${COLUMNS.BASE_PRICE},\r\n        ${COLUMNS.DISCOUNTED_PRICE},\r\n        ${COLUMNS.IS_AVAILABLE},\r\n        ${TABLES.PRODUCTS_SERVICES}!inner(${COLUMNS.BUSINESS_ID})\r\n      `)\r\n      .in(COLUMNS.ID, variantIds);\r\n\r\n    if (error) {\r\n      console.error(`Error fetching variants with product business ID: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching variants with product business ID: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches a business profile by its ID.\r\n * @param businessId The ID of the business profile.\r\n * @returns An object containing the business profile data or an error.\r\n */\r\nexport async function getBusinessProfileById(supabase: SupabaseClient, businessId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.BUSINESS_PROFILES)\r\n      .select(`${COLUMNS.ID}, ${COLUMNS.BUSINESS_NAME}, ${COLUMNS.LOGO_URL}`)\r\n      .eq(COLUMNS.ID, businessId)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching business profile by ID: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching business profile by ID: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches business profile information relevant to subscriptions.\r\n * @param supabase The Supabase client.\r\n * @param businessId The ID of the business profile.\r\n * @returns An object containing the business profile data or an error.\r\n */\r\nexport async function getBusinessProfileSubscriptionInfo(supabase: SupabaseClient, businessId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.BUSINESS_PROFILES)\r\n      .select(`${COLUMNS.ID}, ${COLUMNS.HAS_ACTIVE_SUBSCRIPTION}, ${COLUMNS.TRIAL_END_DATE}, ${COLUMNS.STATUS}`)\r\n      .eq(COLUMNS.ID, businessId)\r\n      .maybeSingle();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching business profile subscription info: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching business profile subscription info: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches a payment subscription by business profile ID.\r\n * @param supabase The Supabase client.\r\n * @param businessProfileId The ID of the business profile.\r\n * @returns An object containing the payment subscription data or an error.\r\n */\r\nexport async function getPaymentSubscriptionByBusinessProfileId(supabase: SupabaseClient, businessProfileId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.PAYMENT_SUBSCRIPTIONS)\r\n      .select(\"*\")\r\n      .eq(COLUMNS.BUSINESS_PROFILE_ID, businessProfileId)\r\n      .order(COLUMNS.CREATED_AT, { ascending: false })\r\n      .limit(1)\r\n      .maybeSingle();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching payment subscription: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching payment subscription: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n\r\n/**\r\n * Performs a bulk update on product variants.\r\n * @param variantIds An array of variant IDs to update.\r\n * @param updateData The data to update.\r\n * @returns An object containing the updated variant data or an error.\r\n */\r\nexport async function bulkUpdateProductVariants(supabase: SupabaseClient, variantIds: string[], updateData: TablesUpdate<'product_variants'>) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.PRODUCT_VARIANTS)\r\n      .update(updateData)\r\n      .in(COLUMNS.ID, variantIds)\r\n      .select(COLUMNS.ID);\r\n\r\n    if (error) {\r\n      console.error(`Error bulk updating product variants: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error bulk updating product variants: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Updates a single product variant.\r\n * @param variantId The ID of the variant to update.\r\n * @param updateData The data to update.\r\n * @returns An object indicating success or an error.\r\n */\r\nexport async function updateProductVariant(supabase: SupabaseClient, variantId: string, updateData: TablesUpdate<'product_variants'>) {\r\n  try {\r\n    const { error } = await supabase\r\n      .from(TABLES.PRODUCT_VARIANTS)\r\n      .update(updateData)\r\n      .eq(COLUMNS.ID, variantId);\r\n\r\n    if (error) {\r\n      console.error(`Error updating product variant: ${error.message}`);\r\n      return { success: false, error: error.message };\r\n    }\r\n    return { success: true, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error updating product variant: ${err}`);\r\n    return { success: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches product details by ID and business ID.\r\n * @param productId The ID of the product.\r\n * @param businessId The ID of the business.\r\n * @returns An object containing the product data or an error.\r\n */\r\nexport async function getProductDetailsByIdAndBusinessId(supabase: SupabaseClient, productId: string, businessId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.PRODUCTS_SERVICES)\r\n      .select(`${COLUMNS.IMAGES}, ${COLUMNS.NAME}`)\r\n      .eq(COLUMNS.ID, productId)\r\n      .eq(COLUMNS.BUSINESS_ID, businessId)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching product details by ID and business ID: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching product details by ID and business ID: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Deletes a product by its ID and business ID.\r\n * @param productId The ID of the product to delete.\r\n * @param businessId The ID of the business.\r\n * @returns An object indicating success or an error.\r\n */\r\nexport async function deleteProductByIdAndBusinessId(supabase: SupabaseClient, productId: string, businessId: string) {\r\n  try {\r\n    const { error } = await supabase\r\n      .from(TABLES.PRODUCTS_SERVICES)\r\n      .delete()\r\n      .eq(COLUMNS.ID, productId)\r\n      .eq(COLUMNS.BUSINESS_ID, businessId);\r\n\r\n    if (error) {\r\n      console.error(`Error deleting product by ID and business ID: ${error.message}`);\r\n      return { success: false, error: error.message };\r\n    }\r\n    return { success: true, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error deleting product by ID and business ID: ${err}`);\r\n    return { success: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Deletes all product variants for a given product ID.\r\n * @param productId The ID of the product.\r\n * @returns An object indicating success or an error.\r\n */\r\nexport async function deleteProductVariantsByProductId(supabase: SupabaseClient, productId: string) {\r\n  try {\r\n    const { error } = await supabase\r\n      .from(TABLES.PRODUCT_VARIANTS)\r\n      .delete()\r\n      .eq(COLUMNS.PRODUCT_ID, productId);\r\n\r\n    if (error) {\r\n      console.error(`Error deleting product variants by product ID: ${error.message}`);\r\n      return { success: false, error: error.message };\r\n    }\r\n    return { success: true, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error deleting product variants by product ID: ${err}`);\r\n    return { success: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Calls the 'get_product_with_variants' RPC function.\r\n * @param productId The ID of the product.\r\n * @returns An object containing the product data with variants or an error.\r\n */\r\nexport async function getRpcProductWithVariants(supabase: SupabaseClient, productId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .rpc(RPC_FUNCTIONS.GET_PRODUCT_WITH_VARIANTS, { product_uuid: productId });\r\n\r\n    if (error) {\r\n      console.error(`Error calling get_product_with_variants: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error in getRpcProductWithVariants: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches the business ID of a product.\r\n * @param productId The ID of the product.\r\n * @param userId The ID of the user.\r\n * @returns An object containing the product data or an error.\r\n */\r\nexport async function getProductBusinessId(supabase: SupabaseClient, productId: string, userId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.PRODUCTS_SERVICES)\r\n      .select(COLUMNS.BUSINESS_ID)\r\n      .eq(COLUMNS.ID, productId)\r\n      .eq(COLUMNS.BUSINESS_ID, userId)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching product business ID: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching product business ID: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Calls the 'get_available_product_variants' RPC function.\r\n * @param productId The ID of the product.\r\n * @returns An object containing the available product variants or an error.\r\n */\r\nexport async function getRpcAvailableProductVariants(supabase: SupabaseClient, productId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .rpc(RPC_FUNCTIONS.GET_AVAILABLE_PRODUCT_VARIANTS, { product_uuid: productId });\r\n\r\n    if (error) {\r\n      console.error(`Error calling get_available_product_variants: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error in getRpcAvailableProductVariants: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches product variants with filtering and sorting.\r\n * @param productId The ID of the product.\r\n * @param options Filtering and sorting options.\r\n * @returns An object containing the product variants data and count, or an error.\r\n */\r\nexport async function getFilteredProductVariants(\r\n  supabase: SupabaseClient,\r\n  productId: string,\r\n  options: {\r\n    includeUnavailable?: boolean;\r\n    sortBy?: \"created_asc\" | \"created_desc\" | \"name_asc\" | \"name_desc\" | \"price_asc\" | \"price_desc\";\r\n    limit?: number;\r\n    offset?: number;\r\n  } = {}\r\n) {\r\n  try {\r\n    let query = supabase\r\n      .from(TABLES.PRODUCT_VARIANTS)\r\n      .select(\"*\", { count: \"exact\" })\r\n      .eq(COLUMNS.PRODUCT_ID, productId);\r\n\r\n    if (!options.includeUnavailable) {\r\n      query = query.eq(COLUMNS.IS_AVAILABLE, true);\r\n    }\r\n\r\n    switch (options.sortBy) {\r\n      case \"created_asc\":\r\n        query = query.order(COLUMNS.CREATED_AT, { ascending: true });\r\n        break;\r\n      case \"created_desc\":\r\n        query = query.order(COLUMNS.CREATED_AT, { ascending: false });\r\n        break;\r\n      case \"name_asc\":\r\n        query = query.order(COLUMNS.VARIANT_NAME, { ascending: true });\r\n        break;\r\n      case \"name_desc\":\r\n        query = query.order(COLUMNS.VARIANT_NAME, { ascending: false });\r\n        break;\r\n      case \"price_asc\":\r\n        query = query.order(COLUMNS.BASE_PRICE, { ascending: true, nullsFirst: false });\r\n        break;\r\n      case \"price_desc\":\r\n        query = query.order(COLUMNS.BASE_PRICE, { ascending: false, nullsFirst: true });\r\n        break;\r\n      default:\r\n        query = query.order(COLUMNS.CREATED_AT, { ascending: false });\r\n    }\r\n\r\n    if (options.limit) {\r\n      query = query.limit(options.limit);\r\n    }\r\n    if (options.offset) {\r\n      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);\r\n    }\r\n\r\n    const { data, error: queryError, count } = await query;\r\n\r\n    if (queryError) {\r\n      console.error(`Error fetching filtered product variants: ${queryError.message}`);\r\n      return { data: null, error: queryError.message, count: null };\r\n    }\r\n\r\n    return { data, error: null, count };\r\n  } catch (error) {\r\n    console.error(`Unexpected error in getFilteredProductVariants: ${error}`);\r\n    return { data: null, error: \"An unexpected error occurred.\", count: null };\r\n  }\r\n}\r\n\r\n/**\r\n * Calls the 'get_business_variant_stats' RPC function.\r\n * @param businessId The ID of the business.\r\n * @returns An object containing the variant statistics or an error.\r\n */\r\nexport async function getRpcBusinessVariantStats(supabase: SupabaseClient, businessId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .rpc(RPC_FUNCTIONS.GET_BUSINESS_VARIANT_STATS, { business_uuid: businessId });\r\n\r\n    if (error) {\r\n      console.error(`Error calling get_business_variant_stats: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error in getRpcBusinessVariantStats: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Calls the 'is_variant_combination_unique' RPC function.\r\n * @param productId The ID of the product.\r\n * @param variantValues The variant values to check.\r\n * @param excludeVariantId Optional. The ID of the variant to exclude from the check.\r\n * @returns An object indicating whether the variant combination is unique or an error.\r\n */\r\nexport async function getRpcIsVariantCombinationUnique(\r\n  supabase: SupabaseClient,\r\n  productId: string,\r\n  variantValues: Json,\r\n  excludeVariantId?: string\r\n) {\r\n  try {\r\n    const { data: result, error: functionError } = await supabase\r\n      .rpc(RPC_FUNCTIONS.IS_VARIANT_COMBINATION_UNIQUE, {\r\n        product_uuid: productId,\r\n        variant_vals: variantValues,\r\n        exclude_variant_id: excludeVariantId || undefined\r\n      });\r\n\r\n    if (functionError) {\r\n      console.error(`Error calling is_variant_combination_unique: ${functionError.message}`);\r\n      return { isUnique: undefined, error: functionError.message };\r\n    }\r\n    return { isUnique: result || undefined, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error in getRpcIsVariantCombinationUnique: ${err}`);\r\n    return { isUnique: undefined, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Fetches products with variant information for a given business.\r\n * @param userId The ID of the business user.\r\n * @param page The page number.\r\n * @param limit The number of items per page.\r\n * @param filters Product filters.\r\n * @param sortBy Product sorting criteria.\r\n * @returns An object containing the products data and count, or an error.\r\n */\r\nexport async function getProductsWithVariantInfo(\r\n  supabase: SupabaseClient,\r\n  userId: string,\r\n  page: number = 1,\r\n  limit: number = 10,\r\n  filters: {\r\n    searchTerm?: string;\r\n    hasVariants?: boolean;\r\n    productType?: string;\r\n  } = {},\r\n  sortBy: \"created_asc\" | \"created_desc\" | \"price_asc\" | \"price_desc\" | \"name_asc\" | \"name_desc\" | \"available_first\" | \"unavailable_first\" | \"variant_count_asc\" | \"variant_count_desc\" = \"created_desc\"\r\n) {\r\n  const offset = (page - 1) * limit;\r\n\r\n  let query = supabase\r\n    .from(TABLES.PRODUCTS_SERVICES)\r\n    .select(\r\n      `\r\n      ${COLUMNS.ID},\r\n      ${COLUMNS.BUSINESS_ID},\r\n      ${COLUMNS.PRODUCT_TYPE},\r\n      ${COLUMNS.NAME},\r\n      ${COLUMNS.DESCRIPTION},\r\n      ${COLUMNS.BASE_PRICE},\r\n      ${COLUMNS.DISCOUNTED_PRICE},\r\n      ${COLUMNS.IS_AVAILABLE},\r\n      ${COLUMNS.IMAGE_URL},\r\n      ${COLUMNS.IMAGES},\r\n      ${COLUMNS.FEATURED_IMAGE_INDEX},\r\n      ${COLUMNS.CREATED_AT},\r\n      ${COLUMNS.UPDATED_AT},\r\n      ${COLUMNS.SLUG},\r\n      ${TABLES.PRODUCT_VARIANTS}(${COLUMNS.ID}, ${COLUMNS.IS_AVAILABLE})\r\n    `,\r\n      { count: \"exact\" }\r\n    )\r\n    .eq(COLUMNS.BUSINESS_ID, userId);\r\n\r\n  // Apply Filters\r\n  if (filters.searchTerm)\r\n    query = query.or(\r\n      `${COLUMNS.NAME}.ilike.%${filters.searchTerm}%,${COLUMNS.DESCRIPTION}.ilike.%${filters.searchTerm}%`\r\n    );\r\n  if (filters.hasVariants !== undefined) {\r\n    if (filters.hasVariants) {\r\n      // Only products that have variants\r\n      query = query.not(TABLES.PRODUCT_VARIANTS, \"is\", null);\r\n    } else {\r\n      // Only products that don't have variants\r\n      query = query.is(TABLES.PRODUCT_VARIANTS, null);\r\n    }\r\n  }\r\n  if (filters.productType)\r\n    query = query.eq(COLUMNS.PRODUCT_TYPE, filters.productType);\r\n\r\n  // Apply Sorting\r\n  switch (sortBy) {\r\n    case \"created_asc\":\r\n      query = query.order(COLUMNS.CREATED_AT, { ascending: true });\r\n      break;\r\n    case \"price_asc\":\r\n      query = query\r\n        .order(COLUMNS.DISCOUNTED_PRICE, { ascending: true, nullsFirst: false })\r\n        .order(COLUMNS.BASE_PRICE, { ascending: true, nullsFirst: false });\r\n      break;\r\n    case \"price_desc\":\r\n      query = query\r\n        .order(COLUMNS.DISCOUNTED_PRICE, { ascending: false, nullsFirst: false })\r\n        .order(COLUMNS.BASE_PRICE, { ascending: false, nullsFirst: false });\r\n      break;\r\n    case \"name_asc\":\r\n      query = query.order(COLUMNS.NAME, { ascending: true });\r\n      break;\r\n    case \"name_desc\":\r\n      query = query.order(COLUMNS.NAME, { ascending: false });\r\n      break;\r\n    case \"available_first\":\r\n      query = query\r\n        .order(COLUMNS.IS_AVAILABLE, { ascending: false })\r\n        .order(COLUMNS.CREATED_AT, { ascending: false });\r\n      break;\r\n    case \"unavailable_first\":\r\n      query = query\r\n        .order(COLUMNS.IS_AVAILABLE, { ascending: true })\r\n        .order(COLUMNS.CREATED_AT, { ascending: false });\r\n      break;\r\n    case \"created_desc\":\r\n    case \"variant_count_asc\": // Handled in post-processing\r\n    case \"variant_count_desc\": // Handled in post-processing\r\n    default:\r\n      query = query.order(COLUMNS.CREATED_AT, { ascending: false });\r\n      break;\r\n  }\r\n\r\n  query = query.range(offset, offset + limit - 1);\r\n  const { data, error, count } = await query;\r\n\r\n  if (error) {\r\n    console.error(\"Fetch Products Error:\", error);\r\n    return { data: null, error: error.message, count: null };\r\n  }\r\n\r\n  return { data, count, error: null };\r\n}\r\n\r\n/**\r\n * Fetches details of a product variant by its ID, including associated product's business ID.\r\n * @param variantId The ID of the product variant.\r\n * @returns An object containing the variant data or an error.\r\n */\r\nexport async function getVariantDetailsWithProductBusinessId(supabase: SupabaseClient, variantId: string): Promise<{ data: (Tables<'product_variants'> & { products_services: Pick<Tables<'products_services'>, 'business_id'> | null }) | null; error: string | null }> {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.PRODUCT_VARIANTS)\r\n      .select(`\r\n        *,\r\n        products_services!inner(business_id)\r\n      `)\r\n      .eq(COLUMNS.ID, variantId)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching variant details by ID: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching variant details by ID: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Updates a product variant's data.\r\n * @param variantId The ID of the variant to update.\r\n * @param updateData The data to update.\r\n * @returns An object indicating success or an error.\r\n */\r\nexport async function updateProductVariantData(supabase: SupabaseClient, variantId: string, updateData: TablesUpdate<'product_variants'>) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.PRODUCT_VARIANTS)\r\n      .update(updateData)\r\n      .eq(COLUMNS.ID, variantId)\r\n      .select()\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(`Error updating product variant data: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error updating product variant data: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Performs a bulk update on product variants.\r\n * @param updates An array of objects containing variant ID and data to update.\r\n * @returns An object indicating success or an error.\r\n */\r\nexport async function bulkUpdateProductVariantsData(supabase: SupabaseClient, updates: { id: string; data: TablesUpdate<'product_variants'> }[]) {\r\n  try {\r\n    const updatePromises = updates.map(update =>\r\n      supabase\r\n        .from(TABLES.PRODUCT_VARIANTS)\r\n        .update(update.data)\r\n        .eq(COLUMNS.ID, update.id)\r\n    );\r\n\r\n    const results = await Promise.all(updatePromises);\r\n    const errors = results.filter(result => result.error).map(result => result.error?.message);\r\n\r\n    if (errors.length > 0) {\r\n      console.error(`Errors during bulk update of product variants: ${errors.join(\", \")}`);\r\n      return { success: false, error: `Failed to update some variants: ${errors.join(\", \")}` };\r\n    }\r\n    return { success: true, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error during bulk update of product variants: ${err}`);\r\n    return { success: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n\r\ntype ProductVariantWithBusinessId = Tables<'product_variants'> & { products_services: Pick<Tables<'products_services'>, 'business_id'>; };\r\n\r\n/**\r\n * Fetches all product variants for a given product ID.\r\n * @param productId The ID of the product.\r\n * @returns An object containing the product variants data or an error.\r\n */\r\nexport async function getVariantsByProductId(supabase: SupabaseClient, productId: string): Promise<{ data: ProductVariantWithBusinessId[] | null; error: string | null }> {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.PRODUCT_VARIANTS)\r\n      .select(`${COLUMNS.ID}, ${COLUMNS.IS_AVAILABLE}, ${COLUMNS.VARIANT_NAME}, ${COLUMNS.IMAGES}, products_services!inner(${COLUMNS.BUSINESS_ID}))`)\r\n      .eq(COLUMNS.PRODUCT_ID, productId);\r\n\r\n    if (error) {\r\n      console.error(`Error fetching variants by product ID: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    // Explicitly map the data to the correct type\r\n    const typedData: ProductVariantWithBusinessId[] = (data as unknown as (Tables<'product_variants'> & { products_services: Pick<Tables<'products_services'>, 'business_id'>; })[]).map(item => ({\r\n      ...item,\r\n      products_services: item.products_services as Pick<Tables<'products_services'>, 'business_id'>,\r\n    }));\r\n    return { data: typedData, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching variants by product ID: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n\r\n/**\r\n * Deletes a product variant by its ID.\r\n * @param variantId The ID of the product variant to delete.\r\n * @returns An object indicating success or an error.\r\n */\r\n/**\r\n * Fetches a business profile with all relevant details for onboarding pre-fill.\r\n * @param supabase The Supabase client.\r\n * @param userId The ID of the user to fetch the profile for.\r\n * @returns An object containing the business profile data or an error.\r\n */\r\nexport async function getBusinessProfileForOnboarding(supabase: SupabaseClient, userId: string) {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(TABLES.BUSINESS_PROFILES)\r\n      .select(`\r\n        ${COLUMNS.BUSINESS_NAME},\r\n        ${COLUMNS.CONTACT_EMAIL},\r\n        ${COLUMNS.MEMBER_NAME},\r\n        ${COLUMNS.TITLE},\r\n        ${COLUMNS.PHONE},\r\n        ${COLUMNS.BUSINESS_CATEGORY},\r\n        ${COLUMNS.BUSINESS_SLUG},\r\n        ${COLUMNS.ADDRESS_LINE},\r\n        ${COLUMNS.PINCODE},\r\n        ${COLUMNS.CITY},\r\n        ${COLUMNS.STATE},\r\n        ${COLUMNS.LOCALITY},\r\n        ${COLUMNS.STATUS}\r\n      `)\r\n      .eq(COLUMNS.ID, userId)\r\n      .maybeSingle();\r\n\r\n    if (error) {\r\n      console.error(`Error fetching business profile for onboarding: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error fetching business profile for onboarding: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Creates a business profile and subscription atomically using a PostgreSQL RPC function.\r\n * @param supabase The Supabase client.\r\n * @param businessData The business profile data to insert.\r\n * @param subscriptionData The subscription data to insert.\r\n * @returns An object containing the RPC function result or an error.\r\n */\r\nexport async function createBusinessProfileAtomic(supabase: SupabaseClient, businessData: Json, subscriptionData: Json) {\r\n  try {\r\n    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.CREATE_BUSINESS_PROFILE_ATOMIC, {\r\n      p_business_data: businessData,\r\n      p_subscription_data: subscriptionData\r\n    });\r\n\r\n    if (error) {\r\n      console.error(`Error calling create_business_profile_atomic: ${error.message}`);\r\n      return { data: null, error: error.message };\r\n    }\r\n    return { data, error: null };\r\n  } catch (err) {\r\n    console.error(`Unexpected error in createBusinessProfileAtomic: ${err}`);\r\n    return { data: null, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\nexport async function deleteProductVariantById(supabase: SupabaseClient, variantId: string) {\r\n    try {\r\n        const { error } = await supabase\r\n            .from(TABLES.PRODUCT_VARIANTS)\r\n            .delete()\r\n            .eq(COLUMNS.ID, variantId);\r\n\r\n        if (error) {\r\n            console.error(`Error deleting product variant by ID: ${error.message}`);\r\n            return { success: false, error: error.message };\r\n        }\r\n        return { success: true, error: null };\r\n    } catch (err) {\r\n        console.error(`Unexpected error deleting product variant by ID: ${err}`);\r\n        return { success: false, error: \"An unexpected error occurred.\" };\r\n    }\r\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;;AAUO,eAAe,6BAA6B,QAAwB,EAAE,MAAc;IACzF,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EACjB,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,QACf,WAAW;QAEd,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,0CAA0C,EAAE,MAAM,OAAO,EAAE;YAC1E,OAAO;gBAAE,QAAQ;gBAAO,OAAO;YAA4C;QAC7E;QAEA,OAAO;YAAE,QAAQ,CAAC,CAAC;YAAM,OAAO;QAAK;IACvC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,4CAA4C,EAAE,KAAK;QAClE,OAAO;YAAE,QAAQ;YAAO,OAAO;QAAgC;IACjE;AACF;AAOO,eAAe,gCAAgC,QAAwB,EAAE,iBAAyB;IACvG,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,gBAAgB,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,cAAc,EAAE,EAC3I,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,mBACf,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,gDAAgD,EAAE,MAAM,OAAO,EAAE;YAChF,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE,MAAM;YAAqC,OAAO;QAAK;IAClE,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,2DAA2D,EAAE,KAAK;QACjF,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AASO,eAAe,yBAAyB,QAAwB,EAAE,UAAkB,EAAE,SAAiB,EAAE,OAAe;IAC7H,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,GAAG,CAAC,4HAAA,CAAA,gBAAa,CAAC,4BAA4B,EAAE;YACrF,CAAC,4HAAA,CAAA,aAAU,CAAC,WAAW,CAAC,EAAE;YAC1B,CAAC,4HAAA,CAAA,aAAU,CAAC,UAAU,CAAC,EAAE;YACzB,CAAC,4HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,EAAE;QACzB;QACA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,yCAAyC,EAAE,MAAM,OAAO,EAAE;YACzE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,oDAAoD,EAAE,KAAK;QAC1E,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAQO,eAAe,0BAA0B,QAAwB,EAAE,UAAkB,EAAE,UAAkB;IAC9G,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,GAAG,CAAC,4HAAA,CAAA,gBAAa,CAAC,6BAA6B,EAAE;YACtF,CAAC,4HAAA,CAAA,aAAU,CAAC,WAAW,CAAC,EAAE;YAC1B,CAAC,4HAAA,CAAA,aAAU,CAAC,WAAW,CAAC,EAAE;QAC5B;QACA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,0CAA0C,EAAE,MAAM,OAAO,EAAE;YAC1E,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,qDAAqD,EAAE,KAAK;QAC3E,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AASO,eAAe,uBAAuB,QAAwB,EAAE,UAAkB,EAAE,UAAkB,EAAE,WAAmB;IAChI,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,GAAG,CAAC,4HAAA,CAAA,gBAAa,CAAC,yBAAyB,EAAE;YAClF,CAAC,4HAAA,CAAA,aAAU,CAAC,WAAW,CAAC,EAAE;YAC1B,CAAC,4HAAA,CAAA,aAAU,CAAC,WAAW,CAAC,EAAE;YAC1B,CAAC,4HAAA,CAAA,aAAU,CAAC,YAAY,CAAC,EAAE;QAC7B;QACA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,sCAAsC,EAAE,MAAM,OAAO,EAAE;YACtE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,iDAAiD,EAAE,KAAK;QACvE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAWO,eAAe,2BAA2B,QAAwB,EAAE,UAAkB,EAAE,SAAiB,EAAE,UAAkB,EAAE,OAAe,EAAE,QAAgB;IACrK,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,GAAG,CAAC,4HAAA,CAAA,gBAAa,CAAC,8BAA8B,EAAE;YACvF,CAAC,4HAAA,CAAA,aAAU,CAAC,WAAW,CAAC,EAAE;YAC1B,CAAC,4HAAA,CAAA,aAAU,CAAC,UAAU,CAAC,EAAE;YACzB,CAAC,4HAAA,CAAA,aAAU,CAAC,WAAW,CAAC,EAAE;YAC1B,CAAC,4HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,EAAE;YACvB,CAAC,4HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,EAAE;QAC1B;QACA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,MAAM,OAAO,EAAE;YAC3E,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,sDAAsD,EAAE,KAAK;QAC5E,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAOO,eAAe,mCAAmC,QAAwB,EAAE,UAAkB;IACnG,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,GAAG,CAAC,4HAAA,CAAA,gBAAa,CAAC,uCAAuC,EAAE;YAChG,CAAC,4HAAA,CAAA,aAAU,CAAC,WAAW,CAAC,EAAE;QAC5B;QACA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,oDAAoD,EAAE,MAAM,OAAO,EAAE;YACpF,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,+DAA+D,EAAE,KAAK;QACrF,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAOO,eAAe,qBAAqB,QAAwB,EAAE,UAAkB;IACrF,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,GAAG,CAAC,4HAAA,CAAA,gBAAa,CAAC,uBAAuB,EAAE;YAChF,CAAC,4HAAA,CAAA,aAAU,CAAC,WAAW,CAAC,EAAE;QAC5B;QACA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,MAAM,OAAO,EAAE;YACpE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,+CAA+C,EAAE,KAAK;QACrE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAOO,eAAe,yCAAyC,QAAwB,EAAE,MAAc;IACrG,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,mBAAmB,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,cAAc,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,gBAAgB,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,cAAc,EAAE,EAC9N,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,QACf,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,0DAA0D,EAAE,MAAM,OAAO,EAAE;YAC1F,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,qEAAqE,EAAE,KAAK;QAC3F,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAOO,eAAe,sBAAsB,QAAwB,EAAE,MAAc;IAClF,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,qBAAqB,EACjC,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EACtB,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,QAChC,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;YAAE,WAAW;QAAM,GAC7C,KAAK,CAAC,GACN,WAAW;QAEd,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,MAAM,OAAO,EAAE;YACpE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,+CAA+C,EAAE,KAAK;QACrE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAOO,eAAe,4BAA4B,QAAwB,EAAE,MAAc;IACxF,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EACzB,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,QACf,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,4CAA4C,EAAE,MAAM,OAAO,EAAE;YAC5E,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE,MAAM,KAAK,UAAU;YAAE,OAAO;QAAK;IAC9C,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,uDAAuD,EAAE,KAAK;QAC7E,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAQO,eAAe,sBAAsB,QAAwB,EAAE,MAAc,EAAE,OAA0C;IAC9H,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,SACP,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,QACf,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,MAAM,OAAO,EAAE;YACjE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,4CAA4C,EAAE,KAAK;QAClE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAOO,eAAe,iCAAiC,QAAwB,EAAE,MAAc;IAC7F,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,eAAe,EAC9B,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,QACf,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,iDAAiD,EAAE,MAAM,OAAO,EAAE;YACjF,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE,MAAM,KAAK,eAAe;YAAoD,OAAO;QAAK;IACrG,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,4DAA4D,EAAE,KAAK;QAClF,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAOO,eAAe,wBAAwB,QAAwB,EAAE,MAAc;IACpF,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,KAAK,EACpB,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,QACf,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,uCAAuC,EAAE,MAAM,OAAO,EAAE;YACvE,OAAO;gBAAE,OAAO;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC7C;QACA,OAAO;YAAE,OAAO,KAAK,KAAK;YAAE,OAAO;QAAK;IAC1C,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,kDAAkD,EAAE,KAAK;QACxE,OAAO;YAAE,OAAO;YAAM,OAAO;QAAgC;IAC/D;AACF;AAOO,eAAe,iCAAiC,QAAwB,EAAE,MAAc;IAC7F,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CACL,CAAC;QACD,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,uBAAuB,CAAC;QACrG,EAAE,4HAAA,CAAA,UAAO,CAAC,cAAc,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QACtI,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,KAAK,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,KAAK,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC;QAC/I,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,eAAe,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,SAAS,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC;QACtH,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,mBAAmB,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,cAAc,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,cAAc,CAAC;QAC9J,EAAE,4HAAA,CAAA,UAAO,CAAC,iBAAiB,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,eAAe,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,gBAAgB,CAAC;MAC9G,CAAC,EAEA,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,QACf,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,kDAAkD,EAAE,MAAM,OAAO,EAAE;YAClF,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,6DAA6D,EAAE,KAAK;QACnF,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAOO,eAAe,4BAA4B,QAAwB,EAAE,MAAc;IACxF,IAAI;QACF,MAAM,EAAE,MAAM,YAAY,EAAE,KAAK,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,qBAAqB,EACjC,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,OAAO,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,EAC3D,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,QAChC,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;YAAE,WAAW;QAAM,GAC7C,KAAK,CAAC,GACN,WAAW;QAEd,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,MAAM,OAAO,EAAE;YAC3E,OAAO;gBAAE,oBAAoB;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC1D;QAEA,OAAO;YAAE,oBAAoB;YAAc,OAAO;QAAK;IACzD,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,sDAAsD,EAAE,KAAK;QAC5E,OAAO;YAAE,oBAAoB;YAAM,OAAO;QAAgC;IAC5E;AACF;AAQO,eAAe,+BAA+B,QAAwB,EAAE,IAAY;IACzF,IAAI;QACF,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACtD,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CACL,CAAC;;QAED,EAAE,4HAAA,CAAA,SAAM,CAAC,qBAAqB,CAAC,CAAC,EAAE,4HAAA,CAAA,UAAO,CAAC,mBAAmB,CAAC;UAC5D,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,CAAC;UAClB,EAAE,4HAAA,CAAA,UAAO,CAAC,mBAAmB,CAAC;;MAElC,CAAC,EAEA,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE,MAC1B,WAAW;QAEd,IAAI,cAAc;YAChB,QAAQ,KAAK,CAAC,CAAC,gDAAgD,EAAE,aAAa,OAAO,EAAE;YACvF,OAAO;gBAAE,MAAM;gBAAM,OAAO,aAAa,OAAO;YAAC;QACnD;QAEA,IAAI,CAAC,aAAa;YAChB,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAqB;QACnD;QAEA,MAAM,WAAW;YACf,GAAG,WAAW;YACd,qBACE,AAAC,WAAW,CAAC,4HAAA,CAAA,SAAM,CAAC,qBAAqB,CAAC,EAAkD,uBAAuB;YACrH,SAAS,AAAC,WAAW,CAAC,4HAAA,CAAA,SAAM,CAAC,qBAAqB,CAAC,EAAkD,WAAW;QAClH;QAEA,OAAO;YAAE,MAAM;YAAU,OAAO;QAAK;IACvC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,2DAA2D,EAAE,KAAK;QACjF,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAQO,eAAe,2CAA2C,QAAwB,EAAE,IAAY;IACrG,IAAI;QACF,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACtD,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CACL,CAAC;;QAED,EAAE,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC;UACzB,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,gBAAgB,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,SAAS,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC;;MAEpO,CAAC,EAEA,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE,MAC1B,WAAW;QAEd,IAAI,cAAc;YAChB,QAAQ,KAAK,CAAC,CAAC,8DAA8D,EAAE,aAAa,OAAO,EAAE;YACrG,OAAO;gBAAE,MAAM;gBAAM,OAAO,aAAa,OAAO;YAAC;QACnD;QAEA,IAAI,CAAC,aAAa;YAChB,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAqB;QACnD;QAEA,MAAM,WAAW;YACf,GAAG,WAAW;YACd,mBAAmB,AAAC,WAAW,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,IAAsC,EAAE;QACnG;QAEA,OAAO;YAAE,MAAM;YAAU,OAAO;QAAK;IACvC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,yEAAyE,EAAE,KAAK;QAC/F,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAOO,eAAe,oBAAoB,QAAwB,EAAE,OAAe;IACjF,IAAI;QACF,kFAAkF;QAClF,MAAM,EAAE,KAAK,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,SAC7C,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK;QAE5C,IAAI,iBAAiB;YACnB,QAAQ,KAAK,CAAC,CAAC,wCAAwC,EAAE,gBAAgB,OAAO,EAAE;YAClF,gDAAgD;YAChD,OAAO;gBAAE,QAAQ;gBAAM,OAAO,gBAAgB,OAAO;YAAC;QACxD;QAEA,qDAAqD;QACrD,IAAI,UAAU,MAAM;YAClB,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,OAAO,EAAE,GAAG,MAAM,SAAS,GAAG,CACzD,4HAAA,CAAA,gBAAa,CAAC,kBAAkB,EAChC;gBAAE,CAAC,4HAAA,CAAA,aAAU,CAAC,cAAc,CAAC,EAAE;YAAQ;YAGzC,IAAI,SAAS;gBACX,QAAQ,KAAK,CAAC,CAAC,8BAA8B,EAAE,QAAQ,EAAE,EAAE,QAAQ,OAAO,EAAE;gBAC5E,OAAO;oBAAE,QAAQ;oBAAM,OAAO,QAAQ,OAAO;gBAAC;YAChD;YACA,OAAO;gBAAE,QAAQ,UAAU,OAAO,MAAM,GAAG,IAAI,MAAM,CAAC,EAAE,GAAG;gBAAM,OAAO;YAAK;QAC/E,OAAO;YACL,gEAAgE;YAChE,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,UAAU,EACtB,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,EAAE,EACxD,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,SAAS,EAAE,MACtB,EAAE,CACD,CAAC,4DAA4D,EAAE,QAAQ,GAAG,CAAC,EAE5E,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;gBAAE,WAAW;YAAM,GAC7C,KAAK,CAAC,GACN,WAAW;YAEd,IAAI,eAAe;gBACjB,QAAQ,KAAK,CAAC,CAAC,qCAAqC,EAAE,cAAc,OAAO,EAAE;gBAC7E,OAAO;oBAAE,QAAQ;oBAAM,OAAO,cAAc,OAAO;gBAAC;YACtD;YACA,OAAO;gBAAE,QAAQ;gBAAU,OAAO;YAAK;QACzC;IACF,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,+CAA+C,EAAE,KAAK;QACrE,OAAO;YAAE,QAAQ;YAAM,OAAO;QAAgC;IAChE;AACF;AAQO,eAAe,uBAAuB,QAAwB,EAAE,UAAkB,EAAE,KAAa;IACtG,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,KAAK;YAAE,OAAO;QAAQ,GAC7B,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,WAAW,EAAE,YACxB,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,YAAY,EAAE,MACzB,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;YAAE,WAAW;QAAM,GAC7C,KAAK,CAAC;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,qCAAqC,EAAE,WAAW,EAAE,EAAE,MAAM,OAAO,EAAE;YACpF,OAAO;gBAAE,UAAU;gBAAM,OAAO;gBAAG,OAAO,MAAM,OAAO;YAAC;QAC1D;QACA,OAAO;YAAE,UAAU;YAAM,OAAO,SAAS;YAAG,OAAO;QAAK;IAC1D,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,iDAAiD,EAAE,KAAK;QACvE,OAAO;YAAE,UAAU;YAAM,OAAO;YAAG,OAAO;QAAgC;IAC5E;AACF;AAOO,eAAe,2BAA2B,QAAwB,EAAE,UAAkB;IAC3F,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,SAC5B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,eAAe,EAC3B,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;YAAE,OAAO;YAAS,MAAM;QAAK,GAChD,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,YAChC,GAAG,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE,aAAa,2BAA2B;QAEhE,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,0CAA0C,EAAE,WAAW,EAAE,EAAE,MAAM,OAAO,EAAE;YACzF,OAAO;gBAAE,OAAO;gBAAG,OAAO,MAAM,OAAO;YAAC;QAC1C;QACA,OAAO;YAAE,OAAO,SAAS;YAAG,OAAO;QAAK;IAC1C,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,sDAAsD,EAAE,KAAK;QAC5E,OAAO;YAAE,OAAO;YAAG,OAAO;QAAgC;IAC5D;AACF;AAOO,eAAe,0BAA0B,QAAwB,EAAE,UAAkB;IAC1F,IAAI;QACF,MAAM,EAAE,MAAM,WAAW,EAAE,KAAK,EAAE,GAAG,MAAM,SACxC,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EACtB,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,YACf,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,yCAAyC,EAAE,MAAM,OAAO,EAAE;YACzE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE,MAAM,YAAY,OAAO;QAAC;IACrC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,oDAAoD,EAAE,KAAK;QAC1E,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAOO,eAAe,oCAAoC,QAAwB,EAAE,YAAoB;IACtG,IAAI;QACF,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,SACrC,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,MAAM,EAAE,EACzC,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE,cAC1B,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,+CAA+C,EAAE,MAAM,OAAO,EAAE;YAC/E,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE,MAAM;YAAU,OAAO;QAAK;IACvC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,0DAA0D,EAAE,KAAK;QAChF,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAOO,eAAe,4BAA4B,QAAwB,EAAE,iBAAyB;IACnG,IAAI;QACF,MAAM,EAAE,MAAM,YAAY,EAAE,KAAK,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,0BAA0B,EACtC,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EACtB,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,mBAChC,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;YAAE,WAAW;QAAM,GAC7C,KAAK,CAAC,GACN,WAAW;QAEd,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,MAAM,OAAO,EAAE;YAC3E,OAAO;gBAAE,QAAQ;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC9C;QACA,OAAO;YAAE,QAAQ,cAAc,WAAW;YAAM,OAAO;QAAK;IAC9D,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,sDAAsD,EAAE,KAAK;QAC5E,OAAO;YAAE,QAAQ;YAAM,OAAO;QAAgC;IAChE;AACF;AAYO,eAAe,oCACpB,QAAwB,EACxB,UAAkB,EAClB,OAAe,CAAC,EAChB,SAAiB,cAAc,EAC/B,WAAmB,EAAE,EACrB,UAA0B,EAC1B,WAA2B;IAE3B,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;IAE5B,IAAI,QAAQ,SACT,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CACL,CAAC;MACD,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;MACb,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC;MACtB,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC;MACf,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC;MACtB,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC;MACrB,EAAE,4HAAA,CAAA,UAAO,CAAC,gBAAgB,CAAC;MAC3B,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC;MACvB,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC;MACvB,EAAE,4HAAA,CAAA,UAAO,CAAC,SAAS,CAAC;MACpB,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC;MACrB,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC;MACrB,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC;IACjB,CAAC,EACC;QAAE,OAAO;IAAQ,GAElB,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,WAAW,EAAE,YACxB,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,YAAY,EAAE;IAE5B,IAAI,cAAc,WAAW,IAAI,GAAG,MAAM,GAAG,GAAG;QAC9C,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,WAAW,IAAI,GAAG,CAAC,CAAC;IAC5D;IAEA,IAAI,eAAe,gBAAgB,OAAO;QACxC,QAAQ,MAAM,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,YAAY,EAAE;IACzC;IAEA,OAAQ;QACN,KAAK;YACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;gBAAE,WAAW;YAAK;YAC1D;QACF,KAAK;YACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;gBAAE,WAAW;YAAM;YAC3D;QACF,KAAK;YACH,QAAQ,MACL,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,gBAAgB,EAAE;gBAAE,WAAW;gBAAM,YAAY;YAAM,GACrE,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;gBAAE,WAAW;gBAAM,YAAY;YAAM;YAClE;QACF,KAAK;YACH,QAAQ,MACL,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,gBAAgB,EAAE;gBAAE,WAAW;gBAAO,YAAY;YAAM,GACtE,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;gBAAE,WAAW;gBAAO,YAAY;YAAM;YACnE;QACF,KAAK;YACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,IAAI,EAAE;gBAAE,WAAW;YAAK;YACpD;QACF,KAAK;YACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,IAAI,EAAE;gBAAE,WAAW;YAAM;YACrD;QACF,KAAK;QACL;YACE,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;gBAAE,WAAW;YAAM;YAC3D;IACJ;IAEA,QAAQ,MAAM,KAAK,CAAC,QAAQ,SAAS,WAAW;IAEhD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;IAErC,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,MAAM,OAAO,EAAE;QACzD,OAAO;YAAE,MAAM;YAAM,OAAO,MAAM,OAAO;YAAE,YAAY;QAAE;IAC3D;IAEA,OAAO;QAAE,MAAM;QAAuC,OAAO;QAAM,YAAY,SAAS;IAAE;AAC5F;AAOO,eAAe,yBAAyB,QAAwB,EAAE,iBAAyB;IAChG,IAAI;QACF,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,MAAM,EACrB,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,mBACf,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,wCAAwC,EAAE,MAAM,OAAO,EAAE;YACxE,OAAO;gBAAE,QAAQ;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC9C;QACA,OAAO;YAAE,QAAQ,QAAQ,MAAM;YAAE,OAAO;QAAK;IAC/C,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,mDAAmD,EAAE,KAAK;QACzE,OAAO;YAAE,QAAQ;YAAM,OAAO;QAAgC;IAChE;AACF;AAOO,eAAe,gBAAgB,QAAwB,EAAE,SAAsC;IACpG,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,WAAW,EAAE,MAAM,CAAC;YAAC;SAAU;QAE5E,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,MAAM,OAAO,EAAE;YAC5D,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QAChD;QACA,OAAO;YAAE,SAAS;YAAM,OAAO;QAAK;IACtC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,uCAAuC,EAAE,KAAK;QAC7D,OAAO;YAAE,SAAS;YAAO,OAAO;QAAgC;IAClE;AACF;AAQO,eAAe,sBACpB,QAAwB,EACxB,MAAc,EACd,OAAsB;IAEtB,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC;QAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,EAAE;QAAsC,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,IAAI,OAAO,WAAW;IAAG,GAClH,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;IAElB,IAAI,aAAa;QACf,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YACL,SAAS;YACT,OAAO,CAAC,oCAAoC,EAAE,YAAY,OAAO,EAAE;QACrE;IACF;IACA,OAAO;QAAE,SAAS;IAAK;AACzB;AAOO,eAAe,mBACpB,QAAwB,EACxB,MAAc;IAEd,MAAM,EAAE,IAAI,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACvC,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,QAAQ,EACvB,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,QACf,MAAM;IAET,IAAI,YAAY;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;YAAE,SAAS;YAAM,OAAO;QAAqC;IACtE;IAEA,OAAO;QAAE,SAAS,MAAM,YAAY;IAAK;AAC3C;AAQO,eAAe,4BAA4B,QAAwB,EAAE,IAAY,EAAE,gBAA+B,IAAI;IAC3H,IAAI;QACF,IAAI,QAAQ,SACT,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EACjB,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE;QAEhC,IAAI,eAAe;YACjB,QAAQ,MAAM,GAAG,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;QAChC;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,WAAW;QAE/C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,yCAAyC,EAAE,MAAM,OAAO,EAAE;YACzE,OAAO;gBAAE,WAAW;gBAAO,OAAO,MAAM,OAAO;YAAC;QAClD;QAEA,OAAO;YAAE,WAAW,CAAC;YAAM,OAAO;QAAK;IACzC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,oDAAoD,EAAE,KAAK;QAC1E,OAAO;YAAE,WAAW;YAAO,OAAO;QAAgC;IACpE;AACF;AAaO,eAAe,0BACpB,QAAwB,EACxB,IAAY,EACZ,MAAc,EACd,QAAuB,EACvB,IAAY,EACZ,KAAa,EACb,MAAc,EACd,SAAkB;IAElB,IAAI;QACF,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;QAE5B,IAAI,QAAQ,SACT,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CACL,CAAC;QACD,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QACtG,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,KAAK,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,KAAK,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,iBAAiB,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC;QAC7K,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,eAAe,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,SAAS,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC;QAC9I,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,mBAAmB,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,cAAc,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,cAAc,CAAC;QACtI,EAAE,4HAAA,CAAA,UAAO,CAAC,cAAc,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,gBAAgB,CAAC;QAC9H,CAAC,EACD;YAAE,OAAO;QAAQ,GAElB,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,IAAI,EAAE,MACjB,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,MAAM,EAAE;QAEtB,IAAI,YAAY,SAAS,IAAI,IAAI;YAC/B,QAAQ,MAAM,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,iBAAiB,EAAE,SAAS,IAAI;QAC3D;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,MAClC,KAAK,CAAC,QAAQ,SAAS,QAAQ,GAC/B,KAAK,CAAC,QAAQ;YAAE;QAAU;QAE7B,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,0CAA0C,EAAE,MAAM,OAAO,EAAE;YAC1E,OAAO;gBAAE,MAAM;gBAAM,OAAO;gBAAM,OAAO,MAAM,OAAO;YAAC;QACzD;QAEA,OAAO;YAAE;YAAM;YAAO,OAAO;QAAK;IACpC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,qDAAqD,EAAE,KAAK;QAC3E,OAAO;YAAE,MAAM;YAAM,OAAO;YAAM,OAAO;QAAgC;IAC3E;AACF;AASO,eAAe,qBACpB,QAAwB,EACxB,IAAY,EACZ,MAAc,EACd,QAAuB;IAEvB,IAAI;QACF,IAAI,QAAQ,SACT,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EACjB,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,IAAI,EAAE,MACjB,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,MAAM,EAAE;QAEtB,IAAI,YAAY,SAAS,IAAI,IAAI;YAC/B,QAAQ,MAAM,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,iBAAiB,EAAE,SAAS,IAAI;QAC3D;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,qCAAqC,EAAE,MAAM,OAAO,EAAE;YACrE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QAEA,OAAO;YAAE,MAAM,KAAK,GAAG,CAAC,CAAC,OAAS,KAAK,EAAE;YAAG,OAAO;QAAK;IAC1D,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,gDAAgD,EAAE,KAAK;QACtE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAQO,eAAe,6BACpB,QAAwB,EACxB,WAAqB,EACrB,WAA0B;IAE1B,IAAI;QACF,IAAI,QAAQ,SACT,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;YAAE,OAAO;QAAQ,GACpC,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,WAAW,EAAE,aACxB,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,YAAY,EAAE;QAE5B,IAAI,aAAa;YACf,QAAQ,MAAM,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,YAAY,EAAE;QACzC;QAEA,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;QAE/B,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,yCAAyC,EAAE,MAAM,OAAO,EAAE;YACzE,OAAO;gBAAE,OAAO;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC7C;QAEA,OAAO;YAAE;YAAO,OAAO;QAAK;IAC9B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,oDAAoD,EAAE,KAAK;QAC1E,OAAO;YAAE,OAAO;YAAM,OAAO;QAAgC;IAC/D;AACF;AAYO,eAAe,yBACpB,QAAwB,EACxB,WAAqB,EACrB,IAAY,EACZ,KAAa,EACb,MAAc,EACd,SAAkB,EAClB,WAA0B;IAE1B,IAAI;QACF,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAC1B,MAAM,KAAK,OAAO,QAAQ;QAE1B,IAAI,QAAQ,SACT,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CACL,CAAC;QACD,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,gBAAgB,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC;QAC3J,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,SAAS,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC;0BAC1F,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC,CAAC,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC;QACjE,CAAC,EAEF,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,WAAW,EAAE,aACxB,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,YAAY,EAAE;QAE5B,IAAI,aAAa;YACf,QAAQ,MAAM,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,YAAY,EAAE;QACzC;QAEA,8CAA8C;QAC9C,IAAI,WAAW,4HAAA,CAAA,UAAO,CAAC,gBAAgB,EAAE;YACvC,QAAQ,MACL,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,gBAAgB,EAAE;gBAAE;gBAAW,YAAY;YAAM,GAC/D,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;gBAAE;gBAAW,YAAY;YAAM;QAC9D,OAAO;YACL,QAAQ,MAAM,KAAK,CAAC,QAAQ;gBAAE;YAAU;QAC1C;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,KAAK,CAAC,MAAM;QAEhD,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,yCAAyC,EAAE,MAAM,OAAO,EAAE;YACzE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QAEA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,oDAAoD,EAAE,KAAK;QAC1E,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAQO,eAAe,4BAA4B,QAAwB,EAAE,SAAiB,EAAE,UAAkB;IAC/G,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,EAAE,EAC9C,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,WACf,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,WAAW,EAAE,YACxB,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,8CAA8C,EAAE,MAAM,OAAO,EAAE;YAC9E,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,yDAAyD,EAAE,KAAK;QAC/E,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAOO,eAAe,2BAA2B,QAAwB,EAAE,SAAiB;IAC1F,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,gBAAgB,EAC5B,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,cAAc,EAAE,EACjD,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;QAE1B,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,0CAA0C,EAAE,MAAM,OAAO,EAAE;YAC1E,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,qDAAqD,EAAE,KAAK;QAC3E,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAOO,eAAe,qBAAqB,QAAwB,EAAE,WAA6C;IAChH,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,gBAAgB,EAC5B,MAAM,CAAC,aACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,MAAM,OAAO,EAAE;YACjE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,4CAA4C,EAAE,KAAK;QAClE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAOO,eAAe,qBAAqB,QAAwB,EAAE,SAAiB;IACpF,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,gBAAgB,EAC5B,MAAM,GACN,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;QAElB,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,MAAM,OAAO,EAAE;YAChE,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QAChD;QACA,OAAO;YAAE,SAAS;YAAM,OAAO;QAAK;IACtC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,KAAK;QACjE,OAAO;YAAE,SAAS;YAAO,OAAO;QAAgC;IAClE;AACF;AASO,eAAe,2BAA2B,QAAwB,EAAE,SAAiB,EAAE,SAAmB,EAAE,kBAA0B;IAC3I,IAAI;QACF,MAAM,aAAa;YACjB,QAAQ;YACR,sBAAsB;QACxB;QACA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,gBAAgB,EAC5B,MAAM,CAAC,YACP,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;QAElB,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,uCAAuC,EAAE,MAAM,OAAO,EAAE;YACvE,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QAChD;QACA,OAAO;YAAE,SAAS;YAAM,OAAO;QAAK;IACtC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,kDAAkD,EAAE,KAAK;QACxE,OAAO;YAAE,SAAS;YAAO,OAAO;QAAgC;IAClE;AACF;AAOO,eAAe,8BAA8B,QAAwB,EAAE,gBAAoD;IAChI,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,gBAAgB,EAC5B,MAAM,CAAC,kBACP,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,MAAM,OAAO,EAAE;YAC3E,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,sDAAsD,EAAE,KAAK;QAC5E,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAOO,eAAe,2BAA2B,QAAwB,EAAE,MAAc;IACvF,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,SAAS,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE,EAClG,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,QACf,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,0CAA0C,EAAE,MAAM,OAAO,EAAE;YAC1E,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,qDAAqD,EAAE,KAAK;QAC3E,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAOO,eAAe,sBAAsB,QAAwB,EAAE,UAAkB;IACtF,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,SAC5B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;QAAE,OAAO;QAAS,MAAM;IAAK,GAChD,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE;IAEnC,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,wCAAwC;QACtD,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,SAAS;AAClB;AASO,eAAe,iBAAiB,QAAwB,EAAE,UAAkB,EAAE,IAAY,EAAE,KAAa;IAC9G,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;IAC1B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE,EAC1C,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,YAChC,KAAK,CAAC,MAAM,OAAO,QAAQ;IAE9B,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAQO,eAAe,gBAAgB,QAAwB,EAAE,UAAkB,EAAE,UAAmB;IACrG,IAAI,aAAa,SACd,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC,CAAC;MACP,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;MACb,EAAE,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC;QACzB,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;QACb,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC;;IAE5B,CAAC,EAAE;QAAE,OAAO;QAAS,MAAM;IAAK,GAC/B,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;IAEvB,IAAI,YAAY;QACd,aAAa,WAAW,KAAK,CAAC,GAAG,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,CAAC,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;IACzG;IAEA,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;IAE/B,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,SAAS;AAClB;AAUO,eAAe,WAAW,QAAwB,EAAE,UAAkB,EAAE,IAAY,EAAE,KAAa,EAAE,UAAmB;IAC7H,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;IAC1B,IAAI,QAAQ,SACT,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC,CAAC;MACP,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;MACb,EAAE,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC;QACzB,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;QACb,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC;QACxB,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC;QACxB,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC;QACnB,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC;QACf,EAAE,4HAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC;QACvB,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC;;IAEvB,CAAC,EACA,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;IAEvB,IAAI,YAAY;QACd,QAAQ,MAAM,KAAK,CAAC,GAAG,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,CAAC,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;IAC/F;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,KAAK,CAAC,MAAM,OAAO,QAAQ;IAE/D,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAOO,eAAe,yBAAyB,QAAwB,EAAE,OAAiB;IACxF,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,KAAK,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,EAAE,EAC/K,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;QAElB,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,yCAAyC,EAAE,MAAM,OAAO,EAAE;YACzE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,oDAAoD,EAAE,KAAK;QAC1E,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAOO,eAAe,cAAc,QAAwB,EAAE,WAA8C;IAC1G,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,aACP,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,gBAAgB,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,SAAS,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,EAAE,EAC9S,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,MAAM,OAAO,EAAE;YACzD,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,KAAK;QAC1D,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAQO,eAAe,cAAc,QAAwB,EAAE,SAAiB,EAAE,OAA0C;IACzH,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,SACP,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,WACf,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,SAAS,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,oBAAoB,EAAE,EAChG,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,MAAM,OAAO,EAAE;YACxD,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,KAAK;QACzD,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAOO,eAAe,eAAe,QAAwB,EAAE,SAAiB;IAC9E,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,gBAAgB,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,SAAS,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,EAAE,EAC9S,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,WACf,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,8BAA8B,EAAE,MAAM,OAAO,EAAE;YAC9D,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,yCAAyC,EAAE,KAAK;QAC/D,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAOO,eAAe,iCAAiC,QAAwB,EAAE,UAAoB;IACnG,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,gBAAgB,EAC5B,MAAM,CAAC,CAAC;QACP,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;QACb,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC;QACrB,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC;QACrB,EAAE,4HAAA,CAAA,UAAO,CAAC,gBAAgB,CAAC;QAC3B,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC;QACvB,EAAE,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,OAAO,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC;MAC1D,CAAC,EACA,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;QAElB,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,kDAAkD,EAAE,MAAM,OAAO,EAAE;YAClF,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,6DAA6D,EAAE,KAAK;QACnF,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAOO,eAAe,uBAAuB,QAAwB,EAAE,UAAkB;IACvF,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,EAAE,EACrE,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,YACf,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,uCAAuC,EAAE,MAAM,OAAO,EAAE;YACvE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,kDAAkD,EAAE,KAAK;QACxE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAQO,eAAe,mCAAmC,QAAwB,EAAE,UAAkB;IACnG,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,uBAAuB,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,cAAc,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,MAAM,EAAE,EACxG,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,YACf,WAAW;QAEd,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,mDAAmD,EAAE,MAAM,OAAO,EAAE;YACnF,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,8DAA8D,EAAE,KAAK;QACpF,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAQO,eAAe,0CAA0C,QAAwB,EAAE,iBAAyB;IACjH,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,qBAAqB,EACjC,MAAM,CAAC,KACP,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,mBAChC,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;YAAE,WAAW;QAAM,GAC7C,KAAK,CAAC,GACN,WAAW;QAEd,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,qCAAqC,EAAE,MAAM,OAAO,EAAE;YACrE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,gDAAgD,EAAE,KAAK;QACtE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AASO,eAAe,0BAA0B,QAAwB,EAAE,UAAoB,EAAE,UAA4C;IAC1I,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,gBAAgB,EAC5B,MAAM,CAAC,YACP,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,YACf,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE;QAEpB,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,sCAAsC,EAAE,MAAM,OAAO,EAAE;YACtE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,iDAAiD,EAAE,KAAK;QACvE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAQO,eAAe,qBAAqB,QAAwB,EAAE,SAAiB,EAAE,UAA4C;IAClI,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,gBAAgB,EAC5B,MAAM,CAAC,YACP,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;QAElB,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,MAAM,OAAO,EAAE;YAChE,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QAChD;QACA,OAAO;YAAE,SAAS;YAAM,OAAO;QAAK;IACtC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,KAAK;QACjE,OAAO;YAAE,SAAS;YAAO,OAAO;QAAgC;IAClE;AACF;AAQO,eAAe,mCAAmC,QAAwB,EAAE,SAAiB,EAAE,UAAkB;IACtH,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,EAAE,EAC3C,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,WACf,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,WAAW,EAAE,YACxB,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,sDAAsD,EAAE,MAAM,OAAO,EAAE;YACtF,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,iEAAiE,EAAE,KAAK;QACvF,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAQO,eAAe,+BAA+B,QAAwB,EAAE,SAAiB,EAAE,UAAkB;IAClH,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,GACN,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,WACf,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,WAAW,EAAE;QAE3B,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,8CAA8C,EAAE,MAAM,OAAO,EAAE;YAC9E,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QAChD;QACA,OAAO;YAAE,SAAS;YAAM,OAAO;QAAK;IACtC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,yDAAyD,EAAE,KAAK;QAC/E,OAAO;YAAE,SAAS;YAAO,OAAO;QAAgC;IAClE;AACF;AAOO,eAAe,iCAAiC,QAAwB,EAAE,SAAiB;IAChG,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,gBAAgB,EAC5B,MAAM,GACN,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;QAE1B,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,+CAA+C,EAAE,MAAM,OAAO,EAAE;YAC/E,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QAChD;QACA,OAAO;YAAE,SAAS;YAAM,OAAO;QAAK;IACtC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,0DAA0D,EAAE,KAAK;QAChF,OAAO;YAAE,SAAS;YAAO,OAAO;QAAgC;IAClE;AACF;AAOO,eAAe,0BAA0B,QAAwB,EAAE,SAAiB;IACzF,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,GAAG,CAAC,4HAAA,CAAA,gBAAa,CAAC,yBAAyB,EAAE;YAAE,cAAc;QAAU;QAE1E,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,yCAAyC,EAAE,MAAM,OAAO,EAAE;YACzE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,+CAA+C,EAAE,KAAK;QACrE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAQO,eAAe,qBAAqB,QAAwB,EAAE,SAAiB,EAAE,MAAc;IACpG,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,WAAW,EAC1B,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,WACf,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,WAAW,EAAE,QACxB,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,MAAM,OAAO,EAAE;YACpE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,+CAA+C,EAAE,KAAK;QACrE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAOO,eAAe,+BAA+B,QAAwB,EAAE,SAAiB;IAC9F,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,GAAG,CAAC,4HAAA,CAAA,gBAAa,CAAC,8BAA8B,EAAE;YAAE,cAAc;QAAU;QAE/E,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,8CAA8C,EAAE,MAAM,OAAO,EAAE;YAC9E,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,oDAAoD,EAAE,KAAK;QAC1E,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAQO,eAAe,2BACpB,QAAwB,EACxB,SAAiB,EACjB,UAKI,CAAC,CAAC;IAEN,IAAI;QACF,IAAI,QAAQ,SACT,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,gBAAgB,EAC5B,MAAM,CAAC,KAAK;YAAE,OAAO;QAAQ,GAC7B,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;QAE1B,IAAI,CAAC,QAAQ,kBAAkB,EAAE;YAC/B,QAAQ,MAAM,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,YAAY,EAAE;QACzC;QAEA,OAAQ,QAAQ,MAAM;YACpB,KAAK;gBACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;oBAAE,WAAW;gBAAK;gBAC1D;YACF,KAAK;gBACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;oBAAE,WAAW;gBAAM;gBAC3D;YACF,KAAK;gBACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,YAAY,EAAE;oBAAE,WAAW;gBAAK;gBAC5D;YACF,KAAK;gBACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,YAAY,EAAE;oBAAE,WAAW;gBAAM;gBAC7D;YACF,KAAK;gBACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;oBAAE,WAAW;oBAAM,YAAY;gBAAM;gBAC7E;YACF,KAAK;gBACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;oBAAE,WAAW;oBAAO,YAAY;gBAAK;gBAC7E;YACF;gBACE,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;oBAAE,WAAW;gBAAM;QAC/D;QAEA,IAAI,QAAQ,KAAK,EAAE;YACjB,QAAQ,MAAM,KAAK,CAAC,QAAQ,KAAK;QACnC;QACA,IAAI,QAAQ,MAAM,EAAE;YAClB,QAAQ,MAAM,KAAK,CAAC,QAAQ,MAAM,EAAE,QAAQ,MAAM,GAAG,CAAC,QAAQ,KAAK,IAAI,EAAE,IAAI;QAC/E;QAEA,MAAM,EAAE,IAAI,EAAE,OAAO,UAAU,EAAE,KAAK,EAAE,GAAG,MAAM;QAEjD,IAAI,YAAY;YACd,QAAQ,KAAK,CAAC,CAAC,0CAA0C,EAAE,WAAW,OAAO,EAAE;YAC/E,OAAO;gBAAE,MAAM;gBAAM,OAAO,WAAW,OAAO;gBAAE,OAAO;YAAK;QAC9D;QAEA,OAAO;YAAE;YAAM,OAAO;YAAM;QAAM;IACpC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,gDAAgD,EAAE,OAAO;QACxE,OAAO;YAAE,MAAM;YAAM,OAAO;YAAiC,OAAO;QAAK;IAC3E;AACF;AAOO,eAAe,2BAA2B,QAAwB,EAAE,UAAkB;IAC3F,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,GAAG,CAAC,4HAAA,CAAA,gBAAa,CAAC,0BAA0B,EAAE;YAAE,eAAe;QAAW;QAE7E,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,0CAA0C,EAAE,MAAM,OAAO,EAAE;YAC1E,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,gDAAgD,EAAE,KAAK;QACtE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AASO,eAAe,iCACpB,QAAwB,EACxB,SAAiB,EACjB,aAAmB,EACnB,gBAAyB;IAEzB,IAAI;QACF,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SAClD,GAAG,CAAC,4HAAA,CAAA,gBAAa,CAAC,6BAA6B,EAAE;YAChD,cAAc;YACd,cAAc;YACd,oBAAoB,oBAAoB;QAC1C;QAEF,IAAI,eAAe;YACjB,QAAQ,KAAK,CAAC,CAAC,6CAA6C,EAAE,cAAc,OAAO,EAAE;YACrF,OAAO;gBAAE,UAAU;gBAAW,OAAO,cAAc,OAAO;YAAC;QAC7D;QACA,OAAO;YAAE,UAAU,UAAU;YAAW,OAAO;QAAK;IACtD,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,sDAAsD,EAAE,KAAK;QAC5E,OAAO;YAAE,UAAU;YAAW,OAAO;QAAgC;IACvE;AACF;AAWO,eAAe,2BACpB,QAAwB,EACxB,MAAc,EACd,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,UAII,CAAC,CAAC,EACN,SAAwL,cAAc;IAEtM,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;IAE5B,IAAI,QAAQ,SACT,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CACL,CAAC;MACD,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;MACb,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC;MACtB,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC;MACvB,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC;MACf,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC;MACtB,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC;MACrB,EAAE,4HAAA,CAAA,UAAO,CAAC,gBAAgB,CAAC;MAC3B,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC;MACvB,EAAE,4HAAA,CAAA,UAAO,CAAC,SAAS,CAAC;MACpB,EAAE,4HAAA,CAAA,UAAO,CAAC,MAAM,CAAC;MACjB,EAAE,4HAAA,CAAA,UAAO,CAAC,oBAAoB,CAAC;MAC/B,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC;MACrB,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC;MACrB,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC;MACf,EAAE,4HAAA,CAAA,SAAM,CAAC,gBAAgB,CAAC,CAAC,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC;IACnE,CAAC,EACC;QAAE,OAAO;IAAQ,GAElB,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,WAAW,EAAE;IAE3B,gBAAgB;IAChB,IAAI,QAAQ,UAAU,EACpB,QAAQ,MAAM,EAAE,CACd,GAAG,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,UAAU,CAAC,CAAC,CAAC;IAExG,IAAI,QAAQ,WAAW,KAAK,WAAW;QACrC,IAAI,QAAQ,WAAW,EAAE;YACvB,mCAAmC;YACnC,QAAQ,MAAM,GAAG,CAAC,4HAAA,CAAA,SAAM,CAAC,gBAAgB,EAAE,MAAM;QACnD,OAAO;YACL,yCAAyC;YACzC,QAAQ,MAAM,EAAE,CAAC,4HAAA,CAAA,SAAM,CAAC,gBAAgB,EAAE;QAC5C;IACF;IACA,IAAI,QAAQ,WAAW,EACrB,QAAQ,MAAM,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,YAAY,EAAE,QAAQ,WAAW;IAE5D,gBAAgB;IAChB,OAAQ;QACN,KAAK;YACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;gBAAE,WAAW;YAAK;YAC1D;QACF,KAAK;YACH,QAAQ,MACL,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,gBAAgB,EAAE;gBAAE,WAAW;gBAAM,YAAY;YAAM,GACrE,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;gBAAE,WAAW;gBAAM,YAAY;YAAM;YAClE;QACF,KAAK;YACH,QAAQ,MACL,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,gBAAgB,EAAE;gBAAE,WAAW;gBAAO,YAAY;YAAM,GACtE,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;gBAAE,WAAW;gBAAO,YAAY;YAAM;YACnE;QACF,KAAK;YACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,IAAI,EAAE;gBAAE,WAAW;YAAK;YACpD;QACF,KAAK;YACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,IAAI,EAAE;gBAAE,WAAW;YAAM;YACrD;QACF,KAAK;YACH,QAAQ,MACL,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,YAAY,EAAE;gBAAE,WAAW;YAAM,GAC/C,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;gBAAE,WAAW;YAAM;YAChD;QACF,KAAK;YACH,QAAQ,MACL,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,YAAY,EAAE;gBAAE,WAAW;YAAK,GAC9C,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;gBAAE,WAAW;YAAM;YAChD;QACF,KAAK;QACL,KAAK;QACL,KAAK;QACL;YACE,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;gBAAE,WAAW;YAAM;YAC3D;IACJ;IAEA,QAAQ,MAAM,KAAK,CAAC,QAAQ,SAAS,QAAQ;IAC7C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;IAErC,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;YAAE,MAAM;YAAM,OAAO,MAAM,OAAO;YAAE,OAAO;QAAK;IACzD;IAEA,OAAO;QAAE;QAAM;QAAO,OAAO;IAAK;AACpC;AAOO,eAAe,uCAAuC,QAAwB,EAAE,SAAiB;IACtG,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,gBAAgB,EAC5B,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,WACf,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,sCAAsC,EAAE,MAAM,OAAO,EAAE;YACtE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,iDAAiD,EAAE,KAAK;QACvE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAQO,eAAe,yBAAyB,QAAwB,EAAE,SAAiB,EAAE,UAA4C;IACtI,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,gBAAgB,EAC5B,MAAM,CAAC,YACP,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,WACf,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,qCAAqC,EAAE,MAAM,OAAO,EAAE;YACrE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,gDAAgD,EAAE,KAAK;QACtE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAOO,eAAe,8BAA8B,QAAwB,EAAE,OAAiE;IAC7I,IAAI;QACF,MAAM,iBAAiB,QAAQ,GAAG,CAAC,CAAA,SACjC,SACG,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,gBAAgB,EAC5B,MAAM,CAAC,OAAO,IAAI,EAClB,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,OAAO,EAAE;QAG7B,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;QAClC,MAAM,SAAS,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,KAAK,EAAE,GAAG,CAAC,CAAA,SAAU,OAAO,KAAK,EAAE;QAElF,IAAI,OAAO,MAAM,GAAG,GAAG;YACrB,QAAQ,KAAK,CAAC,CAAC,+CAA+C,EAAE,OAAO,IAAI,CAAC,OAAO;YACnF,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,gCAAgC,EAAE,OAAO,IAAI,CAAC,OAAO;YAAC;QACzF;QACA,OAAO;YAAE,SAAS;YAAM,OAAO;QAAK;IACtC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,yDAAyD,EAAE,KAAK;QAC/E,OAAO;YAAE,SAAS;YAAO,OAAO;QAAgC;IAClE;AACF;AAUO,eAAe,uBAAuB,QAAwB,EAAE,SAAiB;IACtF,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,gBAAgB,EAC5B,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,0BAA0B,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC,EAAE,CAAC,EAC7I,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;QAE1B,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,uCAAuC,EAAE,MAAM,OAAO,EAAE;YACvE,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,8CAA8C;QAC9C,MAAM,YAA4C,AAAC,KAA8H,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAC5L,GAAG,IAAI;gBACP,mBAAmB,KAAK,iBAAiB;YAC3C,CAAC;QACD,OAAO;YAAE,MAAM;YAAW,OAAO;QAAK;IACxC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,kDAAkD,EAAE,KAAK;QACxE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAcO,eAAe,gCAAgC,QAAwB,EAAE,MAAc;IAC5F,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,CAAC;QACP,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC;QACxB,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC;QACxB,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC;QACtB,EAAE,4HAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB,EAAE,4HAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB,EAAE,4HAAA,CAAA,UAAO,CAAC,iBAAiB,CAAC;QAC5B,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC;QACxB,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC;QACvB,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC;QACf,EAAE,4HAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC;QACnB,EAAE,4HAAA,CAAA,UAAO,CAAC,MAAM,CAAC;MACnB,CAAC,EACA,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,QACf,WAAW;QAEd,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,gDAAgD,EAAE,MAAM,OAAO,EAAE;YAChF,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,2DAA2D,EAAE,KAAK;QACjF,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AASO,eAAe,4BAA4B,QAAwB,EAAE,YAAkB,EAAE,gBAAsB;IACpH,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,GAAG,CAAC,4HAAA,CAAA,gBAAa,CAAC,8BAA8B,EAAE;YACvF,iBAAiB;YACjB,qBAAqB;QACvB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,8CAA8C,EAAE,MAAM,OAAO,EAAE;YAC9E,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,CAAC,iDAAiD,EAAE,KAAK;QACvE,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAEO,eAAe,yBAAyB,QAAwB,EAAE,SAAiB;IACtF,IAAI;QACA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACnB,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,gBAAgB,EAC5B,MAAM,GACN,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;QAEpB,IAAI,OAAO;YACP,QAAQ,KAAK,CAAC,CAAC,sCAAsC,EAAE,MAAM,OAAO,EAAE;YACtE,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QAClD;QACA,OAAO;YAAE,SAAS;YAAM,OAAO;QAAK;IACxC,EAAE,OAAO,KAAK;QACV,QAAQ,KAAK,CAAC,CAAC,iDAAiD,EAAE,KAAK;QACvE,OAAO;YAAE,SAAS;YAAO,OAAO;QAAgC;IACpE;AACJ", "debugId": null}}, {"offset": {"line": 4144, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/server.ts"], "sourcesContent": ["import { createServerClient, type CookieOptions } from '@supabase/ssr';\r\nimport { SupabaseClient } from '@supabase/supabase-js';\r\nimport { cookies, headers } from 'next/headers';\r\nimport { Database } from '@/types/supabase';\r\n\r\nexport async function createClient(): Promise<SupabaseClient<Database>> {\r\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\r\n  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\r\n\r\n  if (!supabaseUrl || !supabaseAnonKey) {\r\n    throw new Error('Supabase environment variables are not set.');\r\n  }\r\n\r\n  // Check if we're in a test environment\r\n  const headersList = await headers();\r\n  const isTestEnvironment =\r\n    process.env.NODE_ENV === 'test' ||\r\n    process.env.PLAYWRIGHT_TESTING === 'true' ||\r\n    headersList.get('x-playwright-testing') === 'true';\r\n\r\n  if (isTestEnvironment) {\r\n    // Return a mocked Supabase client for testing\r\n    return createMockSupabaseClient(headersList) as unknown as SupabaseClient<Database>;\r\n  }\r\n\r\n  const cookieStore = await cookies();\r\n\r\n  return createServerClient(\r\n    supabaseUrl,\r\n    supabaseAnonKey,\r\n    {\r\n      cookies: {\r\n        async getAll() {\r\n          return await cookieStore.getAll();\r\n        },\r\n        async setAll(cookiesToSet: any[]) {\r\n          try {\r\n            for (const { name, value, options } of cookiesToSet) {\r\n              await cookieStore.set(name, value, options);\r\n            }\r\n          } catch {\r\n            // The `setAll` method was called from a Server Component.\r\n            // This can be ignored if you have middleware refreshing\r\n            // user sessions.\r\n          }\r\n        },\r\n      },\r\n    }\r\n  ) as unknown as SupabaseClient<Database>;\r\n}\r\n\r\ntype MockQueryBuilder = {\r\n  select: (columns?: string) => MockQueryBuilder;\r\n  eq: (column: string, value: any) => MockQueryBuilder;\r\n  neq: (column: string, value: any) => MockQueryBuilder;\r\n  gt: (column: string, value: any) => MockQueryBuilder;\r\n  gte: (column: string, value: any) => MockQueryBuilder;\r\n  lt: (column: string, value: any) => MockQueryBuilder;\r\n  lte: (column: string, value: any) => MockQueryBuilder;\r\n  like: (column: string, pattern: string) => MockQueryBuilder;\r\n  ilike: (column: string, pattern: string) => MockQueryBuilder;\r\n  is: (column: string, value: any) => MockQueryBuilder;\r\n  in: (column: string, values: any[]) => MockQueryBuilder;\r\n  contains: (column: string, value: any) => MockQueryBuilder;\r\n  containedBy: (column: string, value: any) => MockQueryBuilder;\r\n  rangeGt: (column: string, value: any) => MockQueryBuilder;\r\n  rangeGte: (column: string, value: any) => MockQueryBuilder;\r\n  rangeLt: (column: string, value: any) => MockQueryBuilder;\r\n  rangeLte: (column: string, value: any) => MockQueryBuilder;\r\n  rangeAdjacent: (column: string, value: any) => MockQueryBuilder;\r\n  overlaps: (column: string, value: any) => MockQueryBuilder;\r\n  textSearch: (column: string, query: string) => MockQueryBuilder;\r\n  match: (query: Record<string, any>) => MockQueryBuilder;\r\n  not: (column: string, operator: string, value: any) => MockQueryBuilder;\r\n  or: (filters: string) => MockQueryBuilder;\r\n  filter: (column: string, operator: string, value: any) => MockQueryBuilder;\r\n  order: (column: string, options?: { ascending?: boolean; nullsFirst?: boolean }) => MockQueryBuilder;\r\n  limit: (count: number, options?: { foreignTable?: string }) => MockQueryBuilder;\r\n  range: (from: number, to: number, options?: { foreignTable?: string }) => MockQueryBuilder;\r\n  abortSignal: (signal: AbortSignal) => MockQueryBuilder;\r\n  single: () => Promise<any>;\r\n  maybeSingle: () => Promise<any>;\r\n  then: (callback?: any) => Promise<any>;\r\n  data: any;\r\n  error: any;\r\n  count: number;\r\n  status: number;\r\n  statusText: string;\r\n};\r\n\r\nfunction createMockSupabaseClient(headersList: Headers) {\r\n  const testAuthState = headersList.get('x-test-auth-state');\r\n  const testUserType = headersList.get('x-test-user-type');\r\n  const testHasProfile = testUserType === 'customer' || testUserType === 'business';\r\n  const testBusinessSlug = headersList.get('x-test-business-slug');\r\n  const testPlanId = headersList.get('x-test-plan-id') || 'free';\r\n\r\n  return {\r\n    auth: {\r\n      getUser: async () => {\r\n        if (testAuthState === 'authenticated') {\r\n          return { data: { user: { id: 'test-user-id', email: '<EMAIL>' } }, error: null };\r\n        }\r\n        return { data: { user: null }, error: { message: 'Unauthorized', name: 'AuthApiError', status: 401 } };\r\n      },\r\n      getSession: async () => {\r\n        if (testAuthState === 'authenticated') {\r\n          return { data: { session: { user: { id: 'test-user-id', email: '<EMAIL>' } } }, error: null };\r\n        }\r\n        return { data: { session: null }, error: { message: 'Unauthorized', name: 'AuthApiError', status: 401 } };\r\n      },\r\n      signInWithOtp: async () => ({ data: { user: null, session: null }, error: null }),\r\n      signOut: async () => ({ error: null }),\r\n    },\r\n    from: (table: string) => createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId),\r\n  };\r\n}\r\n\r\nfunction createMockQueryBuilder(\r\n  table: string,\r\n  testUserType: string | null,\r\n  testHasProfile: boolean,\r\n  testBusinessSlug: string | null,\r\n  testPlanId: string\r\n): any {\r\n  const getMockData = () => getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId);\r\n\r\n  const createChainableMock = (data?: any): MockQueryBuilder => ({\r\n    select: (_columns?: string) => createChainableMock(data),\r\n    eq: (_column: string, _value: any) => createChainableMock(data),\r\n    neq: (_column: string, _value: any) => createChainableMock(data),\r\n    gt: (_column: string, _value: any) => createChainableMock(data),\r\n    gte: (_column: string, _value: any) => createChainableMock(data),\r\n    lt: (_column: string, _value: any) => createChainableMock(data),\r\n    lte: (_column: string, _value: any) => createChainableMock(data),\r\n    like: (_column: string, _pattern: string) => createChainableMock(data),\r\n    ilike: (_column: string, _pattern: string) => createChainableMock(data),\r\n    is: (_column: string, _value: any) => createChainableMock(data),\r\n    in: (_column: string, _values: any[]) => createChainableMock(data),\r\n    contains: (_column: string, _value: any) => createChainableMock(data),\r\n    containedBy: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeGt: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeGte: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeLt: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeLte: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeAdjacent: (_column: string, _value: any) => createChainableMock(data),\r\n    overlaps: (_column: string, _value: any) => createChainableMock(data),\r\n    textSearch: (_column: string, _query: string) => createChainableMock(data),\r\n    match: (_query: Record<string, any>) => createChainableMock(data),\r\n    not: (_column: string, _operator: string, _value: any) => createChainableMock(data),\r\n    or: (_filters: string) => createChainableMock(data),\r\n    filter: (_column: string, _operator: string, _value: any) => createChainableMock(data),\r\n    order: (_column: string, _options?: { ascending?: boolean; nullsFirst?: boolean }) => createChainableMock(data),\r\n    limit: (_count: number, _options?: { foreignTable?: string }) => createChainableMock(data),\r\n    range: (_from: number, _to: number, _options?: { foreignTable?: string }) => createChainableMock(data),\r\n    abortSignal: (_signal: AbortSignal) => createChainableMock(data),\r\n    single: async () => getMockData(),\r\n    maybeSingle: async () => getMockData(),\r\n    then: async (callback?: any) => {\r\n      const result = getMockData();\r\n      return callback ? callback(result) : result;\r\n    },\r\n    data: data || [],\r\n    error: null,\r\n    count: data ? data.length : 0,\r\n    status: 200,\r\n    statusText: 'OK',\r\n  });\r\n\r\n  return {\r\n    select: (_columns?: string) => createChainableMock(),\r\n    insert: (data: any | any[]) => ({\r\n      select: (_columns?: string) => ({\r\n        single: async () => ({\r\n          data: Array.isArray(data) ? data[0] : data,\r\n          error: null,\r\n        }),\r\n        maybeSingle: async () => ({\r\n          data: Array.isArray(data) ? data[0] : data,\r\n          error: null,\r\n        }),\r\n        then: async (_callback?: any) => {\r\n          const result = { data: Array.isArray(data) ? data : [data], error: null };\r\n          return _callback ? _callback(result) : result;\r\n        },\r\n      }),\r\n      then: async (_callback?: any) => {\r\n        const result = { data: Array.isArray(data) ? data : [data], error: null };\r\n        return _callback ? _callback(result) : result;\r\n      },\r\n    }),\r\n    update: (data: any) => createChainableMock(data),\r\n    upsert: (data: any | any[]) => createChainableMock(data),\r\n    delete: () => createChainableMock(),\r\n    rpc: (_functionName: string, _params?: any) => createChainableMock(),\r\n  };\r\n}\r\n\r\n/**\r\n * Helper function to get mock table data based on test state\r\n */\r\nfunction getMockTableData(\r\n  table: string,\r\n  testUserType: string | null,\r\n  testHasProfile: boolean,\r\n  testBusinessSlug: string | null,\r\n  testPlanId: string\r\n) {\r\n  if (table === 'customer_profiles') {\r\n    const hasCustomerProfile = testHasProfile && testUserType === 'customer';\r\n    return {\r\n      data: hasCustomerProfile ? {\r\n        id: 'test-user-id',\r\n        name: 'Test Customer',\r\n        avatar_url: null,\r\n        phone: '+1234567890',\r\n        email: '<EMAIL>',\r\n        address: 'Test Address',\r\n        city: 'Test City',\r\n        state: 'Test State',\r\n        pincode: '123456'\r\n      } : null,\r\n      error: null\r\n    };\r\n  }\r\n\r\n  if (table === 'business_profiles') {\r\n    const hasBusinessProfile = testHasProfile && testUserType === 'business';\r\n    return {\r\n      data: hasBusinessProfile ? {\r\n        id: 'test-user-id',\r\n        business_slug: testBusinessSlug || null,\r\n        trial_end_date: null,\r\n        has_active_subscription: true,\r\n        business_name: 'Test Business',\r\n        city_slug: 'test-city',\r\n        state_slug: 'test-state',\r\n        locality_slug: 'test-locality',\r\n        pincode: '123456',\r\n        business_description: 'Test business description',\r\n        business_category: 'retail',\r\n        phone: '+1234567890',\r\n        email: '<EMAIL>',\r\n        website: 'https://testbusiness.com'\r\n      } : null,\r\n      error: null\r\n    };\r\n  }\r\n\r\n  if (table === 'payment_subscriptions') {\r\n    return {\r\n      data: testUserType === 'business' ? {\r\n        id: 'test-subscription-id',\r\n        plan_id: testPlanId,\r\n        business_profile_id: 'test-user-id',\r\n        status: 'active',\r\n        created_at: '2024-01-01T00:00:00Z'\r\n      } : null,\r\n      error: null\r\n    };\r\n  }\r\n\r\n  if (table === 'products') {\r\n    return {\r\n      data: testUserType === 'business' ? [\r\n        {\r\n          id: 'test-product-1',\r\n          name: 'Test Product 1',\r\n          price: 100,\r\n          business_profile_id: 'test-user-id',\r\n          available: true\r\n        },\r\n        {\r\n          id: 'test-product-2',\r\n          name: 'Test Product 2',\r\n          price: 200,\r\n          business_profile_id: 'test-user-id',\r\n          available: false\r\n        }\r\n      ] : [],\r\n      error: null\r\n    };\r\n  }\r\n\r\n  // Default return for unknown tables\r\n  return { data: null, error: null };\r\n}\r\n\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAEA;;;AAGO,eAAe;IACpB,MAAM;IACN,MAAM;IAEN,uCAAsC;;IAEtC;IAEA,uCAAuC;IACvC,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,oBACJ,oDAAyB,UACzB,QAAQ,GAAG,CAAC,kBAAkB,KAAK,UACnC,YAAY,GAAG,CAAC,4BAA4B;IAE9C,IAAI,mBAAmB;QACrB,8CAA8C;QAC9C,OAAO,yBAAyB;IAClC;IAEA,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,EACtB,aACA,iBACA;QACE,SAAS;YACP,MAAM;gBACJ,OAAO,MAAM,YAAY,MAAM;YACjC;YACA,MAAM,QAAO,YAAmB;gBAC9B,IAAI;oBACF,KAAK,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,aAAc;wBACnD,MAAM,YAAY,GAAG,CAAC,MAAM,OAAO;oBACrC;gBACF,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ;AAyCA,SAAS,yBAAyB,WAAoB;IACpD,MAAM,gBAAgB,YAAY,GAAG,CAAC;IACtC,MAAM,eAAe,YAAY,GAAG,CAAC;IACrC,MAAM,iBAAiB,iBAAiB,cAAc,iBAAiB;IACvE,MAAM,mBAAmB,YAAY,GAAG,CAAC;IACzC,MAAM,aAAa,YAAY,GAAG,CAAC,qBAAqB;IAExD,OAAO;QACL,MAAM;YACJ,SAAS;gBACP,IAAI,kBAAkB,iBAAiB;oBACrC,OAAO;wBAAE,MAAM;4BAAE,MAAM;gCAAE,IAAI;gCAAgB,OAAO;4BAAmB;wBAAE;wBAAG,OAAO;oBAAK;gBAC1F;gBACA,OAAO;oBAAE,MAAM;wBAAE,MAAM;oBAAK;oBAAG,OAAO;wBAAE,SAAS;wBAAgB,MAAM;wBAAgB,QAAQ;oBAAI;gBAAE;YACvG;YACA,YAAY;gBACV,IAAI,kBAAkB,iBAAiB;oBACrC,OAAO;wBAAE,MAAM;4BAAE,SAAS;gCAAE,MAAM;oCAAE,IAAI;oCAAgB,OAAO;gCAAmB;4BAAE;wBAAE;wBAAG,OAAO;oBAAK;gBACvG;gBACA,OAAO;oBAAE,MAAM;wBAAE,SAAS;oBAAK;oBAAG,OAAO;wBAAE,SAAS;wBAAgB,MAAM;wBAAgB,QAAQ;oBAAI;gBAAE;YAC1G;YACA,eAAe,UAAY,CAAC;oBAAE,MAAM;wBAAE,MAAM;wBAAM,SAAS;oBAAK;oBAAG,OAAO;gBAAK,CAAC;YAChF,SAAS,UAAY,CAAC;oBAAE,OAAO;gBAAK,CAAC;QACvC;QACA,MAAM,CAAC,QAAkB,uBAAuB,OAAO,cAAc,gBAAgB,kBAAkB;IACzG;AACF;AAEA,SAAS,uBACP,KAAa,EACb,YAA2B,EAC3B,cAAuB,EACvB,gBAA+B,EAC/B,UAAkB;IAElB,MAAM,cAAc,IAAM,iBAAiB,OAAO,cAAc,gBAAgB,kBAAkB;IAElG,MAAM,sBAAsB,CAAC,OAAiC,CAAC;YAC7D,QAAQ,CAAC,WAAsB,oBAAoB;YACnD,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,KAAK,CAAC,SAAiB,SAAgB,oBAAoB;YAC3D,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,KAAK,CAAC,SAAiB,SAAgB,oBAAoB;YAC3D,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,KAAK,CAAC,SAAiB,SAAgB,oBAAoB;YAC3D,MAAM,CAAC,SAAiB,WAAqB,oBAAoB;YACjE,OAAO,CAAC,SAAiB,WAAqB,oBAAoB;YAClE,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,IAAI,CAAC,SAAiB,UAAmB,oBAAoB;YAC7D,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,aAAa,CAAC,SAAiB,SAAgB,oBAAoB;YACnE,SAAS,CAAC,SAAiB,SAAgB,oBAAoB;YAC/D,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,SAAS,CAAC,SAAiB,SAAgB,oBAAoB;YAC/D,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,eAAe,CAAC,SAAiB,SAAgB,oBAAoB;YACrE,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,YAAY,CAAC,SAAiB,SAAmB,oBAAoB;YACrE,OAAO,CAAC,SAAgC,oBAAoB;YAC5D,KAAK,CAAC,SAAiB,WAAmB,SAAgB,oBAAoB;YAC9E,IAAI,CAAC,WAAqB,oBAAoB;YAC9C,QAAQ,CAAC,SAAiB,WAAmB,SAAgB,oBAAoB;YACjF,OAAO,CAAC,SAAiB,WAA6D,oBAAoB;YAC1G,OAAO,CAAC,QAAgB,WAAyC,oBAAoB;YACrF,OAAO,CAAC,OAAe,KAAa,WAAyC,oBAAoB;YACjG,aAAa,CAAC,UAAyB,oBAAoB;YAC3D,QAAQ,UAAY;YACpB,aAAa,UAAY;YACzB,MAAM,OAAO;gBACX,MAAM,SAAS;gBACf,OAAO,WAAW,SAAS,UAAU;YACvC;YACA,MAAM,QAAQ,EAAE;YAChB,OAAO;YACP,OAAO,OAAO,KAAK,MAAM,GAAG;YAC5B,QAAQ;YACR,YAAY;QACd,CAAC;IAED,OAAO;QACL,QAAQ,CAAC,WAAsB;QAC/B,QAAQ,CAAC,OAAsB,CAAC;gBAC9B,QAAQ,CAAC,WAAsB,CAAC;wBAC9B,QAAQ,UAAY,CAAC;gCACnB,MAAM,MAAM,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,GAAG;gCACtC,OAAO;4BACT,CAAC;wBACD,aAAa,UAAY,CAAC;gCACxB,MAAM,MAAM,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,GAAG;gCACtC,OAAO;4BACT,CAAC;wBACD,MAAM,OAAO;4BACX,MAAM,SAAS;gCAAE,MAAM,MAAM,OAAO,CAAC,QAAQ,OAAO;oCAAC;iCAAK;gCAAE,OAAO;4BAAK;4BACxE,OAAO,YAAY,UAAU,UAAU;wBACzC;oBACF,CAAC;gBACD,MAAM,OAAO;oBACX,MAAM,SAAS;wBAAE,MAAM,MAAM,OAAO,CAAC,QAAQ,OAAO;4BAAC;yBAAK;wBAAE,OAAO;oBAAK;oBACxE,OAAO,YAAY,UAAU,UAAU;gBACzC;YACF,CAAC;QACD,QAAQ,CAAC,OAAc,oBAAoB;QAC3C,QAAQ,CAAC,OAAsB,oBAAoB;QACnD,QAAQ,IAAM;QACd,KAAK,CAAC,eAAuB,UAAkB;IACjD;AACF;AAEA;;CAEC,GACD,SAAS,iBACP,KAAa,EACb,YAA2B,EAC3B,cAAuB,EACvB,gBAA+B,EAC/B,UAAkB;IAElB,IAAI,UAAU,qBAAqB;QACjC,MAAM,qBAAqB,kBAAkB,iBAAiB;QAC9D,OAAO;YACL,MAAM,qBAAqB;gBACzB,IAAI;gBACJ,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,OAAO;gBACP,SAAS;gBACT,MAAM;gBACN,OAAO;gBACP,SAAS;YACX,IAAI;YACJ,OAAO;QACT;IACF;IAEA,IAAI,UAAU,qBAAqB;QACjC,MAAM,qBAAqB,kBAAkB,iBAAiB;QAC9D,OAAO;YACL,MAAM,qBAAqB;gBACzB,IAAI;gBACJ,eAAe,oBAAoB;gBACnC,gBAAgB;gBAChB,yBAAyB;gBACzB,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,eAAe;gBACf,SAAS;gBACT,sBAAsB;gBACtB,mBAAmB;gBACnB,OAAO;gBACP,OAAO;gBACP,SAAS;YACX,IAAI;YACJ,OAAO;QACT;IACF;IAEA,IAAI,UAAU,yBAAyB;QACrC,OAAO;YACL,MAAM,iBAAiB,aAAa;gBAClC,IAAI;gBACJ,SAAS;gBACT,qBAAqB;gBACrB,QAAQ;gBACR,YAAY;YACd,IAAI;YACJ,OAAO;QACT;IACF;IAEA,IAAI,UAAU,YAAY;QACxB,OAAO;YACL,MAAM,iBAAiB,aAAa;gBAClC;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,qBAAqB;oBACrB,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,qBAAqB;oBACrB,WAAW;gBACb;aACD,GAAG,EAAE;YACN,OAAO;QACT;IACF;IAEA,oCAAoC;IACpC,OAAO;QAAE,MAAM;QAAM,OAAO;IAAK;AACnC", "debugId": null}}, {"offset": {"line": 4423, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/redirectAfterLogin.ts"], "sourcesContent": ["/**\r\n * Shared utility to determine the correct post-login redirect path for a user.\r\n * Checks both customer_profiles and business_profiles, and returns the appropriate dashboard, onboarding, or choose-role path.\r\n * Returns \"/\" as a fallback in case of errors.\r\n */\r\n\r\nimport { SupabaseClient } from '@supabase/supabase-js';\r\nimport { Database } from '../../types/supabase';\r\nimport { checkIfCustomerProfileExists } from \"@/lib/supabase/services/customerService\";\r\nimport { checkIfBusinessProfileExists } from \"@/lib/supabase/services/businessService\";\r\nimport { createClient } from \"@/utils/supabase/server\";\r\n\r\nexport async function getPostLoginRedirectPath(\r\n  userId: string\r\n): Promise<string> {\r\n  const supabase = await createClient();\r\n  try {\r\n    // Check both profiles concurrently\r\n    const [customerRes, businessRes] = await Promise.all([\r\n      checkIfCustomerProfileExists(supabase, userId),\r\n      checkIfBusinessProfileExists(supabase, userId),\r\n    ]);\r\n\r\n    if (customerRes.error || businessRes.error) {\r\n      console.error(\"[redirectAfterLogin] Supabase query error:\", customerRes.error, businessRes.error);\r\n      return \"/?view=home\";\r\n    }\r\n\r\n    if (customerRes.exists) {\r\n      return \"/dashboard/customer\";\r\n    }\r\n\r\n    if (businessRes.exists) {\r\n      // Need to fetch the business_slug separately as checkIfBusinessProfileExists only returns a boolean\r\n      // This means we need a new function in businessService to get the business profile with slug\r\n      // For now, I'll assume if it exists, it's onboarded, and will create a new task to address this.\r\n      return \"/dashboard/business\";\r\n    }\r\n\r\n    return \"/choose-role\";\r\n  } catch (err) {\r\n    console.error(\"[redirectAfterLogin] Unexpected error:\", err);\r\n    return \"/?view=home\";\r\n  }\r\n}"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAID;AACA;AACA;;;;AAEO,eAAe,yBACpB,MAAc;IAEd,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,IAAI;QACF,mCAAmC;QACnC,MAAM,CAAC,aAAa,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;YACnD,CAAA,GAAA,8IAAA,CAAA,+BAA4B,AAAD,EAAE,UAAU;YACvC,CAAA,GAAA,8IAAA,CAAA,+BAA4B,AAAD,EAAE,UAAU;SACxC;QAED,IAAI,YAAY,KAAK,IAAI,YAAY,KAAK,EAAE;YAC1C,QAAQ,KAAK,CAAC,8CAA8C,YAAY,KAAK,EAAE,YAAY,KAAK;YAChG,OAAO;QACT;QAEA,IAAI,YAAY,MAAM,EAAE;YACtB,OAAO;QACT;QAEA,IAAI,YAAY,MAAM,EAAE;YACtB,oGAAoG;YACpG,6FAA6F;YAC7F,iGAAiG;YACjG,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,0CAA0C;QACxD,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 4469, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/LoginForm.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useRouter, useSearchParams } from \"next/navigation\";\r\nimport { useState, useEffect, useTransition } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Card } from \"@/components/ui/card\";\r\nimport { toast } from \"sonner\";\r\nimport { sendOTP, verifyOTP, loginWithMobilePassword } from \"./actions\";\r\nimport { EmailOTPForm } from \"./components/EmailOTPForm\";\r\nimport { MobilePasswordForm } from \"./components/MobilePasswordForm\";\r\nimport { AuthMethodToggle } from \"./components/AuthMethodToggle\";\r\nimport { SocialLoginButton } from \"./components/SocialLoginButton\";\r\nimport { getAuthenticatedUser } from \"@/lib/supabase/services/sharedService\";\r\nimport { getPostLoginRedirectPath } from \"@/lib/actions/redirectAfterLogin\";\r\n\r\nexport function LoginForm() {\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const [isPending, startTransition] = useTransition();\r\n  const [redirectSlug, setRedirectSlug] = useState<string | null>(null);\r\n  const [message, setMessage] = useState<string | null>(null);\r\n  const [authMethod, setAuthMethod] = useState<'email-otp' | 'mobile-password'>('email-otp');\r\n  const [step, setStep] = useState<'email' | 'otp'>('email');\r\n  const [email, setEmail] = useState<string>('');\r\n  const [countdown, setCountdown] = useState<number>(0);\r\n\r\n  // Get the redirect and message parameters from the URL\r\n  useEffect(() => {\r\n    const redirect = searchParams.get(\"redirect\");\r\n    if (redirect) {\r\n      setRedirectSlug(redirect);\r\n    }\r\n\r\n    const messageParam = searchParams.get(\"message\");\r\n    if (messageParam) {\r\n      setMessage(messageParam);\r\n    }\r\n  }, [searchParams]);\r\n\r\n  // Countdown timer for resend OTP\r\n  useEffect(() => {\r\n    if (countdown > 0) {\r\n      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [countdown]);\r\n\r\n  // Helper function to handle post-login redirect\r\n  async function handlePostLoginRedirect() {\r\n    try {\r\n      const supabase = await createClient();\r\n      const { user } = await getAuthenticatedUser(supabase);\r\n\r\n      if (!user) {\r\n        console.error(\"No user found after login\");\r\n        router.push(\"/?view=home\");\r\n        return;\r\n      }\r\n\r\n      const redirectPath = await getPostLoginRedirectPath(user.id);\r\n\r\n      // Handle redirect logic\r\n      if (redirectSlug) {\r\n        // IMPORTANT: Prevent open redirect vulnerabilities.\r\n        // Ensure the redirectSlug is a relative path and not an external URL.\r\n        if (redirectSlug.includes('://') || redirectSlug.startsWith('//')) {\r\n          console.warn('Attempted redirect to an external or malformed URL. Redirecting to default path.');\r\n          router.push(redirectPath);\r\n          return;\r\n        }\r\n\r\n        // If it's a relative path, we can proceed.\r\n        // The application's routing will handle the validity of the path (e.g., 404 for invalid slugs).\r\n        router.push(`/${redirectSlug}${message ? `?message=${encodeURIComponent(message)}` : \"\"}`);\r\n      } else {\r\n        router.push(redirectPath);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error determining redirect path:\", error);\r\n      router.push(\"/?view=home\");\r\n    }\r\n  }\r\n\r\n  function onEmailSubmit(values: { email: string }) {\r\n    startTransition(async () => {\r\n      try {\r\n        const result = await sendOTP(values);\r\n\r\n        if (!result.success) {\r\n          // Check if this is a configuration error (email rate limit)\r\n          if ('isConfigurationError' in result && result.isConfigurationError) {\r\n            toast.error(\"Configuration Error\", {\r\n              description: result.error,\r\n              duration: 10000, // Show longer for configuration errors\r\n            });\r\n            // Don't proceed to OTP step for configuration errors\r\n            return;\r\n          }\r\n\r\n          toast.error(\"Failed to send OTP\", {\r\n            description: result.error,\r\n          });\r\n          return;\r\n        }\r\n\r\n        toast.success(\"OTP sent!\", {\r\n          description: result.message,\r\n        });\r\n\r\n        setEmail(values.email);\r\n        setStep('otp');\r\n        setCountdown(60); // 60 second countdown (Supabase rate limit)\r\n      } catch (_error) {\r\n        toast.error(\"Failed to send OTP\", {\r\n          description: \"An unexpected error occurred. Please try again.\",\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  function onOTPSubmit(values: { email: string; otp: string }) {\r\n    startTransition(async () => {\r\n      try {\r\n        const result = await verifyOTP({\r\n          email: values.email,\r\n          otp: values.otp,\r\n        });\r\n\r\n        if (!result.success) {\r\n          toast.error(\"OTP verification failed\", {\r\n            description: result.error,\r\n          });\r\n          return;\r\n        }\r\n\r\n        toast.success(\"Sign in successful!\", {\r\n          description: \"Redirecting to your dashboard...\",\r\n        });\r\n\r\n        // Use proper post-login redirect logic\r\n        await handlePostLoginRedirect();\r\n      } catch (_error) {\r\n        toast.error(\"OTP verification failed\", {\r\n          description: \"An unexpected error occurred. Please try again.\",\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  function handleResendOTP() {\r\n    if (countdown > 0) return;\r\n\r\n    // Use the same logic as onEmailSubmit for resending\r\n    startTransition(async () => {\r\n      try {\r\n        const result = await sendOTP({ email });\r\n\r\n        if (!result.success) {\r\n          // Check if this is a configuration error (email rate limit)\r\n          if ('isConfigurationError' in result && result.isConfigurationError) {\r\n            toast.error(\"Configuration Error\", {\r\n              description: result.error,\r\n              duration: 10000, // Show longer for configuration errors\r\n            });\r\n            return;\r\n          }\r\n\r\n          toast.error(\"Failed to resend OTP\", {\r\n            description: result.error,\r\n          });\r\n          return;\r\n        }\r\n\r\n        toast.success(\"OTP resent!\", {\r\n          description: result.message,\r\n        });\r\n\r\n        setCountdown(60); // Reset countdown\r\n      } catch (_error) {\r\n        toast.error(\"Failed to resend OTP\", {\r\n          description: \"An unexpected error occurred. Please try again.\",\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  function handleBackToEmail() {\r\n    setStep('email');\r\n    setEmail('');\r\n  }\r\n\r\n  function onMobilePasswordSubmit(values: { mobile: string; password: string }) {\r\n    startTransition(async () => {\r\n      try {\r\n        const result = await loginWithMobilePassword(values);\r\n\r\n        if (!result.success) {\r\n          toast.error(\"Login failed\", {\r\n            description: result.error,\r\n          });\r\n          return;\r\n        }\r\n\r\n        toast.success(\"Sign in successful!\", {\r\n          description: \"Redirecting to your dashboard...\",\r\n        });\r\n\r\n        // Use proper post-login redirect logic\r\n        await handlePostLoginRedirect();\r\n      } catch (_error) {\r\n        toast.error(\"Login failed\", {\r\n          description: \"An unexpected error occurred. Please try again.\",\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  function handleAuthMethodChange(method: 'email-otp' | 'mobile-password') {\r\n    if (method !== authMethod) {\r\n      setAuthMethod(method);\r\n      setStep('email');\r\n      // Don't reset forms - let each component manage its own state\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"w-full max-w-[90%] sm:max-w-md md:max-w-lg\">\r\n        <motion.div\r\n          initial={{ opacity: 0, scale: 0.95 }}\r\n          animate={{ opacity: 1, scale: 1 }}\r\n          transition={{ duration: 0.6, delay: 0.2 }}\r\n        >\r\n          <Card className=\"bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-4 sm:p-6 md:p-8 rounded-xl sm:rounded-2xl shadow-lg dark:shadow-[var(--brand-gold)]/10\">\r\n            <div className=\"text-center mb-6 sm:mb-8\">\r\n              <h1 className=\"text-xl sm:text-2xl font-bold text-foreground mb-1 sm:mb-2\">\r\n                {authMethod === 'email-otp' && step === 'otp' ? 'Enter Verification Code' : 'Welcome to Dukancard'}\r\n              </h1>\r\n              <p className=\"text-sm sm:text-base text-muted-foreground\">\r\n                {authMethod === 'email-otp' && step === 'otp'\r\n                  ? 'Check your email for the 6-digit code'\r\n                  : authMethod === 'email-otp'\r\n                  ? 'Sign in or create your account with email'\r\n                  : 'Sign in with your mobile number and password'\r\n                }\r\n              </p>\r\n\r\n              {message && (\r\n                <div className={`mt-4 p-2 sm:p-3 rounded-lg ${\r\n                  message.toLowerCase().includes(\"error\") || message.toLowerCase().includes(\"failed\")\r\n                    ? \"bg-destructive/10 text-destructive\"\r\n                    : \"bg-green-500/10 text-green-600 dark:text-green-400\"\r\n                }`}>\r\n                  <p className=\"text-xs sm:text-sm\">{message}</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Authentication Method Toggle */}\r\n            <AuthMethodToggle\r\n              authMethod={authMethod}\r\n              step={step}\r\n              onMethodChange={handleAuthMethodChange}\r\n            />\r\n\r\n            {/* Social Login Button */}\r\n            <SocialLoginButton\r\n              redirectSlug={redirectSlug}\r\n              message={message}\r\n              disabled={isPending}\r\n            />\r\n\r\n            <div className=\"relative mb-5 sm:mb-6\">\r\n              <div className=\"absolute inset-0 flex items-center\">\r\n                <div className=\"w-full border-t border-border\" />\r\n              </div>\r\n              <div className=\"relative flex justify-center text-xs uppercase\">\r\n                <span className=\"bg-card px-2 text-muted-foreground\">\r\n                  {authMethod === 'email-otp' && step === 'email' ? 'Or continue with email' :\r\n                   authMethod === 'mobile-password' ? 'Or continue with mobile' :\r\n                   'Or use Google instead'}\r\n                </span>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Form Components */}\r\n            {authMethod === 'email-otp' ? (\r\n              <EmailOTPForm\r\n                step={step}\r\n                email={email}\r\n                countdown={countdown}\r\n                isPending={isPending}\r\n                onEmailSubmit={onEmailSubmit}\r\n                onOTPSubmit={onOTPSubmit}\r\n                onResendOTP={handleResendOTP}\r\n                onBackToEmail={handleBackToEmail}\r\n              />\r\n            ) : (\r\n              <MobilePasswordForm\r\n                isPending={isPending}\r\n                onSubmit={onMobilePasswordSubmit}\r\n              />\r\n            )}\r\n          </Card>\r\n        </motion.div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA;;;;;;;;;;;;;;AAeO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,WAAW,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IAC9E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAClD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEnD,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,IAAI,UAAU;YACZ,gBAAgB;QAClB;QAEA,MAAM,eAAe,aAAa,GAAG,CAAC;QACtC,IAAI,cAAc;YAChB,WAAW;QACb;IACF,GAAG;QAAC;KAAa;IAEjB,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,GAAG;YACjB,MAAM,QAAQ,WAAW,IAAM,aAAa,YAAY,IAAI;YAC5D,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;KAAU;IAEd,gDAAgD;IAChD,eAAe;QACb,IAAI;YACF,MAAM,WAAW,MAAM;YACvB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAA,GAAA,4IAAA,CAAA,uBAAoB,AAAD,EAAE;YAE5C,IAAI,CAAC,MAAM;gBACT,QAAQ,KAAK,CAAC;gBACd,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,MAAM,eAAe,MAAM,CAAA,GAAA,oIAAA,CAAA,2BAAwB,AAAD,EAAE,KAAK,EAAE;YAE3D,wBAAwB;YACxB,IAAI,cAAc;gBAChB,oDAAoD;gBACpD,sEAAsE;gBACtE,IAAI,aAAa,QAAQ,CAAC,UAAU,aAAa,UAAU,CAAC,OAAO;oBACjE,QAAQ,IAAI,CAAC;oBACb,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,2CAA2C;gBAC3C,gGAAgG;gBAChG,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,eAAe,UAAU,CAAC,SAAS,EAAE,mBAAmB,UAAU,GAAG,IAAI;YAC3F,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,IAAI,CAAC;QACd;IACF;IAEA,SAAS,cAAc,MAAyB;QAC9C,gBAAgB;YACd,IAAI;gBACF,MAAM,SAAS,MAAM,CAAA,GAAA,gKAAA,CAAA,UAAO,AAAD,EAAE;gBAE7B,IAAI,CAAC,OAAO,OAAO,EAAE;oBACnB,4DAA4D;oBAC5D,IAAI,0BAA0B,UAAU,OAAO,oBAAoB,EAAE;wBACnE,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,uBAAuB;4BACjC,aAAa,OAAO,KAAK;4BACzB,UAAU;wBACZ;wBACA,qDAAqD;wBACrD;oBACF;oBAEA,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,sBAAsB;wBAChC,aAAa,OAAO,KAAK;oBAC3B;oBACA;gBACF;gBAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,aAAa;oBACzB,aAAa,OAAO,OAAO;gBAC7B;gBAEA,SAAS,OAAO,KAAK;gBACrB,QAAQ;gBACR,aAAa,KAAK,4CAA4C;YAChE,EAAE,OAAO,QAAQ;gBACf,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,sBAAsB;oBAChC,aAAa;gBACf;YACF;QACF;IACF;IAEA,SAAS,YAAY,MAAsC;QACzD,gBAAgB;YACd,IAAI;gBACF,MAAM,SAAS,MAAM,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE;oBAC7B,OAAO,OAAO,KAAK;oBACnB,KAAK,OAAO,GAAG;gBACjB;gBAEA,IAAI,CAAC,OAAO,OAAO,EAAE;oBACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,2BAA2B;wBACrC,aAAa,OAAO,KAAK;oBAC3B;oBACA;gBACF;gBAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,uBAAuB;oBACnC,aAAa;gBACf;gBAEA,uCAAuC;gBACvC,MAAM;YACR,EAAE,OAAO,QAAQ;gBACf,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,2BAA2B;oBACrC,aAAa;gBACf;YACF;QACF;IACF;IAEA,SAAS;QACP,IAAI,YAAY,GAAG;QAEnB,oDAAoD;QACpD,gBAAgB;YACd,IAAI;gBACF,MAAM,SAAS,MAAM,CAAA,GAAA,gKAAA,CAAA,UAAO,AAAD,EAAE;oBAAE;gBAAM;gBAErC,IAAI,CAAC,OAAO,OAAO,EAAE;oBACnB,4DAA4D;oBAC5D,IAAI,0BAA0B,UAAU,OAAO,oBAAoB,EAAE;wBACnE,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,uBAAuB;4BACjC,aAAa,OAAO,KAAK;4BACzB,UAAU;wBACZ;wBACA;oBACF;oBAEA,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,wBAAwB;wBAClC,aAAa,OAAO,KAAK;oBAC3B;oBACA;gBACF;gBAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,eAAe;oBAC3B,aAAa,OAAO,OAAO;gBAC7B;gBAEA,aAAa,KAAK,kBAAkB;YACtC,EAAE,OAAO,QAAQ;gBACf,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,wBAAwB;oBAClC,aAAa;gBACf;YACF;QACF;IACF;IAEA,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IAEA,SAAS,uBAAuB,MAA4C;QAC1E,gBAAgB;YACd,IAAI;gBACF,MAAM,SAAS,MAAM,CAAA,GAAA,gKAAA,CAAA,0BAAuB,AAAD,EAAE;gBAE7C,IAAI,CAAC,OAAO,OAAO,EAAE;oBACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gBAAgB;wBAC1B,aAAa,OAAO,KAAK;oBAC3B;oBACA;gBACF;gBAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,uBAAuB;oBACnC,aAAa;gBACf;gBAEA,uCAAuC;gBACvC,MAAM;YACR,EAAE,OAAO,QAAQ;gBACf,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gBAAgB;oBAC1B,aAAa;gBACf;YACF;QACF;IACF;IAEA,SAAS,uBAAuB,MAAuC;QACrE,IAAI,WAAW,YAAY;YACzB,cAAc;YACd,QAAQ;QACR,8DAA8D;QAChE;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACX,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAK;YACnC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,YAAY;gBAAE,UAAU;gBAAK,OAAO;YAAI;sBAExC,cAAA,8OAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,eAAe,eAAe,SAAS,QAAQ,4BAA4B;;;;;;0CAE9E,8OAAC;gCAAE,WAAU;0CACV,eAAe,eAAe,SAAS,QACpC,0CACA,eAAe,cACf,8CACA;;;;;;4BAIL,yBACC,8OAAC;gCAAI,WAAW,CAAC,2BAA2B,EAC1C,QAAQ,WAAW,GAAG,QAAQ,CAAC,YAAY,QAAQ,WAAW,GAAG,QAAQ,CAAC,YACtE,uCACA,sDACJ;0CACA,cAAA,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;;;;;;kCAMzC,8OAAC,2JAAA,CAAA,mBAAgB;wBACf,YAAY;wBACZ,MAAM;wBACN,gBAAgB;;;;;;kCAIlB,8OAAC,4JAAA,CAAA,oBAAiB;wBAChB,cAAc;wBACd,SAAS;wBACT,UAAU;;;;;;kCAGZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;;0CAEjB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CACb,eAAe,eAAe,SAAS,UAAU,2BACjD,eAAe,oBAAoB,4BACnC;;;;;;;;;;;;;;;;;oBAMN,eAAe,4BACd,8OAAC,uJAAA,CAAA,eAAY;wBACX,MAAM;wBACN,OAAO;wBACP,WAAW;wBACX,WAAW;wBACX,eAAe;wBACf,aAAa;wBACb,aAAa;wBACb,eAAe;;;;;6CAGjB,8OAAC,6JAAA,CAAA,qBAAkB;wBACjB,WAAW;wBACX,UAAU;;;;;;;;;;;;;;;;;;;;;;AAO1B", "debugId": null}}, {"offset": {"line": 4845, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/auth/AuthPageBackground.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { motion, useAnimation } from \"framer-motion\";\r\n\r\nexport default function AuthPageBackground() {\r\n  const [isClient, setIsClient] = useState(false);\r\n  const [isMobile, setIsMobile] = useState(false);\r\n\r\n  // Animation controls\r\n  const gradientControls = useAnimation();\r\n  const blob1Controls = useAnimation();\r\n  const blob2Controls = useAnimation();\r\n  const blob3Controls = useAnimation();\r\n  const lineControls = useAnimation();\r\n  const nodeControls = useAnimation();\r\n\r\n  // Only render on client side and detect mobile\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n    setIsMobile(window.innerWidth < 768);\r\n\r\n    const handleResize = () => {\r\n      setIsMobile(window.innerWidth < 768);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  // Setup animations\r\n  useEffect(() => {\r\n    if (isClient) {\r\n      // Gradient animation\r\n      gradientControls.start({\r\n        scale: 1.1,\r\n        transition: {\r\n          duration: 8,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          ease: \"easeInOut\",\r\n        },\r\n      });\r\n\r\n      // Blob animations\r\n      blob1Controls.start({\r\n        scale: 1.2,\r\n        x: 20,\r\n        transition: {\r\n          duration: 10,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          ease: \"easeInOut\",\r\n        },\r\n      });\r\n\r\n      blob2Controls.start({\r\n        scale: 1.15,\r\n        x: -15,\r\n        transition: {\r\n          duration: 12,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          ease: \"easeInOut\",\r\n        },\r\n      });\r\n\r\n      blob3Controls.start({\r\n        scale: 1.25,\r\n        y: 25,\r\n        transition: {\r\n          duration: 14,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          ease: \"easeInOut\",\r\n        },\r\n      });\r\n\r\n      // Line animation\r\n      lineControls.start({\r\n        scale: 1.1,\r\n        transition: {\r\n          duration: 6,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          ease: \"easeInOut\",\r\n        },\r\n      });\r\n\r\n      // Node animation\r\n      nodeControls.start({\r\n        scale: 1.3,\r\n        transition: {\r\n          duration: 4,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          ease: \"easeInOut\",\r\n        },\r\n      });\r\n    }\r\n  }, [isClient, gradientControls, blob1Controls, blob2Controls, blob3Controls, lineControls, nodeControls]);\r\n\r\n  return (\r\n    <div className=\"absolute inset-0 -z-10 overflow-hidden\">\r\n      {isClient && (\r\n        <>\r\n          {/* Main gradient background */}\r\n          <motion.div\r\n            animate={gradientControls}\r\n            className=\"absolute inset-0 opacity-40 dark:opacity-30\"\r\n            style={{\r\n              background: `radial-gradient(circle at 50% 50%,\r\n                var(--brand-gold) 0%,\r\n                rgba(var(--brand-gold-rgb), 0.3) 25%,\r\n                rgba(var(--brand-gold-rgb), 0.1) 50%,\r\n                rgba(0, 0, 255, 0.1) 75%,\r\n                rgba(0, 0, 255, 0.05) 100%)`,\r\n              filter: isMobile ? \"blur(60px)\" : \"blur(80px)\",\r\n            }}\r\n          />\r\n\r\n          {/* Top right blob */}\r\n          <motion.div\r\n            animate={blob1Controls}\r\n            className=\"absolute top-0 right-0 w-[500px] h-[500px] rounded-full bg-[var(--brand-gold-rgb)]/5 blur-3xl dark:bg-[var(--brand-gold-rgb)]/10 opacity-70\"\r\n          />\r\n\r\n          {/* Top left blob */}\r\n          <motion.div\r\n            animate={blob2Controls}\r\n            className=\"absolute -top-20 -left-20 w-[300px] h-[300px] rounded-full bg-blue-500/5 blur-3xl dark:bg-blue-500/10 opacity-70\"\r\n          />\r\n\r\n          {/* Bottom blob */}\r\n          <motion.div\r\n            animate={blob3Controls}\r\n            className=\"absolute bottom-0 left-1/4 w-[400px] h-[400px] rounded-full bg-purple-500/5 blur-3xl dark:bg-purple-500/10 opacity-60\"\r\n          />\r\n\r\n          {/* Circuit-like elements */}\r\n          <svg\r\n            className=\"absolute inset-0 w-full h-full opacity-10 dark:opacity-20 pointer-events-none\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n          >\r\n            <defs>\r\n              <filter id=\"glow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\r\n                <feGaussianBlur stdDeviation=\"2\" result=\"blur\" />\r\n                <feComposite\r\n                  in=\"SourceGraphic\"\r\n                  in2=\"blur\"\r\n                  operator=\"over\"\r\n                  result=\"glow\"\r\n                />\r\n              </filter>\r\n            </defs>\r\n\r\n            {/* Single circuit line */}\r\n            <motion.line\r\n              animate={lineControls}\r\n              x1=\"20%\"\r\n              y1=\"20%\"\r\n              x2=\"80%\"\r\n              y2=\"80%\"\r\n              stroke=\"var(--brand-gold)\"\r\n              strokeWidth=\"0.5\"\r\n              strokeOpacity=\"0.3\"\r\n              filter=\"url(#glow)\"\r\n            />\r\n\r\n            {/* Single circuit node */}\r\n            <motion.circle\r\n              animate={nodeControls}\r\n              cx=\"50%\"\r\n              cy=\"50%\"\r\n              r=\"2\"\r\n              fill=\"var(--brand-gold)\"\r\n              filter=\"url(#glow)\"\r\n            />\r\n          </svg>\r\n        </>\r\n      )}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,qBAAqB;IACrB,MAAM,mBAAmB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD;IACpC,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD;IACjC,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD;IACjC,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD;IACjC,MAAM,eAAe,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD;IAChC,MAAM,eAAe,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD;IAEhC,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;QACZ,YAAY,OAAO,UAAU,GAAG;QAEhC,MAAM,eAAe;YACnB,YAAY,OAAO,UAAU,GAAG;QAClC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,mBAAmB;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,qBAAqB;YACrB,iBAAiB,KAAK,CAAC;gBACrB,OAAO;gBACP,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,MAAM;gBACR;YACF;YAEA,kBAAkB;YAClB,cAAc,KAAK,CAAC;gBAClB,OAAO;gBACP,GAAG;gBACH,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,MAAM;gBACR;YACF;YAEA,cAAc,KAAK,CAAC;gBAClB,OAAO;gBACP,GAAG,CAAC;gBACJ,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,MAAM;gBACR;YACF;YAEA,cAAc,KAAK,CAAC;gBAClB,OAAO;gBACP,GAAG;gBACH,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,MAAM;gBACR;YACF;YAEA,iBAAiB;YACjB,aAAa,KAAK,CAAC;gBACjB,OAAO;gBACP,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,MAAM;gBACR;YACF;YAEA,iBAAiB;YACjB,aAAa,KAAK,CAAC;gBACjB,OAAO;gBACP,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,MAAM;gBACR;YACF;QACF;IACF,GAAG;QAAC;QAAU;QAAkB;QAAe;QAAe;QAAe;QAAc;KAAa;IAExG,qBACE,8OAAC;QAAI,WAAU;kBACZ,0BACC;;8BAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;oBACT,WAAU;oBACV,OAAO;wBACL,YAAY,CAAC;;;;;2CAKgB,CAAC;wBAC9B,QAAQ,WAAW,eAAe;oBACpC;;;;;;8BAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;oBACT,WAAU;;;;;;8BAIZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;oBACT,WAAU;;;;;;8BAIZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;oBACT,WAAU;;;;;;8BAIZ,8OAAC;oBACC,WAAU;oBACV,OAAM;;sCAEN,8OAAC;sCACC,cAAA,8OAAC;gCAAO,IAAG;gCAAO,GAAE;gCAAO,GAAE;gCAAO,OAAM;gCAAO,QAAO;;kDACtD,8OAAC;wCAAe,cAAa;wCAAI,QAAO;;;;;;kDACxC,8OAAC;wCACC,IAAG;wCACH,KAAI;wCACJ,UAAS;wCACT,QAAO;;;;;;;;;;;;;;;;;sCAMb,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,SAAS;4BACT,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,QAAO;4BACP,aAAY;4BACZ,eAAc;4BACd,QAAO;;;;;;sCAIT,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,SAAS;4BACT,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,MAAK;4BACL,QAAO;;;;;;;;;;;;;;;;;;;AAOrB", "debugId": null}}]}