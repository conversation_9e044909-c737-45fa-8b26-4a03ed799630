"use server";



import { createClient } from "@/utils/supabase/server";
import { pincodeSchema, citySchema } from "@/lib/schemas/locationSchemas";
import { getPincodeDetails } from "@/lib/actions/location";
import { getStateNameByCity, getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
import {
  getBusinessProfilesByCity,
  getBusinessIdsByCity,
  getProductCountByBusinessIds,
  getProductsByBusinessIds,
} from "@/lib/supabase/services/businessService";
import {
  BusinessSortBy,
  getSecureBusinessProfilesForDiscover,
  getSecureBusinessProfileIdsForDiscover,
} from "@/lib/actions/businessProfiles";
import {
  DiscoverSearchResult,
  getSortingColumn,
  getSortingDirection,
} from "./types";

// Action to find businesses or products based on location (pincode) and view type
export async function searchDiscoverData(params: {
  pincode?: string;
  city?: string;
  locality?: string | null;
  viewType: "cards" | "products";
  page?: number;
  limit?: number;
  sortBy?: BusinessSortBy;
  productType?: "physical" | "service" | null;
  category?: string | null;
}): Promise<{
  data?: DiscoverSearchResult;
  error?: string;
}> {
  const {
    pincode,
    city,
    locality,
    viewType,
    page = 1,
    limit = 20,
    sortBy = "created_desc",
    productType = null,
    category = null,
  } = params;

  // Check if we have either pincode or city
  if (!pincode && !city) {
    return { error: "Either pincode or city is required." };
  }

  // Initialize Supabase clients early
  const supabase = await createClient();
  

  let locationCity: string;
  let locationState: string;
  let validPincodes: string[] = [];

  // Handle pincode-based search
  if (pincode) {
    // 1. Validate Pincode
    const validatedPincode = pincodeSchema.safeParse({ pincode });
    if (!validatedPincode.success) {
      return { error: "Invalid Pincode format. Must be 6 digits." };
    }
    const validPincode = validatedPincode.data.pincode;

    // 2. Get Location Details
    const locationDetails = await getPincodeDetails(validPincode);
    if (
      locationDetails.error ||
      !locationDetails.city ||
      !locationDetails.state
    ) {
      return { error: locationDetails.error || "Pincode not found." };
    }

    locationCity = locationDetails.city;
    locationState = locationDetails.state;
    validPincodes = [validPincode];
  }
  // Handle city-based search
  else if (city) {
    // 1. Validate City
    const validatedCity = citySchema.safeParse({ city });
    if (!validatedCity.success) {
      return { error: "Invalid city name. Must be at least 2 characters." };
    }
    const validCity = validatedCity.data.city;

    // For city-based search, we'll directly filter by the city column
    // No need to get pincodes or other location details
    locationCity = validCity;

    // Set empty pincodes array to indicate we're doing a direct city search
    validPincodes = [];

    // Try to get the state for display purposes only
    const { stateName, error: stateError } = await getStateNameByCity(supabase, validCity);
    if (stateError) {
      console.error("Error getting state for city:", stateError);
      locationState = ""; // Default empty state on error
    } else {
      locationState = stateName || "";
    }
  } else {
    return { error: "Either pincode or city is required." };
  }

  // 3. Check Authentication
  const { user } = await getAuthenticatedUser(supabase);
  const isAuthenticated = !!user;

  try {
    // Add a small delay to prevent infinite loops
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 4. Build Base Query for Valid Businesses
    // Reference code removed

    // 5. Fetch Data Based on viewType
    if (viewType === "cards") {
      // Check if we're searching by city directly
      if (city && validPincodes.length === 0) {
        const { data: businessesData, count, error: businessesError } = await getBusinessProfilesByCity(
          supabase,
          city,
          "online",
          category,
          page,
          limit,
          getSortingColumn(sortBy),
          getSortingDirection(sortBy)
        );

        if (businessesError) {
          console.error("City Business Query Error:", businessesError);
          return { error: "Database error fetching businesses by city." };
        }

        return {
          data: {
            location: { city: locationCity, state: locationState },
            businesses: businessesData || [],
            isAuthenticated: isAuthenticated,
            totalCount: count || 0,
            hasMore: (count || 0) > page * limit,
            nextPage: (count || 0) > page * limit ? page + 1 : null,
          },
        };
      }

      // Use the secure method to fetch business profiles by pincode
      const {
        data: businessesData,
        count,
        error: businessesError,
      } = await getSecureBusinessProfilesForDiscover(
        validPincodes,
        locality,
        page,
        limit,
        sortBy
      );

      if (businessesError) {
        console.error("Search Discover (Cards) Error:", businessesError);
        return { error: businessesError };
      }

      const totalCount = count || 0;

      // Calculate if there are more pages
      const hasMore = totalCount > page * limit;
      const nextPage = hasMore ? page + 1 : null;

      // Map raw data to BusinessCardData, handling potential nulls
      const businesses =
        businessesData?.map((data) => {
          // Use the actual data from the database
          return {
            id: data.id,
            business_name: data.business_name ?? "",
            contact_email: "", // contact_email is not in BusinessProfilePublicData
            has_active_subscription: data.subscription_status === "active",
            trial_end_date: data.trial_end_date ?? null,
            created_at: data.created_at ?? undefined,
            updated_at: data.updated_at ?? undefined,
            logo_url: data.logo_url ?? "",
            member_name: data.member_name ?? "",
            title: data.title ?? "",
            address_line: data.address_line ?? "",
            city: data.city ?? "",
            state: data.state ?? "",
            pincode: data.pincode ?? "",
            locality: data.locality ?? "",
            phone: data.phone ?? "",
            business_category: data.business_category ?? "",
            instagram_url: data.instagram_url ?? "",
            facebook_url: data.facebook_url ?? "",
            whatsapp_number: data.whatsapp_number ?? "",
            about_bio: data.about_bio ?? "",
            status:
              data.status === "online"
                ? "online"
                : ("offline" as "online" | "offline"),
            business_slug: data.business_slug ?? "",

            // Include metrics data
            total_likes: data.total_likes ?? 0,
            total_subscriptions: data.total_subscriptions ?? 0,
            average_rating: data.average_rating ?? 0,

            // Use actual data if available, otherwise use defaults
            theme_color: data.theme_color ?? "#D4AF37",
            delivery_info: data.delivery_info ?? "",
            business_hours: data.business_hours,

            established_year: data.established_year ?? null,

            // Add default values for fields required by BusinessCardData but not in our query
            website_url: "",
            linkedin_url: "",
            twitter_url: "",
            youtube_url: "",
            call_number: "", // This field doesn't exist in the database
          };
        }) ?? [];

      return {
        data: {
          location: { city: locationCity, state: locationState },
          businesses: businesses,
          isAuthenticated: isAuthenticated,
          totalCount,
          hasMore,
          nextPage,
        },
      };
    } else {
      // viewType === 'products'
      let validBusinessIds: string[] = [];

      // Check if we're searching by city directly
      if (city && validPincodes.length === 0) {
        const { data: ids, error: businessIdsError } = await getBusinessIdsByCity(
          supabase,
          city,
          "online",
          category
        );

        if (businessIdsError) {
          console.error("City Business IDs Error:", businessIdsError);
          return { error: "Database error fetching business IDs by city." };
        }

        validBusinessIds = ids || [];
      } else {
        // First, get IDs of valid businesses using the secure method
        const { data: ids, error: validBusinessesError } =
          await getSecureBusinessProfileIdsForDiscover(
            validPincodes,
            locality,
            sortBy
          );

        if (validBusinessesError) {
          console.error(
            "Search Discover (Product IDs) Error:",
            validBusinessesError
          );
          return { error: validBusinessesError };
        }

        validBusinessIds = ids || [];
      }

      // This check is now handled inside the else block above

      if (!validBusinessIds || validBusinessIds.length === 0) {
        // No valid businesses found, return empty results
        return {
          data: {
            location: { city: locationCity, state: locationState },
            products: [],
            isAuthenticated: isAuthenticated,
            totalCount: 0,
            hasMore: false,
            nextPage: null,
          },
        };
      }

      const { count: totalProductCount, error: productCountError } = await getProductCountByBusinessIds(
        supabase,
        validBusinessIds || [],
        productType
      );

      if (productCountError) {
        console.error(
          "Search Discover (Product Count) Error:",
          productCountError
        );
        return { error: "Database error counting products." };
      }

      // Calculate pagination
      const totalCount = totalProductCount || 0;
      const hasMore = totalCount > page * limit;
      const nextPage = hasMore ? page + 1 : null;

      const { data: productsData, error: productsError } = await getProductsByBusinessIds(
        supabase,
        validBusinessIds || [],
        page,
        limit,
        getSortingColumn(sortBy),
        getSortingDirection(sortBy),
        productType
      );

      if (productsError) {
        console.error("Search Discover (Products) Error:", productsError);
        return { error: "Database error fetching nearby products." };
      }



      // Process products to match the full ProductServiceData structure + business_slug
      const products =
        productsData?.map((p: any) => {
          // Extract business_slug from the joined business_profiles
          let business_slug = null;

          if (p.business_profiles) {
            // Check if it's an array or an object
            if (
              Array.isArray(p.business_profiles) &&
              p.business_profiles.length > 0
            ) {
              business_slug = p.business_profiles[0].business_slug;
            } else if (
              typeof p.business_profiles === "object" &&
              p.business_profiles !== null
            ) {
              // Cast to a more specific type to handle different response formats
              business_slug = (
                p.business_profiles as { business_slug: string | null }
              ).business_slug;
            }
          }

          // Ensure we have a valid business_slug

          const product = {
            id: p.id,
            business_id: p.business_id ?? undefined,
            name: p.name ?? "",
            description: p.description ?? "",
            base_price: p.base_price ?? 0,
            discounted_price: p.discounted_price ?? null,
            product_type: (p.product_type as "physical" | "service") ?? "physical",
            is_available: p.is_available ?? true,
            image_url: p.image_url,
            created_at: p.created_at || undefined,
            updated_at: p.updated_at || undefined,
            business_slug: business_slug,
            featured_image_index: 0, // Default value for NearbyProduct
            images: [], // Default empty array for images
            slug: p.slug || undefined, // Use the fetched slug or undefined if not available
            // Add default/empty values for fields not fetched but required by ProductServiceData
          };
          return product;
        }) ?? [];

      return {
        data: {
          location: { city: locationCity, state: locationState },
          products: products,
          isAuthenticated: isAuthenticated,
          totalCount,
          hasMore,
          nextPage,
        },
      };
    }
  } catch (e) {
    console.error("Search Discover Exception:", e);
    return { error: "An unexpected error occurred during the search." };
  }
}