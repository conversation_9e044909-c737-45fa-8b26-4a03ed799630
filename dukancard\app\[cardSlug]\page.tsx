import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
import { updateBusinessProfile, getSecureBusinessProfileBySlug, getAdDataForPincode, getProductsForBusiness, getReviewsCountForBusiness } from "@/lib/supabase/services/businessService";
import { COLUMNS } from "@/lib/supabase/constants";

import { notFound } from "next/navigation";
import {
  ProductServiceData,
  ProductSortBy,
} from "@/app/(dashboard)/dashboard/business/products/actions";
import PublicCardPageClient from "./PublicCardPageClient";
import { AdData } from "@/types/ad";
import type { Metadata } from "next";


import { getBusinessGalleryImagesForTab } from "@/lib/actions/gallery";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import OfflineBusinessMessage from "./components/OfflineBusinessMessage";
import { createClient } from "@/utils/supabase/server";

const INITIAL_PRODUCTS_PAGE_SIZE = 20;

// Define a type for the profile data we need for these helper functions
type ProfilePlanData = {
  subscription_status: string | null;
  plan_id: string | null;
};

// Helper function to determine user plan
const getUserPlan = (
  profile: ProfilePlanData
): "free" | "basic" | "growth" | "pro" | "enterprise" | undefined => {
  // Simply return the plan_id from the subscription data
  switch (profile.plan_id) {
    case "free":
      return "free";
    case "growth":
      return "growth";
    case "pro":
      return "pro";
    case "enterprise":
      return "enterprise";
    case "basic":
      return "basic";
    default:
      return "free"; // Default to free if no plan_id specified
  }
};

// Helper function to determine if platform ads should be shown
const shouldShowPlatformAds = (): boolean => {
  // Show platform ads for all plans - Pro/Enterprise users can override with their own custom ads
  return true; // Always show platform ads as fallback
};

export default async function PublicCardPage({
  params,
}: {
  params: Promise<{ cardSlug: string }>;
}) {
  const { cardSlug } = await params;

  const supabase = await createClient()

  // Use the secure method to fetch the business profile
  const { data: businessProfile, error: profileError } =
    await getSecureBusinessProfileBySlug(supabase, cardSlug);

  if (profileError || !businessProfile) {
    console.error(`Error fetching profile for slug ${cardSlug}:`, profileError);
    notFound();
  }

  // Check if the profile is online
  if (businessProfile.status !== "online") {
    console.log(
      `Business profile ${cardSlug} is not online (status: ${businessProfile.status}).`
    );
    // Show offline message instead of 404
    return <OfflineBusinessMessage />;
  }

  // Check if required fields are missing but status is still online
  const requiredFields = [
    COLUMNS.MEMBER_NAME,
    COLUMNS.TITLE,
    COLUMNS.BUSINESS_NAME,
    COLUMNS.PHONE,
    COLUMNS.ADDRESS_LINE,
    COLUMNS.PINCODE,
    COLUMNS.CITY,
    COLUMNS.STATE,
    COLUMNS.LOCALITY,
    COLUMNS.CONTACT_EMAIL,
  ];

  const missingRequiredFields = requiredFields.filter(
    (field) =>
      !businessProfile[field as keyof typeof businessProfile] ||
      String(businessProfile[field as keyof typeof businessProfile]).trim() ===
        ""
  );

  if (missingRequiredFields.length > 0 && businessProfile.status === "online") {
    console.log(
      `Business profile ${cardSlug} is missing required fields but status is online, updating to offline. Missing fields: ${missingRequiredFields.join(
        ", "
      )}`
    );

    const { error: updateError } = await updateBusinessProfile(supabase, businessProfile.id, {
      status: "offline",
    });

    if (updateError) {
      console.error("Error forcing card offline:", updateError);
    }

    // Show offline message instead of 404
    return <OfflineBusinessMessage />;
  }

  // We no longer check subscription status, only if the business is online
  // The status field is the only thing that matters now

  // Determine user plan
  const userPlan = getUserPlan(businessProfile);
  let topAdData: AdData = null;

  if (shouldShowPlatformAds()) {
    try {
      const pincode = businessProfile.pincode || "999999";
      const { adData, error: adError } = await getAdDataForPincode(supabase, pincode);

      if (adData) {
        topAdData = {
          type: "custom",
          imageUrl: adData.ad_image_url,
          linkUrl: adData.ad_link_url,
        };
      } else {
        if (adError)
          console.error(`Error fetching ad for pincode ${pincode}:`, adError);
        topAdData = null;
      }
    } catch (adFetchError) {
      console.error(`Error fetching custom ad:`, adFetchError);
      topAdData = null;
    }
  }

  const defaultSortPreference: ProductSortBy = "created_desc";

  const { products: initialProducts, count: totalProductCount, error: productsError } = await getProductsForBusiness(supabase, businessProfile.id, INITIAL_PRODUCTS_PAGE_SIZE);

  if (productsError) {
    console.error(
      `Error fetching initial products for business ${businessProfile.id}:`,
      productsError
    );
  }

  const { user } = await getAuthenticatedUser(supabase);
  const isAuthenticated = !!user;
  const currentUserId = user?.id || null;

  const { count: totalReviews, error: reviewsCountError } = await getReviewsCountForBusiness(supabase, businessProfile.id);

  if (reviewsCountError) {
    console.error(
      `Error fetching reviews count for business ${businessProfile.id}:`,
      reviewsCountError
    );
  }

  const { images: galleryImages, totalCount: galleryTotalCount, error: galleryError } = await getBusinessGalleryImagesForTab(cardSlug);

  if (galleryError) {
    console.error(
      `Error fetching gallery images for business ${businessProfile.id}:`,
      galleryError
    );
  }

  const businessProfileWithReviews: BusinessCardData & { total_reviews: number } = {
    ...businessProfile,
    total_reviews: totalReviews || 0,
    // Ensure all required fields are properly typed and present
    phone: businessProfile.phone || "",
    city: businessProfile.city || "",
    state: businessProfile.state || "",
    pincode: businessProfile.pincode || "",
    locality: businessProfile.locality || "",
    address_line: businessProfile.address_line || "",
    business_name: businessProfile.business_name || "",
    contact_email: businessProfile.contact_email || "",
    member_name: businessProfile.member_name || "",
    status: businessProfile.status as "online" | "offline",
    title: businessProfile.title || "",
    business_category: businessProfile.business_category || "",
    custom_branding: businessProfile.custom_branding as any,
    custom_ads: businessProfile.custom_ads as any,





    whatsapp_number: businessProfile.whatsapp_number || undefined,
    instagram_url: businessProfile.instagram_url || undefined,
    facebook_url: businessProfile.facebook_url || undefined,
    about_bio: businessProfile.about_bio || undefined,
    business_slug: businessProfile.business_slug || undefined,
    theme_color: businessProfile.theme_color || undefined,
    delivery_info: businessProfile.delivery_info || undefined,
    total_likes: businessProfile.total_likes || 0,
    total_subscriptions: businessProfile.total_subscriptions || 0,
    average_rating: businessProfile.average_rating || undefined,
    business_hours: businessProfile.business_hours || null,
    trial_end_date: businessProfile.trial_end_date || null,
    created_at: businessProfile.created_at || undefined,
    updated_at: businessProfile.updated_at || undefined,
    established_year: businessProfile.established_year || null,


  };

  return (
    <div className="min-h-screen flex flex-col">
      <PublicCardPageClient
        businessProfile={businessProfileWithReviews}
        initialProducts={(initialProducts as unknown as ProductServiceData[]) ?? []}
        totalProductCount={totalProductCount ?? 0}
        defaultSortPreference={defaultSortPreference}
        isAuthenticated={isAuthenticated}
        currentUserId={currentUserId}
        userPlan={userPlan}
        topAdData={topAdData}
        galleryImages={galleryImages ?? []}
        galleryTotalCount={galleryTotalCount ?? 0}
      />
    </div>
  );
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ cardSlug: string }>;
}): Promise<Metadata> {
  const { cardSlug } = await params;
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";
  const pageUrl = `${siteUrl}/${cardSlug}`;

  const supabase = await createClient()

  const { data: businessProfile, error: profileError } =
    await getSecureBusinessProfileBySlug(supabase, cardSlug);

  if (profileError || !businessProfile) {
    notFound();
  }

  const businessName = businessProfile.business_name || "Business";

  let baseTitle = businessName;
  let fullAddress = "";

  if (businessProfile.status === "online") {
    const addressComponents = [
      businessProfile.address_line,
      businessProfile.city,
      businessProfile.state,
      businessProfile.pincode,
    ].filter(Boolean);

    fullAddress = addressComponents.join(", ");

    if (fullAddress) {
      baseTitle = `${businessName} - ${fullAddress}`;
    }
  }

  let description = "";

  if (businessProfile.status === "online") {
    description =
      `Visit ${businessName}'s digital business card on Dukancard. ${
        businessProfile.about_bio ? businessProfile.about_bio + " " : ""
      }Find products, services, contact info, and location${
        fullAddress ? ` at ${fullAddress}` : ""
      }.`.trim();
  } else {
    description = `${businessName}'s digital business card on Dukancard is currently offline. Check back later or discover other businesses nearby.`;
  }
  const ogImage = businessProfile.logo_url || `${siteUrl}/opengraph-image.png`;

  const keywords = [
    businessName,
    businessProfile?.business_category,
    businessProfile?.city,
    businessProfile?.state,
    "digital business card",
    "online storefront",
    "shop near me",
    "Dukancard",
  ].filter(Boolean);

  const schema = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    name: businessName,
    description: businessProfile.about_bio || description,
    url: pageUrl,
    image: businessProfile.logo_url,
    telephone: businessProfile?.phone,
    address: {
      "@type": "PostalAddress",
      streetAddress: businessProfile.address_line,
      addressLocality: businessProfile.city,
      addressRegion: businessProfile.state,
      postalCode: businessProfile.pincode,
      addressCountry: "IN",
    },
  };

  return {
    title: baseTitle,
    description,
    keywords: keywords.filter((k): k is string => k !== null),
    alternates: {
      canonical: `/${cardSlug}`,
    },
    openGraph: {
      title: baseTitle,
      description,
      url: pageUrl,
      siteName: "Dukancard",
      type: "profile",
      locale: "en_IN",
      images: [
        {
          url: ogImage,
          alt: `${businessName} - Digital Business Card`,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: baseTitle,
      description,
      images: [ogImage],
    },
    other: {
      "application-ld+json": JSON.stringify(schema),
    },
  };
}
