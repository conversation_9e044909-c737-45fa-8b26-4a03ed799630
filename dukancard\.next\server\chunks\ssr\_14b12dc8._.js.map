{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-card text-card-foreground\",\r\n        destructive:\r\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Alert({\r\n  className,\r\n  variant,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert\"\r\n      role=\"alert\"\r\n      className={cn(alertVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-title\"\r\n      className={cn(\r\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-description\"\r\n      className={cn(\r\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/breadcrumb.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { ChevronRight, MoreHorizontal } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Breadcrumb({ ...props }: React.ComponentProps<\"nav\">) {\r\n  return <nav aria-label=\"breadcrumb\" data-slot=\"breadcrumb\" {...props} />\r\n}\r\n\r\nfunction BreadcrumbList({ className, ...props }: React.ComponentProps<\"ol\">) {\r\n  return (\r\n    <ol\r\n      data-slot=\"breadcrumb-list\"\r\n      className={cn(\r\n        \"text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-item\"\r\n      className={cn(\"inline-flex items-center gap-1.5\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbLink({\r\n  asChild,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"breadcrumb-link\"\r\n      className={cn(\"hover:text-foreground transition-colors\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbPage({ className, ...props }: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-page\"\r\n      role=\"link\"\r\n      aria-disabled=\"true\"\r\n      aria-current=\"page\"\r\n      className={cn(\"text-foreground font-normal\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbSeparator({\r\n  children,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-separator\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"[&>svg]:size-3.5\", className)}\r\n      {...props}\r\n    >\r\n      {children ?? <ChevronRight />}\r\n    </li>\r\n  )\r\n}\r\n\r\nfunction BreadcrumbEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-ellipsis\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"flex size-9 items-center justify-center\", className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontal className=\"size-4\" />\r\n      <span className=\"sr-only\">More</span>\r\n    </span>\r\n  )\r\n}\r\n\r\nexport {\r\n  Breadcrumb,\r\n  BreadcrumbList,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n  BreadcrumbEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AAAA;AAEA;;;;;AAEA,SAAS,WAAW,EAAE,GAAG,OAAoC;IAC3D,qBAAO,8OAAC;QAAI,cAAW;QAAa,aAAU;QAAc,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,OAAO,EACP,SAAS,EACT,GAAG,OAGJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAqC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER,0BAAY,8OAAC,sNAAA,CAAA,eAAY;;;;;;;;;;AAGhC;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;0BAET,8OAAC,gNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/carousel.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport useEmblaCarousel, {\r\n  type UseEmblaCarouselType,\r\n} from \"embla-carousel-react\"\r\nimport { ArrowLeft, ArrowRight } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\n\r\ntype CarouselApi = UseEmblaCarouselType[1]\r\ntype UseCarouselParameters = Parameters<typeof useEmblaCarousel>\r\ntype CarouselOptions = UseCarouselParameters[0]\r\ntype CarouselPlugin = UseCarouselParameters[1]\r\n\r\ntype CarouselProps = {\r\n  opts?: CarouselOptions\r\n  plugins?: CarouselPlugin\r\n  orientation?: \"horizontal\" | \"vertical\"\r\n  setApi?: (_api: CarouselApi) => void\r\n}\r\n\r\ntype CarouselContextProps = {\r\n  carouselRef: ReturnType<typeof useEmblaCarousel>[0]\r\n  api: ReturnType<typeof useEmblaCarousel>[1]\r\n  scrollPrev: () => void\r\n  scrollNext: () => void\r\n  canScrollPrev: boolean\r\n  canScrollNext: boolean\r\n} & CarouselProps\r\n\r\nconst CarouselContext = React.createContext<CarouselContextProps | null>(null)\r\n\r\nfunction useCarousel() {\r\n  const context = React.useContext(CarouselContext)\r\n\r\n  if (!context) {\r\n    throw new Error(\"useCarousel must be used within a <Carousel />\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nfunction Carousel({\r\n  orientation = \"horizontal\",\r\n  opts,\r\n  setApi,\r\n  plugins,\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & CarouselProps) {\r\n  const [carouselRef, api] = useEmblaCarousel(\r\n    {\r\n      ...opts,\r\n      axis: orientation === \"horizontal\" ? \"x\" : \"y\",\r\n    },\r\n    plugins\r\n  )\r\n  const [canScrollPrev, setCanScrollPrev] = React.useState(false)\r\n  const [canScrollNext, setCanScrollNext] = React.useState(false)\r\n\r\n  const onSelect = React.useCallback((api: CarouselApi) => {\r\n    if (!api) return\r\n    setCanScrollPrev(api.canScrollPrev())\r\n    setCanScrollNext(api.canScrollNext())\r\n  }, [])\r\n\r\n  const scrollPrev = React.useCallback(() => {\r\n    api?.scrollPrev()\r\n  }, [api])\r\n\r\n  const scrollNext = React.useCallback(() => {\r\n    api?.scrollNext()\r\n  }, [api])\r\n\r\n  const handleKeyDown = React.useCallback(\r\n    (event: React.KeyboardEvent<HTMLDivElement>) => {\r\n      if (event.key === \"ArrowLeft\") {\r\n        event.preventDefault()\r\n        scrollPrev()\r\n      } else if (event.key === \"ArrowRight\") {\r\n        event.preventDefault()\r\n        scrollNext()\r\n      }\r\n    },\r\n    [scrollPrev, scrollNext]\r\n  )\r\n\r\n  React.useEffect(() => {\r\n    if (!api || !setApi) return\r\n    setApi(api)\r\n  }, [api, setApi])\r\n\r\n  React.useEffect(() => {\r\n    if (!api) return\r\n    onSelect(api)\r\n    api.on(\"reInit\", onSelect)\r\n    api.on(\"select\", onSelect)\r\n\r\n    return () => {\r\n      api?.off(\"select\", onSelect)\r\n    }\r\n  }, [api, onSelect])\r\n\r\n  return (\r\n    <CarouselContext.Provider\r\n      value={{\r\n        carouselRef,\r\n        api: api,\r\n        opts,\r\n        orientation:\r\n          orientation || (opts?.axis === \"y\" ? \"vertical\" : \"horizontal\"),\r\n        scrollPrev,\r\n        scrollNext,\r\n        canScrollPrev,\r\n        canScrollNext,\r\n      }}\r\n    >\r\n      <div\r\n        onKeyDownCapture={handleKeyDown}\r\n        className={cn(\"relative\", className)}\r\n        role=\"region\"\r\n        aria-roledescription=\"carousel\"\r\n        data-slot=\"carousel\"\r\n        {...props}\r\n      >\r\n        {children}\r\n      </div>\r\n    </CarouselContext.Provider>\r\n  )\r\n}\r\n\r\nfunction CarouselContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  const { carouselRef, orientation } = useCarousel()\r\n\r\n  return (\r\n    <div\r\n      ref={carouselRef}\r\n      className=\"overflow-hidden\"\r\n      data-slot=\"carousel-content\"\r\n    >\r\n      <div\r\n        className={cn(\r\n          \"flex\",\r\n          orientation === \"horizontal\" ? \"-ml-4\" : \"-mt-4 flex-col\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction CarouselItem({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  const { orientation } = useCarousel()\r\n\r\n  return (\r\n    <div\r\n      role=\"group\"\r\n      aria-roledescription=\"slide\"\r\n      data-slot=\"carousel-item\"\r\n      className={cn(\r\n        \"min-w-0 shrink-0 grow-0 basis-full\",\r\n        orientation === \"horizontal\" ? \"pl-4\" : \"pt-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CarouselPrevious({\r\n  className,\r\n  variant = \"outline\",\r\n  size = \"icon\",\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { orientation, scrollPrev, canScrollPrev } = useCarousel()\r\n\r\n  return (\r\n    <Button\r\n      data-slot=\"carousel-previous\"\r\n      variant={variant}\r\n      size={size}\r\n      className={cn(\r\n        \"absolute size-8 rounded-full\",\r\n        orientation === \"horizontal\"\r\n          ? \"top-1/2 -left-12 -translate-y-1/2\"\r\n          : \"-top-12 left-1/2 -translate-x-1/2 rotate-90\",\r\n        className\r\n      )}\r\n      disabled={!canScrollPrev}\r\n      onClick={scrollPrev}\r\n      {...props}\r\n    >\r\n      <ArrowLeft />\r\n      <span className=\"sr-only\">Previous slide</span>\r\n    </Button>\r\n  )\r\n}\r\n\r\nfunction CarouselNext({\r\n  className,\r\n  variant = \"outline\",\r\n  size = \"icon\",\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { orientation, scrollNext, canScrollNext } = useCarousel()\r\n\r\n  return (\r\n    <Button\r\n      data-slot=\"carousel-next\"\r\n      variant={variant}\r\n      size={size}\r\n      className={cn(\r\n        \"absolute size-8 rounded-full\",\r\n        orientation === \"horizontal\"\r\n          ? \"top-1/2 -right-12 -translate-y-1/2\"\r\n          : \"-bottom-12 left-1/2 -translate-x-1/2 rotate-90\",\r\n        className\r\n      )}\r\n      disabled={!canScrollNext}\r\n      onClick={scrollNext}\r\n      {...props}\r\n    >\r\n      <ArrowRight />\r\n      <span className=\"sr-only\">Next slide</span>\r\n    </Button>\r\n  )\r\n}\r\n\r\nexport {\r\n  type CarouselApi,\r\n  Carousel,\r\n  CarouselContent,\r\n  CarouselItem,\r\n  CarouselPrevious,\r\n  CarouselNext,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AAGA;AAAA;AAEA;AACA;AATA;;;;;;;AAgCA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAA+B;AAEzE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IAEjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,SAAS,EAChB,cAAc,YAAY,EAC1B,IAAI,EACJ,MAAM,EACN,OAAO,EACP,SAAS,EACT,QAAQ,EACR,GAAG,OACyC;IAC5C,MAAM,CAAC,aAAa,IAAI,GAAG,CAAA,GAAA,sLAAA,CAAA,UAAgB,AAAD,EACxC;QACE,GAAG,IAAI;QACP,MAAM,gBAAgB,eAAe,MAAM;IAC7C,GACA;IAEF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEzD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE,CAAC;QAClC,IAAI,CAAC,KAAK;QACV,iBAAiB,IAAI,aAAa;QAClC,iBAAiB,IAAI,aAAa;IACpC,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QACnC,KAAK;IACP,GAAG;QAAC;KAAI;IAER,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QACnC,KAAK;IACP,GAAG;QAAC;KAAI;IAER,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EACpC,CAAC;QACC,IAAI,MAAM,GAAG,KAAK,aAAa;YAC7B,MAAM,cAAc;YACpB;QACF,OAAO,IAAI,MAAM,GAAG,KAAK,cAAc;YACrC,MAAM,cAAc;YACpB;QACF;IACF,GACA;QAAC;QAAY;KAAW;IAG1B,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,CAAC,OAAO,CAAC,QAAQ;QACrB,OAAO;IACT,GAAG;QAAC;QAAK;KAAO;IAEhB,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,CAAC,KAAK;QACV,SAAS;QACT,IAAI,EAAE,CAAC,UAAU;QACjB,IAAI,EAAE,CAAC,UAAU;QAEjB,OAAO;YACL,KAAK,IAAI,UAAU;QACrB;IACF,GAAG;QAAC;QAAK;KAAS;IAElB,qBACE,8OAAC,gBAAgB,QAAQ;QACvB,OAAO;YACL;YACA,KAAK;YACL;YACA,aACE,eAAe,CAAC,MAAM,SAAS,MAAM,aAAa,YAAY;YAChE;YACA;YACA;YACA;QACF;kBAEA,cAAA,8OAAC;YACC,kBAAkB;YAClB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;YAC1B,MAAK;YACL,wBAAqB;YACrB,aAAU;YACT,GAAG,KAAK;sBAER;;;;;;;;;;;AAIT;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;IAErC,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,aAAU;kBAEV,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,QACA,gBAAgB,eAAe,UAAU,kBACzC;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,MAAM,EAAE,WAAW,EAAE,GAAG;IAExB,qBACE,8OAAC;QACC,MAAK;QACL,wBAAqB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sCACA,gBAAgB,eAAe,SAAS,QACxC;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,MAAM,EACb,GAAG,OACiC;IACpC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEnD,qBACE,8OAAC,2HAAA,CAAA,SAAM;QACL,aAAU;QACV,SAAS;QACT,MAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gCACA,gBAAgB,eACZ,sCACA,+CACJ;QAEF,UAAU,CAAC;QACX,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC,gNAAA,CAAA,YAAS;;;;;0BACV,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,MAAM,EACb,GAAG,OACiC;IACpC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEnD,qBACE,8OAAC,2HAAA,CAAA,SAAM;QACL,aAAU;QACV,SAAS;QACT,MAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gCACA,gBAAgB,eACZ,uCACA,kDACJ;QAEF,UAAU,CAAC;QACX,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC,kNAAA,CAAA,aAAU;;;;;0BACX,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 444, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/icons/WhatsAppIcon.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\n// Directly use React.SVGProps for type safety without an empty interface\r\nconst WhatsAppIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (\r\n  <svg\r\n    xmlns=\"http://www.w3.org/2000/svg\"\r\n    viewBox=\"0 0 24 24\"\r\n    fill=\"currentColor\" // Reverted to fill\r\n    {...props} // Spread any additional props like className, style, etc.\r\n  >\r\n    <path\r\n      d={\r\n        \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z\"\r\n      }\r\n    />\r\n  </svg>\r\n);\r\n\r\nexport default WhatsAppIcon;\r\n"], "names": [], "mappings": ";;;;;AAEA,yEAAyE;AACzE,MAAM,eAAwD,CAAC,sBAC7D,8OAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK,eAAe,mBAAmB;;QACtC,GAAG,KAAK;kBAET,cAAA,8OAAC;YACC,GACE;;;;;;;;;;;uCAMO", "debugId": null}}, {"offset": {"line": 475, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/product/components/WhatsAppButton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport WhatsAppIcon from \"@/app/components/icons/WhatsAppIcon\";\r\nimport { useState } from \"react\";\r\n\r\ninterface WhatsAppButtonProps {\r\n  whatsappNumber: string;\r\n  productName: string;\r\n  businessName: string;\r\n  productUrl: string;\r\n}\r\n\r\nexport default function WhatsAppButton({\r\n  whatsappNumber,\r\n  productName,\r\n  businessName,\r\n  productUrl,\r\n}: WhatsAppButtonProps) {\r\n  const [isHovered, setIsHovered] = useState(false);\r\n\r\n  // Format WhatsApp number (remove any non-digit characters)\r\n  const formattedNumber = whatsappNumber.replace(/\\D/g, \"\");\r\n\r\n  // Create default message with product URL\r\n  const defaultMessage = encodeURIComponent(\r\n    `Hi ${businessName}, I'm interested in your product \"${productName}\" that I saw on your Dukancard (${productUrl}). Can you provide more information?`\r\n  );\r\n\r\n  // Create WhatsApp URL\r\n  const whatsappUrl = `https://wa.me/${formattedNumber}?text=${defaultMessage}`;\r\n\r\n  return (\r\n    <div className=\"relative group\">\r\n      {/* Button glow effect */}\r\n      <div className=\"absolute -inset-0.5 bg-gradient-to-r from-green-500/30 to-green-600/30 rounded-xl blur-md opacity-75 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n\r\n      <Button\r\n        className=\"relative w-full bg-gradient-to-br from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium flex items-center justify-center gap-3 py-6 rounded-xl border border-green-500/20 transition-all duration-300\"\r\n        onMouseEnter={() => setIsHovered(true)}\r\n        onMouseLeave={() => setIsHovered(false)}\r\n        onClick={() => window.open(whatsappUrl, \"_blank\")}\r\n      >\r\n        {/* Shimmer effect */}\r\n        <div className=\"absolute inset-0 w-full h-full overflow-hidden rounded-xl\">\r\n          <div className=\"absolute inset-0 w-[200%] h-full bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-shimmer\"></div>\r\n        </div>\r\n\r\n        <WhatsAppIcon className={`w-5 h-5 ${isHovered ? 'animate-spin-slow' : ''}`} />\r\n        <span className=\"text-base font-semibold tracking-wide\">WhatsApp</span>\r\n      </Button>\r\n    </div>\r\n  );\r\n}\r\n\r\n// Fallback button for when WhatsApp is not available\r\nexport function WhatsAppButtonDisabled() {\r\n  return (\r\n    <div className=\"relative group\">\r\n      {/* Button glow effect (dimmed) */}\r\n      <div className=\"absolute -inset-0.5 bg-gradient-to-r from-neutral-400/20 to-neutral-600/20 rounded-xl blur-md opacity-50 transition-opacity duration-300\"></div>\r\n\r\n      <Button\r\n        className=\"relative w-full bg-gradient-to-br from-neutral-500 to-neutral-600 text-white font-medium flex items-center justify-center gap-3 py-6 cursor-not-allowed opacity-80 rounded-xl border border-neutral-500/10 transition-all duration-300\"\r\n        disabled\r\n      >\r\n        <WhatsAppIcon className=\"w-5 h-5\" />\r\n        <span className=\"text-base font-semibold tracking-wide\">WhatsApp Unavailable</span>\r\n      </Button>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAae,SAAS,eAAe,EACrC,cAAc,EACd,WAAW,EACX,YAAY,EACZ,UAAU,EACU;IACpB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,2DAA2D;IAC3D,MAAM,kBAAkB,eAAe,OAAO,CAAC,OAAO;IAEtD,0CAA0C;IAC1C,MAAM,iBAAiB,mBACrB,CAAC,GAAG,EAAE,aAAa,kCAAkC,EAAE,YAAY,gCAAgC,EAAE,WAAW,oCAAoC,CAAC;IAGvJ,sBAAsB;IACtB,MAAM,cAAc,CAAC,cAAc,EAAE,gBAAgB,MAAM,EAAE,gBAAgB;IAE7E,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC,2HAAA,CAAA,SAAM;gBACL,WAAU;gBACV,cAAc,IAAM,aAAa;gBACjC,cAAc,IAAM,aAAa;gBACjC,SAAS,IAAM,OAAO,IAAI,CAAC,aAAa;;kCAGxC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;kCAGjB,8OAAC,2IAAA,CAAA,UAAY;wBAAC,WAAW,CAAC,QAAQ,EAAE,YAAY,sBAAsB,IAAI;;;;;;kCAC1E,8OAAC;wBAAK,WAAU;kCAAwC;;;;;;;;;;;;;;;;;;AAIhE;AAGO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC,2HAAA,CAAA,SAAM;gBACL,WAAU;gBACV,QAAQ;;kCAER,8OAAC,2IAAA,CAAA,UAAY;wBAAC,WAAU;;;;;;kCACxB,8OAAC;wBAAK,WAAU;kCAAwC;;;;;;;;;;;;;;;;;;AAIhE", "debugId": null}}, {"offset": {"line": 603, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/product/components/PhoneButton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Phone } from \"lucide-react\";\r\nimport { useState } from \"react\";\r\n\r\ninterface PhoneButtonProps {\r\n  phoneNumber: string;\r\n  _businessName: string; // Prefix with underscore to indicate it's intentionally unused\r\n}\r\n\r\nexport default function PhoneButton({\r\n  phoneNumber,\r\n  _businessName, // Prefix with underscore to indicate it's intentionally unused\r\n}: PhoneButtonProps) {\r\n  const [isHovered, setIsHovered] = useState(false);\r\n\r\n  // Format phone number (remove any non-digit characters)\r\n  const formattedNumber = phoneNumber.replace(/\\D/g, \"\");\r\n\r\n  return (\r\n    <div className=\"relative group\">\r\n      {/* Button glow effect */}\r\n      <div className=\"absolute -inset-0.5 bg-gradient-to-r from-blue-500/30 to-blue-600/30 rounded-xl blur-md opacity-75 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n\r\n      <Button\r\n        className=\"relative w-full bg-gradient-to-br from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium flex items-center justify-center gap-3 py-6 rounded-xl border border-blue-500/20 transition-all duration-300\"\r\n        onMouseEnter={() => setIsHovered(true)}\r\n        onMouseLeave={() => setIsHovered(false)}\r\n        onClick={() => window.open(`tel:${formattedNumber}`, \"_blank\")}\r\n      >\r\n        {/* Shimmer effect */}\r\n        <div className=\"absolute inset-0 w-full h-full overflow-hidden rounded-xl\">\r\n          <div className=\"absolute inset-0 w-[200%] h-full bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-shimmer\"></div>\r\n        </div>\r\n\r\n        <Phone className={`w-5 h-5 ${isHovered ? 'animate-pulse' : ''}`} />\r\n        <span className=\"text-base font-semibold tracking-wide\">Call</span>\r\n      </Button>\r\n    </div>\r\n  );\r\n}\r\n\r\n// Fallback button for when phone is not available\r\nexport function PhoneButtonDisabled() {\r\n  return (\r\n    <div className=\"relative group\">\r\n      {/* Button glow effect (dimmed) */}\r\n      <div className=\"absolute -inset-0.5 bg-gradient-to-r from-neutral-400/20 to-neutral-600/20 rounded-xl blur-md opacity-50 transition-opacity duration-300\"></div>\r\n\r\n      <Button\r\n        className=\"relative w-full bg-gradient-to-br from-neutral-500 to-neutral-600 text-white font-medium flex items-center justify-center gap-3 py-6 cursor-not-allowed opacity-80 rounded-xl border border-neutral-500/10 transition-all duration-300\"\r\n        disabled\r\n      >\r\n        <Phone className=\"w-5 h-5\" />\r\n        <span className=\"text-base font-semibold tracking-wide\">Call Unavailable</span>\r\n      </Button>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAWe,SAAS,YAAY,EAClC,WAAW,EACX,aAAa,EACI;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,wDAAwD;IACxD,MAAM,kBAAkB,YAAY,OAAO,CAAC,OAAO;IAEnD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC,2HAAA,CAAA,SAAM;gBACL,WAAU;gBACV,cAAc,IAAM,aAAa;gBACjC,cAAc,IAAM,aAAa;gBACjC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,IAAI,EAAE,iBAAiB,EAAE;;kCAGrD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;kCAGjB,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAW,CAAC,QAAQ,EAAE,YAAY,kBAAkB,IAAI;;;;;;kCAC/D,8OAAC;wBAAK,WAAU;kCAAwC;;;;;;;;;;;;;;;;;;AAIhE;AAGO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC,2HAAA,CAAA,SAAM;gBACL,WAAU;gBACV,QAAQ;;kCAER,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,8OAAC;wBAAK,WAAU;kCAAwC;;;;;;;;;;;;;;;;;;AAIhE", "debugId": null}}, {"offset": {"line": 727, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/product/components/BuyNowButton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { ShoppingCart } from \"lucide-react\";\r\nimport { useState } from \"react\";\r\n\r\nexport default function BuyNowButton() {\r\n  const [isHovered, setIsHovered] = useState(false);\r\n\r\n  return (\r\n    <div className=\"relative group\">\r\n      {/* Button glow effect */}\r\n      <div className=\"absolute -inset-0.5 bg-gradient-to-r from-[var(--brand-gold)]/30 to-[var(--brand-gold)]/20 rounded-xl blur-md opacity-75 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n\r\n      <Button\r\n        className=\"relative w-full bg-gradient-to-br from-[var(--brand-gold)] to-[var(--brand-gold-dark)] hover:from-[var(--brand-gold-dark)] hover:to-[var(--brand-gold)] text-[var(--brand-gold-foreground)] font-medium flex items-center justify-center gap-3 py-6 cursor-not-allowed opacity-90 rounded-xl border border-[var(--brand-gold)]/20 transition-all duration-300\"\r\n        onMouseEnter={() => setIsHovered(true)}\r\n        onMouseLeave={() => setIsHovered(false)}\r\n        disabled\r\n      >\r\n        {/* Shimmer effect */}\r\n        <div className=\"absolute inset-0 w-full h-full overflow-hidden rounded-xl\">\r\n          <div className=\"absolute inset-0 w-[200%] h-full bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-shimmer\"></div>\r\n        </div>\r\n\r\n        <ShoppingCart className={`w-5 h-5 ${isHovered ? 'animate-pulse' : ''}`} />\r\n        <span className=\"text-base font-semibold tracking-wide\">Buy Now</span>\r\n      </Button>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC,2HAAA,CAAA,SAAM;gBACL,WAAU;gBACV,cAAc,IAAM,aAAa;gBACjC,cAAc,IAAM,aAAa;gBACjC,QAAQ;;kCAGR,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;kCAGjB,8OAAC,sNAAA,CAAA,eAAY;wBAAC,WAAW,CAAC,QAAQ,EAAE,YAAY,kBAAkB,IAAI;;;;;;kCACtE,8OAAC;wBAAK,WAAU;kCAAwC;;;;;;;;;;;;;;;;;;AAIhE", "debugId": null}}, {"offset": {"line": 805, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/product/hooks/usePinchZoom.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, RefObject } from \"react\";\r\n\r\ninterface PinchZoomOptions {\r\n  minScale?: number;\r\n  maxScale?: number;\r\n  scaleStep?: number;\r\n}\r\n\r\nexport function usePinchZoom(\r\n  elementRef: RefObject<HTMLElement | null>,\r\n  setScale: (_scale: number | ((_prev: number) => number)) => void,\r\n  currentScale: number,\r\n  options: PinchZoomOptions = {}\r\n) {\r\n  const {\r\n    minScale = 0.5,\r\n    maxScale = 3\r\n    // scaleStep is unused but kept in the interface for future use\r\n  } = options;\r\n\r\n  // Track touch points for pinch gesture\r\n  // We don't directly use touchCache but need the setter\r\n  const [, setTouchCache] = useState<Touch[]>([]);\r\n  const [initialDistance, setInitialDistance] = useState<number | null>(null);\r\n  const [initialScale, setInitialScale] = useState<number>(1);\r\n  const [isPinching, setIsPinching] = useState(false);\r\n  const [lastTouchTime, setLastTouchTime] = useState(0);\r\n\r\n  // Calculate distance between two touch points\r\n  const getDistance = (touches: Touch[]): number => {\r\n    if (touches.length < 2) return 0;\r\n\r\n    const dx = touches[0].clientX - touches[1].clientX;\r\n    const dy = touches[0].clientY - touches[1].clientY;\r\n    return Math.sqrt(dx * dx + dy * dy);\r\n  };\r\n\r\n  useEffect(() => {\r\n    const element = elementRef.current;\r\n    if (!element) return;\r\n\r\n    // Touch start handler\r\n    const handleTouchStart = (e: TouchEvent) => {\r\n      const now = Date.now();\r\n      setLastTouchTime(now);\r\n\r\n      const touches = Array.from(e.touches);\r\n      setTouchCache(touches);\r\n\r\n      if (touches.length === 2) {\r\n        // Start of pinch - store initial distance and scale\r\n        setInitialDistance(getDistance(touches));\r\n        setInitialScale(currentScale);\r\n        setIsPinching(true);\r\n        e.preventDefault();\r\n      }\r\n    };\r\n\r\n    // Touch move handler for pinch zoom\r\n    const handleTouchMove = (e: TouchEvent) => {\r\n      const touches = Array.from(e.touches);\r\n\r\n      // Throttle touch move events for better performance\r\n      const now = Date.now();\r\n      if (now - lastTouchTime < 16) { // ~60fps\r\n        return;\r\n      }\r\n      setLastTouchTime(now);\r\n\r\n      if (touches.length === 2 && initialDistance && initialDistance > 0) {\r\n        // Calculate new scale based on change in distance\r\n        const currentDistance = getDistance(touches);\r\n        const scaleFactor = currentDistance / initialDistance;\r\n        const newScale = initialScale * scaleFactor;\r\n\r\n        // Apply scale within bounds with smoother transition\r\n        const smoothScale = currentScale + (Math.min(Math.max(newScale, minScale), maxScale) - currentScale) * 0.3;\r\n\r\n        // Update scale - progressive centering will be handled by the effect in the parent component\r\n        setScale(smoothScale);\r\n\r\n        // Prevent default to avoid browser gestures interfering\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n      }\r\n    };\r\n\r\n    // Touch end handler\r\n    const handleTouchEnd = (e: TouchEvent) => {\r\n      const touches = Array.from(e.touches);\r\n      setTouchCache(touches);\r\n\r\n      if (touches.length < 2) {\r\n        // End of pinch\r\n        setInitialDistance(null);\r\n        setIsPinching(false);\r\n      }\r\n\r\n      // Double tap detection (for mobile)\r\n      const now = Date.now();\r\n      const timeDiff = now - lastTouchTime;\r\n\r\n      if (timeDiff < 300 && touches.length === 0 && !isPinching) {\r\n        // Double tap detected - toggle zoom\r\n        if (currentScale > 1) {\r\n          setScale(1);\r\n        } else {\r\n          setScale(2);\r\n        }\r\n      }\r\n\r\n      setLastTouchTime(now);\r\n    };\r\n\r\n    // Add event listeners with proper options\r\n    element.addEventListener(\"touchstart\", handleTouchStart, { passive: false });\r\n    element.addEventListener(\"touchmove\", handleTouchMove, { passive: false });\r\n    element.addEventListener(\"touchend\", handleTouchEnd, { passive: false });\r\n    element.addEventListener(\"touchcancel\", handleTouchEnd, { passive: false });\r\n\r\n    // Prevent default on parent elements to avoid interference\r\n    const preventDefaultOnParent = (e: TouchEvent) => {\r\n      if (currentScale > 1 || isPinching) {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n      }\r\n    };\r\n\r\n    // Add listeners to parent elements to prevent scrolling when zoomed\r\n    const parentElement = element.parentElement;\r\n    if (parentElement && currentScale > 1) {\r\n      parentElement.addEventListener(\"touchmove\", preventDefaultOnParent, { passive: false });\r\n    }\r\n\r\n    // Clean up\r\n    return () => {\r\n      element.removeEventListener(\"touchstart\", handleTouchStart);\r\n      element.removeEventListener(\"touchmove\", handleTouchMove);\r\n      element.removeEventListener(\"touchend\", handleTouchEnd);\r\n      element.removeEventListener(\"touchcancel\", handleTouchEnd);\r\n\r\n      if (parentElement && currentScale > 1) {\r\n        parentElement.removeEventListener(\"touchmove\", preventDefaultOnParent);\r\n      }\r\n    };\r\n  }, [elementRef, initialDistance, initialScale, currentScale, setScale, minScale, maxScale, isPinching, lastTouchTime]);\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAUO,SAAS,aACd,UAAyC,EACzC,QAAgE,EAChE,YAAoB,EACpB,UAA4B,CAAC,CAAC;IAE9B,MAAM,EACJ,WAAW,GAAG,EACd,WAAW,CAAC,EAEb,GAAG;IAEJ,uCAAuC;IACvC,uDAAuD;IACvD,MAAM,GAAG,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAC9C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,8CAA8C;IAC9C,MAAM,cAAc,CAAC;QACnB,IAAI,QAAQ,MAAM,GAAG,GAAG,OAAO;QAE/B,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,OAAO,GAAG,OAAO,CAAC,EAAE,CAAC,OAAO;QAClD,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,OAAO,GAAG,OAAO,CAAC,EAAE,CAAC,OAAO;QAClD,OAAO,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;IAClC;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU,WAAW,OAAO;QAClC,IAAI,CAAC,SAAS;QAEd,sBAAsB;QACtB,MAAM,mBAAmB,CAAC;YACxB,MAAM,MAAM,KAAK,GAAG;YACpB,iBAAiB;YAEjB,MAAM,UAAU,MAAM,IAAI,CAAC,EAAE,OAAO;YACpC,cAAc;YAEd,IAAI,QAAQ,MAAM,KAAK,GAAG;gBACxB,oDAAoD;gBACpD,mBAAmB,YAAY;gBAC/B,gBAAgB;gBAChB,cAAc;gBACd,EAAE,cAAc;YAClB;QACF;QAEA,oCAAoC;QACpC,MAAM,kBAAkB,CAAC;YACvB,MAAM,UAAU,MAAM,IAAI,CAAC,EAAE,OAAO;YAEpC,oDAAoD;YACpD,MAAM,MAAM,KAAK,GAAG;YACpB,IAAI,MAAM,gBAAgB,IAAI;gBAC5B;YACF;YACA,iBAAiB;YAEjB,IAAI,QAAQ,MAAM,KAAK,KAAK,mBAAmB,kBAAkB,GAAG;gBAClE,kDAAkD;gBAClD,MAAM,kBAAkB,YAAY;gBACpC,MAAM,cAAc,kBAAkB;gBACtC,MAAM,WAAW,eAAe;gBAEhC,qDAAqD;gBACrD,MAAM,cAAc,eAAe,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,UAAU,WAAW,YAAY,YAAY,IAAI;gBAEvG,6FAA6F;gBAC7F,SAAS;gBAET,wDAAwD;gBACxD,EAAE,cAAc;gBAChB,EAAE,eAAe;YACnB;QACF;QAEA,oBAAoB;QACpB,MAAM,iBAAiB,CAAC;YACtB,MAAM,UAAU,MAAM,IAAI,CAAC,EAAE,OAAO;YACpC,cAAc;YAEd,IAAI,QAAQ,MAAM,GAAG,GAAG;gBACtB,eAAe;gBACf,mBAAmB;gBACnB,cAAc;YAChB;YAEA,oCAAoC;YACpC,MAAM,MAAM,KAAK,GAAG;YACpB,MAAM,WAAW,MAAM;YAEvB,IAAI,WAAW,OAAO,QAAQ,MAAM,KAAK,KAAK,CAAC,YAAY;gBACzD,oCAAoC;gBACpC,IAAI,eAAe,GAAG;oBACpB,SAAS;gBACX,OAAO;oBACL,SAAS;gBACX;YACF;YAEA,iBAAiB;QACnB;QAEA,0CAA0C;QAC1C,QAAQ,gBAAgB,CAAC,cAAc,kBAAkB;YAAE,SAAS;QAAM;QAC1E,QAAQ,gBAAgB,CAAC,aAAa,iBAAiB;YAAE,SAAS;QAAM;QACxE,QAAQ,gBAAgB,CAAC,YAAY,gBAAgB;YAAE,SAAS;QAAM;QACtE,QAAQ,gBAAgB,CAAC,eAAe,gBAAgB;YAAE,SAAS;QAAM;QAEzE,2DAA2D;QAC3D,MAAM,yBAAyB,CAAC;YAC9B,IAAI,eAAe,KAAK,YAAY;gBAClC,EAAE,cAAc;gBAChB,EAAE,eAAe;YACnB;QACF;QAEA,oEAAoE;QACpE,MAAM,gBAAgB,QAAQ,aAAa;QAC3C,IAAI,iBAAiB,eAAe,GAAG;YACrC,cAAc,gBAAgB,CAAC,aAAa,wBAAwB;gBAAE,SAAS;YAAM;QACvF;QAEA,WAAW;QACX,OAAO;YACL,QAAQ,mBAAmB,CAAC,cAAc;YAC1C,QAAQ,mBAAmB,CAAC,aAAa;YACzC,QAAQ,mBAAmB,CAAC,YAAY;YACxC,QAAQ,mBAAmB,CAAC,eAAe;YAE3C,IAAI,iBAAiB,eAAe,GAAG;gBACrC,cAAc,mBAAmB,CAAC,aAAa;YACjD;QACF;IACF,GAAG;QAAC;QAAY;QAAiB;QAAc;QAAc;QAAU;QAAU;QAAU;QAAY;KAAc;AACvH", "debugId": null}}, {"offset": {"line": 944, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/product/components/ImageZoomModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useRef } from \"react\";\r\nimport Image from \"next/image\";\r\nimport { X, ZoomIn, ZoomOut } from \"lucide-react\";\r\nimport { motion, AnimatePresence, useMotionValue } from \"framer-motion\";\r\nimport { usePinchZoom } from \"../hooks/usePinchZoom\";\r\n\r\ninterface ImageZoomModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  imageUrl: string;\r\n  altText: string;\r\n}\r\n\r\nexport default function ImageZoomModal({\r\n  isOpen,\r\n  onClose,\r\n  imageUrl,\r\n  altText,\r\n}: ImageZoomModalProps) {\r\n  const [scale, setScale] = useState(1);\r\n  const [loading, setLoading] = useState(true);\r\n  const imageContainerRef = useRef<HTMLDivElement>(null);\r\n  const imageRef = useRef<HTMLDivElement>(null);\r\n  const [dragConstraints, setDragConstraints] = useState({ top: 0, right: 0, bottom: 0, left: 0 });\r\n\r\n  // For panning the image\r\n  const x = useMotionValue(0);\r\n  const y = useMotionValue(0);\r\n\r\n  // Use our custom pinch zoom hook for mobile devices\r\n  usePinchZoom(imageRef, setScale, scale, {\r\n    minScale: 0.5,\r\n    maxScale: 3,\r\n    scaleStep: 0.1\r\n  });\r\n\r\n  // Update drag constraints when scale changes\r\n  useEffect(() => {\r\n    if (!isOpen) return;\r\n\r\n    // Use requestAnimationFrame to ensure DOM is fully updated\r\n    const updateConstraints = () => {\r\n      if (scale > 1 && imageRef.current && imageContainerRef.current) {\r\n        const containerRect = imageContainerRef.current.getBoundingClientRect();\r\n        const imageRect = imageRef.current.getBoundingClientRect();\r\n\r\n        // Calculate the actual scaled dimensions\r\n        const scaledWidth = imageRect.width * scale;\r\n        const scaledHeight = imageRect.height * scale;\r\n\r\n        // Calculate how much the image can move in each direction\r\n        // This is half the difference between the scaled size and container size\r\n        const horizontalConstraint = Math.max(0, (scaledWidth - containerRect.width) / 2);\r\n        const verticalConstraint = Math.max(0, (scaledHeight - containerRect.height) / 2);\r\n\r\n        // Set constraints with some extra padding for better experience\r\n        setDragConstraints({\r\n          left: -horizontalConstraint - 50,\r\n          right: horizontalConstraint + 50,\r\n          top: -verticalConstraint - 50,\r\n          bottom: verticalConstraint + 50\r\n        });\r\n      } else {\r\n        // Reset constraints and position when not zoomed\r\n        setDragConstraints({ top: 0, right: 0, bottom: 0, left: 0 });\r\n        x.set(0);\r\n        y.set(0);\r\n      }\r\n    };\r\n\r\n    // Initial update\r\n    updateConstraints();\r\n\r\n    // Update again after a short delay to ensure all measurements are accurate\r\n    const timeoutId = setTimeout(updateConstraints, 100);\r\n\r\n    return () => clearTimeout(timeoutId);\r\n  }, [scale, isOpen, x, y]);\r\n\r\n  // Reset scale and position when modal opens\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      setScale(1);\r\n      setLoading(true);\r\n      x.set(0);\r\n      y.set(0);\r\n    }\r\n  }, [isOpen, x, y]);\r\n\r\n  // Progressively recenter image as user zooms out\r\n  useEffect(() => {\r\n    // Only apply when scale is between 1 and 2\r\n    if (scale > 1 && scale < 2) {\r\n      // Calculate how much to recenter based on current scale\r\n      // As scale approaches 1, centeringFactor approaches 1 (full centering)\r\n      const centeringFactor = 1 - (scale - 1);\r\n\r\n      // Get current position values\r\n      const currentX = x.get();\r\n      const currentY = y.get();\r\n\r\n      // Calculate new positions with progressive centering\r\n      // The closer to scale 1, the closer to center (0,0)\r\n      const newX = currentX * (1 - centeringFactor);\r\n      const newY = currentY * (1 - centeringFactor);\r\n\r\n      // Apply the new positions with a small delay for smoother transition\r\n      const applyProgressiveCentering = () => {\r\n        x.set(newX);\r\n        y.set(newY);\r\n      };\r\n\r\n      const timeoutId = setTimeout(applyProgressiveCentering, 10);\r\n      return () => clearTimeout(timeoutId);\r\n    } else if (scale <= 1) {\r\n      // When fully zoomed out, ensure complete centering\r\n      x.set(0);\r\n      y.set(0);\r\n    }\r\n  }, [scale, x, y]);\r\n\r\n  // Handle keyboard events (Escape to close, + to zoom in, - to zoom out)\r\n  useEffect(() => {\r\n    const handleKeyDown = (e: KeyboardEvent) => {\r\n      if (!isOpen) return;\r\n\r\n      if (e.key === \"Escape\") {\r\n        onClose();\r\n      } else if (e.key === \"+\" || e.key === \"=\") {\r\n        setScale((prev) => Math.min(prev + 0.25, 3));\r\n      } else if (e.key === \"-\" || e.key === \"_\") {\r\n        setScale((prev) => Math.max(prev - 0.25, 0.5));\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown);\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\r\n  }, [isOpen, onClose]);\r\n\r\n  // Handle mouse wheel for zooming\r\n  useEffect(() => {\r\n    const handleWheel = (e: WheelEvent) => {\r\n      if (!isOpen || !imageContainerRef.current) return;\r\n\r\n      e.preventDefault();\r\n\r\n      // Get current scale before update\r\n      const prevScale = scale;\r\n\r\n      // Calculate new scale based on wheel direction\r\n      let newScale;\r\n      if (e.deltaY < 0) {\r\n        // Scroll up - zoom in\r\n        newScale = Math.min(prevScale + 0.1, 3);\r\n      } else {\r\n        // Scroll down - zoom out\r\n        newScale = Math.max(prevScale - 0.1, 0.5);\r\n      }\r\n\r\n      // Update scale - the progressive centering effect will handle recentering\r\n      setScale(newScale);\r\n    };\r\n\r\n    // Handle mouse down for better drag experience\r\n    const handleMouseDown = () => {\r\n      if (scale > 1) {\r\n        document.body.style.cursor = 'grabbing';\r\n      }\r\n    };\r\n\r\n    // Handle mouse up to reset cursor\r\n    const handleMouseUp = () => {\r\n      document.body.style.cursor = '';\r\n    };\r\n\r\n    const currentRef = imageContainerRef.current;\r\n    if (currentRef) {\r\n      currentRef.addEventListener('wheel', handleWheel, { passive: false });\r\n      currentRef.addEventListener('mousedown', handleMouseDown);\r\n      window.addEventListener('mouseup', handleMouseUp);\r\n    }\r\n\r\n    return () => {\r\n      if (currentRef) {\r\n        currentRef.removeEventListener('wheel', handleWheel);\r\n        currentRef.removeEventListener('mousedown', handleMouseDown);\r\n        window.removeEventListener('mouseup', handleMouseUp);\r\n      }\r\n      document.body.style.cursor = '';\r\n    };\r\n  }, [isOpen, scale]);\r\n\r\n  // Prevent body scroll when modal is open\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      document.body.style.overflow = \"hidden\";\r\n    } else {\r\n      document.body.style.overflow = \"\";\r\n    }\r\n    return () => {\r\n      document.body.style.overflow = \"\";\r\n    };\r\n  }, [isOpen]);\r\n\r\n  const zoomIn = () => setScale((prev) => Math.min(prev + 0.25, 3));\r\n  const zoomOut = () => setScale((prev) => Math.max(prev - 0.25, 0.5));\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {isOpen && (\r\n        <motion.div\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          exit={{ opacity: 0 }}\r\n          className=\"fixed inset-0 z-50 flex items-center justify-center bg-black p-4\"\r\n          onClick={onClose}\r\n        >\r\n          {/* Close button */}\r\n          <button\r\n            onClick={onClose}\r\n            className=\"absolute top-4 right-4 z-10 p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors\"\r\n            aria-label=\"Close\"\r\n          >\r\n            <X className=\"w-6 h-6\" />\r\n          </button>\r\n\r\n          {/* Zoom controls */}\r\n          <div className=\"absolute bottom-4 left-1/2 -translate-x-1/2 z-10 flex items-center gap-4 bg-black/50 rounded-full p-2\">\r\n            <button\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                zoomOut();\r\n              }}\r\n              className=\"p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors\"\r\n              aria-label=\"Zoom out\"\r\n              disabled={scale <= 0.5}\r\n            >\r\n              <ZoomOut className=\"w-5 h-5\" />\r\n            </button>\r\n            <span className=\"text-white text-sm font-medium\">{Math.round(scale * 100)}%</span>\r\n            <button\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                zoomIn();\r\n              }}\r\n              className=\"p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors\"\r\n              aria-label=\"Zoom in\"\r\n              disabled={scale >= 3}\r\n            >\r\n              <ZoomIn className=\"w-5 h-5\" />\r\n            </button>\r\n          </div>\r\n\r\n          {/* Image container - stop propagation to prevent closing when clicking on image */}\r\n          <motion.div\r\n            ref={imageContainerRef}\r\n            className=\"relative w-full max-w-4xl h-[80vh] overflow-hidden touch-none\"\r\n            onClick={(e) => e.stopPropagation()}\r\n            initial={{ scale: 0.9 }}\r\n            animate={{ scale: 1 }}\r\n            exit={{ scale: 0.9 }}\r\n            style={{ touchAction: \"none\" }}\r\n            onTouchStart={(e) => {\r\n              // Prevent default touch behavior to avoid interference\r\n              if (scale > 1) {\r\n                e.stopPropagation();\r\n              }\r\n            }}\r\n          >\r\n            {loading && (\r\n              <div className=\"absolute inset-0 flex items-center justify-center\">\r\n                <div className=\"w-10 h-10 border-4 border-neutral-300 border-t-[var(--brand-gold)] rounded-full animate-spin\"></div>\r\n              </div>\r\n            )}\r\n            <div className=\"w-full h-full flex items-center justify-center overflow-hidden\">\r\n              <motion.div\r\n                ref={imageRef}\r\n                drag={scale > 1} // Enable dragging only when zoomed in\r\n                dragConstraints={dragConstraints} // Use dynamic constraints based on scale\r\n                dragElastic={0} // No elasticity for more direct control\r\n                dragMomentum={false} // Disable momentum for more precise control\r\n                dragTransition={{ power: 0.1, timeConstant: 200 }} // Smoother dragging\r\n                style={{\r\n                  x,\r\n                  y,\r\n                  scale,\r\n                  touchAction: \"none\", // Disable browser's touch actions for better control\r\n                  cursor: scale > 1 ? \"grab\" : \"default\"\r\n                }}\r\n                className=\"relative touch-none will-change-transform\"\r\n                whileDrag={{ cursor: \"grabbing\" }}\r\n                // Handle double click to zoom in/out\r\n                onDoubleClick={(e) => {\r\n                  if (scale > 1) {\r\n                    // Zoom out to 1 - progressive centering will handle recentering\r\n                    setScale(1);\r\n                  } else {\r\n                    // Zoom in to 2x at the point that was clicked\r\n                    const rect = e.currentTarget.getBoundingClientRect();\r\n                    const offsetX = e.clientX - rect.left;\r\n                    const offsetY = e.clientY - rect.top;\r\n\r\n                    // Calculate center point offset\r\n                    const centerX = rect.width / 2;\r\n                    const centerY = rect.height / 2;\r\n\r\n                    // Calculate the point to zoom to (relative to center)\r\n                    const targetX = (offsetX - centerX) * 0.5;\r\n                    const targetY = (offsetY - centerY) * 0.5;\r\n\r\n                    // Set scale and position\r\n                    setScale(2);\r\n                    x.set(-targetX); // Negative because we're moving the image in the opposite direction\r\n                    y.set(-targetY);\r\n                  }\r\n                }}\r\n              >\r\n                <div className=\"relative\" style={{ pointerEvents: 'none' }}>\r\n                  <Image\r\n                    src={imageUrl}\r\n                    alt={altText}\r\n                    width={1200}\r\n                    height={1800}\r\n                    className=\"max-w-none object-contain select-none\"\r\n                    onLoad={() => setLoading(false)}\r\n                    priority\r\n                    draggable={false}\r\n                    unoptimized={true} // Use unoptimized for better quality when zooming\r\n                    style={{\r\n                      userSelect: 'none',\r\n                      WebkitUserSelect: 'none'\r\n                    }}\r\n                  />\r\n                </div>\r\n              </motion.div>\r\n            </div>\r\n          </motion.div>\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AANA;;;;;;;AAee,SAAS,eAAe,EACrC,MAAM,EACN,OAAO,EACP,QAAQ,EACR,OAAO,EACa;IACpB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACjD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACxC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,KAAK;QAAG,OAAO;QAAG,QAAQ;QAAG,MAAM;IAAE;IAE9F,wBAAwB;IACxB,MAAM,IAAI,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IACzB,MAAM,IAAI,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IAEzB,oDAAoD;IACpD,CAAA,GAAA,uJAAA,CAAA,eAAY,AAAD,EAAE,UAAU,UAAU,OAAO;QACtC,UAAU;QACV,UAAU;QACV,WAAW;IACb;IAEA,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ;QAEb,2DAA2D;QAC3D,MAAM,oBAAoB;YACxB,IAAI,QAAQ,KAAK,SAAS,OAAO,IAAI,kBAAkB,OAAO,EAAE;gBAC9D,MAAM,gBAAgB,kBAAkB,OAAO,CAAC,qBAAqB;gBACrE,MAAM,YAAY,SAAS,OAAO,CAAC,qBAAqB;gBAExD,yCAAyC;gBACzC,MAAM,cAAc,UAAU,KAAK,GAAG;gBACtC,MAAM,eAAe,UAAU,MAAM,GAAG;gBAExC,0DAA0D;gBAC1D,yEAAyE;gBACzE,MAAM,uBAAuB,KAAK,GAAG,CAAC,GAAG,CAAC,cAAc,cAAc,KAAK,IAAI;gBAC/E,MAAM,qBAAqB,KAAK,GAAG,CAAC,GAAG,CAAC,eAAe,cAAc,MAAM,IAAI;gBAE/E,gEAAgE;gBAChE,mBAAmB;oBACjB,MAAM,CAAC,uBAAuB;oBAC9B,OAAO,uBAAuB;oBAC9B,KAAK,CAAC,qBAAqB;oBAC3B,QAAQ,qBAAqB;gBAC/B;YACF,OAAO;gBACL,iDAAiD;gBACjD,mBAAmB;oBAAE,KAAK;oBAAG,OAAO;oBAAG,QAAQ;oBAAG,MAAM;gBAAE;gBAC1D,EAAE,GAAG,CAAC;gBACN,EAAE,GAAG,CAAC;YACR;QACF;QAEA,iBAAiB;QACjB;QAEA,2EAA2E;QAC3E,MAAM,YAAY,WAAW,mBAAmB;QAEhD,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAO;QAAQ;QAAG;KAAE;IAExB,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,SAAS;YACT,WAAW;YACX,EAAE,GAAG,CAAC;YACN,EAAE,GAAG,CAAC;QACR;IACF,GAAG;QAAC;QAAQ;QAAG;KAAE;IAEjB,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,2CAA2C;QAC3C,IAAI,QAAQ,KAAK,QAAQ,GAAG;YAC1B,wDAAwD;YACxD,uEAAuE;YACvE,MAAM,kBAAkB,IAAI,CAAC,QAAQ,CAAC;YAEtC,8BAA8B;YAC9B,MAAM,WAAW,EAAE,GAAG;YACtB,MAAM,WAAW,EAAE,GAAG;YAEtB,qDAAqD;YACrD,oDAAoD;YACpD,MAAM,OAAO,WAAW,CAAC,IAAI,eAAe;YAC5C,MAAM,OAAO,WAAW,CAAC,IAAI,eAAe;YAE5C,qEAAqE;YACrE,MAAM,4BAA4B;gBAChC,EAAE,GAAG,CAAC;gBACN,EAAE,GAAG,CAAC;YACR;YAEA,MAAM,YAAY,WAAW,2BAA2B;YACxD,OAAO,IAAM,aAAa;QAC5B,OAAO,IAAI,SAAS,GAAG;YACrB,mDAAmD;YACnD,EAAE,GAAG,CAAC;YACN,EAAE,GAAG,CAAC;QACR;IACF,GAAG;QAAC;QAAO;QAAG;KAAE;IAEhB,wEAAwE;IACxE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,CAAC,QAAQ;YAEb,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB;YACF,OAAO,IAAI,EAAE,GAAG,KAAK,OAAO,EAAE,GAAG,KAAK,KAAK;gBACzC,SAAS,CAAC,OAAS,KAAK,GAAG,CAAC,OAAO,MAAM;YAC3C,OAAO,IAAI,EAAE,GAAG,KAAK,OAAO,EAAE,GAAG,KAAK,KAAK;gBACzC,SAAS,CAAC,OAAS,KAAK,GAAG,CAAC,OAAO,MAAM;YAC3C;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;QAAQ;KAAQ;IAEpB,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,CAAC;YACnB,IAAI,CAAC,UAAU,CAAC,kBAAkB,OAAO,EAAE;YAE3C,EAAE,cAAc;YAEhB,kCAAkC;YAClC,MAAM,YAAY;YAElB,+CAA+C;YAC/C,IAAI;YACJ,IAAI,EAAE,MAAM,GAAG,GAAG;gBAChB,sBAAsB;gBACtB,WAAW,KAAK,GAAG,CAAC,YAAY,KAAK;YACvC,OAAO;gBACL,yBAAyB;gBACzB,WAAW,KAAK,GAAG,CAAC,YAAY,KAAK;YACvC;YAEA,0EAA0E;YAC1E,SAAS;QACX;QAEA,+CAA+C;QAC/C,MAAM,kBAAkB;YACtB,IAAI,QAAQ,GAAG;gBACb,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;YAC/B;QACF;QAEA,kCAAkC;QAClC,MAAM,gBAAgB;YACpB,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QAC/B;QAEA,MAAM,aAAa,kBAAkB,OAAO;QAC5C,IAAI,YAAY;YACd,WAAW,gBAAgB,CAAC,SAAS,aAAa;gBAAE,SAAS;YAAM;YACnE,WAAW,gBAAgB,CAAC,aAAa;YACzC,OAAO,gBAAgB,CAAC,WAAW;QACrC;QAEA,OAAO;YACL,IAAI,YAAY;gBACd,WAAW,mBAAmB,CAAC,SAAS;gBACxC,WAAW,mBAAmB,CAAC,aAAa;gBAC5C,OAAO,mBAAmB,CAAC,WAAW;YACxC;YACA,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QAC/B;IACF,GAAG;QAAC;QAAQ;KAAM;IAElB,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QACA,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,SAAS,IAAM,SAAS,CAAC,OAAS,KAAK,GAAG,CAAC,OAAO,MAAM;IAC9D,MAAM,UAAU,IAAM,SAAS,CAAC,OAAS,KAAK,GAAG,CAAC,OAAO,MAAM;IAE/D,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,WAAU;YACV,SAAS;;8BAGT,8OAAC;oBACC,SAAS;oBACT,WAAU;oBACV,cAAW;8BAEX,cAAA,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;8BAIf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,CAAC;gCACR,EAAE,eAAe;gCACjB;4BACF;4BACA,WAAU;4BACV,cAAW;4BACX,UAAU,SAAS;sCAEnB,cAAA,8OAAC,4MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAErB,8OAAC;4BAAK,WAAU;;gCAAkC,KAAK,KAAK,CAAC,QAAQ;gCAAK;;;;;;;sCAC1E,8OAAC;4BACC,SAAS,CAAC;gCACR,EAAE,eAAe;gCACjB;4BACF;4BACA,WAAU;4BACV,cAAW;4BACX,UAAU,SAAS;sCAEnB,cAAA,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,KAAK;oBACL,WAAU;oBACV,SAAS,CAAC,IAAM,EAAE,eAAe;oBACjC,SAAS;wBAAE,OAAO;oBAAI;oBACtB,SAAS;wBAAE,OAAO;oBAAE;oBACpB,MAAM;wBAAE,OAAO;oBAAI;oBACnB,OAAO;wBAAE,aAAa;oBAAO;oBAC7B,cAAc,CAAC;wBACb,uDAAuD;wBACvD,IAAI,QAAQ,GAAG;4BACb,EAAE,eAAe;wBACnB;oBACF;;wBAEC,yBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;;sCAGnB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,KAAK;gCACL,MAAM,QAAQ;gCACd,iBAAiB;gCACjB,aAAa;gCACb,cAAc;gCACd,gBAAgB;oCAAE,OAAO;oCAAK,cAAc;gCAAI;gCAChD,OAAO;oCACL;oCACA;oCACA;oCACA,aAAa;oCACb,QAAQ,QAAQ,IAAI,SAAS;gCAC/B;gCACA,WAAU;gCACV,WAAW;oCAAE,QAAQ;gCAAW;gCAChC,qCAAqC;gCACrC,eAAe,CAAC;oCACd,IAAI,QAAQ,GAAG;wCACb,gEAAgE;wCAChE,SAAS;oCACX,OAAO;wCACL,8CAA8C;wCAC9C,MAAM,OAAO,EAAE,aAAa,CAAC,qBAAqB;wCAClD,MAAM,UAAU,EAAE,OAAO,GAAG,KAAK,IAAI;wCACrC,MAAM,UAAU,EAAE,OAAO,GAAG,KAAK,GAAG;wCAEpC,gCAAgC;wCAChC,MAAM,UAAU,KAAK,KAAK,GAAG;wCAC7B,MAAM,UAAU,KAAK,MAAM,GAAG;wCAE9B,sDAAsD;wCACtD,MAAM,UAAU,CAAC,UAAU,OAAO,IAAI;wCACtC,MAAM,UAAU,CAAC,UAAU,OAAO,IAAI;wCAEtC,yBAAyB;wCACzB,SAAS;wCACT,EAAE,GAAG,CAAC,CAAC,UAAU,oEAAoE;wCACrF,EAAE,GAAG,CAAC,CAAC;oCACT;gCACF;0CAEA,cAAA,8OAAC;oCAAI,WAAU;oCAAW,OAAO;wCAAE,eAAe;oCAAO;8CACvD,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAK;wCACL,KAAK;wCACL,OAAO;wCACP,QAAQ;wCACR,WAAU;wCACV,QAAQ,IAAM,WAAW;wCACzB,QAAQ;wCACR,WAAW;wCACX,aAAa;wCACb,OAAO;4CACL,YAAY;4CACZ,kBAAkB;wCACpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpB", "debugId": null}}, {"offset": {"line": 1390, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/constants/predefinedVariants.ts"], "sourcesContent": ["/**\r\n * Simplified predefined variant types and options for common business needs\r\n * Focused on essential variants that most businesses actually use\r\n */\r\n\r\n// VariantType interface definition (since it was removed from types/variants.ts)\r\ninterface VariantType {\r\n  id: string;\r\n  name: string;\r\n  display_name: string;\r\n  description?: string;\r\n  is_predefined: boolean;\r\n  sort_order: number;\r\n}\r\n\r\nexport interface PredefinedVariantOption {\r\n  value: string;\r\n  display_value: string;\r\n  color_code?: string;\r\n  description?: string;\r\n  sort_order: number;\r\n}\r\n\r\n// ============================================================================\r\n// VARIANT TYPES - Essential variants only\r\n// ============================================================================\r\n\r\nexport const PREDEFINED_VARIANT_TYPES: Omit<VariantType, 'created_at' | 'updated_at'>[] = [\r\n  // Most Common Variants\r\n  { id: 'size', name: 'size', display_name: 'Size', description: 'Product size variations (XS, S, M, L, XL, etc.)', is_predefined: true, sort_order: 1 },\r\n  { id: 'color', name: 'color', display_name: 'Color', description: 'Product color variations', is_predefined: true, sort_order: 2 },\r\n  { id: 'material', name: 'material', display_name: 'Material', description: 'Product material (cotton, silk, metal, wood, etc.)', is_predefined: true, sort_order: 3 },\r\n  { id: 'style', name: 'style', display_name: 'Style', description: 'Product style variations (classic, modern, vintage, etc.)', is_predefined: true, sort_order: 4 },\r\n  { id: 'pattern', name: 'pattern', display_name: 'Pattern', description: 'Product pattern or design (solid, striped, floral, etc.)', is_predefined: true, sort_order: 5 },\r\n\r\n  // Technical Specifications\r\n  { id: 'capacity', name: 'capacity', display_name: 'Capacity', description: 'Storage or volume capacity (100ml, 1L, 32GB, etc.)', is_predefined: true, sort_order: 6 },\r\n  { id: 'weight', name: 'weight', display_name: 'Weight', description: 'Product weight variations (100g, 1kg, light, heavy, etc.)', is_predefined: true, sort_order: 7 },\r\n  { id: 'power', name: 'power', display_name: 'Power', description: 'Power rating (5W, 25W, 100W, etc.)', is_predefined: true, sort_order: 8 },\r\n  { id: 'voltage', name: 'voltage', display_name: 'Voltage', description: 'Electrical voltage (12V, 24V, 110V, 220V, etc.)', is_predefined: true, sort_order: 9 },\r\n  { id: 'frequency', name: 'frequency', display_name: 'Frequency', description: 'Operating frequency (50Hz, 60Hz, etc.)', is_predefined: true, sort_order: 10 },\r\n\r\n  // Dimensions & Measurements\r\n  { id: 'length', name: 'length', display_name: 'Length', description: 'Product length (10cm, 1m, 6ft, etc.)', is_predefined: true, sort_order: 11 },\r\n  { id: 'width', name: 'width', display_name: 'Width', description: 'Product width (5cm, 50cm, 2ft, etc.)', is_predefined: true, sort_order: 12 },\r\n  { id: 'height', name: 'height', display_name: 'Height', description: 'Product height (10cm, 1m, 3ft, etc.)', is_predefined: true, sort_order: 13 },\r\n  { id: 'diameter', name: 'diameter', display_name: 'Diameter', description: 'Product diameter (5cm, 10cm, 1ft, etc.)', is_predefined: true, sort_order: 14 },\r\n  { id: 'thickness', name: 'thickness', display_name: 'Thickness', description: 'Product thickness (1mm, 5mm, 1cm, etc.)', is_predefined: true, sort_order: 15 },\r\n\r\n  // Food & Consumables\r\n  { id: 'flavor', name: 'flavor', display_name: 'Flavor', description: 'Taste or flavor variations (vanilla, chocolate, spicy, etc.)', is_predefined: true, sort_order: 16 },\r\n  { id: 'quantity', name: 'quantity', display_name: 'Quantity', description: 'Package quantity or count (1 piece, pack of 6, bulk, etc.)', is_predefined: true, sort_order: 17 },\r\n  { id: 'expiry', name: 'expiry', display_name: 'Expiry', description: 'Expiry or shelf life (6 months, 1 year, etc.)', is_predefined: true, sort_order: 18 },\r\n  { id: 'ingredients', name: 'ingredients', display_name: 'Ingredients', description: 'Key ingredients or composition', is_predefined: true, sort_order: 19 },\r\n\r\n  // Beauty & Personal Care\r\n  { id: 'shade', name: 'shade', display_name: 'Shade', description: 'Color shade or tone (fair, medium, dark, etc.)', is_predefined: true, sort_order: 20 },\r\n  { id: 'skin_type', name: 'skin_type', display_name: 'Skin Type', description: 'Suitable skin type (normal, dry, oily, sensitive, etc.)', is_predefined: true, sort_order: 21 },\r\n  { id: 'coverage', name: 'coverage', display_name: 'Coverage', description: 'Coverage level (light, medium, full, etc.)', is_predefined: true, sort_order: 22 },\r\n  { id: 'spf', name: 'spf', display_name: 'SPF', description: 'Sun protection factor (SPF 15, SPF 30, SPF 50, etc.)', is_predefined: true, sort_order: 23 },\r\n\r\n  // Clothing Specific\r\n  { id: 'fit', name: 'fit', display_name: 'Fit', description: 'Clothing fit type (slim, regular, loose, oversized, etc.)', is_predefined: true, sort_order: 24 },\r\n  { id: 'sleeve', name: 'sleeve', display_name: 'Sleeve', description: 'Sleeve type or length (sleeveless, short, full, etc.)', is_predefined: true, sort_order: 25 },\r\n  { id: 'collar', name: 'collar', display_name: 'Collar', description: 'Collar type (round neck, V-neck, polo, etc.)', is_predefined: true, sort_order: 26 },\r\n  { id: 'occasion', name: 'occasion', display_name: 'Occasion', description: 'Suitable occasion (casual, formal, party, sports, etc.)', is_predefined: true, sort_order: 27 },\r\n  { id: 'season', name: 'season', display_name: 'Season', description: 'Suitable season (summer, winter, all-season, etc.)', is_predefined: true, sort_order: 28 },\r\n\r\n  // Technology & Electronics\r\n  { id: 'connectivity', name: 'connectivity', display_name: 'Connectivity', description: 'Connection type (WiFi, Bluetooth, USB, etc.)', is_predefined: true, sort_order: 29 },\r\n  { id: 'compatibility', name: 'compatibility', display_name: 'Compatibility', description: 'Device compatibility (iPhone, Android, Windows, etc.)', is_predefined: true, sort_order: 30 },\r\n  { id: 'resolution', name: 'resolution', display_name: 'Resolution', description: 'Display resolution (HD, Full HD, 4K, etc.)', is_predefined: true, sort_order: 31 },\r\n  { id: 'storage', name: 'storage', display_name: 'Storage', description: 'Storage capacity (16GB, 64GB, 256GB, etc.)', is_predefined: true, sort_order: 32 },\r\n  { id: 'ram', name: 'ram', display_name: 'RAM', description: 'Memory capacity (4GB, 8GB, 16GB, etc.)', is_predefined: true, sort_order: 33 },\r\n\r\n  // General Product Attributes\r\n  { id: 'type', name: 'type', display_name: 'Type', description: 'Product type or variant (standard, premium, deluxe, etc.)', is_predefined: true, sort_order: 34 },\r\n  { id: 'finish', name: 'finish', display_name: 'Finish', description: 'Product surface finish (matte, glossy, textured, etc.)', is_predefined: true, sort_order: 35 },\r\n  { id: 'grade', name: 'grade', display_name: 'Grade', description: 'Quality grade (A, B, premium, standard, etc.)', is_predefined: true, sort_order: 36 },\r\n  { id: 'brand', name: 'brand', display_name: 'Brand', description: 'Product brand or manufacturer', is_predefined: true, sort_order: 37 },\r\n  { id: 'model', name: 'model', display_name: 'Model', description: 'Product model or version', is_predefined: true, sort_order: 38 },\r\n  { id: 'age_group', name: 'age_group', display_name: 'Age Group', description: 'Target age group (kids, teens, adults, seniors, etc.)', is_predefined: true, sort_order: 39 },\r\n  { id: 'gender', name: 'gender', display_name: 'Gender', description: 'Target gender (men, women, unisex, etc.)', is_predefined: true, sort_order: 40 },\r\n\r\n  // Specialized Categories\r\n  { id: 'fragrance', name: 'fragrance', display_name: 'Fragrance', description: 'Scent or fragrance type (floral, woody, fresh, etc.)', is_predefined: true, sort_order: 41 },\r\n  { id: 'texture', name: 'texture', display_name: 'Texture', description: 'Product texture (smooth, rough, soft, hard, etc.)', is_predefined: true, sort_order: 42 },\r\n  { id: 'temperature', name: 'temperature', display_name: 'Temperature', description: 'Operating temperature (hot, cold, room temp, etc.)', is_predefined: true, sort_order: 43 },\r\n  { id: 'certification', name: 'certification', display_name: 'Certification', description: 'Product certifications (organic, ISO, FDA approved, etc.)', is_predefined: true, sort_order: 44 },\r\n  { id: 'warranty', name: 'warranty', display_name: 'Warranty', description: 'Warranty period (1 year, 2 years, lifetime, etc.)', is_predefined: true, sort_order: 45 },\r\n];\r\n\r\n// ============================================================================\r\n// SIZE OPTIONS - REMOVED: Users will enter sizes manually\r\n// ============================================================================\r\n// All size options have been removed to allow manual input\r\n\r\n// ============================================================================\r\n// COLOR OPTIONS\r\n// ============================================================================\r\n\r\nexport const COLOR_OPTIONS: PredefinedVariantOption[] = [\r\n  // Basic Colors\r\n  { value: 'black', display_value: 'Black', color_code: '#000000', sort_order: 1 },\r\n  { value: 'white', display_value: 'White', color_code: '#FFFFFF', sort_order: 2 },\r\n  { value: 'gray', display_value: 'Gray', color_code: '#808080', sort_order: 3 },\r\n  { value: 'light_gray', display_value: 'Light Gray', color_code: '#D3D3D3', sort_order: 4 },\r\n  { value: 'dark_gray', display_value: 'Dark Gray', color_code: '#404040', sort_order: 5 },\r\n  { value: 'charcoal', display_value: 'Charcoal', color_code: '#36454F', sort_order: 6 },\r\n\r\n  // Red Variants\r\n  { value: 'red', display_value: 'Red', color_code: '#FF0000', sort_order: 7 },\r\n  { value: 'dark_red', display_value: 'Dark Red', color_code: '#8B0000', sort_order: 8 },\r\n  { value: 'light_red', display_value: 'Light Red', color_code: '#FFB6C1', sort_order: 9 },\r\n  { value: 'crimson', display_value: 'Crimson', color_code: '#DC143C', sort_order: 10 },\r\n  { value: 'maroon', display_value: 'Maroon', color_code: '#800000', sort_order: 11 },\r\n  { value: 'burgundy', display_value: 'Burgundy', color_code: '#800020', sort_order: 12 },\r\n\r\n  // Blue Variants\r\n  { value: 'blue', display_value: 'Blue', color_code: '#0000FF', sort_order: 13 },\r\n  { value: 'navy', display_value: 'Navy', color_code: '#000080', sort_order: 14 },\r\n  { value: 'light_blue', display_value: 'Light Blue', color_code: '#ADD8E6', sort_order: 15 },\r\n  { value: 'dark_blue', display_value: 'Dark Blue', color_code: '#00008B', sort_order: 16 },\r\n  { value: 'sky_blue', display_value: 'Sky Blue', color_code: '#87CEEB', sort_order: 17 },\r\n  { value: 'royal_blue', display_value: 'Royal Blue', color_code: '#4169E1', sort_order: 18 },\r\n  { value: 'turquoise', display_value: 'Turquoise', color_code: '#40E0D0', sort_order: 19 },\r\n  { value: 'teal', display_value: 'Teal', color_code: '#008080', sort_order: 20 },\r\n  { value: 'aqua', display_value: 'Aqua', color_code: '#00FFFF', sort_order: 21 },\r\n\r\n  // Green Variants\r\n  { value: 'green', display_value: 'Green', color_code: '#008000', sort_order: 22 },\r\n  { value: 'dark_green', display_value: 'Dark Green', color_code: '#006400', sort_order: 23 },\r\n  { value: 'light_green', display_value: 'Light Green', color_code: '#90EE90', sort_order: 24 },\r\n  { value: 'lime', display_value: 'Lime', color_code: '#00FF00', sort_order: 25 },\r\n  { value: 'olive', display_value: 'Olive', color_code: '#808000', sort_order: 26 },\r\n  { value: 'forest_green', display_value: 'Forest Green', color_code: '#228B22', sort_order: 27 },\r\n  { value: 'mint', display_value: 'Mint', color_code: '#98FB98', sort_order: 28 },\r\n\r\n  // Yellow/Orange Variants\r\n  { value: 'yellow', display_value: 'Yellow', color_code: '#FFFF00', sort_order: 29 },\r\n  { value: 'light_yellow', display_value: 'Light Yellow', color_code: '#FFFFE0', sort_order: 30 },\r\n  { value: 'orange', display_value: 'Orange', color_code: '#FFA500', sort_order: 31 },\r\n  { value: 'dark_orange', display_value: 'Dark Orange', color_code: '#FF8C00', sort_order: 32 },\r\n  { value: 'coral', display_value: 'Coral', color_code: '#FF7F50', sort_order: 33 },\r\n  { value: 'peach', display_value: 'Peach', color_code: '#FFCBA4', sort_order: 34 },\r\n\r\n  // Purple/Pink Variants\r\n  { value: 'purple', display_value: 'Purple', color_code: '#800080', sort_order: 35 },\r\n  { value: 'dark_purple', display_value: 'Dark Purple', color_code: '#4B0082', sort_order: 36 },\r\n  { value: 'light_purple', display_value: 'Light Purple', color_code: '#DDA0DD', sort_order: 37 },\r\n  { value: 'violet', display_value: 'Violet', color_code: '#8A2BE2', sort_order: 38 },\r\n  { value: 'lavender', display_value: 'Lavender', color_code: '#E6E6FA', sort_order: 39 },\r\n  { value: 'indigo', display_value: 'Indigo', color_code: '#4B0082', sort_order: 40 },\r\n  { value: 'pink', display_value: 'Pink', color_code: '#FFC0CB', sort_order: 41 },\r\n  { value: 'hot_pink', display_value: 'Hot Pink', color_code: '#FF69B4', sort_order: 42 },\r\n  { value: 'magenta', display_value: 'Magenta', color_code: '#FF00FF', sort_order: 43 },\r\n\r\n  // Brown Variants\r\n  { value: 'brown', display_value: 'Brown', color_code: '#A52A2A', sort_order: 44 },\r\n  { value: 'dark_brown', display_value: 'Dark Brown', color_code: '#654321', sort_order: 45 },\r\n  { value: 'light_brown', display_value: 'Light Brown', color_code: '#D2B48C', sort_order: 46 },\r\n  { value: 'tan', display_value: 'Tan', color_code: '#D2B48C', sort_order: 47 },\r\n  { value: 'beige', display_value: 'Beige', color_code: '#F5F5DC', sort_order: 48 },\r\n  { value: 'cream', display_value: 'Cream', color_code: '#FFFDD0', sort_order: 49 },\r\n  { value: 'ivory', display_value: 'Ivory', color_code: '#FFFFF0', sort_order: 50 },\r\n\r\n  // Metallic Colors\r\n  { value: 'gold', display_value: 'Gold', color_code: '#FFD700', sort_order: 51 },\r\n  { value: 'silver', display_value: 'Silver', color_code: '#C0C0C0', sort_order: 52 },\r\n  { value: 'bronze', display_value: 'Bronze', color_code: '#CD7F32', sort_order: 53 },\r\n  { value: 'copper', display_value: 'Copper', color_code: '#B87333', sort_order: 54 },\r\n  { value: 'rose_gold', display_value: 'Rose Gold', color_code: '#E8B4B8', sort_order: 55 },\r\n\r\n  // Special\r\n  { value: 'multicolor', display_value: 'Multicolor', sort_order: 56 },\r\n];\r\n\r\n// ============================================================================\r\n// MATERIAL, STYLE, PATTERN, FINISH OPTIONS - REMOVED: Users will enter values manually\r\n// ============================================================================\r\n// All non-color variant options have been removed to allow manual input\r\n\r\n// ============================================================================\r\n// ALL OTHER VARIANT OPTIONS - REMOVED: Users will enter values manually\r\n// ============================================================================\r\n// All non-color variant options (flavor, capacity, weight, power, quantity,\r\n// shade, skin_type, fit, sleeve, type) have been removed to allow manual input\r\n\r\n// ============================================================================\r\n// HELPER FUNCTIONS\r\n// ============================================================================\r\n\r\n/**\r\n * Get predefined options for a specific variant type\r\n * Only colors have predefined options for swatches, all other types use manual input\r\n */\r\nexport const getPredefinedOptionsForType = (variantTypeName: string): PredefinedVariantOption[] => {\r\n  switch (variantTypeName.toLowerCase()) {\r\n    case 'color':\r\n      return COLOR_OPTIONS;\r\n    // All other variant types now use manual input instead of predefined options\r\n    case 'size':\r\n    case 'material':\r\n    case 'style':\r\n    case 'pattern':\r\n    case 'capacity':\r\n    case 'weight':\r\n    case 'power':\r\n    case 'voltage':\r\n    case 'frequency':\r\n    case 'length':\r\n    case 'width':\r\n    case 'height':\r\n    case 'diameter':\r\n    case 'thickness':\r\n    case 'flavor':\r\n    case 'quantity':\r\n    case 'expiry':\r\n    case 'ingredients':\r\n    case 'shade':\r\n    case 'skin_type':\r\n    case 'coverage':\r\n    case 'spf':\r\n    case 'fit':\r\n    case 'sleeve':\r\n    case 'collar':\r\n    case 'occasion':\r\n    case 'season':\r\n    case 'connectivity':\r\n    case 'compatibility':\r\n    case 'resolution':\r\n    case 'storage':\r\n    case 'ram':\r\n    case 'type':\r\n    case 'finish':\r\n    case 'grade':\r\n    case 'brand':\r\n    case 'model':\r\n    case 'age_group':\r\n    case 'gender':\r\n    case 'fragrance':\r\n    case 'texture':\r\n    case 'temperature':\r\n    case 'certification':\r\n    case 'warranty':\r\n    default:\r\n      return [];\r\n  }\r\n};\r\n\r\n/**\r\n * Get all available variant types\r\n */\r\nexport const getAllVariantTypes = (): Omit<VariantType, 'created_at' | 'updated_at'>[] => {\r\n  return PREDEFINED_VARIANT_TYPES;\r\n};\r\n\r\n/**\r\n * Check if a variant type has predefined options\r\n * Only color variants have predefined options for swatches\r\n */\r\nexport const hasPredefineOptions = (variantTypeName: string): boolean => {\r\n  return variantTypeName.toLowerCase() === 'color';\r\n};\r\n\r\n/**\r\n * Search variant options by query string\r\n */\r\nexport const searchVariantOptions = (variantTypeName: string, query: string): PredefinedVariantOption[] => {\r\n  const options = getPredefinedOptionsForType(variantTypeName);\r\n  if (!query) return options;\r\n\r\n  const lowerQuery = query.toLowerCase();\r\n  return options.filter(option =>\r\n    option.display_value.toLowerCase().includes(lowerQuery) ||\r\n    option.value.toLowerCase().includes(lowerQuery)\r\n  );\r\n};\r\n\r\n/**\r\n * Get variant type by name\r\n */\r\nexport const getVariantTypeByName = (name: string): Omit<VariantType, 'created_at' | 'updated_at'> | null => {\r\n  return PREDEFINED_VARIANT_TYPES.find(type => type.name === name.toLowerCase()) || null;\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,iFAAiF;;;;;;;;;;AAsB1E,MAAM,2BAA6E;IACxF,uBAAuB;IACvB;QAAE,IAAI;QAAQ,MAAM;QAAQ,cAAc;QAAQ,aAAa;QAAmD,eAAe;QAAM,YAAY;IAAE;IACrJ;QAAE,IAAI;QAAS,MAAM;QAAS,cAAc;QAAS,aAAa;QAA4B,eAAe;QAAM,YAAY;IAAE;IACjI;QAAE,IAAI;QAAY,MAAM;QAAY,cAAc;QAAY,aAAa;QAAsD,eAAe;QAAM,YAAY;IAAE;IACpK;QAAE,IAAI;QAAS,MAAM;QAAS,cAAc;QAAS,aAAa;QAA6D,eAAe;QAAM,YAAY;IAAE;IAClK;QAAE,IAAI;QAAW,MAAM;QAAW,cAAc;QAAW,aAAa;QAA4D,eAAe;QAAM,YAAY;IAAE;IAEvK,2BAA2B;IAC3B;QAAE,IAAI;QAAY,MAAM;QAAY,cAAc;QAAY,aAAa;QAAsD,eAAe;QAAM,YAAY;IAAE;IACpK;QAAE,IAAI;QAAU,MAAM;QAAU,cAAc;QAAU,aAAa;QAA6D,eAAe;QAAM,YAAY;IAAE;IACrK;QAAE,IAAI;QAAS,MAAM;QAAS,cAAc;QAAS,aAAa;QAAsC,eAAe;QAAM,YAAY;IAAE;IAC3I;QAAE,IAAI;QAAW,MAAM;QAAW,cAAc;QAAW,aAAa;QAAmD,eAAe;QAAM,YAAY;IAAE;IAC9J;QAAE,IAAI;QAAa,MAAM;QAAa,cAAc;QAAa,aAAa;QAA0C,eAAe;QAAM,YAAY;IAAG;IAE5J,4BAA4B;IAC5B;QAAE,IAAI;QAAU,MAAM;QAAU,cAAc;QAAU,aAAa;QAAwC,eAAe;QAAM,YAAY;IAAG;IACjJ;QAAE,IAAI;QAAS,MAAM;QAAS,cAAc;QAAS,aAAa;QAAwC,eAAe;QAAM,YAAY;IAAG;IAC9I;QAAE,IAAI;QAAU,MAAM;QAAU,cAAc;QAAU,aAAa;QAAwC,eAAe;QAAM,YAAY;IAAG;IACjJ;QAAE,IAAI;QAAY,MAAM;QAAY,cAAc;QAAY,aAAa;QAA2C,eAAe;QAAM,YAAY;IAAG;IAC1J;QAAE,IAAI;QAAa,MAAM;QAAa,cAAc;QAAa,aAAa;QAA2C,eAAe;QAAM,YAAY;IAAG;IAE7J,qBAAqB;IACrB;QAAE,IAAI;QAAU,MAAM;QAAU,cAAc;QAAU,aAAa;QAAgE,eAAe;QAAM,YAAY;IAAG;IACzK;QAAE,IAAI;QAAY,MAAM;QAAY,cAAc;QAAY,aAAa;QAA8D,eAAe;QAAM,YAAY;IAAG;IAC7K;QAAE,IAAI;QAAU,MAAM;QAAU,cAAc;QAAU,aAAa;QAAiD,eAAe;QAAM,YAAY;IAAG;IAC1J;QAAE,IAAI;QAAe,MAAM;QAAe,cAAc;QAAe,aAAa;QAAkC,eAAe;QAAM,YAAY;IAAG;IAE1J,yBAAyB;IACzB;QAAE,IAAI;QAAS,MAAM;QAAS,cAAc;QAAS,aAAa;QAAkD,eAAe;QAAM,YAAY;IAAG;IACxJ;QAAE,IAAI;QAAa,MAAM;QAAa,cAAc;QAAa,aAAa;QAA2D,eAAe;QAAM,YAAY;IAAG;IAC7K;QAAE,IAAI;QAAY,MAAM;QAAY,cAAc;QAAY,aAAa;QAA8C,eAAe;QAAM,YAAY;IAAG;IAC7J;QAAE,IAAI;QAAO,MAAM;QAAO,cAAc;QAAO,aAAa;QAAwD,eAAe;QAAM,YAAY;IAAG;IAExJ,oBAAoB;IACpB;QAAE,IAAI;QAAO,MAAM;QAAO,cAAc;QAAO,aAAa;QAA6D,eAAe;QAAM,YAAY;IAAG;IAC7J;QAAE,IAAI;QAAU,MAAM;QAAU,cAAc;QAAU,aAAa;QAAyD,eAAe;QAAM,YAAY;IAAG;IAClK;QAAE,IAAI;QAAU,MAAM;QAAU,cAAc;QAAU,aAAa;QAAgD,eAAe;QAAM,YAAY;IAAG;IACzJ;QAAE,IAAI;QAAY,MAAM;QAAY,cAAc;QAAY,aAAa;QAA2D,eAAe;QAAM,YAAY;IAAG;IAC1K;QAAE,IAAI;QAAU,MAAM;QAAU,cAAc;QAAU,aAAa;QAAsD,eAAe;QAAM,YAAY;IAAG;IAE/J,2BAA2B;IAC3B;QAAE,IAAI;QAAgB,MAAM;QAAgB,cAAc;QAAgB,aAAa;QAAgD,eAAe;QAAM,YAAY;IAAG;IAC3K;QAAE,IAAI;QAAiB,MAAM;QAAiB,cAAc;QAAiB,aAAa;QAAyD,eAAe;QAAM,YAAY;IAAG;IACvL;QAAE,IAAI;QAAc,MAAM;QAAc,cAAc;QAAc,aAAa;QAA8C,eAAe;QAAM,YAAY;IAAG;IACnK;QAAE,IAAI;QAAW,MAAM;QAAW,cAAc;QAAW,aAAa;QAA8C,eAAe;QAAM,YAAY;IAAG;IAC1J;QAAE,IAAI;QAAO,MAAM;QAAO,cAAc;QAAO,aAAa;QAA0C,eAAe;QAAM,YAAY;IAAG;IAE1I,6BAA6B;IAC7B;QAAE,IAAI;QAAQ,MAAM;QAAQ,cAAc;QAAQ,aAAa;QAA6D,eAAe;QAAM,YAAY;IAAG;IAChK;QAAE,IAAI;QAAU,MAAM;QAAU,cAAc;QAAU,aAAa;QAA0D,eAAe;QAAM,YAAY;IAAG;IACnK;QAAE,IAAI;QAAS,MAAM;QAAS,cAAc;QAAS,aAAa;QAAiD,eAAe;QAAM,YAAY;IAAG;IACvJ;QAAE,IAAI;QAAS,MAAM;QAAS,cAAc;QAAS,aAAa;QAAiC,eAAe;QAAM,YAAY;IAAG;IACvI;QAAE,IAAI;QAAS,MAAM;QAAS,cAAc;QAAS,aAAa;QAA4B,eAAe;QAAM,YAAY;IAAG;IAClI;QAAE,IAAI;QAAa,MAAM;QAAa,cAAc;QAAa,aAAa;QAAyD,eAAe;QAAM,YAAY;IAAG;IAC3K;QAAE,IAAI;QAAU,MAAM;QAAU,cAAc;QAAU,aAAa;QAA4C,eAAe;QAAM,YAAY;IAAG;IAErJ,yBAAyB;IACzB;QAAE,IAAI;QAAa,MAAM;QAAa,cAAc;QAAa,aAAa;QAAwD,eAAe;QAAM,YAAY;IAAG;IAC1K;QAAE,IAAI;QAAW,MAAM;QAAW,cAAc;QAAW,aAAa;QAAqD,eAAe;QAAM,YAAY;IAAG;IACjK;QAAE,IAAI;QAAe,MAAM;QAAe,cAAc;QAAe,aAAa;QAAsD,eAAe;QAAM,YAAY;IAAG;IAC9K;QAAE,IAAI;QAAiB,MAAM;QAAiB,cAAc;QAAiB,aAAa;QAA6D,eAAe;QAAM,YAAY;IAAG;IAC3L;QAAE,IAAI;QAAY,MAAM;QAAY,cAAc;QAAY,aAAa;QAAqD,eAAe;QAAM,YAAY;IAAG;CACrK;AAWM,MAAM,gBAA2C;IACtD,eAAe;IACf;QAAE,OAAO;QAAS,eAAe;QAAS,YAAY;QAAW,YAAY;IAAE;IAC/E;QAAE,OAAO;QAAS,eAAe;QAAS,YAAY;QAAW,YAAY;IAAE;IAC/E;QAAE,OAAO;QAAQ,eAAe;QAAQ,YAAY;QAAW,YAAY;IAAE;IAC7E;QAAE,OAAO;QAAc,eAAe;QAAc,YAAY;QAAW,YAAY;IAAE;IACzF;QAAE,OAAO;QAAa,eAAe;QAAa,YAAY;QAAW,YAAY;IAAE;IACvF;QAAE,OAAO;QAAY,eAAe;QAAY,YAAY;QAAW,YAAY;IAAE;IAErF,eAAe;IACf;QAAE,OAAO;QAAO,eAAe;QAAO,YAAY;QAAW,YAAY;IAAE;IAC3E;QAAE,OAAO;QAAY,eAAe;QAAY,YAAY;QAAW,YAAY;IAAE;IACrF;QAAE,OAAO;QAAa,eAAe;QAAa,YAAY;QAAW,YAAY;IAAE;IACvF;QAAE,OAAO;QAAW,eAAe;QAAW,YAAY;QAAW,YAAY;IAAG;IACpF;QAAE,OAAO;QAAU,eAAe;QAAU,YAAY;QAAW,YAAY;IAAG;IAClF;QAAE,OAAO;QAAY,eAAe;QAAY,YAAY;QAAW,YAAY;IAAG;IAEtF,gBAAgB;IAChB;QAAE,OAAO;QAAQ,eAAe;QAAQ,YAAY;QAAW,YAAY;IAAG;IAC9E;QAAE,OAAO;QAAQ,eAAe;QAAQ,YAAY;QAAW,YAAY;IAAG;IAC9E;QAAE,OAAO;QAAc,eAAe;QAAc,YAAY;QAAW,YAAY;IAAG;IAC1F;QAAE,OAAO;QAAa,eAAe;QAAa,YAAY;QAAW,YAAY;IAAG;IACxF;QAAE,OAAO;QAAY,eAAe;QAAY,YAAY;QAAW,YAAY;IAAG;IACtF;QAAE,OAAO;QAAc,eAAe;QAAc,YAAY;QAAW,YAAY;IAAG;IAC1F;QAAE,OAAO;QAAa,eAAe;QAAa,YAAY;QAAW,YAAY;IAAG;IACxF;QAAE,OAAO;QAAQ,eAAe;QAAQ,YAAY;QAAW,YAAY;IAAG;IAC9E;QAAE,OAAO;QAAQ,eAAe;QAAQ,YAAY;QAAW,YAAY;IAAG;IAE9E,iBAAiB;IACjB;QAAE,OAAO;QAAS,eAAe;QAAS,YAAY;QAAW,YAAY;IAAG;IAChF;QAAE,OAAO;QAAc,eAAe;QAAc,YAAY;QAAW,YAAY;IAAG;IAC1F;QAAE,OAAO;QAAe,eAAe;QAAe,YAAY;QAAW,YAAY;IAAG;IAC5F;QAAE,OAAO;QAAQ,eAAe;QAAQ,YAAY;QAAW,YAAY;IAAG;IAC9E;QAAE,OAAO;QAAS,eAAe;QAAS,YAAY;QAAW,YAAY;IAAG;IAChF;QAAE,OAAO;QAAgB,eAAe;QAAgB,YAAY;QAAW,YAAY;IAAG;IAC9F;QAAE,OAAO;QAAQ,eAAe;QAAQ,YAAY;QAAW,YAAY;IAAG;IAE9E,yBAAyB;IACzB;QAAE,OAAO;QAAU,eAAe;QAAU,YAAY;QAAW,YAAY;IAAG;IAClF;QAAE,OAAO;QAAgB,eAAe;QAAgB,YAAY;QAAW,YAAY;IAAG;IAC9F;QAAE,OAAO;QAAU,eAAe;QAAU,YAAY;QAAW,YAAY;IAAG;IAClF;QAAE,OAAO;QAAe,eAAe;QAAe,YAAY;QAAW,YAAY;IAAG;IAC5F;QAAE,OAAO;QAAS,eAAe;QAAS,YAAY;QAAW,YAAY;IAAG;IAChF;QAAE,OAAO;QAAS,eAAe;QAAS,YAAY;QAAW,YAAY;IAAG;IAEhF,uBAAuB;IACvB;QAAE,OAAO;QAAU,eAAe;QAAU,YAAY;QAAW,YAAY;IAAG;IAClF;QAAE,OAAO;QAAe,eAAe;QAAe,YAAY;QAAW,YAAY;IAAG;IAC5F;QAAE,OAAO;QAAgB,eAAe;QAAgB,YAAY;QAAW,YAAY;IAAG;IAC9F;QAAE,OAAO;QAAU,eAAe;QAAU,YAAY;QAAW,YAAY;IAAG;IAClF;QAAE,OAAO;QAAY,eAAe;QAAY,YAAY;QAAW,YAAY;IAAG;IACtF;QAAE,OAAO;QAAU,eAAe;QAAU,YAAY;QAAW,YAAY;IAAG;IAClF;QAAE,OAAO;QAAQ,eAAe;QAAQ,YAAY;QAAW,YAAY;IAAG;IAC9E;QAAE,OAAO;QAAY,eAAe;QAAY,YAAY;QAAW,YAAY;IAAG;IACtF;QAAE,OAAO;QAAW,eAAe;QAAW,YAAY;QAAW,YAAY;IAAG;IAEpF,iBAAiB;IACjB;QAAE,OAAO;QAAS,eAAe;QAAS,YAAY;QAAW,YAAY;IAAG;IAChF;QAAE,OAAO;QAAc,eAAe;QAAc,YAAY;QAAW,YAAY;IAAG;IAC1F;QAAE,OAAO;QAAe,eAAe;QAAe,YAAY;QAAW,YAAY;IAAG;IAC5F;QAAE,OAAO;QAAO,eAAe;QAAO,YAAY;QAAW,YAAY;IAAG;IAC5E;QAAE,OAAO;QAAS,eAAe;QAAS,YAAY;QAAW,YAAY;IAAG;IAChF;QAAE,OAAO;QAAS,eAAe;QAAS,YAAY;QAAW,YAAY;IAAG;IAChF;QAAE,OAAO;QAAS,eAAe;QAAS,YAAY;QAAW,YAAY;IAAG;IAEhF,kBAAkB;IAClB;QAAE,OAAO;QAAQ,eAAe;QAAQ,YAAY;QAAW,YAAY;IAAG;IAC9E;QAAE,OAAO;QAAU,eAAe;QAAU,YAAY;QAAW,YAAY;IAAG;IAClF;QAAE,OAAO;QAAU,eAAe;QAAU,YAAY;QAAW,YAAY;IAAG;IAClF;QAAE,OAAO;QAAU,eAAe;QAAU,YAAY;QAAW,YAAY;IAAG;IAClF;QAAE,OAAO;QAAa,eAAe;QAAa,YAAY;QAAW,YAAY;IAAG;IAExF,UAAU;IACV;QAAE,OAAO;QAAc,eAAe;QAAc,YAAY;IAAG;CACpE;AAqBM,MAAM,8BAA8B,CAAC;IAC1C,OAAQ,gBAAgB,WAAW;QACjC,KAAK;YACH,OAAO;QACT,6EAA6E;QAC7E,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL;YACE,OAAO,EAAE;IACb;AACF;AAKO,MAAM,qBAAqB;IAChC,OAAO;AACT;AAMO,MAAM,sBAAsB,CAAC;IAClC,OAAO,gBAAgB,WAAW,OAAO;AAC3C;AAKO,MAAM,uBAAuB,CAAC,iBAAyB;IAC5D,MAAM,UAAU,4BAA4B;IAC5C,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,aAAa,MAAM,WAAW;IACpC,OAAO,QAAQ,MAAM,CAAC,CAAA,SACpB,OAAO,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,eAC5C,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;AAExC;AAKO,MAAM,uBAAuB,CAAC;IACnC,OAAO,yBAAyB,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,KAAK,WAAW,OAAO;AACpF", "debugId": null}}, {"offset": {"line": 2194, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/product/components/VariantSelector.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\n\r\nimport { Check } from \"lucide-react\";\r\nimport { ProductVariant } from \"@/types/variants\";\r\nimport { getPredefinedOptionsForType } from \"@/lib/constants/predefinedVariants\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface VariantSelectorProps {\r\n  variants: ProductVariant[];\r\n  selectedVariant?: ProductVariant | null;\r\n  onVariantSelect: (_variant: ProductVariant | null) => void;\r\n  className?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface VariantOption {\r\n  type: string;\r\n  value: string;\r\n  display_value: string;\r\n  color_code?: string;\r\n  available: boolean;\r\n  variants: ProductVariant[];\r\n}\r\n\r\nexport default function VariantSelector({\r\n  variants,\r\n  selectedVariant,\r\n  onVariantSelect,\r\n  className,\r\n  disabled = false,\r\n}: VariantSelectorProps) {\r\n  const [variantTypes, setVariantTypes] = useState<\r\n    Record<string, VariantOption[]>\r\n  >({});\r\n  const [selectedOptions, setSelectedOptions] = useState<\r\n    Record<string, string>\r\n  >({});\r\n\r\n  // Process variants to extract variant types and options\r\n  useEffect(() => {\r\n    if (!variants || variants.length === 0) return;\r\n\r\n    const types: Record<string, Set<string>> = {};\r\n    const typeOptions: Record<string, VariantOption[]> = {};\r\n\r\n    // Extract all variant types and their possible values from actual variants\r\n    variants.forEach((variant) => {\r\n      Object.entries(variant.variant_values || {}).forEach(([type, value]) => {\r\n        if (!types[type]) {\r\n          types[type] = new Set();\r\n        }\r\n        types[type].add(value as string);\r\n      });\r\n    });\r\n\r\n    // Create variant options for each type, only showing values that exist in variants\r\n    Object.entries(types).forEach(([type, values]) => {\r\n      const predefinedOptions = getPredefinedOptionsForType(type);\r\n\r\n      typeOptions[type] = Array.from(values)\r\n        .map((value) => {\r\n          const variantsWithThisOption = variants.filter(\r\n            (variant) => variant.variant_values && (variant.variant_values as any)[type] === value\r\n          );\r\n\r\n          // Find predefined option for display value and color code\r\n          const predefinedOption = predefinedOptions.find(\r\n            (opt) => opt.value === value\r\n          );\r\n\r\n          return {\r\n            type,\r\n            value,\r\n            display_value: predefinedOption?.display_value || value,\r\n            color_code: predefinedOption?.color_code,\r\n            available: variantsWithThisOption.some((v) => v.is_available),\r\n            variants: variantsWithThisOption,\r\n          };\r\n        })\r\n        .sort((a, b) => {\r\n          // Sort by predefined sort order if available\r\n          const aPredefined = predefinedOptions.find(\r\n            (opt) => opt.value === a.value\r\n          );\r\n          const bPredefined = predefinedOptions.find(\r\n            (opt) => opt.value === b.value\r\n          );\r\n          if (aPredefined && bPredefined) {\r\n            return aPredefined.sort_order - bPredefined.sort_order;\r\n          }\r\n          return a.display_value.localeCompare(b.display_value);\r\n        });\r\n    });\r\n\r\n    setVariantTypes(typeOptions);\r\n\r\n    // Don't auto-select any options - let user make their own choices\r\n    // This ensures all available options are visible initially\r\n  }, [variants, selectedOptions]);\r\n\r\n  // Update selected variant when options change, or clear selection when no options\r\n  useEffect(() => {\r\n    // If no options are selected, clear the variant selection\r\n    if (Object.keys(selectedOptions).length === 0) {\r\n      if (selectedVariant !== null) {\r\n        onVariantSelect(null);\r\n      }\r\n      return;\r\n    }\r\n\r\n    const matchingVariant = variants.find((variant) => {\r\n      return Object.entries(selectedOptions).every(\r\n        ([type, value]) => variant.variant_values && (variant.variant_values as any)[type] === value\r\n      );\r\n    });\r\n\r\n    // If we found a matching variant and it's different from current selection\r\n    if (matchingVariant && matchingVariant !== selectedVariant) {\r\n      onVariantSelect(matchingVariant);\r\n    }\r\n    // If no matching variant found but we have a selected variant, clear it\r\n    else if (!matchingVariant && selectedVariant !== null) {\r\n      onVariantSelect(null);\r\n    }\r\n  }, [selectedOptions, variants, selectedVariant, onVariantSelect]);\r\n\r\n  // Handle option selection with deselection support\r\n  const handleOptionSelect = (type: string, value: string) => {\r\n    if (disabled) return;\r\n\r\n    setSelectedOptions((prev) => {\r\n      const newSelection = { ...prev };\r\n\r\n      // If option is already selected, deselect it\r\n      if (newSelection[type] === value) {\r\n        delete newSelection[type];\r\n      } else {\r\n        // Otherwise, select it\r\n        newSelection[type] = value;\r\n      }\r\n\r\n      return newSelection;\r\n    });\r\n  };\r\n\r\n  // Check if an option is available given current selections (progressive filtering)\r\n  const isOptionAvailable = (type: string, value: string): boolean => {\r\n    // If no selections have been made yet, show all options that exist in any available variant\r\n    if (Object.keys(selectedOptions).length === 0) {\r\n      return variants.some(\r\n        (variant) =>\r\n          variant.is_available && variant.variant_values && (variant.variant_values as any)[type] === value\r\n      );\r\n    }\r\n\r\n    // If selections have been made, use progressive filtering\r\n    const testSelection = { ...selectedOptions, [type]: value };\r\n\r\n    // Check if any available variant can satisfy this partial selection\r\n    return variants.some((variant) => {\r\n      if (!variant.is_available) return false;\r\n\r\n      // Check if this variant matches all currently selected options plus the new one\r\n      return Object.entries(testSelection).every(([testType, testValue]) => {\r\n        return variant.variant_values && (variant.variant_values as any)[testType] === testValue;\r\n      });\r\n    });\r\n  };\r\n\r\n  if (!variants || variants.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className={cn(\"space-y-6\", className)}>\r\n      {Object.entries(variantTypes).map(([type, options]) => (\r\n        <motion.div\r\n          key={type}\r\n          initial={{ opacity: 0, y: 10 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.3 }}\r\n          className=\"space-y-3\"\r\n        >\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <h3 className=\"text-base font-semibold text-neutral-900 dark:text-neutral-100 capitalize\">\r\n              {type}\r\n            </h3>\r\n            {selectedOptions[type] && (\r\n              <Badge variant=\"secondary\" className=\"text-xs font-medium\">\r\n                {options.find((opt) => opt.value === selectedOptions[type])\r\n                  ?.display_value || selectedOptions[type]}\r\n              </Badge>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"flex flex-wrap gap-3\">\r\n            {options.map((option) => {\r\n              const isSelected = selectedOptions[type] === option.value;\r\n              const isAvailable = isOptionAvailable(type, option.value);\r\n              const isColorType = type.toLowerCase() === \"color\";\r\n\r\n              return (\r\n                <motion.div\r\n                  key={`${type}-${option.value}`}\r\n                  whileHover={isAvailable && !disabled ? { scale: 1.05 } : {}}\r\n                  whileTap={isAvailable && !disabled ? { scale: 0.95 } : {}}\r\n                  className=\"relative\"\r\n                >\r\n                  {isColorType && option.color_code ? (\r\n                    // Color swatch for color variants\r\n                    <button\r\n                      onClick={() => handleOptionSelect(type, option.value)}\r\n                      disabled={(!isAvailable && !isSelected) || disabled}\r\n                      className={cn(\r\n                        \"relative w-12 h-12 rounded-full transition-all duration-200 border-2\",\r\n                        \"border-neutral-300 dark:border-neutral-600\",\r\n                        isSelected &&\r\n                          \"ring-2 ring-[var(--brand-gold)] ring-offset-2 ring-offset-white dark:ring-offset-neutral-900\",\r\n                        !isAvailable &&\r\n                          !isSelected &&\r\n                          \"opacity-60 cursor-not-allowed\",\r\n                        (isAvailable || isSelected) &&\r\n                          \"cursor-pointer hover:scale-105\"\r\n                      )}\r\n                      style={{ backgroundColor: option.color_code }}\r\n                      title={\r\n                        isSelected\r\n                          ? `${option.display_value} (Click to deselect)`\r\n                          : option.display_value\r\n                      }\r\n                    >\r\n                      <AnimatePresence>\r\n                        {isSelected && (\r\n                          <motion.div\r\n                            initial={{ scale: 0, opacity: 0 }}\r\n                            animate={{ scale: 1, opacity: 1 }}\r\n                            exit={{ scale: 0, opacity: 0 }}\r\n                            transition={{ duration: 0.2 }}\r\n                            className=\"absolute inset-0 flex items-center justify-center\"\r\n                          >\r\n                            <Check\r\n                              className={cn(\r\n                                \"h-5 w-5 drop-shadow-sm\",\r\n                                // Use white check for dark colors, dark check for light colors\r\n                                option.color_code === \"#FFFFFF\" ||\r\n                                  option.color_code === \"#FFFF00\" ||\r\n                                  option.color_code === \"#FFC0CB\"\r\n                                  ? \"text-neutral-800\"\r\n                                  : \"text-white\"\r\n                              )}\r\n                            />\r\n                          </motion.div>\r\n                        )}\r\n                      </AnimatePresence>\r\n                    </button>\r\n                  ) : (\r\n                    // Regular button for non-color variants\r\n                    <button\r\n                      onClick={() => handleOptionSelect(type, option.value)}\r\n                      disabled={(!isAvailable && !isSelected) || disabled}\r\n                      className={cn(\r\n                        \"relative px-4 py-2.5 rounded-lg transition-all duration-200 font-medium text-sm border-2\",\r\n                        \"border-neutral-300 dark:border-neutral-600\",\r\n                        isSelected && [\r\n                          \"bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)]\",\r\n                          \"border-[var(--brand-gold)] ring-2 ring-[var(--brand-gold)]/30\",\r\n                        ],\r\n                        !isAvailable &&\r\n                          !isSelected && [\r\n                            \"opacity-60 cursor-not-allowed\",\r\n                            \"text-neutral-400 dark:text-neutral-600\",\r\n                          ],\r\n                        (isAvailable || isSelected) &&\r\n                          !isSelected && [\r\n                            \"text-neutral-700 dark:text-neutral-300\",\r\n                            \"hover:scale-105 hover:border-neutral-400 dark:hover:border-neutral-500\",\r\n                          ],\r\n                        (isAvailable || isSelected) && \"cursor-pointer\"\r\n                      )}\r\n                    >\r\n                      <span className=\"flex items-center gap-2\">\r\n                        {option.display_value}\r\n                        <AnimatePresence>\r\n                          {isSelected && (\r\n                            <motion.div\r\n                              initial={{ scale: 0, opacity: 0 }}\r\n                              animate={{ scale: 1, opacity: 1 }}\r\n                              exit={{ scale: 0, opacity: 0 }}\r\n                              transition={{ duration: 0.2 }}\r\n                            >\r\n                              <Check className=\"h-4 w-4\" />\r\n                            </motion.div>\r\n                          )}\r\n                        </AnimatePresence>\r\n                      </span>\r\n                    </button>\r\n                  )}\r\n                </motion.div>\r\n              );\r\n            })}\r\n          </div>\r\n        </motion.div>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAEA;AAEA;AACA;AATA;;;;;;;;AA4Be,SAAS,gBAAgB,EACtC,QAAQ,EACR,eAAe,EACf,eAAe,EACf,SAAS,EACT,WAAW,KAAK,EACK;IACrB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAE7C,CAAC;IACH,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAEnD,CAAC;IAEH,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;QAExC,MAAM,QAAqC,CAAC;QAC5C,MAAM,cAA+C,CAAC;QAEtD,2EAA2E;QAC3E,SAAS,OAAO,CAAC,CAAC;YAChB,OAAO,OAAO,CAAC,QAAQ,cAAc,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,MAAM,MAAM;gBACjE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;oBAChB,KAAK,CAAC,KAAK,GAAG,IAAI;gBACpB;gBACA,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC;YAClB;QACF;QAEA,mFAAmF;QACnF,OAAO,OAAO,CAAC,OAAO,OAAO,CAAC,CAAC,CAAC,MAAM,OAAO;YAC3C,MAAM,oBAAoB,CAAA,GAAA,sIAAA,CAAA,8BAA2B,AAAD,EAAE;YAEtD,WAAW,CAAC,KAAK,GAAG,MAAM,IAAI,CAAC,QAC5B,GAAG,CAAC,CAAC;gBACJ,MAAM,yBAAyB,SAAS,MAAM,CAC5C,CAAC,UAAY,QAAQ,cAAc,IAAI,AAAC,QAAQ,cAAc,AAAQ,CAAC,KAAK,KAAK;gBAGnF,0DAA0D;gBAC1D,MAAM,mBAAmB,kBAAkB,IAAI,CAC7C,CAAC,MAAQ,IAAI,KAAK,KAAK;gBAGzB,OAAO;oBACL;oBACA;oBACA,eAAe,kBAAkB,iBAAiB;oBAClD,YAAY,kBAAkB;oBAC9B,WAAW,uBAAuB,IAAI,CAAC,CAAC,IAAM,EAAE,YAAY;oBAC5D,UAAU;gBACZ;YACF,GACC,IAAI,CAAC,CAAC,GAAG;gBACR,6CAA6C;gBAC7C,MAAM,cAAc,kBAAkB,IAAI,CACxC,CAAC,MAAQ,IAAI,KAAK,KAAK,EAAE,KAAK;gBAEhC,MAAM,cAAc,kBAAkB,IAAI,CACxC,CAAC,MAAQ,IAAI,KAAK,KAAK,EAAE,KAAK;gBAEhC,IAAI,eAAe,aAAa;oBAC9B,OAAO,YAAY,UAAU,GAAG,YAAY,UAAU;gBACxD;gBACA,OAAO,EAAE,aAAa,CAAC,aAAa,CAAC,EAAE,aAAa;YACtD;QACJ;QAEA,gBAAgB;IAEhB,kEAAkE;IAClE,2DAA2D;IAC7D,GAAG;QAAC;QAAU;KAAgB;IAE9B,kFAAkF;IAClF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0DAA0D;QAC1D,IAAI,OAAO,IAAI,CAAC,iBAAiB,MAAM,KAAK,GAAG;YAC7C,IAAI,oBAAoB,MAAM;gBAC5B,gBAAgB;YAClB;YACA;QACF;QAEA,MAAM,kBAAkB,SAAS,IAAI,CAAC,CAAC;YACrC,OAAO,OAAO,OAAO,CAAC,iBAAiB,KAAK,CAC1C,CAAC,CAAC,MAAM,MAAM,GAAK,QAAQ,cAAc,IAAI,AAAC,QAAQ,cAAc,AAAQ,CAAC,KAAK,KAAK;QAE3F;QAEA,2EAA2E;QAC3E,IAAI,mBAAmB,oBAAoB,iBAAiB;YAC1D,gBAAgB;QAClB,OAEK,IAAI,CAAC,mBAAmB,oBAAoB,MAAM;YACrD,gBAAgB;QAClB;IACF,GAAG;QAAC;QAAiB;QAAU;QAAiB;KAAgB;IAEhE,mDAAmD;IACnD,MAAM,qBAAqB,CAAC,MAAc;QACxC,IAAI,UAAU;QAEd,mBAAmB,CAAC;YAClB,MAAM,eAAe;gBAAE,GAAG,IAAI;YAAC;YAE/B,6CAA6C;YAC7C,IAAI,YAAY,CAAC,KAAK,KAAK,OAAO;gBAChC,OAAO,YAAY,CAAC,KAAK;YAC3B,OAAO;gBACL,uBAAuB;gBACvB,YAAY,CAAC,KAAK,GAAG;YACvB;YAEA,OAAO;QACT;IACF;IAEA,mFAAmF;IACnF,MAAM,oBAAoB,CAAC,MAAc;QACvC,4FAA4F;QAC5F,IAAI,OAAO,IAAI,CAAC,iBAAiB,MAAM,KAAK,GAAG;YAC7C,OAAO,SAAS,IAAI,CAClB,CAAC,UACC,QAAQ,YAAY,IAAI,QAAQ,cAAc,IAAI,AAAC,QAAQ,cAAc,AAAQ,CAAC,KAAK,KAAK;QAElG;QAEA,0DAA0D;QAC1D,MAAM,gBAAgB;YAAE,GAAG,eAAe;YAAE,CAAC,KAAK,EAAE;QAAM;QAE1D,oEAAoE;QACpE,OAAO,SAAS,IAAI,CAAC,CAAC;YACpB,IAAI,CAAC,QAAQ,YAAY,EAAE,OAAO;YAElC,gFAAgF;YAChF,OAAO,OAAO,OAAO,CAAC,eAAe,KAAK,CAAC,CAAC,CAAC,UAAU,UAAU;gBAC/D,OAAO,QAAQ,cAAc,IAAI,AAAC,QAAQ,cAAc,AAAQ,CAAC,SAAS,KAAK;YACjF;QACF;IACF;IAEA,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;QACtC,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B,OAAO,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,MAAM,QAAQ,iBAChD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX;;;;;;4BAEF,eAAe,CAAC,KAAK,kBACpB,8OAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAClC,QAAQ,IAAI,CAAC,CAAC,MAAQ,IAAI,KAAK,KAAK,eAAe,CAAC,KAAK,GACtD,iBAAiB,eAAe,CAAC,KAAK;;;;;;;;;;;;kCAKhD,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC;4BACZ,MAAM,aAAa,eAAe,CAAC,KAAK,KAAK,OAAO,KAAK;4BACzD,MAAM,cAAc,kBAAkB,MAAM,OAAO,KAAK;4BACxD,MAAM,cAAc,KAAK,WAAW,OAAO;4BAE3C,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,YAAY,eAAe,CAAC,WAAW;oCAAE,OAAO;gCAAK,IAAI,CAAC;gCAC1D,UAAU,eAAe,CAAC,WAAW;oCAAE,OAAO;gCAAK,IAAI,CAAC;gCACxD,WAAU;0CAET,eAAe,OAAO,UAAU,GAC/B,kCAAkC;8CAClC,8OAAC;oCACC,SAAS,IAAM,mBAAmB,MAAM,OAAO,KAAK;oCACpD,UAAU,AAAC,CAAC,eAAe,CAAC,cAAe;oCAC3C,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wEACA,8CACA,cACE,gGACF,CAAC,eACC,CAAC,cACD,iCACF,CAAC,eAAe,UAAU,KACxB;oCAEJ,OAAO;wCAAE,iBAAiB,OAAO,UAAU;oCAAC;oCAC5C,OACE,aACI,GAAG,OAAO,aAAa,CAAC,oBAAoB,CAAC,GAC7C,OAAO,aAAa;8CAG1B,cAAA,8OAAC,yLAAA,CAAA,kBAAe;kDACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,OAAO;gDAAG,SAAS;4CAAE;4CAChC,SAAS;gDAAE,OAAO;gDAAG,SAAS;4CAAE;4CAChC,MAAM;gDAAE,OAAO;gDAAG,SAAS;4CAAE;4CAC7B,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,WAAU;sDAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDACJ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0BACA,+DAA+D;gDAC/D,OAAO,UAAU,KAAK,aACpB,OAAO,UAAU,KAAK,aACtB,OAAO,UAAU,KAAK,YACpB,qBACA;;;;;;;;;;;;;;;;;;;;2CAQhB,wCAAwC;8CACxC,8OAAC;oCACC,SAAS,IAAM,mBAAmB,MAAM,OAAO,KAAK;oCACpD,UAAU,AAAC,CAAC,eAAe,CAAC,cAAe;oCAC3C,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4FACA,8CACA,cAAc;wCACZ;wCACA;qCACD,EACD,CAAC,eACC,CAAC,cAAc;wCACb;wCACA;qCACD,EACH,CAAC,eAAe,UAAU,KACxB,CAAC,cAAc;wCACb;wCACA;qCACD,EACH,CAAC,eAAe,UAAU,KAAK;8CAGjC,cAAA,8OAAC;wCAAK,WAAU;;4CACb,OAAO,aAAa;0DACrB,8OAAC,yLAAA,CAAA,kBAAe;0DACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,OAAO;wDAAG,SAAS;oDAAE;oDAChC,SAAS;wDAAE,OAAO;wDAAG,SAAS;oDAAE;oDAChC,MAAM;wDAAE,OAAO;wDAAG,SAAS;oDAAE;oDAC7B,YAAY;wDAAE,UAAU;oDAAI;8DAE5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAvFxB,GAAG,KAAK,CAAC,EAAE,OAAO,KAAK,EAAE;;;;;wBAgGpC;;;;;;;eA1HG;;;;;;;;;;AAgIf", "debugId": null}}, {"offset": {"line": 2523, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/product/components/ProductAdSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion, Variants } from \"framer-motion\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { AdData } from \"@/types/ad\";\r\n\r\ninterface ProductAdSectionProps {\r\n  topAdData: AdData | null;\r\n  itemVariants: Variants;\r\n  businessCustomAd?: {\r\n    enabled?: boolean;\r\n    image_url?: string;\r\n    link_url?: string;\r\n  } | null;\r\n  userPlan?: \"free\" | \"basic\" | \"growth\" | \"pro\" | \"enterprise\";\r\n}\r\n\r\n// Simple URL validation utility\r\nconst isValidUrl = (url: string): boolean => {\r\n  try {\r\n    new URL(url);\r\n    return true;\r\n  } catch {\r\n    return false;\r\n  }\r\n};\r\n\r\nexport default function ProductAdSection({ topAdData, itemVariants, businessCustomAd, userPlan }: ProductAdSectionProps) {\r\n  // Check if business owner has Pro/Enterprise access for custom ads\r\n  const hasProEnterpriseAccess = userPlan === \"pro\" || userPlan === \"enterprise\";\r\n\r\n  // Validate custom ad data - must have Pro/Enterprise plan, enabled=true, and valid image URL\r\n  const isCustomAdValid = businessCustomAd &&\r\n    typeof businessCustomAd === 'object' &&\r\n    businessCustomAd.enabled === true &&\r\n    businessCustomAd.image_url &&\r\n    typeof businessCustomAd.image_url === 'string' &&\r\n    businessCustomAd.image_url.trim() !== \"\" &&\r\n    isValidUrl(businessCustomAd.image_url);\r\n\r\n  // Validate custom ad link URL if provided\r\n  const hasValidLinkUrl = businessCustomAd?.link_url &&\r\n    typeof businessCustomAd.link_url === 'string' &&\r\n    businessCustomAd.link_url.trim() !== \"\" &&\r\n    isValidUrl(businessCustomAd.link_url);\r\n\r\n  // Determine which ad to show - business custom ad takes priority (only for Pro/Enterprise with valid data and enabled=true)\r\n  const shouldShowBusinessAd = hasProEnterpriseAccess && isCustomAdValid;\r\n  const shouldShowTopAd = !shouldShowBusinessAd && topAdData && topAdData.imageUrl;\r\n\r\n\r\n\r\n  return (\r\n    <motion.div\r\n      variants={itemVariants}\r\n      className=\"w-full\"\r\n    >\r\n      {/* Ad Slot - No card container */}\r\n      <div className=\"flex-shrink-0 w-full overflow-hidden\">\r\n        {shouldShowBusinessAd ? (\r\n          /* Business Custom Ad */\r\n          hasValidLinkUrl ? (\r\n            <Link\r\n              href={businessCustomAd!.link_url!}\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              className=\"block w-full overflow-hidden\"\r\n            >\r\n              <div className=\"relative w-full\">\r\n                <Image\r\n                  src={businessCustomAd!.image_url!}\r\n                  alt=\"Business Advertisement\"\r\n                  width={1200}\r\n                  height={675} // 16:9 aspect ratio\r\n                  className=\"w-full h-auto object-contain max-w-full\"\r\n                  unoptimized\r\n                />\r\n                {/* Business ad indicator */}\r\n                <div className=\"absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded\">\r\n                  Sponsored\r\n                </div>\r\n              </div>\r\n            </Link>\r\n          ) : (\r\n            /* Non-clickable Business Custom Ad (no link URL) */\r\n            <div className=\"block w-full overflow-hidden\">\r\n              <div className=\"relative w-full\">\r\n                <Image\r\n                  src={businessCustomAd!.image_url!}\r\n                  alt=\"Business Advertisement\"\r\n                  width={1200}\r\n                  height={675} // 16:9 aspect ratio\r\n                  className=\"w-full h-auto object-contain max-w-full\"\r\n                  unoptimized\r\n                />\r\n                {/* Business ad indicator */}\r\n                <div className=\"absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded\">\r\n                  Sponsored\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )\r\n        ) : shouldShowTopAd ? (\r\n          <Link\r\n            href={topAdData!.linkUrl || \"#\"}\r\n            target=\"_blank\"\r\n            rel=\"noopener noreferrer\"\r\n            className=\"block w-full overflow-hidden\"\r\n          >\r\n            <div className=\"relative w-full\">\r\n              <Image\r\n                src={topAdData!.imageUrl}\r\n                alt=\"Advertisement\"\r\n                width={1200}\r\n                height={675} // 16:9 aspect ratio\r\n                className=\"w-full h-auto object-contain max-w-full\"\r\n                unoptimized\r\n              />\r\n            </div>\r\n          </Link>\r\n        ) : (\r\n          <div className=\"border border-dashed rounded-lg p-4 flex items-center justify-center w-full text-neutral-500 bg-neutral-50 dark:bg-neutral-800/50 min-h-[300px]\">\r\n            {/* Placeholder */}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAkBA,gCAAgC;AAChC,MAAM,aAAa,CAAC;IAClB,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEe,SAAS,iBAAiB,EAAE,SAAS,EAAE,YAAY,EAAE,gBAAgB,EAAE,QAAQ,EAAyB;IACrH,mEAAmE;IACnE,MAAM,yBAAyB,aAAa,SAAS,aAAa;IAElE,6FAA6F;IAC7F,MAAM,kBAAkB,oBACtB,OAAO,qBAAqB,YAC5B,iBAAiB,OAAO,KAAK,QAC7B,iBAAiB,SAAS,IAC1B,OAAO,iBAAiB,SAAS,KAAK,YACtC,iBAAiB,SAAS,CAAC,IAAI,OAAO,MACtC,WAAW,iBAAiB,SAAS;IAEvC,0CAA0C;IAC1C,MAAM,kBAAkB,kBAAkB,YACxC,OAAO,iBAAiB,QAAQ,KAAK,YACrC,iBAAiB,QAAQ,CAAC,IAAI,OAAO,MACrC,WAAW,iBAAiB,QAAQ;IAEtC,4HAA4H;IAC5H,MAAM,uBAAuB,0BAA0B;IACvD,MAAM,kBAAkB,CAAC,wBAAwB,aAAa,UAAU,QAAQ;IAIhF,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,WAAU;kBAGV,cAAA,8OAAC;YAAI,WAAU;sBACZ,uBACC,sBAAsB,GACtB,gCACE,8OAAC,4JAAA,CAAA,UAAI;gBACH,MAAM,iBAAkB,QAAQ;gBAChC,QAAO;gBACP,KAAI;gBACJ,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK,iBAAkB,SAAS;4BAChC,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;4BACV,WAAW;;;;;;sCAGb,8OAAC;4BAAI,WAAU;sCAA0E;;;;;;;;;;;;;;;;uBAM7F,kDAAkD,iBAClD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK,iBAAkB,SAAS;4BAChC,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;4BACV,WAAW;;;;;;sCAGb,8OAAC;4BAAI,WAAU;sCAA0E;;;;;;;;;;;;;;;;uBAM7F,gCACF,8OAAC,4JAAA,CAAA,UAAI;gBACH,MAAM,UAAW,OAAO,IAAI;gBAC5B,QAAO;gBACP,KAAI;gBACJ,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,UAAW,QAAQ;wBACxB,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;wBACV,WAAW;;;;;;;;;;;;;;;qCAKjB,8OAAC;gBAAI,WAAU;;;;;;;;;;;;;;;;AAOzB", "debugId": null}}, {"offset": {"line": 2684, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/product/components/ProductDetail.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport Image from \"next/image\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Package, Share2, Home, ZoomIn, Info } from \"lucide-react\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { Alert, AlertDescription, AlertTitle } from \"@/components/ui/alert\";\r\nimport {\r\n  Breadcrumb,\r\n  BreadcrumbList,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator\r\n} from \"@/components/ui/breadcrumb\";\r\nimport {\r\n  Carousel,\r\n  CarouselContent,\r\n  CarouselItem,\r\n  type CarouselApi\r\n} from \"@/components/ui/carousel\";\r\n\r\nimport { ProductVariant } from \"@/types/variants\";\r\nimport { ProductWithVariants } from \"@/types/products\";\r\nimport WhatsAppButton, { WhatsAppButtonDisabled } from \"./WhatsAppButton\";\r\nimport PhoneButton, { PhoneButtonDisabled } from \"./PhoneButton\";\r\nimport BuyNowButton from \"./BuyNowButton\";\r\nimport ImageZoomModal from \"./ImageZoomModal\";\r\nimport VariantSelector from \"./VariantSelector\";\r\nimport { formatCurrency } from \"@/lib/utils\";\r\nimport { AdData } from \"@/types/ad\";\r\nimport ProductAdSection from \"./ProductAdSection\";\r\n\r\ninterface ProductDetailProps {\r\n  product: ProductWithVariants;\r\n  variants?: ProductVariant[];\r\n  businessSlug: string;\r\n  businessName: string;\r\n  whatsappNumber: string | null;\r\n  phoneNumber: string | null;\r\n  topAdData: AdData;\r\n  businessCustomAd?: {\r\n    enabled?: boolean;\r\n    image_url?: string;\r\n    link_url?: string;\r\n  } | null;\r\n  userPlan?: \"free\" | \"basic\" | \"growth\" | \"pro\" | \"enterprise\";\r\n}\r\n\r\nexport default function ProductDetail({\r\n  product,\r\n  variants = [],\r\n  businessSlug,\r\n  businessName,\r\n  whatsappNumber,\r\n  phoneNumber,\r\n  topAdData,\r\n  businessCustomAd,\r\n  userPlan,\r\n}: ProductDetailProps) {\r\n  const [imageLoaded, setImageLoaded] = useState<Record<string, boolean>>({});\r\n  const [imageError, setImageError] = useState<Record<string, boolean>>({});\r\n  const [productUrl, setProductUrl] = useState('');\r\n  const [isHovered, setIsHovered] = useState(false);\r\n  const [isZoomModalOpen, setIsZoomModalOpen] = useState(false);\r\n  const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);\r\n  const [mainCarouselApi, setMainCarouselApi] = useState<CarouselApi>();\r\n  const [thumbnailCarouselApi, setThumbnailCarouselApi] = useState<CarouselApi>();\r\n  const [allImages, setAllImages] = useState<string[]>([]);\r\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\r\n\r\n  // Variant state\r\n  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);\r\n  const [currentProduct, setCurrentProduct] = useState(product);\r\n\r\n  // Handle variant selection and deselection\r\n  const handleVariantSelect = (variant: ProductVariant | null) => {\r\n    setSelectedVariant(variant);\r\n\r\n    if (variant) {\r\n      // Update current product data with variant-specific information\r\n      const updatedProduct = {\r\n        ...product,\r\n        base_price: variant.base_price || product.base_price,\r\n        discounted_price: variant.discounted_price || product.discounted_price,\r\n        images: variant.images && variant.images.length > 0 ? variant.images : product.images,\r\n        featured_image_index: variant.images && variant.images.length > 0 ? variant.featured_image_index : product.featured_image_index,\r\n      };\r\n\r\n      setCurrentProduct(updatedProduct);\r\n    } else {\r\n      // Revert to base product when no variant is selected\r\n      setCurrentProduct(product);\r\n    }\r\n  };\r\n\r\n  // No auto-selection of variants - let users choose their own combinations\r\n\r\n  // Initialize images and product URL\r\n  useEffect(() => {\r\n    setProductUrl(window.location.href);\r\n\r\n    const images: string[] = [];\r\n\r\n    // If we have a selected variant and it has images, use only variant images\r\n    if (selectedVariant && selectedVariant.images && selectedVariant.images.length > 0) {\r\n      images.push(...selectedVariant.images);\r\n    }\r\n    // Otherwise, use product images (base product or when no variant selected)\r\n    else {\r\n      if (currentProduct.images && Array.isArray(currentProduct.images)) {\r\n        images.push(...currentProduct.images);\r\n      }\r\n      // Only add image_url if we don't have images array or it's empty\r\n      if (images.length === 0 && currentProduct.image_url) {\r\n        images.push(currentProduct.image_url);\r\n      }\r\n    }\r\n    setAllImages(images);\r\n\r\n    if (images.length > 0) {\r\n      const featuredIndex = typeof currentProduct.featured_image_index === 'number'\r\n        ? Math.min(currentProduct.featured_image_index, images.length - 1)\r\n        : 0;\r\n      setCurrentImageIndex(featuredIndex);\r\n      setSelectedImageUrl(images[featuredIndex]);\r\n\r\n      // Force carousel to scroll to the featured image when images change\r\n      if (mainCarouselApi) {\r\n        mainCarouselApi.scrollTo(featuredIndex);\r\n      }\r\n    }\r\n  }, [currentProduct.images, currentProduct.image_url, currentProduct.featured_image_index, selectedVariant, mainCarouselApi]);\r\n\r\n  // Sync main carousel with currentImageIndex\r\n  useEffect(() => {\r\n    if (!mainCarouselApi) return;\r\n    mainCarouselApi.scrollTo(currentImageIndex);\r\n  }, [mainCarouselApi, currentImageIndex]);\r\n\r\n  // Force carousel reinitialization when images change (for variant switching)\r\n  useEffect(() => {\r\n    if (!mainCarouselApi || !thumbnailCarouselApi) return;\r\n\r\n    // Reinitialize carousels when images change\r\n    mainCarouselApi.reInit();\r\n    thumbnailCarouselApi.reInit();\r\n\r\n    // Scroll to the current image index after reinitialization\r\n    setTimeout(() => {\r\n      mainCarouselApi.scrollTo(currentImageIndex);\r\n      thumbnailCarouselApi.scrollTo(currentImageIndex);\r\n    }, 100);\r\n  }, [allImages, mainCarouselApi, thumbnailCarouselApi, currentImageIndex]);\r\n\r\n  // Sync thumbnail carousel with main carousel\r\n  useEffect(() => {\r\n    if (!mainCarouselApi || !thumbnailCarouselApi) return;\r\n\r\n    const onSelect = () => {\r\n      const newIndex = mainCarouselApi.selectedScrollSnap();\r\n      setCurrentImageIndex(newIndex);\r\n      setSelectedImageUrl(allImages[newIndex]);\r\n      thumbnailCarouselApi.scrollTo(newIndex);\r\n    };\r\n\r\n    mainCarouselApi.on(\"select\", onSelect);\r\n    mainCarouselApi.on(\"reInit\", onSelect); // Re-initialize on reInit\r\n\r\n    return () => {\r\n      mainCarouselApi.off(\"select\", onSelect);\r\n      mainCarouselApi.off(\"reInit\", onSelect);\r\n    };\r\n  }, [mainCarouselApi, thumbnailCarouselApi, allImages]);\r\n\r\n  // Format prices using current product data (which may include variant pricing)\r\n  const formattedBasePrice = currentProduct.base_price ? formatCurrency(currentProduct.base_price) : \"Price not set\";\r\n  const formattedDiscountedPrice = currentProduct.discounted_price\r\n    ? formatCurrency(currentProduct.discounted_price)\r\n    : null;\r\n\r\n  // Calculate discount percentage if applicable\r\n  let discountPercentage = 0;\r\n  if (\r\n    currentProduct.discounted_price &&\r\n    currentProduct.base_price &&\r\n    currentProduct.discounted_price < currentProduct.base_price\r\n  ) {\r\n    discountPercentage = Math.round(\r\n      ((currentProduct.base_price - currentProduct.discounted_price) / currentProduct.base_price) * 100\r\n    );\r\n  }\r\n\r\n  // Animation variants\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1,\r\n      },\r\n    },\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: { duration: 0.5 },\r\n    },\r\n  };\r\n\r\n  const imageVariants = {\r\n    hidden: { opacity: 0, x: -30 },\r\n    visible: {\r\n      opacity: 1,\r\n      x: 0,\r\n      transition: {\r\n        duration: 0.7,\r\n        ease: \"easeOut\"\r\n      },\r\n    },\r\n  };\r\n\r\n  const detailsVariants = {\r\n    hidden: { opacity: 0, x: 30 },\r\n    visible: {\r\n      opacity: 1,\r\n      x: 0,\r\n      transition: {\r\n        duration: 0.7,\r\n        ease: \"easeOut\",\r\n        delay: 0.2\r\n      },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      className=\"w-full mx-auto flex flex-col\"\r\n      variants={containerVariants}\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n    >\r\n      {/* Image Zoom Modal */}\r\n      {isZoomModalOpen && selectedImageUrl && (\r\n        <ImageZoomModal\r\n          isOpen={isZoomModalOpen}\r\n          onClose={() => setIsZoomModalOpen(false)}\r\n          imageUrl={selectedImageUrl}\r\n          altText={product.name || \"Product image\"}\r\n        />\r\n      )}\r\n      {/* Breadcrumb navigation */}\r\n      <motion.div variants={itemVariants} className=\"mb-5\">\r\n        <Breadcrumb>\r\n          <BreadcrumbList>\r\n            <BreadcrumbItem>\r\n              <BreadcrumbLink href=\"/\">\r\n                <Home className=\"w-4 h-4\" />\r\n              </BreadcrumbLink>\r\n            </BreadcrumbItem>\r\n            <BreadcrumbSeparator />\r\n            <BreadcrumbItem>\r\n              <BreadcrumbLink href={`/${businessSlug}`}>\r\n                {businessName}\r\n              </BreadcrumbLink>\r\n            </BreadcrumbItem>\r\n            <BreadcrumbSeparator />\r\n            <BreadcrumbItem>\r\n              <BreadcrumbPage>{product.name}</BreadcrumbPage>\r\n            </BreadcrumbItem>\r\n          </BreadcrumbList>\r\n        </Breadcrumb>\r\n      </motion.div>\r\n\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12 lg:gap-16\">\r\n        {/* Left column - Image Carousel */}\r\n        <motion.div variants={imageVariants} className=\"relative md:sticky md:top-20 self-start md:px-4\">\r\n          {/* Main image display container */}\r\n\r\n          {/* Main image display */}\r\n          <Carousel\r\n            key={`main-carousel-${allImages.length}-${allImages[0] || 'empty'}`}\r\n            className=\"w-full mb-4\"\r\n            opts={{\r\n              align: \"start\",\r\n              loop: true,\r\n            }}\r\n            setApi={setMainCarouselApi}\r\n          >\r\n            <CarouselContent>\r\n              {allImages.length > 0 ? (\r\n                allImages.map((imageUrl, index) => (\r\n                  <CarouselItem key={`main-${imageUrl}-${index}`}>\r\n                    <div\r\n                      className=\"relative w-full aspect-square overflow-hidden bg-neutral-50 dark:bg-neutral-900 group rounded-xl border border-neutral-200 dark:border-neutral-800 cursor-pointer\"\r\n                      onClick={() => {\r\n                        if (imageUrl && !imageError[imageUrl]) {\r\n                          setIsZoomModalOpen(true);\r\n                        }\r\n                      }}\r\n                    >\r\n                      {/* Loading skeleton */}\r\n                      {!imageLoaded[imageUrl] && !imageError[imageUrl] && (\r\n                        <Skeleton className=\"absolute inset-0\" />\r\n                      )}\r\n\r\n                      {/* Image display */}\r\n                      {!imageError[imageUrl] ? (\r\n                        <>\r\n                          <Image\r\n                            src={imageUrl}\r\n                            alt={product.name}\r\n                            fill\r\n                            className={`object-cover transition-all duration-500 ${\r\n                              imageLoaded[imageUrl] ? \"opacity-100\" : \"opacity-0\"\r\n                            } group-hover:scale-105`}\r\n                            onLoad={() => setImageLoaded(prev => ({ ...prev, [imageUrl]: true }))}\r\n                            onError={() => setImageError(prev => ({ ...prev, [imageUrl]: true }))}\r\n                            sizes=\"(max-width: 768px) 100vw, 50vw\"\r\n                            priority\r\n                          />\r\n                          {/* Zoom indicator */}\r\n                          {imageLoaded[imageUrl] && (\r\n                            <div\r\n                              className=\"absolute bottom-3 right-3 bg-black/50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 cursor-pointer\"\r\n                            >\r\n                              <ZoomIn className=\"w-5 h-5\" />\r\n                            </div>\r\n                          )}\r\n                        </>\r\n                      ) : (\r\n                        <div className=\"absolute inset-0 flex items-center justify-center\">\r\n                          <Package className=\"w-20 h-20 text-neutral-300 dark:text-neutral-700\" />\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Discount badge */}\r\n                      {discountPercentage > 0 && (\r\n                        <motion.div\r\n                          initial={{ scale: 0.8, opacity: 0 }}\r\n                          animate={{ scale: 1, opacity: 1 }}\r\n                          transition={{ type: \"spring\", stiffness: 400, damping: 10 }}\r\n                          className=\"absolute top-4 right-4 bg-red-500 text-white px-3 py-1.5 rounded-lg font-bold text-sm shadow-lg\"\r\n                        >\r\n                          <div className=\"flex flex-col items-center justify-center\">\r\n                            <span className=\"text-xs font-medium sm:text-[0.6rem]\">SAVE</span>\r\n                            <span className=\"text-sm leading-none sm:text-xs\">{discountPercentage}%</span>\r\n                          </div>\r\n                        </motion.div>\r\n                      )}\r\n                    </div>\r\n                  </CarouselItem>\r\n                ))\r\n              ) : (\r\n                <CarouselItem>\r\n                  <div className=\"relative w-full aspect-square overflow-hidden bg-neutral-50 dark:bg-neutral-900 group rounded-xl border border-neutral-200 dark:border-neutral-800 cursor-pointer mb-4 flex items-center justify-center\">\r\n                    <Package className=\"w-20 h-20 text-neutral-300 dark:text-neutral-700\" />\r\n                  </div>\r\n                </CarouselItem>\r\n              )}\r\n            </CarouselContent>\r\n          </Carousel>\r\n\r\n          {/* Image carousel for multiple images */}\r\n          {allImages.length > 1 && (\r\n            <Carousel\r\n              key={`thumbnail-carousel-${allImages.length}-${allImages[0] || 'empty'}`}\r\n              className=\"w-full\"\r\n              opts={{\r\n                align: \"start\",\r\n                loop: true,\r\n              }}\r\n              setApi={setThumbnailCarouselApi}\r\n            >\r\n              <CarouselContent className=\"justify-center\">\r\n                {allImages.map((imageUrl, index) => (\r\n                  <CarouselItem key={`thumb-${imageUrl}-${index}`} className=\"basis-1/4 md:basis-1/5 lg:basis-1/6\">\r\n                    <div\r\n                      className={`relative aspect-square overflow-hidden rounded-md cursor-pointer border-2 ${\r\n                        selectedImageUrl === imageUrl\r\n                          ? 'border-[var(--brand-gold)]'\r\n                          : 'border-transparent'\r\n                      }`}\r\n                      onClick={() => setCurrentImageIndex(index)}\r\n                    >\r\n                      <Image\r\n                        src={imageUrl}\r\n                        alt={`${product.name || \"Product\"} - Image ${index + 1}`}\r\n                        fill\r\n                        className=\"object-cover\"\r\n                        sizes=\"(max-width: 768px) 25vw, 10vw\"\r\n                        onLoad={() => setImageLoaded(prev => ({ ...prev, [imageUrl]: true }))}\r\n                        onError={() => setImageError(prev => ({ ...prev, [imageUrl]: true }))}\r\n                      />\r\n                    </div>\r\n                  </CarouselItem>\r\n                ))}\r\n              </CarouselContent>\r\n            </Carousel>\r\n          )}\r\n        </motion.div>\r\n\r\n        {/* Right column - Details */}\r\n        <motion.div variants={detailsVariants} className=\"flex flex-col space-y-5 p-6 md:p-8 lg:p-10 md:sticky md:top-20 self-start md:px-4\">\r\n\r\n          {/* Product name */}\r\n          <h1 className=\"text-2xl md:text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 leading-tight\">\r\n            {product.name}\r\n          </h1>\r\n\r\n          {/* Product/Service Badge */}\r\n          <Badge variant=\"secondary\" className=\"w-fit capitalize\">\r\n            {product.product_type}\r\n          </Badge>\r\n\r\n          {/* Price section */}\r\n          <div className=\"flex items-baseline gap-3\">\r\n            {formattedDiscountedPrice ? (\r\n              <>\r\n                <span className=\"text-2xl md:text-3xl font-bold text-neutral-900 dark:text-neutral-50\">\r\n                  {formattedDiscountedPrice}\r\n                </span>\r\n                <span className=\"text-sm md:text-base line-through text-neutral-500 dark:text-neutral-400\">\r\n                  {formattedBasePrice}\r\n                </span>\r\n              </>\r\n            ) : (\r\n              <span className=\"text-2xl md:text-3xl font-bold text-neutral-900 dark:text-neutral-50\">\r\n                {formattedBasePrice}\r\n              </span>\r\n            )}\r\n          </div>\r\n\r\n          {/* Price Disclaimer Message */}\r\n          <Alert variant=\"default\" className=\"mt-4 bg-yellow-50 dark:bg-yellow-950 border border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200\">\r\n            <Info className=\"h-4 w-4\" />\r\n            <AlertTitle>Important Price Information</AlertTitle>\r\n            <AlertDescription>\r\n              Prices are indicative and may vary in-store. Visit us or contact directly for final deals and confirmation.\r\n            </AlertDescription>\r\n          </Alert>\r\n\r\n          {/* Variant Selector */}\r\n          {variants && variants.length > 0 && (\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 10 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.5, delay: 0.3 }}\r\n              className=\"mt-8\"\r\n            >\r\n              <VariantSelector\r\n                variants={variants}\r\n                selectedVariant={selectedVariant}\r\n                onVariantSelect={handleVariantSelect}\r\n              />\r\n            </motion.div>\r\n          )}\r\n\r\n          {/* Action buttons */}\r\n          <div className=\"mt-6 pt-5 border-t border-neutral-200 dark:border-neutral-800 space-y-4\">\r\n            {/* Buy Now button (disabled with coming soon message) */}\r\n            <BuyNowButton />\r\n\r\n            {/* WhatsApp and Phone buttons in two columns on desktop */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n              {/* WhatsApp button */}\r\n              <div>\r\n                {whatsappNumber ? (\r\n                  <WhatsAppButton\r\n                    whatsappNumber={whatsappNumber}\r\n                    productName={product.name}\r\n                    businessName={businessName}\r\n                    productUrl={productUrl || `${process.env.NEXT_PUBLIC_BASE_URL || 'https://dukancard.in'}/${businessSlug}/product/${product.slug || product.id}`}\r\n                  />\r\n                ) : (\r\n                  <WhatsAppButtonDisabled />\r\n                )}\r\n              </div>\r\n\r\n              {/* Phone button */}\r\n              <div>\r\n                {phoneNumber ? (\r\n                  <PhoneButton\r\n                    phoneNumber={phoneNumber}\r\n                    _businessName={businessName}\r\n                  />\r\n                ) : (\r\n                  <PhoneButtonDisabled />\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Description */}\r\n          {product.description && (\r\n            <div className=\"mt-6 text-neutral-700 dark:text-neutral-300\">\r\n              <h2 className=\"text-lg font-semibold mb-2\">Description</h2>\r\n              <p className=\"whitespace-pre-line leading-relaxed\">{product.description}</p>\r\n            </div>\r\n          )}\r\n\r\n          {/* Share button */}\r\n          <div className=\"mt-4 relative group\">\r\n            {/* Button glow effect */}\r\n            <div className=\"absolute -inset-0.5 bg-gradient-to-r from-neutral-500/20 to-neutral-600/20 rounded-xl blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n\r\n            <Button\r\n              variant=\"outline\"\r\n              className=\"relative w-full flex items-center justify-center gap-3 py-5 border border-neutral-300/50 dark:border-neutral-700/50 hover:border-[var(--brand-gold)]/50 dark:hover:border-[var(--brand-gold)]/50 rounded-xl transition-all duration-300 bg-white/50 dark:bg-black/50 backdrop-blur-sm\"\r\n              onMouseEnter={() => setIsHovered(true)}\r\n              onMouseLeave={() => setIsHovered(false)}\r\n              onClick={() => {\r\n                const shareUrl = productUrl || `${process.env.NEXT_PUBLIC_BASE_URL || 'https://dukancard.in'}/${businessSlug}/product/${product.slug || product.id}`;\r\n\r\n                // Create variant-specific sharing text\r\n                let shareText = `Check out ${product.name} from ${businessName} on Dukancard`;\r\n                if (selectedVariant && variants.length > 0) {\r\n                  const variantInfo = Object.entries(selectedVariant.variant_values || {})\r\n                    .map(([type, value]) => `${type}: ${value}`)\r\n                    .join(', ');\r\n                  shareText = `Check out ${product.name} (${variantInfo}) from ${businessName} on Dukancard`;\r\n                }\r\n\r\n                if (navigator.share) {\r\n                  navigator.share({\r\n                    title: product.name,\r\n                    text: shareText,\r\n                    url: shareUrl,\r\n                  });\r\n                } else {\r\n                  navigator.clipboard.writeText(shareUrl);\r\n                  alert(\"Link copied to clipboard!\");\r\n                }\r\n              }}\r\n            >\r\n              <Share2 className={`w-5 h-5 text-[var(--brand-gold)] transition-all duration-300 ${isHovered ? 'rotate-12' : ''}`} />\r\n              <span className=\"text-base font-medium tracking-wide\">Share</span>\r\n            </Button>\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n\r\n      {/* Custom Ad Section - Added below product details */}\r\n      <motion.div\r\n        variants={itemVariants}\r\n        className=\"mt-8 md:mt-12\"\r\n      >\r\n        <ProductAdSection\r\n          topAdData={topAdData}\r\n          itemVariants={itemVariants}\r\n          businessCustomAd={businessCustomAd}\r\n          userPlan={userPlan}\r\n        />\r\n      </motion.div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAQA;AASA;AACA;AACA;AACA;AACA;AACA;AAEA;AAlCA;;;;;;;;;;;;;;;;;;;AAoDe,SAAS,cAAc,EACpC,OAAO,EACP,WAAW,EAAE,EACb,YAAY,EACZ,YAAY,EACZ,cAAc,EACd,WAAW,EACX,SAAS,EACT,gBAAgB,EAChB,QAAQ,EACW;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACzE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IACrD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,gBAAgB;IAChB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC9E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,2CAA2C;IAC3C,MAAM,sBAAsB,CAAC;QAC3B,mBAAmB;QAEnB,IAAI,SAAS;YACX,gEAAgE;YAChE,MAAM,iBAAiB;gBACrB,GAAG,OAAO;gBACV,YAAY,QAAQ,UAAU,IAAI,QAAQ,UAAU;gBACpD,kBAAkB,QAAQ,gBAAgB,IAAI,QAAQ,gBAAgB;gBACtE,QAAQ,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,IAAI,QAAQ,MAAM,GAAG,QAAQ,MAAM;gBACrF,sBAAsB,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,IAAI,QAAQ,oBAAoB,GAAG,QAAQ,oBAAoB;YACjI;YAEA,kBAAkB;QACpB,OAAO;YACL,qDAAqD;YACrD,kBAAkB;QACpB;IACF;IAEA,0EAA0E;IAE1E,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc,OAAO,QAAQ,CAAC,IAAI;QAElC,MAAM,SAAmB,EAAE;QAE3B,2EAA2E;QAC3E,IAAI,mBAAmB,gBAAgB,MAAM,IAAI,gBAAgB,MAAM,CAAC,MAAM,GAAG,GAAG;YAClF,OAAO,IAAI,IAAI,gBAAgB,MAAM;QACvC,OAEK;YACH,IAAI,eAAe,MAAM,IAAI,MAAM,OAAO,CAAC,eAAe,MAAM,GAAG;gBACjE,OAAO,IAAI,IAAI,eAAe,MAAM;YACtC;YACA,iEAAiE;YACjE,IAAI,OAAO,MAAM,KAAK,KAAK,eAAe,SAAS,EAAE;gBACnD,OAAO,IAAI,CAAC,eAAe,SAAS;YACtC;QACF;QACA,aAAa;QAEb,IAAI,OAAO,MAAM,GAAG,GAAG;YACrB,MAAM,gBAAgB,OAAO,eAAe,oBAAoB,KAAK,WACjE,KAAK,GAAG,CAAC,eAAe,oBAAoB,EAAE,OAAO,MAAM,GAAG,KAC9D;YACJ,qBAAqB;YACrB,oBAAoB,MAAM,CAAC,cAAc;YAEzC,oEAAoE;YACpE,IAAI,iBAAiB;gBACnB,gBAAgB,QAAQ,CAAC;YAC3B;QACF;IACF,GAAG;QAAC,eAAe,MAAM;QAAE,eAAe,SAAS;QAAE,eAAe,oBAAoB;QAAE;QAAiB;KAAgB;IAE3H,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,iBAAiB;QACtB,gBAAgB,QAAQ,CAAC;IAC3B,GAAG;QAAC;QAAiB;KAAkB;IAEvC,6EAA6E;IAC7E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,mBAAmB,CAAC,sBAAsB;QAE/C,4CAA4C;QAC5C,gBAAgB,MAAM;QACtB,qBAAqB,MAAM;QAE3B,2DAA2D;QAC3D,WAAW;YACT,gBAAgB,QAAQ,CAAC;YACzB,qBAAqB,QAAQ,CAAC;QAChC,GAAG;IACL,GAAG;QAAC;QAAW;QAAiB;QAAsB;KAAkB;IAExE,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,mBAAmB,CAAC,sBAAsB;QAE/C,MAAM,WAAW;YACf,MAAM,WAAW,gBAAgB,kBAAkB;YACnD,qBAAqB;YACrB,oBAAoB,SAAS,CAAC,SAAS;YACvC,qBAAqB,QAAQ,CAAC;QAChC;QAEA,gBAAgB,EAAE,CAAC,UAAU;QAC7B,gBAAgB,EAAE,CAAC,UAAU,WAAW,0BAA0B;QAElE,OAAO;YACL,gBAAgB,GAAG,CAAC,UAAU;YAC9B,gBAAgB,GAAG,CAAC,UAAU;QAChC;IACF,GAAG;QAAC;QAAiB;QAAsB;KAAU;IAErD,+EAA+E;IAC/E,MAAM,qBAAqB,eAAe,UAAU,GAAG,CAAA,GAAA,4GAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,UAAU,IAAI;IACnG,MAAM,2BAA2B,eAAe,gBAAgB,GAC5D,CAAA,GAAA,4GAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,gBAAgB,IAC9C;IAEJ,8CAA8C;IAC9C,IAAI,qBAAqB;IACzB,IACE,eAAe,gBAAgB,IAC/B,eAAe,UAAU,IACzB,eAAe,gBAAgB,GAAG,eAAe,UAAU,EAC3D;QACA,qBAAqB,KAAK,KAAK,CAC7B,AAAC,CAAC,eAAe,UAAU,GAAG,eAAe,gBAAgB,IAAI,eAAe,UAAU,GAAI;IAElG;IAEA,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,gBAAgB;QACpB,QAAQ;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC7B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;gBACN,OAAO;YACT;QACF;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,UAAU;QACV,SAAQ;QACR,SAAQ;;YAGP,mBAAmB,kCAClB,8OAAC,+JAAA,CAAA,UAAc;gBACb,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,UAAU;gBACV,SAAS,QAAQ,IAAI,IAAI;;;;;;0BAI7B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;gBAAc,WAAU;0BAC5C,cAAA,8OAAC,+HAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,+HAAA,CAAA,iBAAc;;0CACb,8OAAC,+HAAA,CAAA,iBAAc;0CACb,cAAA,8OAAC,+HAAA,CAAA,iBAAc;oCAAC,MAAK;8CACnB,cAAA,8OAAC,mMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGpB,8OAAC,+HAAA,CAAA,sBAAmB;;;;;0CACpB,8OAAC,+HAAA,CAAA,iBAAc;0CACb,cAAA,8OAAC,+HAAA,CAAA,iBAAc;oCAAC,MAAM,CAAC,CAAC,EAAE,cAAc;8CACrC;;;;;;;;;;;0CAGL,8OAAC,+HAAA,CAAA,sBAAmB;;;;;0CACpB,8OAAC,+HAAA,CAAA,iBAAc;0CACb,cAAA,8OAAC,+HAAA,CAAA,iBAAc;8CAAE,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMrC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAe,WAAU;;0CAI7C,8OAAC,6HAAA,CAAA,WAAQ;gCAEP,WAAU;gCACV,MAAM;oCACJ,OAAO;oCACP,MAAM;gCACR;gCACA,QAAQ;0CAER,cAAA,8OAAC,6HAAA,CAAA,kBAAe;8CACb,UAAU,MAAM,GAAG,IAClB,UAAU,GAAG,CAAC,CAAC,UAAU,sBACvB,8OAAC,6HAAA,CAAA,eAAY;sDACX,cAAA,8OAAC;gDACC,WAAU;gDACV,SAAS;oDACP,IAAI,YAAY,CAAC,UAAU,CAAC,SAAS,EAAE;wDACrC,mBAAmB;oDACrB;gDACF;;oDAGC,CAAC,WAAW,CAAC,SAAS,IAAI,CAAC,UAAU,CAAC,SAAS,kBAC9C,8OAAC,6HAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAIrB,CAAC,UAAU,CAAC,SAAS,iBACpB;;0EACE,8OAAC,6HAAA,CAAA,UAAK;gEACJ,KAAK;gEACL,KAAK,QAAQ,IAAI;gEACjB,IAAI;gEACJ,WAAW,CAAC,yCAAyC,EACnD,WAAW,CAAC,SAAS,GAAG,gBAAgB,YACzC,sBAAsB,CAAC;gEACxB,QAAQ,IAAM,eAAe,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,CAAC,SAAS,EAAE;wEAAK,CAAC;gEACnE,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,CAAC,SAAS,EAAE;wEAAK,CAAC;gEACnE,OAAM;gEACN,QAAQ;;;;;;4DAGT,WAAW,CAAC,SAAS,kBACpB,8OAAC;gEACC,WAAU;0EAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;qFAKxB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;oDAKtB,qBAAqB,mBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,SAAS;4DAAE,OAAO;4DAAK,SAAS;wDAAE;wDAClC,SAAS;4DAAE,OAAO;4DAAG,SAAS;wDAAE;wDAChC,YAAY;4DAAE,MAAM;4DAAU,WAAW;4DAAK,SAAS;wDAAG;wDAC1D,WAAU;kEAEV,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAuC;;;;;;8EACvD,8OAAC;oEAAK,WAAU;;wEAAmC;wEAAmB;;;;;;;;;;;;;;;;;;;;;;;;2CAtD7D,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,OAAO;;;;kEA8DhD,8OAAC,6HAAA,CAAA,eAAY;kDACX,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;+BA3EtB,CAAC,cAAc,EAAE,UAAU,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,IAAI,SAAS;;;;;4BAmFpE,UAAU,MAAM,GAAG,mBAClB,8OAAC,6HAAA,CAAA,WAAQ;gCAEP,WAAU;gCACV,MAAM;oCACJ,OAAO;oCACP,MAAM;gCACR;gCACA,QAAQ;0CAER,cAAA,8OAAC,6HAAA,CAAA,kBAAe;oCAAC,WAAU;8CACxB,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8OAAC,6HAAA,CAAA,eAAY;4CAAoC,WAAU;sDACzD,cAAA,8OAAC;gDACC,WAAW,CAAC,0EAA0E,EACpF,qBAAqB,WACjB,+BACA,sBACJ;gDACF,SAAS,IAAM,qBAAqB;0DAEpC,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK;oDACL,KAAK,GAAG,QAAQ,IAAI,IAAI,UAAU,SAAS,EAAE,QAAQ,GAAG;oDACxD,IAAI;oDACJ,WAAU;oDACV,OAAM;oDACN,QAAQ,IAAM,eAAe,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,CAAC,SAAS,EAAE;4DAAK,CAAC;oDACnE,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,CAAC,SAAS,EAAE;4DAAK,CAAC;;;;;;;;;;;2CAhBtD,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,OAAO;;;;;;;;;;+BAV9C,CAAC,mBAAmB,EAAE,UAAU,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,IAAI,SAAS;;;;;;;;;;;kCAqC9E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAiB,WAAU;;0CAG/C,8OAAC;gCAAG,WAAU;0CACX,QAAQ,IAAI;;;;;;0CAIf,8OAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAClC,QAAQ,YAAY;;;;;;0CAIvB,8OAAC;gCAAI,WAAU;0CACZ,yCACC;;sDACE,8OAAC;4CAAK,WAAU;sDACb;;;;;;sDAEH,8OAAC;4CAAK,WAAU;sDACb;;;;;;;iEAIL,8OAAC;oCAAK,WAAU;8CACb;;;;;;;;;;;0CAMP,8OAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;kDACjC,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC,0HAAA,CAAA,aAAU;kDAAC;;;;;;kDACZ,8OAAC,0HAAA,CAAA,mBAAgB;kDAAC;;;;;;;;;;;;4BAMnB,YAAY,SAAS,MAAM,GAAG,mBAC7B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;0CAEV,cAAA,8OAAC,gKAAA,CAAA,UAAe;oCACd,UAAU;oCACV,iBAAiB;oCACjB,iBAAiB;;;;;;;;;;;0CAMvB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,6JAAA,CAAA,UAAY;;;;;kDAGb,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;0DACE,+BACC,8OAAC,+JAAA,CAAA,UAAc;oDACb,gBAAgB;oDAChB,aAAa,QAAQ,IAAI;oDACzB,cAAc;oDACd,YAAY,cAAc,GAAG,6DAAoC,uBAAuB,CAAC,EAAE,aAAa,SAAS,EAAE,QAAQ,IAAI,IAAI,QAAQ,EAAE,EAAE;;;;;yEAGjJ,8OAAC,+JAAA,CAAA,yBAAsB;;;;;;;;;;0DAK3B,8OAAC;0DACE,4BACC,8OAAC,4JAAA,CAAA,UAAW;oDACV,aAAa;oDACb,eAAe;;;;;yEAGjB,8OAAC,4JAAA,CAAA,sBAAmB;;;;;;;;;;;;;;;;;;;;;;4BAO3B,QAAQ,WAAW,kBAClB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAuC,QAAQ,WAAW;;;;;;;;;;;;0CAK3E,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;;;;;kDAEf,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,cAAc,IAAM,aAAa;wCACjC,cAAc,IAAM,aAAa;wCACjC,SAAS;4CACP,MAAM,WAAW,cAAc,GAAG,6DAAoC,uBAAuB,CAAC,EAAE,aAAa,SAAS,EAAE,QAAQ,IAAI,IAAI,QAAQ,EAAE,EAAE;4CAEpJ,uCAAuC;4CACvC,IAAI,YAAY,CAAC,UAAU,EAAE,QAAQ,IAAI,CAAC,MAAM,EAAE,aAAa,aAAa,CAAC;4CAC7E,IAAI,mBAAmB,SAAS,MAAM,GAAG,GAAG;gDAC1C,MAAM,cAAc,OAAO,OAAO,CAAC,gBAAgB,cAAc,IAAI,CAAC,GACnE,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM,GAAK,GAAG,KAAK,EAAE,EAAE,OAAO,EAC1C,IAAI,CAAC;gDACR,YAAY,CAAC,UAAU,EAAE,QAAQ,IAAI,CAAC,EAAE,EAAE,YAAY,OAAO,EAAE,aAAa,aAAa,CAAC;4CAC5F;4CAEA,IAAI,UAAU,KAAK,EAAE;gDACnB,UAAU,KAAK,CAAC;oDACd,OAAO,QAAQ,IAAI;oDACnB,MAAM;oDACN,KAAK;gDACP;4CACF,OAAO;gDACL,UAAU,SAAS,CAAC,SAAS,CAAC;gDAC9B,MAAM;4CACR;wCACF;;0DAEA,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAW,CAAC,6DAA6D,EAAE,YAAY,cAAc,IAAI;;;;;;0DACjH,8OAAC;gDAAK,WAAU;0DAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,WAAU;0BAEV,cAAA,8OAAC,iKAAA,CAAA,UAAgB;oBACf,WAAW;oBACX,cAAc;oBACd,kBAAkB;oBAClB,UAAU;;;;;;;;;;;;;;;;;AAKpB", "debugId": null}}, {"offset": {"line": 3530, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/ProductListItem.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { ProductServiceData } from \"@/app/(dashboard)/dashboard/business/products/actions\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { NearbyProduct } from \"@/app/(main)/discover/actions/types\";\r\n\r\ninterface ProductListItemProps {\r\n  product: ProductServiceData | NearbyProduct;\r\n  isLink?: boolean;\r\n}\r\n\r\n// Helper to format currency\r\nconst formatCurrency = (amount: number | null | undefined) => {\r\n  if (amount === null || amount === undefined) return null;\r\n  return amount.toLocaleString(\"en-IN\", {\r\n    style: \"currency\",\r\n    currency: \"INR\",\r\n    minimumFractionDigits: 0,\r\n    maximumFractionDigits: 2,\r\n  });\r\n};\r\n\r\n// Animation variants\r\nconst cardContainerVariants = {\r\n  hidden: { opacity: 0, y: 10 },\r\n  show: { opacity: 1, y: 0 },\r\n};\r\n\r\nconst discountBadgeVariants = {\r\n  initial: { scale: 0.9, opacity: 0 },\r\n  animate: {\r\n    scale: 1,\r\n    opacity: 1,\r\n    transition: {\r\n      duration: 0.5,\r\n      type: \"spring\",\r\n      stiffness: 400,\r\n      damping: 10,\r\n    },\r\n  },\r\n  hover: {\r\n    scale: 1.05,\r\n    rotate: -2,\r\n    transition: { type: \"spring\", stiffness: 500 },\r\n  },\r\n};\r\n\r\nexport default function ProductListItem({\r\n  product,\r\n  isLink = true,\r\n}: ProductListItemProps) {\r\n  // If isLink is true, we'll wrap the content in a link\r\n  const [imageError, setImageError] = useState(false);\r\n\r\n  const formattedBasePrice = formatCurrency(product.base_price);\r\n  const formattedDiscountedPrice = formatCurrency(product.discounted_price); // Format discounted price\r\n\r\n  // Determine final price and if there's a discount shown\r\n  let finalPrice = formattedBasePrice;\r\n  let priceToShowStrikethrough: string | null = null;\r\n  let discountPercentage = 0;\r\n\r\n  const hasDiscountedPrice =\r\n    typeof product.discounted_price === \"number\" &&\r\n    product.discounted_price > 0;\r\n  const hasBasePrice =\r\n    typeof product.base_price === \"number\" && product.base_price > 0;\r\n\r\n  if (\r\n    hasDiscountedPrice &&\r\n    hasBasePrice &&\r\n    product.discounted_price! < product.base_price!\r\n  ) {\r\n    // Scenario 1: Discounted price is valid and less than base price\r\n    finalPrice = formattedDiscountedPrice;\r\n    priceToShowStrikethrough = formattedBasePrice; // Strike through base price\r\n    discountPercentage = Math.round(\r\n      ((product.base_price! - product.discounted_price!) /\r\n        product.base_price!) *\r\n        100\r\n    );\r\n  } else {\r\n    // Scenario 2: No discount applicable, show base price\r\n    finalPrice = formattedBasePrice;\r\n    priceToShowStrikethrough = null;\r\n    discountPercentage = 0;\r\n  }\r\n\r\n  // Ensure finalPrice has a fallback if both prices are null/undefined\r\n  if (!finalPrice) {\r\n    finalPrice = \"Price unavailable\";\r\n  }\r\n\r\n  const showDiscountBadge = discountPercentage > 0;\r\n\r\n  // State for lazy loading images\r\n  const [isImageLoaded, setIsImageLoaded] = useState(false);\r\n\r\n  // Check if product is out of stock\r\n  const isOutOfStock = !product.is_available;\r\n\r\n  // Ensure we're not using business_id as a key\r\n  // Use the product's own ID for any keys needed\r\n  const content = (\r\n    <motion.div\r\n      variants={cardContainerVariants}\r\n      initial=\"hidden\"\r\n      animate=\"show\"\r\n      className=\"w-full overflow-hidden\"\r\n    >\r\n      <div className=\"relative h-full border border-neutral-200 dark:border-neutral-800 p-1 sm:p-1.5 md:p-2 overflow-hidden rounded-lg\">\r\n        <div className=\"relative w-full overflow-hidden rounded-lg\">\r\n          {/* Image container */}\r\n          <div className=\"relative w-full overflow-hidden rounded-t-xl\">\r\n            {/* Get the featured image from the images array if available, otherwise fall back to image_url */}\r\n            {/* Determine the image URL to use */}\r\n            {(() => {\r\n              // Get the image URL to display\r\n              let imageUrl = product.image_url;\r\n\r\n              // If product has images array and it's not empty, use the featured image\r\n              if (product.images && Array.isArray(product.images) && product.images.length > 0) {\r\n                const featuredIndex = typeof product.featured_image_index === 'number'\r\n                  ? Math.min(product.featured_image_index, product.images.length - 1)\r\n                  : 0;\r\n                imageUrl = product.images[featuredIndex];\r\n              }\r\n\r\n              if (imageUrl && !imageError) {\r\n                return (\r\n                  <div className=\"overflow-hidden\">\r\n                    {!isImageLoaded && (\r\n                      <Skeleton className=\"absolute inset-0 rounded-t-xl\" />\r\n                    )}\r\n                    <motion.div className=\"w-full\">\r\n                      <Image\r\n                        src={imageUrl}\r\n                        alt={product.name ?? \"Product image\"}\r\n                        width={500}\r\n                        height={750}\r\n                        className={`w-full aspect-square object-cover ${\r\n                          isOutOfStock\r\n                            ? \"filter grayscale opacity-70 transition-all duration-500\"\r\n                            : \"\"\r\n                        } ${\r\n                          isImageLoaded ? \"opacity-100\" : \"opacity-0\"\r\n                        } max-w-full`}\r\n                        loading=\"lazy\"\r\n                        onError={() => setImageError(true)}\r\n                        onLoad={() => setIsImageLoaded(true)}\r\n                        quality={80}\r\n                        blurDataURL=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=\"\r\n                        placeholder=\"blur\"\r\n                        style={{ objectFit: \"cover\" }}\r\n                      />\r\n                    </motion.div>\r\n                  </div>\r\n                );\r\n              } else {\r\n                return (\r\n                  <div className=\"w-full aspect-square flex items-center justify-center bg-neutral-100 dark:bg-neutral-800 rounded-t-xl\">\r\n                    <svg\r\n                      className=\"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 text-neutral-400 dark:text-neutral-500\"\r\n                      fill=\"none\"\r\n                      stroke=\"currentColor\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        strokeWidth={1}\r\n                        d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\r\n                      />\r\n                    </svg>\r\n                  </div>\r\n                );\r\n              }\r\n            })()}\r\n\r\n            {/* Out of Stock Overlay */}\r\n            {isOutOfStock && (\r\n              <div className=\"absolute inset-0 flex items-center justify-center bg-gradient-to-t from-black/70 to-black/40\">\r\n                <div className=\"px-6 py-2 backdrop-blur-sm rounded-full bg-background/80 text-foreground\">\r\n                  <span className=\"font-medium tracking-wide uppercase text-xs sm:text-sm\">\r\n                    Out of Stock\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Discount Badge Overlay */}\r\n            {showDiscountBadge && (\r\n              <AnimatePresence>\r\n                <motion.div\r\n                  key={`discount-badge-${product.id}`}\r\n                  variants={discountBadgeVariants}\r\n                  initial=\"initial\"\r\n                  animate=\"animate\"\r\n                  whileHover=\"hover\"\r\n                  className={cn(\r\n                    \"absolute top-2 sm:top-3 md:top-4 right-2 sm:right-3 md:right-4 px-1.5 py-0.5 rounded-md font-bold text-[8px] sm:text-xs shadow-lg\",\r\n                    \"bg-destructive\",\r\n                    \"text-destructive-foreground border border-destructive-foreground/20\",\r\n                    \"transform-gpu\"\r\n                  )}\r\n                >\r\n                  <div className=\"flex flex-col items-center justify-center\">\r\n                    <span className=\"text-[7px] sm:text-[9px] md:text-[10px] font-medium\">\r\n                      SAVE\r\n                    </span>\r\n                    <span className=\"text-[9px] sm:text-xs md:text-sm leading-none\">\r\n                      {discountPercentage}%\r\n                    </span>\r\n                  </div>\r\n                </motion.div>\r\n              </AnimatePresence>\r\n            )}\r\n          </div>\r\n\r\n          {/* Content Section */}\r\n          <div className=\"px-1.5 sm:px-2 pt-1 sm:pt-1.5 pb-1.5 sm:pb-2 space-y-0.5 sm:space-y-1\">\r\n            {/* Title */}\r\n            <p className=\"font-semibold text-xs sm:text-sm md:text-sm lg:text-base line-clamp-1 truncate text-neutral-800 dark:text-neutral-100 max-w-full overflow-hidden\">\r\n              {product.name ?? \"Unnamed Product\"}\r\n            </p>\r\n\r\n            {/* Description (optional) */}\r\n            {product.description && (\r\n              <p className=\"line-clamp-1 text-[10px] sm:text-xs text-neutral-500 dark:text-neutral-400 max-w-full overflow-hidden truncate\">\r\n                {product.description}\r\n              </p>\r\n            )}\r\n\r\n            {/* Price and Badge Container */}\r\n            <div className=\"flex items-center gap-1 sm:gap-2 pt-0.5 sm:pt-1\">\r\n              {/* Price Group */}\r\n              <div className=\"flex justify-between items-baseline space-x-1 sm:space-x-2 text-xs sm:text-sm md:text-sm lg:text-base flex-grow min-w-0 overflow-hidden w-full\">\r\n                {finalPrice && (\r\n                  <p className=\"truncate font-bold text-neutral-800 dark:text-neutral-100 max-w-full\">\r\n                    {finalPrice}\r\n                  </p>\r\n                )}\r\n                {priceToShowStrikethrough && (\r\n                  <p className=\"line-through opacity-60 truncate text-[10px] sm:text-xs text-neutral-500\">\r\n                    {priceToShowStrikethrough}\r\n                  </p>\r\n                )}\r\n              </div>\r\n\r\n              {/* Product Type Badge removed as per instructions */}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n\r\n  // If isLink is true, wrap the content in a link to the product detail page\r\n  if (isLink && \"business_slug\" in product && product.business_slug) {\r\n    return (\r\n      <Link\r\n        href={`/${product.business_slug}/product/${product.slug || product.id}`}\r\n        className=\"block h-full\"\r\n      >\r\n        {content}\r\n      </Link>\r\n    );\r\n  }\r\n\r\n  // Otherwise, just return the content\r\n  return content;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAAA;AACA;AARA;;;;;;;;AAgBA,4BAA4B;AAC5B,MAAM,iBAAiB,CAAC;IACtB,IAAI,WAAW,QAAQ,WAAW,WAAW,OAAO;IACpD,OAAO,OAAO,cAAc,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB;AACF;AAEA,qBAAqB;AACrB,MAAM,wBAAwB;IAC5B,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,MAAM;QAAE,SAAS;QAAG,GAAG;IAAE;AAC3B;AAEA,MAAM,wBAAwB;IAC5B,SAAS;QAAE,OAAO;QAAK,SAAS;IAAE;IAClC,SAAS;QACP,OAAO;QACP,SAAS;QACT,YAAY;YACV,UAAU;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;IACF;IACA,OAAO;QACL,OAAO;QACP,QAAQ,CAAC;QACT,YAAY;YAAE,MAAM;YAAU,WAAW;QAAI;IAC/C;AACF;AAEe,SAAS,gBAAgB,EACtC,OAAO,EACP,SAAS,IAAI,EACQ;IACrB,sDAAsD;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,qBAAqB,eAAe,QAAQ,UAAU;IAC5D,MAAM,2BAA2B,eAAe,QAAQ,gBAAgB,GAAG,0BAA0B;IAErG,wDAAwD;IACxD,IAAI,aAAa;IACjB,IAAI,2BAA0C;IAC9C,IAAI,qBAAqB;IAEzB,MAAM,qBACJ,OAAO,QAAQ,gBAAgB,KAAK,YACpC,QAAQ,gBAAgB,GAAG;IAC7B,MAAM,eACJ,OAAO,QAAQ,UAAU,KAAK,YAAY,QAAQ,UAAU,GAAG;IAEjE,IACE,sBACA,gBACA,QAAQ,gBAAgB,GAAI,QAAQ,UAAU,EAC9C;QACA,iEAAiE;QACjE,aAAa;QACb,2BAA2B,oBAAoB,4BAA4B;QAC3E,qBAAqB,KAAK,KAAK,CAC7B,AAAC,CAAC,QAAQ,UAAU,GAAI,QAAQ,gBAAgB,AAAC,IAC/C,QAAQ,UAAU,GAClB;IAEN,OAAO;QACL,sDAAsD;QACtD,aAAa;QACb,2BAA2B;QAC3B,qBAAqB;IACvB;IAEA,qEAAqE;IACrE,IAAI,CAAC,YAAY;QACf,aAAa;IACf;IAEA,MAAM,oBAAoB,qBAAqB;IAE/C,gCAAgC;IAChC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,mCAAmC;IACnC,MAAM,eAAe,CAAC,QAAQ,YAAY;IAE1C,8CAA8C;IAC9C,+CAA+C;IAC/C,MAAM,wBACJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,WAAU;kBAEV,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;4BAGZ,CAAC;gCACA,+BAA+B;gCAC/B,IAAI,WAAW,QAAQ,SAAS;gCAEhC,yEAAyE;gCACzE,IAAI,QAAQ,MAAM,IAAI,MAAM,OAAO,CAAC,QAAQ,MAAM,KAAK,QAAQ,MAAM,CAAC,MAAM,GAAG,GAAG;oCAChF,MAAM,gBAAgB,OAAO,QAAQ,oBAAoB,KAAK,WAC1D,KAAK,GAAG,CAAC,QAAQ,oBAAoB,EAAE,QAAQ,MAAM,CAAC,MAAM,GAAG,KAC/D;oCACJ,WAAW,QAAQ,MAAM,CAAC,cAAc;gCAC1C;gCAEA,IAAI,YAAY,CAAC,YAAY;oCAC3B,qBACE,8OAAC;wCAAI,WAAU;;4CACZ,CAAC,+BACA,8OAAC,6HAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DAEtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAAC,WAAU;0DACpB,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK;oDACL,KAAK,QAAQ,IAAI,IAAI;oDACrB,OAAO;oDACP,QAAQ;oDACR,WAAW,CAAC,kCAAkC,EAC5C,eACI,4DACA,GACL,CAAC,EACA,gBAAgB,gBAAgB,YACjC,WAAW,CAAC;oDACb,SAAQ;oDACR,SAAS,IAAM,cAAc;oDAC7B,QAAQ,IAAM,iBAAiB;oDAC/B,SAAS;oDACT,aAAY;oDACZ,aAAY;oDACZ,OAAO;wDAAE,WAAW;oDAAQ;;;;;;;;;;;;;;;;;gCAKtC,OAAO;oCACL,qBACE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,SAAQ;4CACR,OAAM;sDAEN,cAAA,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;gCAKZ;4BACF,CAAC;4BAGA,8BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAyD;;;;;;;;;;;;;;;;4BAQ9E,mCACC,8OAAC,yLAAA,CAAA,kBAAe;0CACd,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,UAAU;oCACV,SAAQ;oCACR,SAAQ;oCACR,YAAW;oCACX,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qIACA,kBACA,uEACA;8CAGF,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAsD;;;;;;0DAGtE,8OAAC;gDAAK,WAAU;;oDACb;oDAAmB;;;;;;;;;;;;;mCAjBnB,CAAC,eAAe,EAAE,QAAQ,EAAE,EAAE;;;;;;;;;;;;;;;;kCA0B3C,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAE,WAAU;0CACV,QAAQ,IAAI,IAAI;;;;;;4BAIlB,QAAQ,WAAW,kBAClB,8OAAC;gCAAE,WAAU;0CACV,QAAQ,WAAW;;;;;;0CAKxB,8OAAC;gCAAI,WAAU;0CAEb,cAAA,8OAAC;oCAAI,WAAU;;wCACZ,4BACC,8OAAC;4CAAE,WAAU;sDACV;;;;;;wCAGJ,0CACC,8OAAC;4CAAE,WAAU;sDACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAanB,2EAA2E;IAC3E,IAAI,UAAU,mBAAmB,WAAW,QAAQ,aAAa,EAAE;QACjE,qBACE,8OAAC,4JAAA,CAAA,UAAI;YACH,MAAM,CAAC,CAAC,EAAE,QAAQ,aAAa,CAAC,SAAS,EAAE,QAAQ,IAAI,IAAI,QAAQ,EAAE,EAAE;YACvE,WAAU;sBAET;;;;;;IAGP;IAEA,qCAAqC;IACrC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3890, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/product/components/ProductRecommendations.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ProductServiceData } from \"@/app/(dashboard)/dashboard/business/products/actions\";\r\nimport ProductListItem from \"@/app/components/ProductListItem\";\r\nimport Link from \"next/link\";\r\nimport { motion } from \"framer-motion\";\r\n\r\ninterface ProductRecommendationsProps {\r\n  businessProducts: ProductServiceData[];\r\n  otherBusinessProducts: Array<ProductServiceData & { business_slug: string }>;\r\n  businessSlug: string;\r\n}\r\n\r\nexport default function ProductRecommendations({\r\n  businessProducts,\r\n  otherBusinessProducts,\r\n  businessSlug,\r\n}: ProductRecommendationsProps) {\r\n  // Animation variants\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1,\r\n      },\r\n    },\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: { duration: 0.5 },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <div className=\"mt-8 sm:mt-12 md:mt-16 space-y-8 sm:space-y-12 md:space-y-16 md:px-4\">\r\n      {/* More from this business */}\r\n      {businessProducts.length > 0 && (\r\n        <motion.section\r\n          variants={containerVariants}\r\n          initial=\"hidden\"\r\n          animate=\"visible\"\r\n          className=\"space-y-6\"\r\n        >\r\n          <motion.div\r\n            variants={itemVariants}\r\n            className=\"flex items-center justify-between\"\r\n          >\r\n            <h2 className=\"text-2xl md:text-3xl font-bold text-neutral-900 dark:text-neutral-50 relative\">\r\n              More from this business\r\n              <span className=\"absolute -bottom-2 left-0 w-16 h-1 bg-primary rounded-full\"></span>\r\n            </h2>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            variants={itemVariants}\r\n            className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6\"\r\n          >\r\n            {businessProducts.map((product) => (\r\n              <Link\r\n                key={product.id}\r\n                href={`/${businessSlug}/product/${product.slug || product.id}`}\r\n                className=\"block h-full transform transition-transform duration-300 hover:-translate-y-1\"\r\n              >\r\n                <ProductListItem product={product} isLink={false} />\r\n              </Link>\r\n            ))}\r\n          </motion.div>\r\n        </motion.section>\r\n      )}\r\n\r\n      {/* Products from other businesses */}\r\n      {otherBusinessProducts.length > 0 && (\r\n        <motion.section\r\n          variants={containerVariants}\r\n          initial=\"hidden\"\r\n          animate=\"visible\"\r\n          className=\"space-y-6\"\r\n        >\r\n          <motion.div\r\n            variants={itemVariants}\r\n            className=\"flex items-center justify-between\"\r\n          >\r\n            <h2 className=\"text-2xl md:text-3xl font-bold text-neutral-900 dark:text-neutral-50 relative\">\r\n              You might also like\r\n              <span className=\"absolute -bottom-2 left-0 w-16 h-1 bg-primary rounded-full\"></span>\r\n            </h2>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            variants={itemVariants}\r\n            className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6\"\r\n          >\r\n            {otherBusinessProducts.map((product) => (\r\n              <Link\r\n                key={product.id}\r\n                href={`/${product.business_slug}/product/${product.slug || product.id}`}\r\n                className=\"block h-full transform transition-transform duration-300 hover:-translate-y-1\"\r\n              >\r\n                <ProductListItem product={product} isLink={false} />\r\n              </Link>\r\n            ))}\r\n          </motion.div>\r\n        </motion.section>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAae,SAAS,uBAAuB,EAC7C,gBAAgB,EAChB,qBAAqB,EACrB,YAAY,EACgB;IAC5B,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,iBAAiB,MAAM,GAAG,mBACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;gBACb,UAAU;gBACV,SAAQ;gBACR,SAAQ;gBACR,WAAU;;kCAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAU;kCAEV,cAAA,8OAAC;4BAAG,WAAU;;gCAAgF;8CAE5F,8OAAC;oCAAK,WAAU;;;;;;;;;;;;;;;;;kCAIpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAU;kCAET,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,CAAC,CAAC,EAAE,aAAa,SAAS,EAAE,QAAQ,IAAI,IAAI,QAAQ,EAAE,EAAE;gCAC9D,WAAU;0CAEV,cAAA,8OAAC,qIAAA,CAAA,UAAe;oCAAC,SAAS;oCAAS,QAAQ;;;;;;+BAJtC,QAAQ,EAAE;;;;;;;;;;;;;;;;YAYxB,sBAAsB,MAAM,GAAG,mBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;gBACb,UAAU;gBACV,SAAQ;gBACR,SAAQ;gBACR,WAAU;;kCAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAU;kCAEV,cAAA,8OAAC;4BAAG,WAAU;;gCAAgF;8CAE5F,8OAAC;oCAAK,WAAU;;;;;;;;;;;;;;;;;kCAIpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAU;kCAET,sBAAsB,GAAG,CAAC,CAAC,wBAC1B,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,CAAC,CAAC,EAAE,QAAQ,aAAa,CAAC,SAAS,EAAE,QAAQ,IAAI,IAAI,QAAQ,EAAE,EAAE;gCACvE,WAAU;0CAEV,cAAA,8OAAC,qIAAA,CAAA,UAAe;oCAAC,SAAS;oCAAS,QAAQ;;;;;;+BAJtC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;AAY/B", "debugId": null}}, {"offset": {"line": 4066, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/product/%5BproductSlug%5D/ProductDetailClient.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Suspense } from \"react\";\r\nimport { ProductServiceData } from \"@/app/(dashboard)/dashboard/business/products/actions\";\r\nimport { ProductVariant } from \"@/types/variants\";\r\nimport { ProductWithVariants } from \"@/types/products\";\r\nimport ProductDetail from \"../components/ProductDetail\";\r\nimport ProductRecommendations from \"../components/ProductRecommendations\";\r\nimport { AdData } from \"@/types/ad\";\r\n\r\ninterface ProductDetailClientProps {\r\n  product: ProductWithVariants;\r\n  variants?: ProductVariant[];\r\n  businessSlug: string;\r\n  businessName: string;\r\n  whatsappNumber: string | null;\r\n  phoneNumber: string | null;\r\n  businessProducts: ProductServiceData[];\r\n  otherBusinessProducts: Array<ProductServiceData & { business_slug: string }>;\r\n  topAdData: AdData;\r\n  businessCustomAd?: {\r\n    enabled?: boolean;\r\n    image_url?: string;\r\n    link_url?: string;\r\n  } | null;\r\n  userPlan?: \"free\" | \"basic\" | \"growth\" | \"pro\" | \"enterprise\";\r\n}\r\n\r\nexport default function ProductDetailClient({\r\n  product,\r\n  variants = [],\r\n  businessSlug,\r\n  businessName,\r\n  whatsappNumber,\r\n  phoneNumber,\r\n  businessProducts,\r\n  otherBusinessProducts,\r\n  topAdData,\r\n  businessCustomAd,\r\n  userPlan,\r\n}: ProductDetailClientProps) {\r\n  return (\r\n    <Suspense fallback={<div className=\"min-h-screen flex items-center justify-center\">Loading product details...</div>}>\r\n      <div className=\"w-full mx-auto px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 py-8 min-h-screen max-w-7xl\">\r\n        <ProductDetail\r\n          product={product}\r\n          variants={variants}\r\n          businessSlug={businessSlug}\r\n          businessName={businessName}\r\n          whatsappNumber={whatsappNumber}\r\n          phoneNumber={phoneNumber}\r\n          topAdData={topAdData}\r\n          businessCustomAd={businessCustomAd}\r\n          userPlan={userPlan}\r\n        />\r\n\r\n        {/* Product recommendations */}\r\n        {(businessProducts.length > 0 || otherBusinessProducts.length > 0) && (\r\n          <ProductRecommendations\r\n            businessProducts={businessProducts.slice(0, 12)}\r\n            otherBusinessProducts={otherBusinessProducts.slice(0, 12)}\r\n            businessSlug={businessSlug}\r\n          />\r\n        )}\r\n      </div>\r\n    </Suspense>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAIA;AACA;AAPA;;;;;AA4Be,SAAS,oBAAoB,EAC1C,OAAO,EACP,WAAW,EAAE,EACb,YAAY,EACZ,YAAY,EACZ,cAAc,EACd,WAAW,EACX,gBAAgB,EAChB,qBAAqB,EACrB,SAAS,EACT,gBAAgB,EAChB,QAAQ,EACiB;IACzB,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QAAC,wBAAU,8OAAC;YAAI,WAAU;sBAAgD;;;;;;kBACjF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,8JAAA,CAAA,UAAa;oBACZ,SAAS;oBACT,UAAU;oBACV,cAAc;oBACd,cAAc;oBACd,gBAAgB;oBAChB,aAAa;oBACb,WAAW;oBACX,kBAAkB;oBAClB,UAAU;;;;;;gBAIX,CAAC,iBAAiB,MAAM,GAAG,KAAK,sBAAsB,MAAM,GAAG,CAAC,mBAC/D,8OAAC,uKAAA,CAAA,UAAsB;oBACrB,kBAAkB,iBAAiB,KAAK,CAAC,GAAG;oBAC5C,uBAAuB,sBAAsB,KAAK,CAAC,GAAG;oBACtD,cAAc;;;;;;;;;;;;;;;;;AAM1B", "debugId": null}}, {"offset": {"line": 4133, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/product/components/OfflineProductMessage.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Store, Package } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\nexport default function OfflineProductMessage() {\r\n  const router = useRouter();\r\n  const [isClient, setIsClient] = useState(false);\r\n\r\n  // Use useEffect to detect client-side rendering\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex flex-col items-center justify-center p-4 bg-white dark:bg-black\">\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5 }}\r\n        className=\"max-w-md w-full bg-white dark:bg-neutral-900 rounded-xl shadow-lg p-8 border border-neutral-200 dark:border-neutral-800 text-center\"\r\n      >\r\n        <div className=\"mb-6 flex justify-center\">\r\n          <div className=\"p-4 bg-neutral-100 dark:bg-neutral-800 rounded-full\">\r\n            <Package className=\"h-12 w-12 text-[var(--brand-gold)]\" />\r\n          </div>\r\n        </div>\r\n\r\n        <h2 className=\"text-2xl font-bold mb-3 text-neutral-900 dark:text-neutral-100\">\r\n          Product Unavailable\r\n        </h2>\r\n        \r\n        <p className=\"text-neutral-600 dark:text-neutral-400 mb-6\">\r\n          This product is currently unavailable because the business is offline or in private mode.\r\n          You cannot view or purchase this product at the moment.\r\n        </p>\r\n\r\n        <div className=\"relative group\">\r\n          {/* Button glow effect with properly rounded corners */}\r\n          {isClient && (\r\n            <motion.div\r\n              className=\"absolute -inset-0.5 rounded-md blur-md\"\r\n              style={{\r\n                background: \"linear-gradient(to right, rgba(var(--brand-gold-rgb), 0.6), rgba(var(--brand-gold-rgb), 0.8))\"\r\n              }}\r\n              initial={{ opacity: 0.7 }}\r\n              animate={{\r\n                opacity: [0.7, 0.9, 0.7],\r\n                boxShadow: [\r\n                  \"0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)\",\r\n                  \"0 0 20px 4px rgba(var(--brand-gold-rgb), 0.5)\",\r\n                  \"0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)\"\r\n                ]\r\n              }}\r\n              transition={{\r\n                duration: 2,\r\n                repeat: Infinity,\r\n                repeatType: \"reverse\"\r\n              }}\r\n            />\r\n          )}\r\n\r\n          <Button\r\n            onClick={() => router.push(\"/discover\")}\r\n            className=\"relative w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black font-medium px-8 py-6 h-12 rounded-md shadow-md hover:shadow-xl transition-all duration-300 cursor-pointer\"\r\n          >\r\n            <span className=\"relative z-10 flex items-center justify-center gap-2\">\r\n              Discover Products\r\n              <Store className=\"h-5 w-5\" />\r\n            </span>\r\n          </Button>\r\n        </div>\r\n      </motion.div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAU;;8BAEV,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;;;;;;8BAIvB,8OAAC;oBAAG,WAAU;8BAAiE;;;;;;8BAI/E,8OAAC;oBAAE,WAAU;8BAA8C;;;;;;8BAK3D,8OAAC;oBAAI,WAAU;;wBAEZ,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,OAAO;gCACL,YAAY;4BACd;4BACA,SAAS;gCAAE,SAAS;4BAAI;4BACxB,SAAS;gCACP,SAAS;oCAAC;oCAAK;oCAAK;iCAAI;gCACxB,WAAW;oCACT;oCACA;oCACA;iCACD;4BACH;4BACA,YAAY;gCACV,UAAU;gCACV,QAAQ;gCACR,YAAY;4BACd;;;;;;sCAIJ,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,WAAU;sCAEV,cAAA,8OAAC;gCAAK,WAAU;;oCAAuD;kDAErE,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/B", "debugId": null}}]}