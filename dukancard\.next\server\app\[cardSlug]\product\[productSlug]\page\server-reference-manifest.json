{"node": {"40039e5475159c6d152f6658179feab0b3344d8bcc": {"workers": {"app/[cardSlug]/product/[productSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/product/[productSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/[cardSlug]/product/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/product/[productSlug]/page": "rsc"}}, "4090bf49bde90611f206004fcfecc276d0bd89ff93": {"workers": {"app/[cardSlug]/product/[productSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/product/[productSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/[cardSlug]/product/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/product/[productSlug]/page": "rsc"}}, "400897f4fcf08b6ff241e55ca68679f59b009f833f": {"workers": {"app/[cardSlug]/product/[productSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/product/[productSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/[cardSlug]/product/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/product/[productSlug]/page": "rsc"}}, "604da278527a3b3ebc839ac04faa50dcf6b1fc354d": {"workers": {"app/[cardSlug]/product/[productSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/product/[productSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/[cardSlug]/product/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/product/[productSlug]/page": "rsc"}}, "6086dbe3b7d9dde97a2afcbc0842cce29d93604d12": {"workers": {"app/[cardSlug]/product/[productSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/product/[productSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/[cardSlug]/product/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/product/[productSlug]/page": "rsc"}}, "60d4c90f1bb7d3950939f7f34a7e2c8dda1f2df44e": {"workers": {"app/[cardSlug]/product/[productSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/product/[productSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/[cardSlug]/product/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/product/[productSlug]/page": "rsc"}}, "60ecdd28a201e7e993d1c9af4cd41063d0f854d817": {"workers": {"app/[cardSlug]/product/[productSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/product/[productSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/[cardSlug]/product/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/product/[productSlug]/page": "rsc"}}, "70a09fe73f3bf03ea5636420c66e2eb3cb262d219c": {"workers": {"app/[cardSlug]/product/[productSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/product/[productSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/[cardSlug]/product/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/product/[productSlug]/page": "rsc"}}, "00c2d9624329cded09d08193b88cb56d91c74b75b9": {"workers": {"app/[cardSlug]/product/[productSlug]/page": {"moduleId": "[project]/.next-internal/server/app/[cardSlug]/product/[productSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/[cardSlug]/product/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[cardSlug]/product/[productSlug]/page": "rsc"}}}, "edge": {}}