{"node": {"00089e22292962faf3dc20145ff66dcc48cc14156d": {"workers": {"app/(main)/page": {"moduleId": "[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(main)/page": "rsc"}}, "006dd2da128ed0928b5dfe57d5e39b7acfa1e4a058": {"workers": {"app/(main)/page": {"moduleId": "[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(main)/page": "rsc"}}, "00c2d9624329cded09d08193b88cb56d91c74b75b9": {"workers": {"app/(main)/page": {"moduleId": "[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(main)/page": "rsc"}}, "4099bda4cdd529b888f7cedd4915015131201e8e46": {"workers": {"app/(main)/page": {"moduleId": "[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(main)/page": "action-browser"}}, "40ab1879cfbb4ab5aaf23c401e255af71aeeab13a9": {"workers": {"app/(main)/page": {"moduleId": "[project]/.next-internal/server/app/(main)/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/actions/getHomepageBusinessCard.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/(main)/discover/actions/productActions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/(main)/discover/actions/businessActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(main)/page": "action-browser"}}}, "edge": {}}