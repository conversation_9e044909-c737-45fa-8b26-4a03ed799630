{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/reviews.ts"], "sourcesContent": ["\"use server\";\n\nimport { createClient } from \"@/utils/supabase/server\";\n\n// revalidatePath is imported but not used in this file\n// import { revalidatePath } from 'next/cache';\nimport { getUserProfilesForReviews } from \"./secureCustomerProfiles\";\nimport { Tables } from \"@/types/supabase\";\n\ntype ReviewWithUser = Tables<'ratings_reviews'> & {\n  user_profile?: {\n    id: string;\n    name: string | null;\n    avatar_url: string | null;\n    is_business: boolean;\n    business_slug: string | null;\n  };\n};\nexport type { ReviewWithUser };\n\nexport type ReviewSortBy =\n  | \"newest\"\n  | \"oldest\"\n  | \"highest_rating\"\n  | \"lowest_rating\";\n\n// Fetch reviews for a business with pagination and sorting\nexport async function fetchBusinessReviews(\n  businessProfileId: string,\n  page: number = 1,\n  limit: number = 5,\n  sortBy: ReviewSortBy = \"newest\"\n): Promise<{\n  data: ReviewWithUser[];\n  totalCount: number;\n  error?: string;\n}> {\n  const supabase = await createClient();\n\n  try {\n    // Create admin client for secure operations\n    const supabase = await createClient();\n\n    // First, get the reviews with pagination and sorting\n    let query = supabase\n      .from(\"ratings_reviews\")\n      .select(\"*\", { count: \"exact\" })\n      .eq(\"business_profile_id\", businessProfileId)\n      // Don't show reviews where the user is reviewing their own business\n      .neq(\"user_id\", businessProfileId);\n\n    // Apply sorting\n    switch (sortBy) {\n      case \"oldest\":\n        query = query.order(\"created_at\", { ascending: true });\n        break;\n      case \"highest_rating\":\n        query = query.order(\"rating\", { ascending: false });\n        break;\n      case \"lowest_rating\":\n        query = query.order(\"rating\", { ascending: true });\n        break;\n      case \"newest\":\n      default:\n        query = query.order(\"created_at\", { ascending: false });\n        break;\n    }\n\n    // Apply pagination\n    const from = (page - 1) * limit;\n    const to = from + limit - 1;\n    query = query.range(from, to);\n\n    // Execute the query\n    const { data: reviewsData, error: reviewsError, count } = await query;\n\n    if (reviewsError) {\n      console.error(\"Error fetching reviews:\", reviewsError);\n      return { data: [], totalCount: 0, error: reviewsError.message };\n    }\n\n    // If no reviews, return empty array\n    if (!reviewsData || reviewsData.length === 0) {\n      return { data: [], totalCount: count || 0 };\n    }\n\n    // Get all user IDs from the reviews\n    const userIds = [...new Set(reviewsData.map((review: { user_id: string }) => review.user_id))] as string[];\n\n    // Use the secure method to fetch user profiles (both customer and business)\n    const { data: profilesMap, error: profilesError } =\n      await getUserProfilesForReviews(userIds);\n\n    if (profilesError) {\n      console.error(\"Error fetching user profiles:\", profilesError);\n      // Continue without profiles\n    }\n\n    // Get business user IDs from the profiles\n    const businessUserIds = userIds.filter(\n      (id: string) => profilesMap?.[id]?.is_business\n    );\n\n    // Create a map of business IDs to their slugs\n    let businessSlugMap: Record<string, string | null> = {};\n\n    // Fetch business slugs for all business reviewers at once\n    if (businessUserIds.length > 0) {\n      const { data: businessSlugs } = await supabase\n        .from(\"business_profiles\")\n        .select(\"id, business_slug\")\n        .in(\"id\", businessUserIds);\n\n      // Create a map of business IDs to their slugs\n      if (businessSlugs) {\n        businessSlugMap = businessSlugs.reduce((acc: Record<string, string | null>, business: { id: string; business_slug: string | null }) => {\n          acc[business.id] = business.business_slug;\n          return acc;\n        }, {} as Record<string, string | null>);\n      }\n    }\n\n    // Process the reviews data with profile information\n    const processedData: ReviewWithUser[] = reviewsData.map((review: any) => {\n      const profile = profilesMap?.[review.user_id];\n      const userProfile = profile\n        ? {\n            ...profile,\n            business_slug: profile.is_business\n              ? businessSlugMap[review.user_id] || null\n              : null,\n          }\n        : undefined;\n\n      return {\n        ...review,\n        user_profile: userProfile,\n      };\n    });\n\n    return {\n      data: processedData,\n      totalCount: count || 0,\n    };\n  } catch (error) {\n    console.error(\"Unexpected error in fetchBusinessReviews:\", error);\n    const errorMessage =\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\n    return { data: [], totalCount: 0, error: errorMessage };\n  }\n}\n"], "names": [], "mappings": ";;;;;;IA2BsB,uBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/interactions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { revalidatePath } from \"next/cache\";\r\n// getSecureBusinessProfileBySlug is imported but not used in this file\r\n// import { getSecureBusinessProfileBySlug } from './secureBusinessProfiles';\r\n// import { cookies } from 'next/headers'; // Removed unused import\r\n\r\nexport async function subscribeToBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies(); // No longer needed here\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from subscribing to their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot subscribe to your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Insert subscription - Use regular client with proper RLS\r\n    const { error: insertError } = await supabase\r\n      .from(\"subscriptions\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already subscribed) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already subscribed to business ${businessProfileId}.`\r\n        );\r\n        // Optionally return success true if already subscribed is acceptable\r\n        return { success: true };\r\n      }\r\n      console.error(\"Error inserting subscription:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Revalidate the specific card page and potentially the user's dashboard\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in subscribeToBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\n// --- Implementation for other actions ---\r\n\r\nexport async function unsubscribeFromBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unsubscribing from their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unsubscribe from your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Delete subscription - Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"subscriptions\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting subscription:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unsubscribeFromBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function submitReview(\r\n  businessProfileId: string,\r\n  rating: number,\r\n  reviewText?: string | null // Allow null for review text\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from reviewing their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot review your own business card.\" };\r\n  }\r\n\r\n  if (rating < 1 || rating > 5) {\r\n    return { success: false, error: \"Rating must be between 1 and 5.\" };\r\n  }\r\n\r\n  try {\r\n    // Upsert the review: insert if not exists, update if exists - Use regular client with proper RLS\r\n    const { error: upsertError } = await supabase\r\n      .from(\"ratings_reviews\")\r\n      .upsert(\r\n        {\r\n          user_id: user.id,\r\n          business_profile_id: businessProfileId,\r\n          rating: rating,\r\n          review_text: reviewText, // Pass reviewText directly\r\n          updated_at: new Date().toISOString(), // Explicitly set updated_at on upsert\r\n        },\r\n        {\r\n          onConflict: \"user_id, business_profile_id\", // Specify conflict target\r\n        }\r\n      );\r\n\r\n    if (upsertError) {\r\n      console.error(\"Error submitting review:\", upsertError);\r\n      throw new Error(upsertError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard where reviews might be shown\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in submitReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function deleteReview(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  try {\r\n    // Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"ratings_reviews\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting review:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in deleteReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function likeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from liking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot like your own business card.\" };\r\n  }\r\n\r\n  try {\r\n    // 1. Insert like - Use regular client with proper RLS\r\n    const { error: insertError } = await supabase\r\n      .from(\"likes\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already liked) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already liked business ${businessProfileId}.`\r\n        );\r\n        return { success: true }; // Consider it success if already liked\r\n      }\r\n      console.error(\"Error inserting like:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in likeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function unlikeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unliking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unlike your own business card.\",\r\n    };\r\n  }\r\n\r\n  try {\r\n    // 1. Delete like - Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"likes\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting like:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unlikeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function getInteractionStatus(businessProfileId: string): Promise<{\r\n  isSubscribed: boolean;\r\n  hasLiked: boolean;\r\n  userRating: number | null;\r\n  userReview: string | null;\r\n  error?: string;\r\n}> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n  let userId: string | null = null;\r\n\r\n  // Try to get authenticated user, but proceed even if not logged in\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n  if (user) {\r\n    userId = user.id;\r\n  }\r\n\r\n  // Default status for anonymous users\r\n  const defaultStatus = {\r\n    isSubscribed: false,\r\n    hasLiked: false,\r\n    userRating: null,\r\n    userReview: null,\r\n  };\r\n\r\n  if (!userId) {\r\n    return defaultStatus; // Return default if no user is logged in\r\n  }\r\n\r\n  try {\r\n    // Use regular client - all these tables have public read access\r\n    // Fetch all statuses in parallel\r\n    const [subscriptionRes, likeRes, reviewRes] = await Promise.all([\r\n      supabase\r\n        .from(\"subscriptions\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabase\r\n        .from(\"likes\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabase\r\n        .from(\"ratings_reviews\")\r\n        .select(\"rating, review_text\")\r\n        .match({ user_id: userId, business_profile_id: businessProfileId })\r\n        .maybeSingle(), // Use maybeSingle as user might not have reviewed\r\n    ]);\r\n\r\n    // Check for errors in parallel fetches\r\n    if (subscriptionRes.error)\r\n      throw new Error(\r\n        `Subscription fetch error: ${subscriptionRes.error.message}`\r\n      );\r\n    if (likeRes.error)\r\n      throw new Error(`Like fetch error: ${likeRes.error.message}`);\r\n    if (reviewRes.error)\r\n      throw new Error(`Review fetch error: ${reviewRes.error.message}`);\r\n\r\n    const reviewData = reviewRes.data;\r\n\r\n    return {\r\n      isSubscribed: (subscriptionRes.count ?? 0) > 0,\r\n      hasLiked: (likeRes.count ?? 0) > 0,\r\n      userRating: reviewData?.rating ?? null,\r\n      userReview: reviewData?.review_text ?? null,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching interaction status:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    // Return default status but include the error message\r\n    return { ...defaultStatus, error: errorMessage };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA2KsB,eAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/interactions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { revalidatePath } from \"next/cache\";\r\n// getSecureBusinessProfileBySlug is imported but not used in this file\r\n// import { getSecureBusinessProfileBySlug } from './secureBusinessProfiles';\r\n// import { cookies } from 'next/headers'; // Removed unused import\r\n\r\nexport async function subscribeToBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies(); // No longer needed here\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from subscribing to their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot subscribe to your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Insert subscription - Use regular client with proper RLS\r\n    const { error: insertError } = await supabase\r\n      .from(\"subscriptions\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already subscribed) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already subscribed to business ${businessProfileId}.`\r\n        );\r\n        // Optionally return success true if already subscribed is acceptable\r\n        return { success: true };\r\n      }\r\n      console.error(\"Error inserting subscription:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Revalidate the specific card page and potentially the user's dashboard\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in subscribeToBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\n// --- Implementation for other actions ---\r\n\r\nexport async function unsubscribeFromBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unsubscribing from their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unsubscribe from your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Delete subscription - Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"subscriptions\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting subscription:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unsubscribeFromBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function submitReview(\r\n  businessProfileId: string,\r\n  rating: number,\r\n  reviewText?: string | null // Allow null for review text\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from reviewing their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot review your own business card.\" };\r\n  }\r\n\r\n  if (rating < 1 || rating > 5) {\r\n    return { success: false, error: \"Rating must be between 1 and 5.\" };\r\n  }\r\n\r\n  try {\r\n    // Upsert the review: insert if not exists, update if exists - Use regular client with proper RLS\r\n    const { error: upsertError } = await supabase\r\n      .from(\"ratings_reviews\")\r\n      .upsert(\r\n        {\r\n          user_id: user.id,\r\n          business_profile_id: businessProfileId,\r\n          rating: rating,\r\n          review_text: reviewText, // Pass reviewText directly\r\n          updated_at: new Date().toISOString(), // Explicitly set updated_at on upsert\r\n        },\r\n        {\r\n          onConflict: \"user_id, business_profile_id\", // Specify conflict target\r\n        }\r\n      );\r\n\r\n    if (upsertError) {\r\n      console.error(\"Error submitting review:\", upsertError);\r\n      throw new Error(upsertError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard where reviews might be shown\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in submitReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function deleteReview(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  try {\r\n    // Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"ratings_reviews\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting review:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in deleteReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function likeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from liking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot like your own business card.\" };\r\n  }\r\n\r\n  try {\r\n    // 1. Insert like - Use regular client with proper RLS\r\n    const { error: insertError } = await supabase\r\n      .from(\"likes\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already liked) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already liked business ${businessProfileId}.`\r\n        );\r\n        return { success: true }; // Consider it success if already liked\r\n      }\r\n      console.error(\"Error inserting like:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in likeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function unlikeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unliking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unlike your own business card.\",\r\n    };\r\n  }\r\n\r\n  try {\r\n    // 1. Delete like - Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"likes\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting like:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unlikeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function getInteractionStatus(businessProfileId: string): Promise<{\r\n  isSubscribed: boolean;\r\n  hasLiked: boolean;\r\n  userRating: number | null;\r\n  userReview: string | null;\r\n  error?: string;\r\n}> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n  let userId: string | null = null;\r\n\r\n  // Try to get authenticated user, but proceed even if not logged in\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n  if (user) {\r\n    userId = user.id;\r\n  }\r\n\r\n  // Default status for anonymous users\r\n  const defaultStatus = {\r\n    isSubscribed: false,\r\n    hasLiked: false,\r\n    userRating: null,\r\n    userReview: null,\r\n  };\r\n\r\n  if (!userId) {\r\n    return defaultStatus; // Return default if no user is logged in\r\n  }\r\n\r\n  try {\r\n    // Use regular client - all these tables have public read access\r\n    // Fetch all statuses in parallel\r\n    const [subscriptionRes, likeRes, reviewRes] = await Promise.all([\r\n      supabase\r\n        .from(\"subscriptions\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabase\r\n        .from(\"likes\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabase\r\n        .from(\"ratings_reviews\")\r\n        .select(\"rating, review_text\")\r\n        .match({ user_id: userId, business_profile_id: businessProfileId })\r\n        .maybeSingle(), // Use maybeSingle as user might not have reviewed\r\n    ]);\r\n\r\n    // Check for errors in parallel fetches\r\n    if (subscriptionRes.error)\r\n      throw new Error(\r\n        `Subscription fetch error: ${subscriptionRes.error.message}`\r\n      );\r\n    if (likeRes.error)\r\n      throw new Error(`Like fetch error: ${likeRes.error.message}`);\r\n    if (reviewRes.error)\r\n      throw new Error(`Review fetch error: ${reviewRes.error.message}`);\r\n\r\n    const reviewData = reviewRes.data;\r\n\r\n    return {\r\n      isSubscribed: (subscriptionRes.count ?? 0) > 0,\r\n      hasLiked: (likeRes.count ?? 0) > 0,\r\n      userRating: reviewData?.rating ?? null,\r\n      userReview: reviewData?.review_text ?? null,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching interaction status:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    // Return default status but include the error message\r\n    return { ...defaultStatus, error: errorMessage };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAsPsB,eAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/interactions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { revalidatePath } from \"next/cache\";\r\n// getSecureBusinessProfileBySlug is imported but not used in this file\r\n// import { getSecureBusinessProfileBySlug } from './secureBusinessProfiles';\r\n// import { cookies } from 'next/headers'; // Removed unused import\r\n\r\nexport async function subscribeToBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies(); // No longer needed here\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from subscribing to their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot subscribe to your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Insert subscription - Use regular client with proper RLS\r\n    const { error: insertError } = await supabase\r\n      .from(\"subscriptions\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already subscribed) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already subscribed to business ${businessProfileId}.`\r\n        );\r\n        // Optionally return success true if already subscribed is acceptable\r\n        return { success: true };\r\n      }\r\n      console.error(\"Error inserting subscription:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Revalidate the specific card page and potentially the user's dashboard\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in subscribeToBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\n// --- Implementation for other actions ---\r\n\r\nexport async function unsubscribeFromBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unsubscribing from their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unsubscribe from your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Delete subscription - Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"subscriptions\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting subscription:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unsubscribeFromBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function submitReview(\r\n  businessProfileId: string,\r\n  rating: number,\r\n  reviewText?: string | null // Allow null for review text\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from reviewing their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot review your own business card.\" };\r\n  }\r\n\r\n  if (rating < 1 || rating > 5) {\r\n    return { success: false, error: \"Rating must be between 1 and 5.\" };\r\n  }\r\n\r\n  try {\r\n    // Upsert the review: insert if not exists, update if exists - Use regular client with proper RLS\r\n    const { error: upsertError } = await supabase\r\n      .from(\"ratings_reviews\")\r\n      .upsert(\r\n        {\r\n          user_id: user.id,\r\n          business_profile_id: businessProfileId,\r\n          rating: rating,\r\n          review_text: reviewText, // Pass reviewText directly\r\n          updated_at: new Date().toISOString(), // Explicitly set updated_at on upsert\r\n        },\r\n        {\r\n          onConflict: \"user_id, business_profile_id\", // Specify conflict target\r\n        }\r\n      );\r\n\r\n    if (upsertError) {\r\n      console.error(\"Error submitting review:\", upsertError);\r\n      throw new Error(upsertError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard where reviews might be shown\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in submitReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function deleteReview(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  try {\r\n    // Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"ratings_reviews\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting review:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in deleteReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function likeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from liking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot like your own business card.\" };\r\n  }\r\n\r\n  try {\r\n    // 1. Insert like - Use regular client with proper RLS\r\n    const { error: insertError } = await supabase\r\n      .from(\"likes\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already liked) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already liked business ${businessProfileId}.`\r\n        );\r\n        return { success: true }; // Consider it success if already liked\r\n      }\r\n      console.error(\"Error inserting like:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in likeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function unlikeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unliking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unlike your own business card.\",\r\n    };\r\n  }\r\n\r\n  try {\r\n    // 1. Delete like - Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"likes\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting like:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unlikeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function getInteractionStatus(businessProfileId: string): Promise<{\r\n  isSubscribed: boolean;\r\n  hasLiked: boolean;\r\n  userRating: number | null;\r\n  userReview: string | null;\r\n  error?: string;\r\n}> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n  let userId: string | null = null;\r\n\r\n  // Try to get authenticated user, but proceed even if not logged in\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n  if (user) {\r\n    userId = user.id;\r\n  }\r\n\r\n  // Default status for anonymous users\r\n  const defaultStatus = {\r\n    isSubscribed: false,\r\n    hasLiked: false,\r\n    userRating: null,\r\n    userReview: null,\r\n  };\r\n\r\n  if (!userId) {\r\n    return defaultStatus; // Return default if no user is logged in\r\n  }\r\n\r\n  try {\r\n    // Use regular client - all these tables have public read access\r\n    // Fetch all statuses in parallel\r\n    const [subscriptionRes, likeRes, reviewRes] = await Promise.all([\r\n      supabase\r\n        .from(\"subscriptions\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabase\r\n        .from(\"likes\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabase\r\n        .from(\"ratings_reviews\")\r\n        .select(\"rating, review_text\")\r\n        .match({ user_id: userId, business_profile_id: businessProfileId })\r\n        .maybeSingle(), // Use maybeSingle as user might not have reviewed\r\n    ]);\r\n\r\n    // Check for errors in parallel fetches\r\n    if (subscriptionRes.error)\r\n      throw new Error(\r\n        `Subscription fetch error: ${subscriptionRes.error.message}`\r\n      );\r\n    if (likeRes.error)\r\n      throw new Error(`Like fetch error: ${likeRes.error.message}`);\r\n    if (reviewRes.error)\r\n      throw new Error(`Review fetch error: ${reviewRes.error.message}`);\r\n\r\n    const reviewData = reviewRes.data;\r\n\r\n    return {\r\n      isSubscribed: (subscriptionRes.count ?? 0) > 0,\r\n      hasLiked: (likeRes.count ?? 0) > 0,\r\n      userRating: reviewData?.rating ?? null,\r\n      userReview: reviewData?.review_text ?? null,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching interaction status:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    // Return default status but include the error message\r\n    return { ...defaultStatus, error: errorMessage };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAocsB,uBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/interactions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { revalidatePath } from \"next/cache\";\r\n// getSecureBusinessProfileBySlug is imported but not used in this file\r\n// import { getSecureBusinessProfileBySlug } from './secureBusinessProfiles';\r\n// import { cookies } from 'next/headers'; // Removed unused import\r\n\r\nexport async function subscribeToBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies(); // No longer needed here\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from subscribing to their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot subscribe to your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Insert subscription - Use regular client with proper RLS\r\n    const { error: insertError } = await supabase\r\n      .from(\"subscriptions\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already subscribed) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already subscribed to business ${businessProfileId}.`\r\n        );\r\n        // Optionally return success true if already subscribed is acceptable\r\n        return { success: true };\r\n      }\r\n      console.error(\"Error inserting subscription:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Revalidate the specific card page and potentially the user's dashboard\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in subscribeToBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\n// --- Implementation for other actions ---\r\n\r\nexport async function unsubscribeFromBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unsubscribing from their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unsubscribe from your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Delete subscription - Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"subscriptions\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting subscription:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unsubscribeFromBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function submitReview(\r\n  businessProfileId: string,\r\n  rating: number,\r\n  reviewText?: string | null // Allow null for review text\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from reviewing their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot review your own business card.\" };\r\n  }\r\n\r\n  if (rating < 1 || rating > 5) {\r\n    return { success: false, error: \"Rating must be between 1 and 5.\" };\r\n  }\r\n\r\n  try {\r\n    // Upsert the review: insert if not exists, update if exists - Use regular client with proper RLS\r\n    const { error: upsertError } = await supabase\r\n      .from(\"ratings_reviews\")\r\n      .upsert(\r\n        {\r\n          user_id: user.id,\r\n          business_profile_id: businessProfileId,\r\n          rating: rating,\r\n          review_text: reviewText, // Pass reviewText directly\r\n          updated_at: new Date().toISOString(), // Explicitly set updated_at on upsert\r\n        },\r\n        {\r\n          onConflict: \"user_id, business_profile_id\", // Specify conflict target\r\n        }\r\n      );\r\n\r\n    if (upsertError) {\r\n      console.error(\"Error submitting review:\", upsertError);\r\n      throw new Error(upsertError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard where reviews might be shown\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in submitReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function deleteReview(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  try {\r\n    // Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"ratings_reviews\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting review:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in deleteReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function likeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from liking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot like your own business card.\" };\r\n  }\r\n\r\n  try {\r\n    // 1. Insert like - Use regular client with proper RLS\r\n    const { error: insertError } = await supabase\r\n      .from(\"likes\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already liked) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already liked business ${businessProfileId}.`\r\n        );\r\n        return { success: true }; // Consider it success if already liked\r\n      }\r\n      console.error(\"Error inserting like:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in likeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function unlikeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unliking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unlike your own business card.\",\r\n    };\r\n  }\r\n\r\n  try {\r\n    // 1. Delete like - Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"likes\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting like:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unlikeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function getInteractionStatus(businessProfileId: string): Promise<{\r\n  isSubscribed: boolean;\r\n  hasLiked: boolean;\r\n  userRating: number | null;\r\n  userReview: string | null;\r\n  error?: string;\r\n}> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n  let userId: string | null = null;\r\n\r\n  // Try to get authenticated user, but proceed even if not logged in\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n  if (user) {\r\n    userId = user.id;\r\n  }\r\n\r\n  // Default status for anonymous users\r\n  const defaultStatus = {\r\n    isSubscribed: false,\r\n    hasLiked: false,\r\n    userRating: null,\r\n    userReview: null,\r\n  };\r\n\r\n  if (!userId) {\r\n    return defaultStatus; // Return default if no user is logged in\r\n  }\r\n\r\n  try {\r\n    // Use regular client - all these tables have public read access\r\n    // Fetch all statuses in parallel\r\n    const [subscriptionRes, likeRes, reviewRes] = await Promise.all([\r\n      supabase\r\n        .from(\"subscriptions\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabase\r\n        .from(\"likes\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabase\r\n        .from(\"ratings_reviews\")\r\n        .select(\"rating, review_text\")\r\n        .match({ user_id: userId, business_profile_id: businessProfileId })\r\n        .maybeSingle(), // Use maybeSingle as user might not have reviewed\r\n    ]);\r\n\r\n    // Check for errors in parallel fetches\r\n    if (subscriptionRes.error)\r\n      throw new Error(\r\n        `Subscription fetch error: ${subscriptionRes.error.message}`\r\n      );\r\n    if (likeRes.error)\r\n      throw new Error(`Like fetch error: ${likeRes.error.message}`);\r\n    if (reviewRes.error)\r\n      throw new Error(`Review fetch error: ${reviewRes.error.message}`);\r\n\r\n    const reviewData = reviewRes.data;\r\n\r\n    return {\r\n      isSubscribed: (subscriptionRes.count ?? 0) > 0,\r\n      hasLiked: (likeRes.count ?? 0) > 0,\r\n      userRating: reviewData?.rating ?? null,\r\n      userReview: reviewData?.review_text ?? null,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching interaction status:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    // Return default status but include the error message\r\n    return { ...defaultStatus, error: errorMessage };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA4SsB,eAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/interactions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { revalidatePath } from \"next/cache\";\r\n// getSecureBusinessProfileBySlug is imported but not used in this file\r\n// import { getSecureBusinessProfileBySlug } from './secureBusinessProfiles';\r\n// import { cookies } from 'next/headers'; // Removed unused import\r\n\r\nexport async function subscribeToBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies(); // No longer needed here\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from subscribing to their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot subscribe to your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Insert subscription - Use regular client with proper RLS\r\n    const { error: insertError } = await supabase\r\n      .from(\"subscriptions\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already subscribed) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already subscribed to business ${businessProfileId}.`\r\n        );\r\n        // Optionally return success true if already subscribed is acceptable\r\n        return { success: true };\r\n      }\r\n      console.error(\"Error inserting subscription:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Revalidate the specific card page and potentially the user's dashboard\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in subscribeToBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\n// --- Implementation for other actions ---\r\n\r\nexport async function unsubscribeFromBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unsubscribing from their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unsubscribe from your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Delete subscription - Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"subscriptions\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting subscription:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unsubscribeFromBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function submitReview(\r\n  businessProfileId: string,\r\n  rating: number,\r\n  reviewText?: string | null // Allow null for review text\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from reviewing their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot review your own business card.\" };\r\n  }\r\n\r\n  if (rating < 1 || rating > 5) {\r\n    return { success: false, error: \"Rating must be between 1 and 5.\" };\r\n  }\r\n\r\n  try {\r\n    // Upsert the review: insert if not exists, update if exists - Use regular client with proper RLS\r\n    const { error: upsertError } = await supabase\r\n      .from(\"ratings_reviews\")\r\n      .upsert(\r\n        {\r\n          user_id: user.id,\r\n          business_profile_id: businessProfileId,\r\n          rating: rating,\r\n          review_text: reviewText, // Pass reviewText directly\r\n          updated_at: new Date().toISOString(), // Explicitly set updated_at on upsert\r\n        },\r\n        {\r\n          onConflict: \"user_id, business_profile_id\", // Specify conflict target\r\n        }\r\n      );\r\n\r\n    if (upsertError) {\r\n      console.error(\"Error submitting review:\", upsertError);\r\n      throw new Error(upsertError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard where reviews might be shown\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in submitReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function deleteReview(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  try {\r\n    // Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"ratings_reviews\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting review:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in deleteReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function likeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from liking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot like your own business card.\" };\r\n  }\r\n\r\n  try {\r\n    // 1. Insert like - Use regular client with proper RLS\r\n    const { error: insertError } = await supabase\r\n      .from(\"likes\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already liked) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already liked business ${businessProfileId}.`\r\n        );\r\n        return { success: true }; // Consider it success if already liked\r\n      }\r\n      console.error(\"Error inserting like:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in likeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function unlikeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unliking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unlike your own business card.\",\r\n    };\r\n  }\r\n\r\n  try {\r\n    // 1. Delete like - Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"likes\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting like:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unlikeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function getInteractionStatus(businessProfileId: string): Promise<{\r\n  isSubscribed: boolean;\r\n  hasLiked: boolean;\r\n  userRating: number | null;\r\n  userReview: string | null;\r\n  error?: string;\r\n}> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n  let userId: string | null = null;\r\n\r\n  // Try to get authenticated user, but proceed even if not logged in\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n  if (user) {\r\n    userId = user.id;\r\n  }\r\n\r\n  // Default status for anonymous users\r\n  const defaultStatus = {\r\n    isSubscribed: false,\r\n    hasLiked: false,\r\n    userRating: null,\r\n    userReview: null,\r\n  };\r\n\r\n  if (!userId) {\r\n    return defaultStatus; // Return default if no user is logged in\r\n  }\r\n\r\n  try {\r\n    // Use regular client - all these tables have public read access\r\n    // Fetch all statuses in parallel\r\n    const [subscriptionRes, likeRes, reviewRes] = await Promise.all([\r\n      supabase\r\n        .from(\"subscriptions\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabase\r\n        .from(\"likes\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabase\r\n        .from(\"ratings_reviews\")\r\n        .select(\"rating, review_text\")\r\n        .match({ user_id: userId, business_profile_id: businessProfileId })\r\n        .maybeSingle(), // Use maybeSingle as user might not have reviewed\r\n    ]);\r\n\r\n    // Check for errors in parallel fetches\r\n    if (subscriptionRes.error)\r\n      throw new Error(\r\n        `Subscription fetch error: ${subscriptionRes.error.message}`\r\n      );\r\n    if (likeRes.error)\r\n      throw new Error(`Like fetch error: ${likeRes.error.message}`);\r\n    if (reviewRes.error)\r\n      throw new Error(`Review fetch error: ${reviewRes.error.message}`);\r\n\r\n    const reviewData = reviewRes.data;\r\n\r\n    return {\r\n      isSubscribed: (subscriptionRes.count ?? 0) > 0,\r\n      hasLiked: (likeRes.count ?? 0) > 0,\r\n      userRating: reviewData?.rating ?? null,\r\n      userReview: reviewData?.review_text ?? null,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching interaction status:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    // Return default status but include the error message\r\n    return { ...defaultStatus, error: errorMessage };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAQsB,sBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/interactions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { revalidatePath } from \"next/cache\";\r\n// getSecureBusinessProfileBySlug is imported but not used in this file\r\n// import { getSecureBusinessProfileBySlug } from './secureBusinessProfiles';\r\n// import { cookies } from 'next/headers'; // Removed unused import\r\n\r\nexport async function subscribeToBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies(); // No longer needed here\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from subscribing to their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot subscribe to your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Insert subscription - Use regular client with proper RLS\r\n    const { error: insertError } = await supabase\r\n      .from(\"subscriptions\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already subscribed) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already subscribed to business ${businessProfileId}.`\r\n        );\r\n        // Optionally return success true if already subscribed is acceptable\r\n        return { success: true };\r\n      }\r\n      console.error(\"Error inserting subscription:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Revalidate the specific card page and potentially the user's dashboard\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in subscribeToBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\n// --- Implementation for other actions ---\r\n\r\nexport async function unsubscribeFromBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unsubscribing from their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unsubscribe from your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Delete subscription - Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"subscriptions\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting subscription:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unsubscribeFromBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function submitReview(\r\n  businessProfileId: string,\r\n  rating: number,\r\n  reviewText?: string | null // Allow null for review text\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from reviewing their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot review your own business card.\" };\r\n  }\r\n\r\n  if (rating < 1 || rating > 5) {\r\n    return { success: false, error: \"Rating must be between 1 and 5.\" };\r\n  }\r\n\r\n  try {\r\n    // Upsert the review: insert if not exists, update if exists - Use regular client with proper RLS\r\n    const { error: upsertError } = await supabase\r\n      .from(\"ratings_reviews\")\r\n      .upsert(\r\n        {\r\n          user_id: user.id,\r\n          business_profile_id: businessProfileId,\r\n          rating: rating,\r\n          review_text: reviewText, // Pass reviewText directly\r\n          updated_at: new Date().toISOString(), // Explicitly set updated_at on upsert\r\n        },\r\n        {\r\n          onConflict: \"user_id, business_profile_id\", // Specify conflict target\r\n        }\r\n      );\r\n\r\n    if (upsertError) {\r\n      console.error(\"Error submitting review:\", upsertError);\r\n      throw new Error(upsertError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard where reviews might be shown\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in submitReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function deleteReview(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  try {\r\n    // Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"ratings_reviews\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting review:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in deleteReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function likeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from liking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot like your own business card.\" };\r\n  }\r\n\r\n  try {\r\n    // 1. Insert like - Use regular client with proper RLS\r\n    const { error: insertError } = await supabase\r\n      .from(\"likes\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already liked) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already liked business ${businessProfileId}.`\r\n        );\r\n        return { success: true }; // Consider it success if already liked\r\n      }\r\n      console.error(\"Error inserting like:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in likeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function unlikeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unliking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unlike your own business card.\",\r\n    };\r\n  }\r\n\r\n  try {\r\n    // 1. Delete like - Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"likes\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting like:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unlikeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function getInteractionStatus(businessProfileId: string): Promise<{\r\n  isSubscribed: boolean;\r\n  hasLiked: boolean;\r\n  userRating: number | null;\r\n  userReview: string | null;\r\n  error?: string;\r\n}> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n  let userId: string | null = null;\r\n\r\n  // Try to get authenticated user, but proceed even if not logged in\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n  if (user) {\r\n    userId = user.id;\r\n  }\r\n\r\n  // Default status for anonymous users\r\n  const defaultStatus = {\r\n    isSubscribed: false,\r\n    hasLiked: false,\r\n    userRating: null,\r\n    userReview: null,\r\n  };\r\n\r\n  if (!userId) {\r\n    return defaultStatus; // Return default if no user is logged in\r\n  }\r\n\r\n  try {\r\n    // Use regular client - all these tables have public read access\r\n    // Fetch all statuses in parallel\r\n    const [subscriptionRes, likeRes, reviewRes] = await Promise.all([\r\n      supabase\r\n        .from(\"subscriptions\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabase\r\n        .from(\"likes\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabase\r\n        .from(\"ratings_reviews\")\r\n        .select(\"rating, review_text\")\r\n        .match({ user_id: userId, business_profile_id: businessProfileId })\r\n        .maybeSingle(), // Use maybeSingle as user might not have reviewed\r\n    ]);\r\n\r\n    // Check for errors in parallel fetches\r\n    if (subscriptionRes.error)\r\n      throw new Error(\r\n        `Subscription fetch error: ${subscriptionRes.error.message}`\r\n      );\r\n    if (likeRes.error)\r\n      throw new Error(`Like fetch error: ${likeRes.error.message}`);\r\n    if (reviewRes.error)\r\n      throw new Error(`Review fetch error: ${reviewRes.error.message}`);\r\n\r\n    const reviewData = reviewRes.data;\r\n\r\n    return {\r\n      isSubscribed: (subscriptionRes.count ?? 0) > 0,\r\n      hasLiked: (likeRes.count ?? 0) > 0,\r\n      userRating: reviewData?.rating ?? null,\r\n      userReview: reviewData?.review_text ?? null,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching interaction status:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    // Return default status but include the error message\r\n    return { ...defaultStatus, error: errorMessage };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA0XsB,iBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/interactions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { revalidatePath } from \"next/cache\";\r\n// getSecureBusinessProfileBySlug is imported but not used in this file\r\n// import { getSecureBusinessProfileBySlug } from './secureBusinessProfiles';\r\n// import { cookies } from 'next/headers'; // Removed unused import\r\n\r\nexport async function subscribeToBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies(); // No longer needed here\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from subscribing to their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot subscribe to your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Insert subscription - Use regular client with proper RLS\r\n    const { error: insertError } = await supabase\r\n      .from(\"subscriptions\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already subscribed) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already subscribed to business ${businessProfileId}.`\r\n        );\r\n        // Optionally return success true if already subscribed is acceptable\r\n        return { success: true };\r\n      }\r\n      console.error(\"Error inserting subscription:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Revalidate the specific card page and potentially the user's dashboard\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in subscribeToBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\n// --- Implementation for other actions ---\r\n\r\nexport async function unsubscribeFromBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unsubscribing from their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unsubscribe from your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Delete subscription - Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"subscriptions\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting subscription:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unsubscribeFromBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function submitReview(\r\n  businessProfileId: string,\r\n  rating: number,\r\n  reviewText?: string | null // Allow null for review text\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from reviewing their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot review your own business card.\" };\r\n  }\r\n\r\n  if (rating < 1 || rating > 5) {\r\n    return { success: false, error: \"Rating must be between 1 and 5.\" };\r\n  }\r\n\r\n  try {\r\n    // Upsert the review: insert if not exists, update if exists - Use regular client with proper RLS\r\n    const { error: upsertError } = await supabase\r\n      .from(\"ratings_reviews\")\r\n      .upsert(\r\n        {\r\n          user_id: user.id,\r\n          business_profile_id: businessProfileId,\r\n          rating: rating,\r\n          review_text: reviewText, // Pass reviewText directly\r\n          updated_at: new Date().toISOString(), // Explicitly set updated_at on upsert\r\n        },\r\n        {\r\n          onConflict: \"user_id, business_profile_id\", // Specify conflict target\r\n        }\r\n      );\r\n\r\n    if (upsertError) {\r\n      console.error(\"Error submitting review:\", upsertError);\r\n      throw new Error(upsertError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard where reviews might be shown\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in submitReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function deleteReview(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  try {\r\n    // Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"ratings_reviews\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting review:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in deleteReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function likeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from liking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot like your own business card.\" };\r\n  }\r\n\r\n  try {\r\n    // 1. Insert like - Use regular client with proper RLS\r\n    const { error: insertError } = await supabase\r\n      .from(\"likes\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already liked) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already liked business ${businessProfileId}.`\r\n        );\r\n        return { success: true }; // Consider it success if already liked\r\n      }\r\n      console.error(\"Error inserting like:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in likeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function unlikeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unliking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unlike your own business card.\",\r\n    };\r\n  }\r\n\r\n  try {\r\n    // 1. Delete like - Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"likes\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting like:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unlikeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function getInteractionStatus(businessProfileId: string): Promise<{\r\n  isSubscribed: boolean;\r\n  hasLiked: boolean;\r\n  userRating: number | null;\r\n  userReview: string | null;\r\n  error?: string;\r\n}> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n  let userId: string | null = null;\r\n\r\n  // Try to get authenticated user, but proceed even if not logged in\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n  if (user) {\r\n    userId = user.id;\r\n  }\r\n\r\n  // Default status for anonymous users\r\n  const defaultStatus = {\r\n    isSubscribed: false,\r\n    hasLiked: false,\r\n    userRating: null,\r\n    userReview: null,\r\n  };\r\n\r\n  if (!userId) {\r\n    return defaultStatus; // Return default if no user is logged in\r\n  }\r\n\r\n  try {\r\n    // Use regular client - all these tables have public read access\r\n    // Fetch all statuses in parallel\r\n    const [subscriptionRes, likeRes, reviewRes] = await Promise.all([\r\n      supabase\r\n        .from(\"subscriptions\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabase\r\n        .from(\"likes\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabase\r\n        .from(\"ratings_reviews\")\r\n        .select(\"rating, review_text\")\r\n        .match({ user_id: userId, business_profile_id: businessProfileId })\r\n        .maybeSingle(), // Use maybeSingle as user might not have reviewed\r\n    ]);\r\n\r\n    // Check for errors in parallel fetches\r\n    if (subscriptionRes.error)\r\n      throw new Error(\r\n        `Subscription fetch error: ${subscriptionRes.error.message}`\r\n      );\r\n    if (likeRes.error)\r\n      throw new Error(`Like fetch error: ${likeRes.error.message}`);\r\n    if (reviewRes.error)\r\n      throw new Error(`Review fetch error: ${reviewRes.error.message}`);\r\n\r\n    const reviewData = reviewRes.data;\r\n\r\n    return {\r\n      isSubscribed: (subscriptionRes.count ?? 0) > 0,\r\n      hasLiked: (likeRes.count ?? 0) > 0,\r\n      userRating: reviewData?.rating ?? null,\r\n      userReview: reviewData?.review_text ?? null,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching interaction status:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    // Return default status but include the error message\r\n    return { ...defaultStatus, error: errorMessage };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA+FsB,0BAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/schemas/authSchemas.ts"], "sourcesContent": ["import { z } from \"zod\";\n\nexport const IndianMobileSchema = z\n  .string()\n  .trim()\n  .min(10, { message: \"Mobile number must be 10 digits\" })\n  .max(10, { message: \"Mobile number must be 10 digits\" })\n  .regex(/^[6-9]\\d{9}$/, { message: \"Please enter a valid Indian mobile number\" });\n\nexport const EmailOTPSchema = z.object({\n  email: z\n    .string()\n    .trim()\n    .min(1, { message: \"Email is required\" })\n    .email({ message: \"Please enter a valid email address\" }),\n});\n\nexport const VerifyOTPSchema = z.object({\n  email: z\n    .string()\n    .trim()\n    .min(1, { message: \"Email is required\" })\n    .email({ message: \"Please enter a valid email address\" }),\n  otp: z\n    .string()\n    .trim()\n    .min(6, { message: \"OTP must be 6 digits\" })\n    .max(6, { message: \"OTP must be 6 digits\" })\n    .regex(/^\\d{6}$/, { message: \"OTP must be 6 digits\" }),\n});\n\nexport const PasswordComplexitySchema = z\n  .string()\n  .min(6, \"Password must be at least 6 characters long\")\n  .regex(/[A-Z]/, \"Password must contain at least one uppercase letter\")\n  .regex(/[a-z]/, \"Password must contain at least one lowercase letter\")\n  .regex(/\\d/, \"Password must contain at least one number\")\n  .regex(/[^a-zA-Z0-9]/, \"Password must contain at least one special character\");\n\nexport const MobilePasswordLoginSchema = z.object({\n  mobile: IndianMobileSchema,\n  password: z.string().trim().min(1, { message: \"Password is required\" }),\n});"], "names": [], "mappings": ";;;;;;;AAAA;;AAEO,MAAM,qBAAqB,uIAAA,CAAA,IAAC,CAChC,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,IAAI;IAAE,SAAS;AAAkC,GACrD,GAAG,CAAC,IAAI;IAAE,SAAS;AAAkC,GACrD,KAAK,CAAC,gBAAgB;IAAE,SAAS;AAA4C;AAEzE,MAAM,iBAAiB,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,OAAO,uIAAA,CAAA,IAAC,CACL,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoB,GACtC,KAAK,CAAC;QAAE,SAAS;IAAqC;AAC3D;AAEO,MAAM,kBAAkB,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,OAAO,uIAAA,CAAA,IAAC,CACL,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoB,GACtC,KAAK,CAAC;QAAE,SAAS;IAAqC;IACzD,KAAK,uIAAA,CAAA,IAAC,CACH,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB,GACzC,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB,GACzC,KAAK,CAAC,WAAW;QAAE,SAAS;IAAuB;AACxD;AAEO,MAAM,2BAA2B,uIAAA,CAAA,IAAC,CACtC,MAAM,GACN,GAAG,CAAC,GAAG,+CACP,KAAK,CAAC,SAAS,uDACf,KAAK,CAAC,SAAS,uDACf,KAAK,CAAC,MAAM,6CACZ,KAAK,CAAC,gBAAgB;AAElB,MAAM,4BAA4B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChD,QAAQ;IACR,UAAU,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB;AACvE", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/customBranding.ts"], "sourcesContent": ["export type UserPlan = \"free\" | \"basic\" | \"growth\" | \"pro\" | \"enterprise\" | \"trial\";\r\n\r\nexport interface CustomBrandingSettings {\r\n  custom_header_text?: string;\r\n  custom_header_image_url?: string; // Legacy field for backward compatibility\r\n  custom_header_image_light_url?: string; // Light theme specific image\r\n  custom_header_image_dark_url?: string; // Dark theme specific image\r\n  hide_dukancard_branding?: boolean;\r\n}\r\n\r\n/**\r\n * Check if a user has access to custom branding features\r\n */\r\nexport function hasCustomBrandingAccess(userPlan?: UserPlan | null): boolean {\r\n  return userPlan === \"pro\" || userPlan === \"enterprise\";\r\n}\r\n\r\n/**\r\n * Check if Dukancard branding should be shown\r\n */\r\nexport function shouldShowDukancardBranding(\r\n  userPlan?: UserPlan | null,\r\n  customBranding?: CustomBrandingSettings | null\r\n): boolean {\r\n  // For users NOT on Pro/Enterprise plans, ALWAYS show DukanCard branding\r\n  // regardless of any custom branding settings\r\n  if (!hasCustomBrandingAccess(userPlan)) {\r\n    return true;\r\n  }\r\n\r\n  // For Pro/Enterprise users, only hide branding if they explicitly set it to hidden\r\n  return !customBranding?.hide_dukancard_branding;\r\n}\r\n\r\n/**\r\n * Get the theme-specific header image URL with fallback logic\r\n */\r\nexport function getThemeSpecificHeaderImage(\r\n  userPlan?: UserPlan | null,\r\n  customBranding?: CustomBrandingSettings | null,\r\n  currentTheme?: string | null\r\n): string | null {\r\n  // Only return custom images for Pro/Enterprise users\r\n  if (!hasCustomBrandingAccess(userPlan) || !customBranding) {\r\n    return null;\r\n  }\r\n\r\n  const isDark = currentTheme === \"dark\";\r\n\r\n  // Priority 1: Theme-specific images\r\n  if (isDark && customBranding.custom_header_image_dark_url?.trim()) {\r\n    return customBranding.custom_header_image_dark_url;\r\n  }\r\n\r\n  if (!isDark && customBranding.custom_header_image_light_url?.trim()) {\r\n    return customBranding.custom_header_image_light_url;\r\n  }\r\n\r\n  // Priority 2: Fallback to opposite theme if current theme image is missing\r\n  if (isDark && customBranding.custom_header_image_light_url?.trim()) {\r\n    return customBranding.custom_header_image_light_url;\r\n  }\r\n\r\n  if (!isDark && customBranding.custom_header_image_dark_url?.trim()) {\r\n    return customBranding.custom_header_image_dark_url;\r\n  }\r\n\r\n  // Priority 3: Legacy single image field for backward compatibility\r\n  if (customBranding.custom_header_image_url?.trim()) {\r\n    return customBranding.custom_header_image_url;\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\n/**\r\n * Get the branding text to display (custom or default)\r\n */\r\nexport function getBrandingText(\r\n  userPlan?: UserPlan | null,\r\n  customBranding?: CustomBrandingSettings | null\r\n): string | null {\r\n  // Only return custom text for Pro/Enterprise users\r\n  if (hasCustomBrandingAccess(userPlan) && customBranding?.custom_header_text) {\r\n    return customBranding.custom_header_text;\r\n  }\r\n  return null; // Will show default Dukancard branding\r\n}\r\n\r\n/**\r\n * Get the primary theme color (custom or default)\r\n */\r\nexport function getPrimaryThemeColor(\r\n  userPlan?: UserPlan | null,\r\n  customBranding?: CustomBrandingSettings | null,\r\n  fallbackThemeColor?: string\r\n): string {\r\n  const defaultColor = \"var(--brand-gold)\";\r\n\r\n  // Use theme_color if available and user is Pro/Enterprise\r\n  if (hasCustomBrandingAccess(userPlan) && fallbackThemeColor) {\r\n    return fallbackThemeColor;\r\n  }\r\n\r\n  return defaultColor;\r\n}\r\n\r\n\r\n\r\n/**\r\n * Generate custom CSS variables for the card based on custom branding\r\n */\r\nexport function generateCustomBrandingStyles(\r\n  userPlan?: UserPlan | null,\r\n  customBranding?: CustomBrandingSettings | null,\r\n  fallbackThemeColor?: string\r\n): React.CSSProperties {\r\n  const primaryColor = getPrimaryThemeColor(userPlan, customBranding, fallbackThemeColor);\r\n\r\n  const styles = {\r\n    \"--theme-color\": primaryColor,\r\n    \"--theme-color-80\": `${primaryColor}CC`,\r\n    \"--theme-color-50\": `${primaryColor}80`,\r\n    \"--theme-color-30\": `${primaryColor}4D`,\r\n    \"--theme-color-20\": `${primaryColor}33`,\r\n    \"--theme-color-10\": `${primaryColor}1A`,\r\n    \"--theme-color-5\": `${primaryColor}0D`,\r\n    \"--theme-accent-end\": \"#E5C76E\", // Default accent\r\n  } as React.CSSProperties;\r\n\r\n  return styles;\r\n}\r\n\r\n/**\r\n * Validate custom branding settings\r\n */\r\nexport function validateCustomBrandingSettings(\r\n  settings: Partial<CustomBrandingSettings>\r\n): { valid: boolean; errors: string[] } {\r\n  const errors: string[] = [];\r\n\r\n  // Validate header text length\r\n  if (settings.custom_header_text && settings.custom_header_text.length > 50) {\r\n    errors.push(\"Custom header text must be 50 characters or less\");\r\n  }\r\n\r\n  return {\r\n    valid: errors.length === 0,\r\n    errors\r\n  };\r\n}\r\n\r\n/**\r\n * Get default custom branding settings\r\n */\r\nexport function getDefaultCustomBrandingSettings(): CustomBrandingSettings {\r\n  return {\r\n    custom_header_text: \"\",\r\n    hide_dukancard_branding: false,\r\n  };\r\n}\r\n\r\n/**\r\n * Check if custom branding has any active settings\r\n */\r\nexport function hasActiveCustomBranding(\r\n  customBranding?: CustomBrandingSettings | null\r\n): boolean {\r\n  if (!customBranding) return false;\r\n\r\n  return !!(\r\n    customBranding.custom_header_text ||\r\n    customBranding.custom_header_image_url ||\r\n    customBranding.custom_header_image_light_url ||\r\n    customBranding.custom_header_image_dark_url ||\r\n    customBranding.hide_dukancard_branding\r\n  );\r\n}\r\n\r\n/**\r\n * Check if any header image exists (for any theme)\r\n */\r\nexport function hasAnyHeaderImage(\r\n  customBranding?: CustomBrandingSettings | null\r\n): boolean {\r\n  if (!customBranding) return false;\r\n\r\n  return !!(\r\n    customBranding.custom_header_image_url?.trim() ||\r\n    customBranding.custom_header_image_light_url?.trim() ||\r\n    customBranding.custom_header_image_dark_url?.trim()\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAaO,SAAS,wBAAwB,QAA0B;IAChE,OAAO,aAAa,SAAS,aAAa;AAC5C;AAKO,SAAS,4BACd,QAA0B,EAC1B,cAA8C;IAE9C,wEAAwE;IACxE,6CAA6C;IAC7C,IAAI,CAAC,wBAAwB,WAAW;QACtC,OAAO;IACT;IAEA,mFAAmF;IACnF,OAAO,CAAC,gBAAgB;AAC1B;AAKO,SAAS,4BACd,QAA0B,EAC1B,cAA8C,EAC9C,YAA4B;IAE5B,qDAAqD;IACrD,IAAI,CAAC,wBAAwB,aAAa,CAAC,gBAAgB;QACzD,OAAO;IACT;IAEA,MAAM,SAAS,iBAAiB;IAEhC,oCAAoC;IACpC,IAAI,UAAU,eAAe,4BAA4B,EAAE,QAAQ;QACjE,OAAO,eAAe,4BAA4B;IACpD;IAEA,IAAI,CAAC,UAAU,eAAe,6BAA6B,EAAE,QAAQ;QACnE,OAAO,eAAe,6BAA6B;IACrD;IAEA,2EAA2E;IAC3E,IAAI,UAAU,eAAe,6BAA6B,EAAE,QAAQ;QAClE,OAAO,eAAe,6BAA6B;IACrD;IAEA,IAAI,CAAC,UAAU,eAAe,4BAA4B,EAAE,QAAQ;QAClE,OAAO,eAAe,4BAA4B;IACpD;IAEA,mEAAmE;IACnE,IAAI,eAAe,uBAAuB,EAAE,QAAQ;QAClD,OAAO,eAAe,uBAAuB;IAC/C;IAEA,OAAO;AACT;AAKO,SAAS,gBACd,QAA0B,EAC1B,cAA8C;IAE9C,mDAAmD;IACnD,IAAI,wBAAwB,aAAa,gBAAgB,oBAAoB;QAC3E,OAAO,eAAe,kBAAkB;IAC1C;IACA,OAAO,MAAM,uCAAuC;AACtD;AAKO,SAAS,qBACd,QAA0B,EAC1B,cAA8C,EAC9C,kBAA2B;IAE3B,MAAM,eAAe;IAErB,0DAA0D;IAC1D,IAAI,wBAAwB,aAAa,oBAAoB;QAC3D,OAAO;IACT;IAEA,OAAO;AACT;AAOO,SAAS,6BACd,QAA0B,EAC1B,cAA8C,EAC9C,kBAA2B;IAE3B,MAAM,eAAe,qBAAqB,UAAU,gBAAgB;IAEpE,MAAM,SAAS;QACb,iBAAiB;QACjB,oBAAoB,GAAG,aAAa,EAAE,CAAC;QACvC,oBAAoB,GAAG,aAAa,EAAE,CAAC;QACvC,oBAAoB,GAAG,aAAa,EAAE,CAAC;QACvC,oBAAoB,GAAG,aAAa,EAAE,CAAC;QACvC,oBAAoB,GAAG,aAAa,EAAE,CAAC;QACvC,mBAAmB,GAAG,aAAa,EAAE,CAAC;QACtC,sBAAsB;IACxB;IAEA,OAAO;AACT;AAKO,SAAS,+BACd,QAAyC;IAEzC,MAAM,SAAmB,EAAE;IAE3B,8BAA8B;IAC9B,IAAI,SAAS,kBAAkB,IAAI,SAAS,kBAAkB,CAAC,MAAM,GAAG,IAAI;QAC1E,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,OAAO,OAAO,MAAM,KAAK;QACzB;IACF;AACF;AAKO,SAAS;IACd,OAAO;QACL,oBAAoB;QACpB,yBAAyB;IAC3B;AACF;AAKO,SAAS,wBACd,cAA8C;IAE9C,IAAI,CAAC,gBAAgB,OAAO;IAE5B,OAAO,CAAC,CAAC,CACP,eAAe,kBAAkB,IACjC,eAAe,uBAAuB,IACtC,eAAe,6BAA6B,IAC5C,eAAe,4BAA4B,IAC3C,eAAe,uBAAuB,AACxC;AACF;AAKO,SAAS,kBACd,cAA8C;IAE9C,IAAI,CAAC,gBAAgB,OAAO;IAE5B,OAAO,CAAC,CAAC,CACP,eAAe,uBAAuB,EAAE,UACxC,eAAe,6BAA6B,EAAE,UAC9C,eAAe,4BAA4B,EAAE,MAC/C;AACF", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/qrCodeGenerator.ts"], "sourcesContent": ["/**\r\n * Utility functions to generate and download QR codes in different formats:\r\n * 1. Enhanced QR code with business information in A4 format for better printing options\r\n * 2. Raw QR image for digital use\r\n */\r\n\r\ninterface BusinessQRInfo {\r\n  businessName: string;\r\n  ownerName: string;\r\n  address: string;\r\n  slug: string;\r\n  qrValue: string;\r\n  themeColor?: string;\r\n}\r\n\r\n/**\r\n * Downloads just the raw QR code image without any additional formatting\r\n * Creates a high-quality, large PNG image suitable for various uses\r\n */\r\nexport async function downloadRawQRImage(\r\n  svgElement: SVGSVGElement,\r\n  slug: string\r\n): Promise<void> {\r\n  const svgData = new XMLSerializer().serializeToString(svgElement);\r\n\r\n  return new Promise((resolve, reject) => {\r\n    const img = new Image();\r\n    img.onload = () => {\r\n      // Create a canvas with padding around the QR code\r\n      // Increase size for higher quality output\r\n      const padding = 50; // Increased padding for better appearance\r\n      const canvas = document.createElement(\"canvas\");\r\n\r\n      // Set a larger fixed size for better quality (1000x1000 pixels)\r\n      const canvasSize = 1000;\r\n      canvas.width = canvasSize;\r\n      canvas.height = canvasSize;\r\n\r\n      const ctx = canvas.getContext(\"2d\");\r\n      if (!ctx) {\r\n        reject(new Error(\"Could not get canvas context\"));\r\n        return;\r\n      }\r\n\r\n      // Fill with white background\r\n      ctx.fillStyle = \"#FFFFFF\";\r\n      ctx.fillRect(0, 0, canvasSize, canvasSize);\r\n\r\n      // Enable high-quality image rendering\r\n      ctx.imageSmoothingEnabled = true;\r\n      ctx.imageSmoothingQuality = \"high\";\r\n\r\n      // Calculate QR code size (canvas size minus padding on all sides)\r\n      const qrSize = canvasSize - (padding * 2);\r\n\r\n      // Draw QR code centered with higher quality\r\n      ctx.drawImage(\r\n        img,\r\n        padding,\r\n        padding,\r\n        qrSize,\r\n        qrSize\r\n      );\r\n\r\n      // Add a subtle border around the QR code for better definition\r\n      ctx.strokeStyle = \"#EEEEEE\";\r\n      ctx.lineWidth = 2;\r\n      ctx.strokeRect(padding - 2, padding - 2, qrSize + 4, qrSize + 4);\r\n\r\n      // Convert to PNG with maximum quality and trigger download\r\n      const pngFile = canvas.toDataURL(\"image/png\", 1.0);\r\n      const link = document.createElement(\"a\");\r\n      link.download = `${slug}-qr-code.png`;\r\n      link.href = pngFile;\r\n      link.click();\r\n\r\n      resolve();\r\n    };\r\n\r\n    img.onerror = () => {\r\n      reject(new Error(\"Could not load QR code SVG\"));\r\n    };\r\n\r\n    img.src = `data:image/svg+xml;base64,${btoa(svgData)}`;\r\n  });\r\n}\r\n\r\nexport async function generateAndDownloadQRCode(\r\n  svgElement: SVGSVGElement,\r\n  businessInfo: BusinessQRInfo\r\n): Promise<void> {\r\n  const {\r\n    businessName,\r\n    ownerName,\r\n    address,\r\n    slug,\r\n    themeColor = \"#F59E0B\",\r\n  } = businessInfo;\r\n\r\n  // A4 dimensions in pixels at 300 DPI\r\n  // A4 is 210mm × 297mm, which is approximately 2480 × 3508 pixels at 300 DPI\r\n  const width = 2480;\r\n  const height = 3508;\r\n\r\n  // Create canvas\r\n  const canvas = document.createElement(\"canvas\");\r\n  canvas.width = width;\r\n  canvas.height = height;\r\n\r\n  const ctx = canvas.getContext(\"2d\");\r\n  if (!ctx) {\r\n    throw new Error(\"Could not get canvas context\");\r\n  }\r\n\r\n  // Fill background with white\r\n  ctx.fillStyle = \"#FFFFFF\";\r\n  ctx.fillRect(0, 0, width, height);\r\n\r\n  // Create a subtle gradient background\r\n  const gradient = ctx.createLinearGradient(0, 0, 0, height);\r\n  gradient.addColorStop(0, \"#FFFFFF\");\r\n  gradient.addColorStop(1, \"#F8F8F8\");\r\n  ctx.fillStyle = gradient;\r\n  ctx.fillRect(0, 0, width, height);\r\n\r\n  // Add a subtle pattern overlay for texture\r\n  createSubtlePattern(ctx, width, height);\r\n\r\n  // Draw a more sophisticated frame with enhanced visual design\r\n  drawSophisticatedFrame(ctx, width, height, themeColor);\r\n\r\n  // Add decorative elements\r\n  drawDecorativeElements(ctx, width, height, themeColor);\r\n\r\n  // Add header with business name - handle long business names\r\n  ctx.fillStyle = \"#333333\";\r\n  ctx.textAlign = \"center\";\r\n\r\n  // Start with large font size and reduce if needed\r\n  let businessNameFontSize = 160;\r\n  ctx.font = `bold ${businessNameFontSize}px 'Arial'`;\r\n\r\n  // Check if business name is too long and reduce font size if needed\r\n  let businessNameWidth = ctx.measureText(businessName).width;\r\n  while (businessNameWidth > width * 0.75 && businessNameFontSize > 80) {\r\n    businessNameFontSize -= 10;\r\n    ctx.font = `bold ${businessNameFontSize}px 'Arial'`;\r\n    businessNameWidth = ctx.measureText(businessName).width;\r\n  }\r\n\r\n  // If still too long, split into multiple lines\r\n  if (businessNameWidth > width * 0.75) {\r\n    const businessNameLines = splitTextIntoLines(businessName, 30);\r\n    let yPos = height * 0.12;\r\n    businessNameLines.forEach((line) => {\r\n      ctx.fillText(line, width / 2, yPos, width * 0.8);\r\n      yPos += businessNameFontSize * 0.8; // Add spacing between lines\r\n    });\r\n  } else {\r\n    ctx.fillText(businessName, width / 2, height * 0.15, width * 0.8);\r\n  }\r\n\r\n  // Add a subtle underline below the business name\r\n  const textWidth = ctx.measureText(businessName).width;\r\n  const underlineWidth = Math.min(textWidth, width * 0.6);\r\n  ctx.beginPath();\r\n  ctx.moveTo(width / 2 - underlineWidth / 2, height * 0.17);\r\n  ctx.lineTo(width / 2 + underlineWidth / 2, height * 0.17);\r\n  ctx.strokeStyle = themeColor;\r\n  ctx.lineWidth = 6;\r\n  ctx.stroke();\r\n\r\n  // Add \"Scan to view our digital card\" text with better styling\r\n  ctx.font = \"80px 'Arial'\";\r\n  ctx.fillStyle = \"#555555\";\r\n  ctx.fillText(\r\n    \"Scan to view our digital card\",\r\n    width / 2,\r\n    height * 0.22,\r\n    width * 0.8\r\n  );\r\n\r\n  // QR code size and positioning - adjust based on business name length\r\n  // Use smaller QR code if business name is very long (multiple lines)\r\n  const businessNameLines = splitTextIntoLines(businessName, 30);\r\n  const qrSizeMultiplier = businessNameLines.length > 1 ? 0.3 : 0.35;\r\n  const qrSize = Math.min(width, height) * qrSizeMultiplier;\r\n  const qrX = (width - qrSize) / 2;\r\n  const qrY = businessNameLines.length > 1 ? height * 0.35 : height * 0.3;\r\n\r\n  // Draw an elegant container for the QR code\r\n  drawQRCodeContainer(ctx, qrX, qrY, qrSize, themeColor);\r\n\r\n  // Draw QR code\r\n  const svgData = new XMLSerializer().serializeToString(svgElement);\r\n\r\n  return new Promise((resolve, reject) => {\r\n    const img = new Image();\r\n    img.onload = () => {\r\n      // Draw QR code centered\r\n      ctx.drawImage(img, qrX, qrY, qrSize, qrSize);\r\n\r\n      // Add URL text below QR code with better styling\r\n      // Handle potentially long slugs by reducing font size if needed\r\n      const urlText = `dukancard.in/${slug}`;\r\n      let urlFontSize = 70;\r\n      ctx.font = `bold ${urlFontSize}px 'Arial'`;\r\n\r\n      // Check if URL is too long and reduce font size if needed\r\n      let urlWidth = ctx.measureText(urlText).width;\r\n      while (urlWidth > width * 0.7 && urlFontSize > 40) {\r\n        urlFontSize -= 5;\r\n        ctx.font = `bold ${urlFontSize}px 'Arial'`;\r\n        urlWidth = ctx.measureText(urlText).width;\r\n      }\r\n\r\n      // Position URL with sufficient distance from QR code\r\n      ctx.fillStyle = \"#333333\";\r\n      ctx.fillText(\r\n        urlText,\r\n        width / 2,\r\n        qrY + qrSize + 180, // Increased distance from QR code\r\n        width * 0.8\r\n      );\r\n\r\n      // Add a divider line - position based on URL position\r\n      const dividerY = qrY + qrSize + 240; // Position after URL\r\n      drawDivider(ctx, width, dividerY, width * 0.7, themeColor);\r\n\r\n      // Add owner name with better styling - handle long names\r\n      let ownerNameFontSize = 100;\r\n      ctx.font = `bold ${ownerNameFontSize}px 'Arial'`;\r\n      ctx.fillStyle = \"#333333\";\r\n\r\n      // Check if owner name is too long and reduce font size if needed\r\n      let ownerNameWidth = ctx.measureText(ownerName).width;\r\n      while (ownerNameWidth > width * 0.75 && ownerNameFontSize > 60) {\r\n        ownerNameFontSize -= 5;\r\n        ctx.font = `bold ${ownerNameFontSize}px 'Arial'`;\r\n        ownerNameWidth = ctx.measureText(ownerName).width;\r\n      }\r\n\r\n      // If still too long, split into multiple lines\r\n      if (ownerNameWidth > width * 0.75) {\r\n        const ownerNameLines = splitTextIntoLines(ownerName, 25);\r\n        let yPos = height * 0.75;\r\n        ownerNameLines.forEach((line) => {\r\n          ctx.fillText(line, width / 2, yPos, width * 0.8);\r\n          yPos += ownerNameFontSize * 0.7; // Add spacing between lines\r\n        });\r\n      } else {\r\n        ctx.fillText(ownerName, width / 2, height * 0.75, width * 0.8);\r\n      }\r\n\r\n      // We're removing the location icon as it's causing visual issues\r\n      // No need to draw the location icon anymore\r\n\r\n      // Add address with better styling\r\n      let addressFontSize = 70;\r\n      ctx.font = `${addressFontSize}px 'Arial'`;\r\n      ctx.fillStyle = \"#555555\";\r\n\r\n      // Calculate starting position based on owner name position and length\r\n      let yPosition: number;\r\n      if (ownerNameWidth > width * 0.75) {\r\n        // If owner name was split into multiple lines, position address accordingly\r\n        const ownerNameLines = splitTextIntoLines(ownerName, 25);\r\n        yPosition =\r\n          height * 0.75 + ownerNameLines.length * ownerNameFontSize * 0.7 + 50;\r\n      } else {\r\n        yPosition = height * 0.8;\r\n      }\r\n\r\n      // Split address into multiple lines\r\n      const addressLines = splitTextIntoLines(address, 50);\r\n\r\n      // If address is very long, reduce font size\r\n      if (addressLines.length > 3) {\r\n        addressFontSize = 60;\r\n        ctx.font = `${addressFontSize}px 'Arial'`;\r\n      }\r\n\r\n      // Draw each line of the address\r\n      addressLines.forEach((line) => {\r\n        ctx.fillText(line, width / 2, yPosition, width * 0.8);\r\n        yPosition += addressFontSize + 20; // Line height with spacing\r\n      });\r\n\r\n      // Add a footer with powered by text - position dynamically based on content\r\n      ctx.font = \"50px 'Arial'\";\r\n      ctx.fillStyle = \"#888888\";\r\n\r\n      // Calculate footer position based on address length\r\n      const footerY = Math.min(height - 100, yPosition + 150);\r\n\r\n      ctx.fillText(\"Powered by Dukancard\", width / 2, footerY, width * 0.8);\r\n\r\n      // Convert to JPG and trigger download\r\n      const jpgFile = canvas.toDataURL(\"image/jpeg\", 0.95);\r\n      const link = document.createElement(\"a\");\r\n      link.download = `${slug}-qrcode.jpg`;\r\n      link.href = jpgFile;\r\n      link.click();\r\n\r\n      resolve();\r\n    };\r\n\r\n    img.onerror = () => {\r\n      reject(new Error(\"Could not load QR code SVG\"));\r\n    };\r\n\r\n    img.src = `data:image/svg+xml;base64,${btoa(svgData)}`;\r\n  });\r\n}\r\n\r\n/**\r\n * Creates a subtle pattern overlay for texture\r\n */\r\nfunction createSubtlePattern(\r\n  ctx: CanvasRenderingContext2D,\r\n  width: number,\r\n  height: number\r\n) {\r\n  ctx.save();\r\n  ctx.globalAlpha = 0.03;\r\n\r\n  // Create a pattern of small dots\r\n  const patternSize = 20;\r\n  for (let x = 0; x < width; x += patternSize) {\r\n    for (let y = 0; y < height; y += patternSize) {\r\n      ctx.beginPath();\r\n      ctx.arc(x, y, 1, 0, Math.PI * 2);\r\n      ctx.fillStyle = \"#000000\";\r\n      ctx.fill();\r\n    }\r\n  }\r\n\r\n  ctx.restore();\r\n}\r\n\r\n/**\r\n * Draws a sophisticated frame with enhanced visual design\r\n */\r\nfunction drawSophisticatedFrame(\r\n  ctx: CanvasRenderingContext2D,\r\n  width: number,\r\n  height: number,\r\n  themeColor: string\r\n) {\r\n  // Create a more elegant border with gradient\r\n  const borderGradient = ctx.createLinearGradient(0, 0, width, height);\r\n  borderGradient.addColorStop(0, themeColor);\r\n  borderGradient.addColorStop(0.5, adjustColor(themeColor, 20));\r\n  borderGradient.addColorStop(1, themeColor);\r\n\r\n  // Draw outer border with gradient\r\n  ctx.strokeStyle = borderGradient;\r\n  ctx.lineWidth = 3;\r\n  ctx.strokeRect(40, 40, width - 80, height - 80);\r\n\r\n  // Draw inner border with gradient and shadow\r\n  ctx.shadowColor = \"rgba(0, 0, 0, 0.1)\";\r\n  ctx.shadowBlur = 15;\r\n  ctx.shadowOffsetX = 0;\r\n  ctx.shadowOffsetY = 0;\r\n  ctx.strokeStyle = borderGradient;\r\n  ctx.lineWidth = 8;\r\n  ctx.strokeRect(80, 80, width - 160, height - 160);\r\n\r\n  // Reset shadow\r\n  ctx.shadowColor = \"transparent\";\r\n  ctx.shadowBlur = 0;\r\n  ctx.shadowOffsetX = 0;\r\n  ctx.shadowOffsetY = 0;\r\n\r\n  // Add corner decorations with enhanced design\r\n  const cornerSize = 120;\r\n\r\n  // Top-left corner\r\n  drawCornerDecoration(ctx, 80, 80, cornerSize, themeColor, \"top-left\");\r\n  // Top-right corner\r\n  drawCornerDecoration(\r\n    ctx,\r\n    width - 80,\r\n    80,\r\n    cornerSize,\r\n    themeColor,\r\n    \"top-right\"\r\n  );\r\n  // Bottom-left corner\r\n  drawCornerDecoration(\r\n    ctx,\r\n    80,\r\n    height - 80,\r\n    cornerSize,\r\n    themeColor,\r\n    \"bottom-left\"\r\n  );\r\n  // Bottom-right corner\r\n  drawCornerDecoration(\r\n    ctx,\r\n    width - 80,\r\n    height - 80,\r\n    cornerSize,\r\n    themeColor,\r\n    \"bottom-right\"\r\n  );\r\n\r\n  // Add subtle decorative patterns along the borders\r\n  drawBorderPatterns(ctx, width, height, themeColor);\r\n}\r\n\r\n/**\r\n * Draws enhanced corner decorations\r\n */\r\nfunction drawCornerDecoration(\r\n  ctx: CanvasRenderingContext2D,\r\n  x: number,\r\n  y: number,\r\n  size: number,\r\n  color: string,\r\n  position: \"top-left\" | \"top-right\" | \"bottom-left\" | \"bottom-right\"\r\n) {\r\n  ctx.save();\r\n\r\n  // Create gradient for more elegant corners\r\n  const cornerGradient = ctx.createLinearGradient(\r\n    position.includes(\"left\") ? x : x - size,\r\n    position.includes(\"top\") ? y : y - size,\r\n    position.includes(\"left\") ? x + size : x,\r\n    position.includes(\"top\") ? y + size : y\r\n  );\r\n\r\n  cornerGradient.addColorStop(0, color);\r\n  cornerGradient.addColorStop(1, adjustColor(color, 20));\r\n\r\n  ctx.strokeStyle = cornerGradient;\r\n  ctx.lineWidth = 8;\r\n  ctx.lineCap = \"round\";\r\n  ctx.beginPath();\r\n\r\n  if (position === \"top-left\") {\r\n    ctx.moveTo(x, y + size);\r\n    ctx.lineTo(x, y);\r\n    ctx.lineTo(x + size, y);\r\n\r\n    // Add decorative dot\r\n    ctx.moveTo(x + 30, y + 30);\r\n    ctx.arc(x + 30, y + 30, 8, 0, Math.PI * 2);\r\n  } else if (position === \"top-right\") {\r\n    ctx.moveTo(x - size, y);\r\n    ctx.lineTo(x, y);\r\n    ctx.lineTo(x, y + size);\r\n\r\n    // Add decorative dot\r\n    ctx.moveTo(x - 30, y + 30);\r\n    ctx.arc(x - 30, y + 30, 8, 0, Math.PI * 2);\r\n  } else if (position === \"bottom-left\") {\r\n    ctx.moveTo(x, y - size);\r\n    ctx.lineTo(x, y);\r\n    ctx.lineTo(x + size, y);\r\n\r\n    // Add decorative dot\r\n    ctx.moveTo(x + 30, y - 30);\r\n    ctx.arc(x + 30, y - 30, 8, 0, Math.PI * 2);\r\n  } else if (position === \"bottom-right\") {\r\n    ctx.moveTo(x - size, y);\r\n    ctx.lineTo(x, y);\r\n    ctx.lineTo(x, y - size);\r\n\r\n    // Add decorative dot\r\n    ctx.moveTo(x - 30, y - 30);\r\n    ctx.arc(x - 30, y - 30, 8, 0, Math.PI * 2);\r\n  }\r\n\r\n  ctx.stroke();\r\n\r\n  // Fill the decorative dots\r\n  ctx.fillStyle = color;\r\n  if (position === \"top-left\") {\r\n    ctx.beginPath();\r\n    ctx.arc(x + 30, y + 30, 8, 0, Math.PI * 2);\r\n    ctx.fill();\r\n  } else if (position === \"top-right\") {\r\n    ctx.beginPath();\r\n    ctx.arc(x - 30, y + 30, 8, 0, Math.PI * 2);\r\n    ctx.fill();\r\n  } else if (position === \"bottom-left\") {\r\n    ctx.beginPath();\r\n    ctx.arc(x + 30, y - 30, 8, 0, Math.PI * 2);\r\n    ctx.fill();\r\n  } else if (position === \"bottom-right\") {\r\n    ctx.beginPath();\r\n    ctx.arc(x - 30, y - 30, 8, 0, Math.PI * 2);\r\n    ctx.fill();\r\n  }\r\n\r\n  ctx.restore();\r\n}\r\n\r\n/**\r\n * Draws enhanced decorative elements\r\n */\r\nfunction drawDecorativeElements(\r\n  ctx: CanvasRenderingContext2D,\r\n  width: number,\r\n  height: number,\r\n  themeColor: string\r\n) {\r\n  // Add subtle decorative elements with enhanced design\r\n  ctx.save();\r\n\r\n  // Create gradient for decorative elements\r\n  const gradientTopLeft = ctx.createRadialGradient(0, 0, 0, 0, 0, 400);\r\n  gradientTopLeft.addColorStop(0, themeColor);\r\n  gradientTopLeft.addColorStop(1, \"rgba(255, 255, 255, 0)\");\r\n\r\n  const gradientBottomRight = ctx.createRadialGradient(\r\n    width,\r\n    height,\r\n    0,\r\n    width,\r\n    height,\r\n    400\r\n  );\r\n  gradientBottomRight.addColorStop(0, themeColor);\r\n  gradientBottomRight.addColorStop(1, \"rgba(255, 255, 255, 0)\");\r\n\r\n  // Adjust opacity for subtlety\r\n  ctx.globalAlpha = 0.08;\r\n\r\n  // Draw decorative gradient in top-left\r\n  ctx.beginPath();\r\n  ctx.arc(0, 0, 400, 0, Math.PI * 2);\r\n  ctx.fillStyle = gradientTopLeft;\r\n  ctx.fill();\r\n\r\n  // Draw decorative gradient in bottom-right\r\n  ctx.beginPath();\r\n  ctx.arc(width, height, 400, 0, Math.PI * 2);\r\n  ctx.fillStyle = gradientBottomRight;\r\n  ctx.fill();\r\n\r\n  // Add decorative patterns\r\n  ctx.globalAlpha = 0.05;\r\n\r\n  // Draw decorative pattern in center\r\n  const patternSize = 60;\r\n  const patternRows = Math.ceil(height / patternSize);\r\n  const patternCols = Math.ceil(width / patternSize);\r\n\r\n  for (let row = 0; row < patternRows; row++) {\r\n    for (let col = 0; col < patternCols; col++) {\r\n      // Only draw pattern in a diamond shape in the center\r\n      const distanceFromCenter =\r\n        Math.abs(row - patternRows / 2) + Math.abs(col - patternCols / 2);\r\n      if (distanceFromCenter < patternRows / 3) {\r\n        const x = col * patternSize;\r\n        const y = row * patternSize;\r\n\r\n        // Draw subtle pattern element\r\n        if ((row + col) % 2 === 0) {\r\n          ctx.beginPath();\r\n          ctx.arc(x + patternSize / 2, y + patternSize / 2, 2, 0, Math.PI * 2);\r\n          ctx.fillStyle = themeColor;\r\n          ctx.fill();\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  ctx.restore();\r\n}\r\n\r\n/**\r\n * Draws an elegant container for the QR code with enhanced visual design\r\n */\r\nfunction drawQRCodeContainer(\r\n  ctx: CanvasRenderingContext2D,\r\n  x: number,\r\n  y: number,\r\n  size: number,\r\n  themeColor: string\r\n) {\r\n  const padding = 100;\r\n  const containerWidth = size + padding * 2;\r\n  const containerHeight = size + padding * 2;\r\n  const containerX = x - padding;\r\n  const containerY = y - padding;\r\n\r\n  // Draw white background with rounded corners\r\n  ctx.fillStyle = \"#FFFFFF\";\r\n  roundRect(ctx, containerX, containerY, containerWidth, containerHeight, 20);\r\n  ctx.fill();\r\n\r\n  // Draw subtle shadow\r\n  ctx.shadowColor = \"rgba(0, 0, 0, 0.15)\";\r\n  ctx.shadowBlur = 40;\r\n  ctx.shadowOffsetX = 0;\r\n  ctx.shadowOffsetY = 15;\r\n  ctx.fillStyle = \"#FFFFFF\";\r\n  roundRect(ctx, containerX, containerY, containerWidth, containerHeight, 20);\r\n  ctx.fill();\r\n\r\n  // Reset shadow\r\n  ctx.shadowColor = \"transparent\";\r\n  ctx.shadowBlur = 0;\r\n  ctx.shadowOffsetX = 0;\r\n  ctx.shadowOffsetY = 0;\r\n\r\n  // Draw elegant border with gradient\r\n  const borderGradient = ctx.createLinearGradient(\r\n    containerX,\r\n    containerY,\r\n    containerX + containerWidth,\r\n    containerY + containerHeight\r\n  );\r\n  borderGradient.addColorStop(0, themeColor);\r\n  borderGradient.addColorStop(0.5, adjustColor(themeColor, 20));\r\n  borderGradient.addColorStop(1, themeColor);\r\n\r\n  ctx.strokeStyle = borderGradient;\r\n  ctx.lineWidth = 3;\r\n  roundRect(ctx, containerX, containerY, containerWidth, containerHeight, 20);\r\n  ctx.stroke();\r\n\r\n  // Add decorative corner elements to the QR container\r\n  const cornerSize = 30;\r\n\r\n  // Top-left corner decoration\r\n  ctx.beginPath();\r\n  ctx.moveTo(containerX, containerY + cornerSize);\r\n  ctx.lineTo(containerX, containerY);\r\n  ctx.lineTo(containerX + cornerSize, containerY);\r\n  ctx.strokeStyle = themeColor;\r\n  ctx.lineWidth = 5;\r\n  ctx.stroke();\r\n\r\n  // Top-right corner decoration\r\n  ctx.beginPath();\r\n  ctx.moveTo(containerX + containerWidth - cornerSize, containerY);\r\n  ctx.lineTo(containerX + containerWidth, containerY);\r\n  ctx.lineTo(containerX + containerWidth, containerY + cornerSize);\r\n  ctx.stroke();\r\n\r\n  // Bottom-left corner decoration\r\n  ctx.beginPath();\r\n  ctx.moveTo(containerX, containerY + containerHeight - cornerSize);\r\n  ctx.lineTo(containerX, containerY + containerHeight);\r\n  ctx.lineTo(containerX + cornerSize, containerY + containerHeight);\r\n  ctx.stroke();\r\n\r\n  // Bottom-right corner decoration\r\n  ctx.beginPath();\r\n  ctx.moveTo(\r\n    containerX + containerWidth - cornerSize,\r\n    containerY + containerHeight\r\n  );\r\n  ctx.lineTo(containerX + containerWidth, containerY + containerHeight);\r\n  ctx.lineTo(\r\n    containerX + containerWidth,\r\n    containerY + containerHeight - cornerSize\r\n  );\r\n  ctx.stroke();\r\n}\r\n\r\n/**\r\n * Draws an enhanced divider line with decorative elements\r\n */\r\nfunction drawDivider(\r\n  ctx: CanvasRenderingContext2D,\r\n  x: number,\r\n  y: number,\r\n  width: number,\r\n  color: string\r\n) {\r\n  ctx.save();\r\n\r\n  // Create gradient for divider\r\n  const dividerGradient = ctx.createLinearGradient(\r\n    x / 2 - width / 2,\r\n    y,\r\n    x / 2 + width / 2,\r\n    y\r\n  );\r\n  dividerGradient.addColorStop(0, \"rgba(255, 255, 255, 0)\");\r\n  dividerGradient.addColorStop(0.1, color);\r\n  dividerGradient.addColorStop(0.5, adjustColor(color, 20));\r\n  dividerGradient.addColorStop(0.9, color);\r\n  dividerGradient.addColorStop(1, \"rgba(255, 255, 255, 0)\");\r\n\r\n  // Draw main line with gradient\r\n  ctx.beginPath();\r\n  ctx.moveTo(x / 2 - width / 2, y);\r\n  ctx.lineTo(x / 2 + width / 2, y);\r\n  ctx.strokeStyle = dividerGradient;\r\n  ctx.lineWidth = 3;\r\n  ctx.stroke();\r\n\r\n  // Draw decorative element in the middle\r\n  ctx.beginPath();\r\n  ctx.arc(x / 2, y, 15, 0, Math.PI * 2);\r\n  ctx.fillStyle = color;\r\n  ctx.fill();\r\n\r\n  // Add outer ring to the decorative element\r\n  ctx.beginPath();\r\n  ctx.arc(x / 2, y, 20, 0, Math.PI * 2);\r\n  ctx.strokeStyle = color;\r\n  ctx.lineWidth = 2;\r\n  ctx.stroke();\r\n\r\n  // Add small decorative elements along the line\r\n  const numDots = 6;\r\n  const dotSpacing = width / 4 / numDots;\r\n\r\n  // Left side dots\r\n  for (let i = 1; i <= numDots; i++) {\r\n    const dotX = x / 2 - width / 8 - i * dotSpacing;\r\n    ctx.beginPath();\r\n    ctx.arc(dotX, y, 3, 0, Math.PI * 2);\r\n    ctx.fillStyle = color;\r\n    ctx.fill();\r\n  }\r\n\r\n  // Right side dots\r\n  for (let i = 1; i <= numDots; i++) {\r\n    const dotX = x / 2 + width / 8 + i * dotSpacing;\r\n    ctx.beginPath();\r\n    ctx.arc(dotX, y, 3, 0, Math.PI * 2);\r\n    ctx.fillStyle = color;\r\n    ctx.fill();\r\n  }\r\n\r\n  ctx.restore();\r\n}\r\n\r\n/**\r\n * Helper function to draw a rectangle with rounded corners\r\n */\r\nfunction roundRect(\r\n  ctx: CanvasRenderingContext2D,\r\n  x: number,\r\n  y: number,\r\n  width: number,\r\n  height: number,\r\n  radius: number\r\n) {\r\n  ctx.beginPath();\r\n  ctx.moveTo(x + radius, y);\r\n  ctx.lineTo(x + width - radius, y);\r\n  ctx.quadraticCurveTo(x + width, y, x + width, y + radius);\r\n  ctx.lineTo(x + width, y + height - radius);\r\n  ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);\r\n  ctx.lineTo(x + radius, y + height);\r\n  ctx.quadraticCurveTo(x, y + height, x, y + height - radius);\r\n  ctx.lineTo(x, y + radius);\r\n  ctx.quadraticCurveTo(x, y, x + radius, y);\r\n  ctx.closePath();\r\n}\r\n\r\n/**\r\n * Helper function to adjust color brightness\r\n */\r\nfunction adjustColor(color: string, amount: number): string {\r\n  // Convert hex to RGB\r\n  let r, g, b;\r\n  if (color.startsWith(\"#\")) {\r\n    r = parseInt(color.slice(1, 3), 16);\r\n    g = parseInt(color.slice(3, 5), 16);\r\n    b = parseInt(color.slice(5, 7), 16);\r\n  } else {\r\n    // Default fallback color\r\n    r = 245;\r\n    g = 158;\r\n    b = 11; // Default to brand gold\r\n  }\r\n\r\n  // Adjust brightness\r\n  r = Math.max(0, Math.min(255, r + amount));\r\n  g = Math.max(0, Math.min(255, g + amount));\r\n  b = Math.max(0, Math.min(255, b + amount));\r\n\r\n  // Convert back to hex\r\n  return `#${r.toString(16).padStart(2, \"0\")}${g\r\n    .toString(16)\r\n    .padStart(2, \"0\")}${b.toString(16).padStart(2, \"0\")}`;\r\n}\r\n\r\n/**\r\n * Draws decorative patterns along the borders\r\n */\r\nfunction drawBorderPatterns(\r\n  ctx: CanvasRenderingContext2D,\r\n  width: number,\r\n  height: number,\r\n  themeColor: string\r\n) {\r\n  ctx.save();\r\n  ctx.globalAlpha = 0.2;\r\n\r\n  // Top border pattern\r\n  for (let x = 120; x < width - 120; x += 40) {\r\n    ctx.beginPath();\r\n    ctx.arc(x, 80, 2, 0, Math.PI * 2);\r\n    ctx.fillStyle = themeColor;\r\n    ctx.fill();\r\n  }\r\n\r\n  // Bottom border pattern\r\n  for (let x = 120; x < width - 120; x += 40) {\r\n    ctx.beginPath();\r\n    ctx.arc(x, height - 80, 2, 0, Math.PI * 2);\r\n    ctx.fillStyle = themeColor;\r\n    ctx.fill();\r\n  }\r\n\r\n  // Left border pattern\r\n  for (let y = 120; y < height - 120; y += 40) {\r\n    ctx.beginPath();\r\n    ctx.arc(80, y, 2, 0, Math.PI * 2);\r\n    ctx.fillStyle = themeColor;\r\n    ctx.fill();\r\n  }\r\n\r\n  // Right border pattern\r\n  for (let y = 120; y < height - 120; y += 40) {\r\n    ctx.beginPath();\r\n    ctx.arc(width - 80, y, 2, 0, Math.PI * 2);\r\n    ctx.fillStyle = themeColor;\r\n    ctx.fill();\r\n  }\r\n\r\n  ctx.restore();\r\n}\r\n\r\n/**\r\n * Helper function to split text into multiple lines\r\n */\r\nfunction splitTextIntoLines(text: string, maxCharsPerLine: number): string[] {\r\n  const words = text.split(\" \");\r\n  const lines: string[] = [];\r\n  let currentLine = \"\";\r\n\r\n  words.forEach((word) => {\r\n    if ((currentLine + word).length <= maxCharsPerLine) {\r\n      currentLine += (currentLine ? \" \" : \"\") + word;\r\n    } else {\r\n      lines.push(currentLine);\r\n      currentLine = word;\r\n    }\r\n  });\r\n\r\n  if (currentLine) {\r\n    lines.push(currentLine);\r\n  }\r\n\r\n  return lines;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAeM,eAAe,mBACpB,UAAyB,EACzB,IAAY;IAEZ,MAAM,UAAU,IAAI,gBAAgB,iBAAiB,CAAC;IAEtD,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,GAAG;YACX,kDAAkD;YAClD,0CAA0C;YAC1C,MAAM,UAAU,IAAI,0CAA0C;YAC9D,MAAM,SAAS,SAAS,aAAa,CAAC;YAEtC,gEAAgE;YAChE,MAAM,aAAa;YACnB,OAAO,KAAK,GAAG;YACf,OAAO,MAAM,GAAG;YAEhB,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,IAAI,CAAC,KAAK;gBACR,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,6BAA6B;YAC7B,IAAI,SAAS,GAAG;YAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,YAAY;YAE/B,sCAAsC;YACtC,IAAI,qBAAqB,GAAG;YAC5B,IAAI,qBAAqB,GAAG;YAE5B,kEAAkE;YAClE,MAAM,SAAS,aAAc,UAAU;YAEvC,4CAA4C;YAC5C,IAAI,SAAS,CACX,KACA,SACA,SACA,QACA;YAGF,+DAA+D;YAC/D,IAAI,WAAW,GAAG;YAClB,IAAI,SAAS,GAAG;YAChB,IAAI,UAAU,CAAC,UAAU,GAAG,UAAU,GAAG,SAAS,GAAG,SAAS;YAE9D,2DAA2D;YAC3D,MAAM,UAAU,OAAO,SAAS,CAAC,aAAa;YAC9C,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,QAAQ,GAAG,GAAG,KAAK,YAAY,CAAC;YACrC,KAAK,IAAI,GAAG;YACZ,KAAK,KAAK;YAEV;QACF;QAEA,IAAI,OAAO,GAAG;YACZ,OAAO,IAAI,MAAM;QACnB;QAEA,IAAI,GAAG,GAAG,CAAC,0BAA0B,EAAE,KAAK,UAAU;IACxD;AACF;AAEO,eAAe,0BACpB,UAAyB,EACzB,YAA4B;IAE5B,MAAM,EACJ,YAAY,EACZ,SAAS,EACT,OAAO,EACP,IAAI,EACJ,aAAa,SAAS,EACvB,GAAG;IAEJ,qCAAqC;IACrC,4EAA4E;IAC5E,MAAM,QAAQ;IACd,MAAM,SAAS;IAEf,gBAAgB;IAChB,MAAM,SAAS,SAAS,aAAa,CAAC;IACtC,OAAO,KAAK,GAAG;IACf,OAAO,MAAM,GAAG;IAEhB,MAAM,MAAM,OAAO,UAAU,CAAC;IAC9B,IAAI,CAAC,KAAK;QACR,MAAM,IAAI,MAAM;IAClB;IAEA,6BAA6B;IAC7B,IAAI,SAAS,GAAG;IAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO;IAE1B,sCAAsC;IACtC,MAAM,WAAW,IAAI,oBAAoB,CAAC,GAAG,GAAG,GAAG;IACnD,SAAS,YAAY,CAAC,GAAG;IACzB,SAAS,YAAY,CAAC,GAAG;IACzB,IAAI,SAAS,GAAG;IAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO;IAE1B,2CAA2C;IAC3C,oBAAoB,KAAK,OAAO;IAEhC,8DAA8D;IAC9D,uBAAuB,KAAK,OAAO,QAAQ;IAE3C,0BAA0B;IAC1B,uBAAuB,KAAK,OAAO,QAAQ;IAE3C,6DAA6D;IAC7D,IAAI,SAAS,GAAG;IAChB,IAAI,SAAS,GAAG;IAEhB,kDAAkD;IAClD,IAAI,uBAAuB;IAC3B,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,qBAAqB,UAAU,CAAC;IAEnD,oEAAoE;IACpE,IAAI,oBAAoB,IAAI,WAAW,CAAC,cAAc,KAAK;IAC3D,MAAO,oBAAoB,QAAQ,QAAQ,uBAAuB,GAAI;QACpE,wBAAwB;QACxB,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,qBAAqB,UAAU,CAAC;QACnD,oBAAoB,IAAI,WAAW,CAAC,cAAc,KAAK;IACzD;IAEA,+CAA+C;IAC/C,IAAI,oBAAoB,QAAQ,MAAM;QACpC,MAAM,oBAAoB,mBAAmB,cAAc;QAC3D,IAAI,OAAO,SAAS;QACpB,kBAAkB,OAAO,CAAC,CAAC;YACzB,IAAI,QAAQ,CAAC,MAAM,QAAQ,GAAG,MAAM,QAAQ;YAC5C,QAAQ,uBAAuB,KAAK,4BAA4B;QAClE;IACF,OAAO;QACL,IAAI,QAAQ,CAAC,cAAc,QAAQ,GAAG,SAAS,MAAM,QAAQ;IAC/D;IAEA,iDAAiD;IACjD,MAAM,YAAY,IAAI,WAAW,CAAC,cAAc,KAAK;IACrD,MAAM,iBAAiB,KAAK,GAAG,CAAC,WAAW,QAAQ;IACnD,IAAI,SAAS;IACb,IAAI,MAAM,CAAC,QAAQ,IAAI,iBAAiB,GAAG,SAAS;IACpD,IAAI,MAAM,CAAC,QAAQ,IAAI,iBAAiB,GAAG,SAAS;IACpD,IAAI,WAAW,GAAG;IAClB,IAAI,SAAS,GAAG;IAChB,IAAI,MAAM;IAEV,+DAA+D;IAC/D,IAAI,IAAI,GAAG;IACX,IAAI,SAAS,GAAG;IAChB,IAAI,QAAQ,CACV,iCACA,QAAQ,GACR,SAAS,MACT,QAAQ;IAGV,sEAAsE;IACtE,qEAAqE;IACrE,MAAM,oBAAoB,mBAAmB,cAAc;IAC3D,MAAM,mBAAmB,kBAAkB,MAAM,GAAG,IAAI,MAAM;IAC9D,MAAM,SAAS,KAAK,GAAG,CAAC,OAAO,UAAU;IACzC,MAAM,MAAM,CAAC,QAAQ,MAAM,IAAI;IAC/B,MAAM,MAAM,kBAAkB,MAAM,GAAG,IAAI,SAAS,OAAO,SAAS;IAEpE,4CAA4C;IAC5C,oBAAoB,KAAK,KAAK,KAAK,QAAQ;IAE3C,eAAe;IACf,MAAM,UAAU,IAAI,gBAAgB,iBAAiB,CAAC;IAEtD,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,GAAG;YACX,wBAAwB;YACxB,IAAI,SAAS,CAAC,KAAK,KAAK,KAAK,QAAQ;YAErC,iDAAiD;YACjD,gEAAgE;YAChE,MAAM,UAAU,CAAC,aAAa,EAAE,MAAM;YACtC,IAAI,cAAc;YAClB,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,YAAY,UAAU,CAAC;YAE1C,0DAA0D;YAC1D,IAAI,WAAW,IAAI,WAAW,CAAC,SAAS,KAAK;YAC7C,MAAO,WAAW,QAAQ,OAAO,cAAc,GAAI;gBACjD,eAAe;gBACf,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,YAAY,UAAU,CAAC;gBAC1C,WAAW,IAAI,WAAW,CAAC,SAAS,KAAK;YAC3C;YAEA,qDAAqD;YACrD,IAAI,SAAS,GAAG;YAChB,IAAI,QAAQ,CACV,SACA,QAAQ,GACR,MAAM,SAAS,KACf,QAAQ;YAGV,sDAAsD;YACtD,MAAM,WAAW,MAAM,SAAS,KAAK,qBAAqB;YAC1D,YAAY,KAAK,OAAO,UAAU,QAAQ,KAAK;YAE/C,yDAAyD;YACzD,IAAI,oBAAoB;YACxB,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,kBAAkB,UAAU,CAAC;YAChD,IAAI,SAAS,GAAG;YAEhB,iEAAiE;YACjE,IAAI,iBAAiB,IAAI,WAAW,CAAC,WAAW,KAAK;YACrD,MAAO,iBAAiB,QAAQ,QAAQ,oBAAoB,GAAI;gBAC9D,qBAAqB;gBACrB,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,kBAAkB,UAAU,CAAC;gBAChD,iBAAiB,IAAI,WAAW,CAAC,WAAW,KAAK;YACnD;YAEA,+CAA+C;YAC/C,IAAI,iBAAiB,QAAQ,MAAM;gBACjC,MAAM,iBAAiB,mBAAmB,WAAW;gBACrD,IAAI,OAAO,SAAS;gBACpB,eAAe,OAAO,CAAC,CAAC;oBACtB,IAAI,QAAQ,CAAC,MAAM,QAAQ,GAAG,MAAM,QAAQ;oBAC5C,QAAQ,oBAAoB,KAAK,4BAA4B;gBAC/D;YACF,OAAO;gBACL,IAAI,QAAQ,CAAC,WAAW,QAAQ,GAAG,SAAS,MAAM,QAAQ;YAC5D;YAEA,iEAAiE;YACjE,4CAA4C;YAE5C,kCAAkC;YAClC,IAAI,kBAAkB;YACtB,IAAI,IAAI,GAAG,GAAG,gBAAgB,UAAU,CAAC;YACzC,IAAI,SAAS,GAAG;YAEhB,sEAAsE;YACtE,IAAI;YACJ,IAAI,iBAAiB,QAAQ,MAAM;gBACjC,4EAA4E;gBAC5E,MAAM,iBAAiB,mBAAmB,WAAW;gBACrD,YACE,SAAS,OAAO,eAAe,MAAM,GAAG,oBAAoB,MAAM;YACtE,OAAO;gBACL,YAAY,SAAS;YACvB;YAEA,oCAAoC;YACpC,MAAM,eAAe,mBAAmB,SAAS;YAEjD,4CAA4C;YAC5C,IAAI,aAAa,MAAM,GAAG,GAAG;gBAC3B,kBAAkB;gBAClB,IAAI,IAAI,GAAG,GAAG,gBAAgB,UAAU,CAAC;YAC3C;YAEA,gCAAgC;YAChC,aAAa,OAAO,CAAC,CAAC;gBACpB,IAAI,QAAQ,CAAC,MAAM,QAAQ,GAAG,WAAW,QAAQ;gBACjD,aAAa,kBAAkB,IAAI,2BAA2B;YAChE;YAEA,4EAA4E;YAC5E,IAAI,IAAI,GAAG;YACX,IAAI,SAAS,GAAG;YAEhB,oDAAoD;YACpD,MAAM,UAAU,KAAK,GAAG,CAAC,SAAS,KAAK,YAAY;YAEnD,IAAI,QAAQ,CAAC,wBAAwB,QAAQ,GAAG,SAAS,QAAQ;YAEjE,sCAAsC;YACtC,MAAM,UAAU,OAAO,SAAS,CAAC,cAAc;YAC/C,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,QAAQ,GAAG,GAAG,KAAK,WAAW,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,KAAK;YAEV;QACF;QAEA,IAAI,OAAO,GAAG;YACZ,OAAO,IAAI,MAAM;QACnB;QAEA,IAAI,GAAG,GAAG,CAAC,0BAA0B,EAAE,KAAK,UAAU;IACxD;AACF;AAEA;;CAEC,GACD,SAAS,oBACP,GAA6B,EAC7B,KAAa,EACb,MAAc;IAEd,IAAI,IAAI;IACR,IAAI,WAAW,GAAG;IAElB,iCAAiC;IACjC,MAAM,cAAc;IACpB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,KAAK,YAAa;QAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,KAAK,YAAa;YAC5C,IAAI,SAAS;YACb,IAAI,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,KAAK,EAAE,GAAG;YAC9B,IAAI,SAAS,GAAG;YAChB,IAAI,IAAI;QACV;IACF;IAEA,IAAI,OAAO;AACb;AAEA;;CAEC,GACD,SAAS,uBACP,GAA6B,EAC7B,KAAa,EACb,MAAc,EACd,UAAkB;IAElB,6CAA6C;IAC7C,MAAM,iBAAiB,IAAI,oBAAoB,CAAC,GAAG,GAAG,OAAO;IAC7D,eAAe,YAAY,CAAC,GAAG;IAC/B,eAAe,YAAY,CAAC,KAAK,YAAY,YAAY;IACzD,eAAe,YAAY,CAAC,GAAG;IAE/B,kCAAkC;IAClC,IAAI,WAAW,GAAG;IAClB,IAAI,SAAS,GAAG;IAChB,IAAI,UAAU,CAAC,IAAI,IAAI,QAAQ,IAAI,SAAS;IAE5C,6CAA6C;IAC7C,IAAI,WAAW,GAAG;IAClB,IAAI,UAAU,GAAG;IACjB,IAAI,aAAa,GAAG;IACpB,IAAI,aAAa,GAAG;IACpB,IAAI,WAAW,GAAG;IAClB,IAAI,SAAS,GAAG;IAChB,IAAI,UAAU,CAAC,IAAI,IAAI,QAAQ,KAAK,SAAS;IAE7C,eAAe;IACf,IAAI,WAAW,GAAG;IAClB,IAAI,UAAU,GAAG;IACjB,IAAI,aAAa,GAAG;IACpB,IAAI,aAAa,GAAG;IAEpB,8CAA8C;IAC9C,MAAM,aAAa;IAEnB,kBAAkB;IAClB,qBAAqB,KAAK,IAAI,IAAI,YAAY,YAAY;IAC1D,mBAAmB;IACnB,qBACE,KACA,QAAQ,IACR,IACA,YACA,YACA;IAEF,qBAAqB;IACrB,qBACE,KACA,IACA,SAAS,IACT,YACA,YACA;IAEF,sBAAsB;IACtB,qBACE,KACA,QAAQ,IACR,SAAS,IACT,YACA,YACA;IAGF,mDAAmD;IACnD,mBAAmB,KAAK,OAAO,QAAQ;AACzC;AAEA;;CAEC,GACD,SAAS,qBACP,GAA6B,EAC7B,CAAS,EACT,CAAS,EACT,IAAY,EACZ,KAAa,EACb,QAAmE;IAEnE,IAAI,IAAI;IAER,2CAA2C;IAC3C,MAAM,iBAAiB,IAAI,oBAAoB,CAC7C,SAAS,QAAQ,CAAC,UAAU,IAAI,IAAI,MACpC,SAAS,QAAQ,CAAC,SAAS,IAAI,IAAI,MACnC,SAAS,QAAQ,CAAC,UAAU,IAAI,OAAO,GACvC,SAAS,QAAQ,CAAC,SAAS,IAAI,OAAO;IAGxC,eAAe,YAAY,CAAC,GAAG;IAC/B,eAAe,YAAY,CAAC,GAAG,YAAY,OAAO;IAElD,IAAI,WAAW,GAAG;IAClB,IAAI,SAAS,GAAG;IAChB,IAAI,OAAO,GAAG;IACd,IAAI,SAAS;IAEb,IAAI,aAAa,YAAY;QAC3B,IAAI,MAAM,CAAC,GAAG,IAAI;QAClB,IAAI,MAAM,CAAC,GAAG;QACd,IAAI,MAAM,CAAC,IAAI,MAAM;QAErB,qBAAqB;QACrB,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI;QACvB,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG;IAC1C,OAAO,IAAI,aAAa,aAAa;QACnC,IAAI,MAAM,CAAC,IAAI,MAAM;QACrB,IAAI,MAAM,CAAC,GAAG;QACd,IAAI,MAAM,CAAC,GAAG,IAAI;QAElB,qBAAqB;QACrB,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI;QACvB,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG;IAC1C,OAAO,IAAI,aAAa,eAAe;QACrC,IAAI,MAAM,CAAC,GAAG,IAAI;QAClB,IAAI,MAAM,CAAC,GAAG;QACd,IAAI,MAAM,CAAC,IAAI,MAAM;QAErB,qBAAqB;QACrB,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI;QACvB,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG;IAC1C,OAAO,IAAI,aAAa,gBAAgB;QACtC,IAAI,MAAM,CAAC,IAAI,MAAM;QACrB,IAAI,MAAM,CAAC,GAAG;QACd,IAAI,MAAM,CAAC,GAAG,IAAI;QAElB,qBAAqB;QACrB,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI;QACvB,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG;IAC1C;IAEA,IAAI,MAAM;IAEV,2BAA2B;IAC3B,IAAI,SAAS,GAAG;IAChB,IAAI,aAAa,YAAY;QAC3B,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG;QACxC,IAAI,IAAI;IACV,OAAO,IAAI,aAAa,aAAa;QACnC,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG;QACxC,IAAI,IAAI;IACV,OAAO,IAAI,aAAa,eAAe;QACrC,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG;QACxC,IAAI,IAAI;IACV,OAAO,IAAI,aAAa,gBAAgB;QACtC,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG;QACxC,IAAI,IAAI;IACV;IAEA,IAAI,OAAO;AACb;AAEA;;CAEC,GACD,SAAS,uBACP,GAA6B,EAC7B,KAAa,EACb,MAAc,EACd,UAAkB;IAElB,sDAAsD;IACtD,IAAI,IAAI;IAER,0CAA0C;IAC1C,MAAM,kBAAkB,IAAI,oBAAoB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG;IAChE,gBAAgB,YAAY,CAAC,GAAG;IAChC,gBAAgB,YAAY,CAAC,GAAG;IAEhC,MAAM,sBAAsB,IAAI,oBAAoB,CAClD,OACA,QACA,GACA,OACA,QACA;IAEF,oBAAoB,YAAY,CAAC,GAAG;IACpC,oBAAoB,YAAY,CAAC,GAAG;IAEpC,8BAA8B;IAC9B,IAAI,WAAW,GAAG;IAElB,uCAAuC;IACvC,IAAI,SAAS;IACb,IAAI,GAAG,CAAC,GAAG,GAAG,KAAK,GAAG,KAAK,EAAE,GAAG;IAChC,IAAI,SAAS,GAAG;IAChB,IAAI,IAAI;IAER,2CAA2C;IAC3C,IAAI,SAAS;IACb,IAAI,GAAG,CAAC,OAAO,QAAQ,KAAK,GAAG,KAAK,EAAE,GAAG;IACzC,IAAI,SAAS,GAAG;IAChB,IAAI,IAAI;IAER,0BAA0B;IAC1B,IAAI,WAAW,GAAG;IAElB,oCAAoC;IACpC,MAAM,cAAc;IACpB,MAAM,cAAc,KAAK,IAAI,CAAC,SAAS;IACvC,MAAM,cAAc,KAAK,IAAI,CAAC,QAAQ;IAEtC,IAAK,IAAI,MAAM,GAAG,MAAM,aAAa,MAAO;QAC1C,IAAK,IAAI,MAAM,GAAG,MAAM,aAAa,MAAO;YAC1C,qDAAqD;YACrD,MAAM,qBACJ,KAAK,GAAG,CAAC,MAAM,cAAc,KAAK,KAAK,GAAG,CAAC,MAAM,cAAc;YACjE,IAAI,qBAAqB,cAAc,GAAG;gBACxC,MAAM,IAAI,MAAM;gBAChB,MAAM,IAAI,MAAM;gBAEhB,8BAA8B;gBAC9B,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,GAAG;oBACzB,IAAI,SAAS;oBACb,IAAI,GAAG,CAAC,IAAI,cAAc,GAAG,IAAI,cAAc,GAAG,GAAG,GAAG,KAAK,EAAE,GAAG;oBAClE,IAAI,SAAS,GAAG;oBAChB,IAAI,IAAI;gBACV;YACF;QACF;IACF;IAEA,IAAI,OAAO;AACb;AAEA;;CAEC,GACD,SAAS,oBACP,GAA6B,EAC7B,CAAS,EACT,CAAS,EACT,IAAY,EACZ,UAAkB;IAElB,MAAM,UAAU;IAChB,MAAM,iBAAiB,OAAO,UAAU;IACxC,MAAM,kBAAkB,OAAO,UAAU;IACzC,MAAM,aAAa,IAAI;IACvB,MAAM,aAAa,IAAI;IAEvB,6CAA6C;IAC7C,IAAI,SAAS,GAAG;IAChB,UAAU,KAAK,YAAY,YAAY,gBAAgB,iBAAiB;IACxE,IAAI,IAAI;IAER,qBAAqB;IACrB,IAAI,WAAW,GAAG;IAClB,IAAI,UAAU,GAAG;IACjB,IAAI,aAAa,GAAG;IACpB,IAAI,aAAa,GAAG;IACpB,IAAI,SAAS,GAAG;IAChB,UAAU,KAAK,YAAY,YAAY,gBAAgB,iBAAiB;IACxE,IAAI,IAAI;IAER,eAAe;IACf,IAAI,WAAW,GAAG;IAClB,IAAI,UAAU,GAAG;IACjB,IAAI,aAAa,GAAG;IACpB,IAAI,aAAa,GAAG;IAEpB,oCAAoC;IACpC,MAAM,iBAAiB,IAAI,oBAAoB,CAC7C,YACA,YACA,aAAa,gBACb,aAAa;IAEf,eAAe,YAAY,CAAC,GAAG;IAC/B,eAAe,YAAY,CAAC,KAAK,YAAY,YAAY;IACzD,eAAe,YAAY,CAAC,GAAG;IAE/B,IAAI,WAAW,GAAG;IAClB,IAAI,SAAS,GAAG;IAChB,UAAU,KAAK,YAAY,YAAY,gBAAgB,iBAAiB;IACxE,IAAI,MAAM;IAEV,qDAAqD;IACrD,MAAM,aAAa;IAEnB,6BAA6B;IAC7B,IAAI,SAAS;IACb,IAAI,MAAM,CAAC,YAAY,aAAa;IACpC,IAAI,MAAM,CAAC,YAAY;IACvB,IAAI,MAAM,CAAC,aAAa,YAAY;IACpC,IAAI,WAAW,GAAG;IAClB,IAAI,SAAS,GAAG;IAChB,IAAI,MAAM;IAEV,8BAA8B;IAC9B,IAAI,SAAS;IACb,IAAI,MAAM,CAAC,aAAa,iBAAiB,YAAY;IACrD,IAAI,MAAM,CAAC,aAAa,gBAAgB;IACxC,IAAI,MAAM,CAAC,aAAa,gBAAgB,aAAa;IACrD,IAAI,MAAM;IAEV,gCAAgC;IAChC,IAAI,SAAS;IACb,IAAI,MAAM,CAAC,YAAY,aAAa,kBAAkB;IACtD,IAAI,MAAM,CAAC,YAAY,aAAa;IACpC,IAAI,MAAM,CAAC,aAAa,YAAY,aAAa;IACjD,IAAI,MAAM;IAEV,iCAAiC;IACjC,IAAI,SAAS;IACb,IAAI,MAAM,CACR,aAAa,iBAAiB,YAC9B,aAAa;IAEf,IAAI,MAAM,CAAC,aAAa,gBAAgB,aAAa;IACrD,IAAI,MAAM,CACR,aAAa,gBACb,aAAa,kBAAkB;IAEjC,IAAI,MAAM;AACZ;AAEA;;CAEC,GACD,SAAS,YACP,GAA6B,EAC7B,CAAS,EACT,CAAS,EACT,KAAa,EACb,KAAa;IAEb,IAAI,IAAI;IAER,8BAA8B;IAC9B,MAAM,kBAAkB,IAAI,oBAAoB,CAC9C,IAAI,IAAI,QAAQ,GAChB,GACA,IAAI,IAAI,QAAQ,GAChB;IAEF,gBAAgB,YAAY,CAAC,GAAG;IAChC,gBAAgB,YAAY,CAAC,KAAK;IAClC,gBAAgB,YAAY,CAAC,KAAK,YAAY,OAAO;IACrD,gBAAgB,YAAY,CAAC,KAAK;IAClC,gBAAgB,YAAY,CAAC,GAAG;IAEhC,+BAA+B;IAC/B,IAAI,SAAS;IACb,IAAI,MAAM,CAAC,IAAI,IAAI,QAAQ,GAAG;IAC9B,IAAI,MAAM,CAAC,IAAI,IAAI,QAAQ,GAAG;IAC9B,IAAI,WAAW,GAAG;IAClB,IAAI,SAAS,GAAG;IAChB,IAAI,MAAM;IAEV,wCAAwC;IACxC,IAAI,SAAS;IACb,IAAI,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,GAAG,KAAK,EAAE,GAAG;IACnC,IAAI,SAAS,GAAG;IAChB,IAAI,IAAI;IAER,2CAA2C;IAC3C,IAAI,SAAS;IACb,IAAI,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,GAAG,KAAK,EAAE,GAAG;IACnC,IAAI,WAAW,GAAG;IAClB,IAAI,SAAS,GAAG;IAChB,IAAI,MAAM;IAEV,+CAA+C;IAC/C,MAAM,UAAU;IAChB,MAAM,aAAa,QAAQ,IAAI;IAE/B,iBAAiB;IACjB,IAAK,IAAI,IAAI,GAAG,KAAK,SAAS,IAAK;QACjC,MAAM,OAAO,IAAI,IAAI,QAAQ,IAAI,IAAI;QACrC,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,GAAG,KAAK,EAAE,GAAG;QACjC,IAAI,SAAS,GAAG;QAChB,IAAI,IAAI;IACV;IAEA,kBAAkB;IAClB,IAAK,IAAI,IAAI,GAAG,KAAK,SAAS,IAAK;QACjC,MAAM,OAAO,IAAI,IAAI,QAAQ,IAAI,IAAI;QACrC,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,GAAG,KAAK,EAAE,GAAG;QACjC,IAAI,SAAS,GAAG;QAChB,IAAI,IAAI;IACV;IAEA,IAAI,OAAO;AACb;AAEA;;CAEC,GACD,SAAS,UACP,GAA6B,EAC7B,CAAS,EACT,CAAS,EACT,KAAa,EACb,MAAc,EACd,MAAc;IAEd,IAAI,SAAS;IACb,IAAI,MAAM,CAAC,IAAI,QAAQ;IACvB,IAAI,MAAM,CAAC,IAAI,QAAQ,QAAQ;IAC/B,IAAI,gBAAgB,CAAC,IAAI,OAAO,GAAG,IAAI,OAAO,IAAI;IAClD,IAAI,MAAM,CAAC,IAAI,OAAO,IAAI,SAAS;IACnC,IAAI,gBAAgB,CAAC,IAAI,OAAO,IAAI,QAAQ,IAAI,QAAQ,QAAQ,IAAI;IACpE,IAAI,MAAM,CAAC,IAAI,QAAQ,IAAI;IAC3B,IAAI,gBAAgB,CAAC,GAAG,IAAI,QAAQ,GAAG,IAAI,SAAS;IACpD,IAAI,MAAM,CAAC,GAAG,IAAI;IAClB,IAAI,gBAAgB,CAAC,GAAG,GAAG,IAAI,QAAQ;IACvC,IAAI,SAAS;AACf;AAEA;;CAEC,GACD,SAAS,YAAY,KAAa,EAAE,MAAc;IAChD,qBAAqB;IACrB,IAAI,GAAG,GAAG;IACV,IAAI,MAAM,UAAU,CAAC,MAAM;QACzB,IAAI,SAAS,MAAM,KAAK,CAAC,GAAG,IAAI;QAChC,IAAI,SAAS,MAAM,KAAK,CAAC,GAAG,IAAI;QAChC,IAAI,SAAS,MAAM,KAAK,CAAC,GAAG,IAAI;IAClC,OAAO;QACL,yBAAyB;QACzB,IAAI;QACJ,IAAI;QACJ,IAAI,IAAI,wBAAwB;IAClC;IAEA,oBAAoB;IACpB,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,IAAI;IAClC,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,IAAI;IAClC,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,IAAI;IAElC,sBAAsB;IACtB,OAAO,CAAC,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,OAAO,EAC1C,QAAQ,CAAC,IACT,QAAQ,CAAC,GAAG,OAAO,EAAE,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,MAAM;AACzD;AAEA;;CAEC,GACD,SAAS,mBACP,GAA6B,EAC7B,KAAa,EACb,MAAc,EACd,UAAkB;IAElB,IAAI,IAAI;IACR,IAAI,WAAW,GAAG;IAElB,qBAAqB;IACrB,IAAK,IAAI,IAAI,KAAK,IAAI,QAAQ,KAAK,KAAK,GAAI;QAC1C,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG;QAC/B,IAAI,SAAS,GAAG;QAChB,IAAI,IAAI;IACV;IAEA,wBAAwB;IACxB,IAAK,IAAI,IAAI,KAAK,IAAI,QAAQ,KAAK,KAAK,GAAI;QAC1C,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,GAAG,SAAS,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG;QACxC,IAAI,SAAS,GAAG;QAChB,IAAI,IAAI;IACV;IAEA,sBAAsB;IACtB,IAAK,IAAI,IAAI,KAAK,IAAI,SAAS,KAAK,KAAK,GAAI;QAC3C,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK,EAAE,GAAG;QAC/B,IAAI,SAAS,GAAG;QAChB,IAAI,IAAI;IACV;IAEA,uBAAuB;IACvB,IAAK,IAAI,IAAI,KAAK,IAAI,SAAS,KAAK,KAAK,GAAI;QAC3C,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,QAAQ,IAAI,GAAG,GAAG,GAAG,KAAK,EAAE,GAAG;QACvC,IAAI,SAAS,GAAG;QAChB,IAAI,IAAI;IACV;IAEA,IAAI,OAAO;AACb;AAEA;;CAEC,GACD,SAAS,mBAAmB,IAAY,EAAE,eAAuB;IAC/D,MAAM,QAAQ,KAAK,KAAK,CAAC;IACzB,MAAM,QAAkB,EAAE;IAC1B,IAAI,cAAc;IAElB,MAAM,OAAO,CAAC,CAAC;QACb,IAAI,CAAC,cAAc,IAAI,EAAE,MAAM,IAAI,iBAAiB;YAClD,eAAe,CAAC,cAAc,MAAM,EAAE,IAAI;QAC5C,OAAO;YACL,MAAM,IAAI,CAAC;YACX,cAAc;QAChB;IACF;IAEA,IAAI,aAAa;QACf,MAAM,IAAI,CAAC;IACb;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 904, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/cardDownloader.ts"], "sourcesContent": ["/**\r\n * Utility functions to download business cards as PNG images\r\n * Uses modern-screenshot for HTML to image conversion with proper rounded corners\r\n */\r\n\r\nimport { domToPng } from 'modern-screenshot';\r\n\r\ninterface BusinessCardDownloadOptions {\r\n  businessName: string;\r\n  businessSlug: string;\r\n  quality?: number;\r\n  scale?: number;\r\n  preserveRoundedCorners?: boolean;\r\n}\r\n\r\n/**\r\n * Downloads the business card as a PNG image with proper rounded corners\r\n */\r\nexport async function downloadBusinessCardAsPNG(\r\n  cardElement: HTMLElement,\r\n  options: BusinessCardDownloadOptions\r\n): Promise<void> {\r\n  const { businessSlug, quality = 1, scale = 3, preserveRoundedCorners = true } = options;\r\n\r\n  try {\r\n    // Get the actual dimensions of the card element\r\n    const rect = cardElement.getBoundingClientRect();\r\n    console.log('Card element dimensions:', {\r\n      width: rect.width,\r\n      height: rect.height,\r\n      offsetWidth: cardElement.offsetWidth,\r\n      offsetHeight: cardElement.offsetHeight,\r\n      className: cardElement.className,\r\n      tagName: cardElement.tagName\r\n    });\r\n\r\n    // Ensure we have valid dimensions\r\n    if (rect.width === 0 || rect.height === 0) {\r\n      throw new Error('Card element has invalid dimensions');\r\n    }\r\n\r\n    // Generate high-quality PNG using modern-screenshot with proper settings for rounded corners\r\n    const dataUrl = await domToPng(cardElement, {\r\n      quality,\r\n      scale,\r\n      backgroundColor: 'transparent', // Use transparent background to preserve rounded corners\r\n      style: {\r\n        // Ensure consistent rendering and preserve rounded corners\r\n        transform: 'scale(1)',\r\n        transformOrigin: 'top left',\r\n        borderRadius: preserveRoundedCorners ? 'inherit' : '0',\r\n        // Ensure the element is properly sized\r\n        width: `${rect.width}px`,\r\n        height: `${rect.height}px`,\r\n        maxWidth: `${rect.width}px`,\r\n        maxHeight: `${rect.height}px`,\r\n        overflow: 'hidden',\r\n      },\r\n      // Use the actual rendered dimensions\r\n      width: rect.width,\r\n      height: rect.height,\r\n    });\r\n\r\n    // Create download link\r\n    const link = document.createElement('a');\r\n    link.download = `${businessSlug}-digital-card.png`;\r\n    link.href = dataUrl;\r\n    link.click();\r\n  } catch (error) {\r\n    console.error('Error downloading business card as PNG:', error);\r\n    throw new Error('Failed to download business card as PNG');\r\n  }\r\n}\r\n\r\n/**\r\n * Main function to download business card as PNG\r\n */\r\nexport async function downloadBusinessCard(\r\n  cardElement: HTMLElement,\r\n  options: BusinessCardDownloadOptions\r\n): Promise<void> {\r\n  return downloadBusinessCardAsPNG(cardElement, options);\r\n}\r\n\r\n/**\r\n * Helper function to find the business card element in the DOM\r\n * Prioritizes cards that are likely to have correct authentication context\r\n */\r\nexport function findBusinessCardElement(containerRef?: React.RefObject<HTMLElement>): HTMLElement | null {\r\n  // Try to find the card element using various selectors, prioritizing the most specific\r\n  const selectors = [\r\n    '[data-card-element]', // The actual business card component\r\n    '.business-card-preview',\r\n    '.business-card',\r\n    '#business-card',\r\n    '.card-preview',\r\n  ];\r\n\r\n  const container = containerRef?.current || document;\r\n  const candidates: HTMLElement[] = [];\r\n\r\n  // Collect all potential card elements\r\n  for (const selector of selectors) {\r\n    const elements = container.querySelectorAll(selector) as NodeListOf<HTMLElement>;\r\n    elements.forEach(element => {\r\n      const rect = element.getBoundingClientRect();\r\n      // Business card should have reasonable dimensions (not too large)\r\n      // Max width should be around 384px (max-w-sm) and height should be proportional\r\n      if (rect.width > 0 && rect.height > 0 && rect.width <= 500) {\r\n        candidates.push(element);\r\n      }\r\n    });\r\n  }\r\n\r\n  if (candidates.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  if (candidates.length === 1) {\r\n    return candidates[0];\r\n  }\r\n\r\n  // If multiple candidates, prioritize based on context\r\n  // 1. Prefer cards that are not in demo mode (check for unmasked data)\r\n  // 2. Prefer cards that are visible and not hidden\r\n  // 3. Prefer cards that are in the main content area (not in headers/footers)\r\n\r\n  const scoredCandidates = candidates.map(element => {\r\n    let score = 0;\r\n\r\n    // Check if the card has unmasked phone/email (indicates authenticated context)\r\n    const phoneElement = element.querySelector('a[href^=\"tel:\"]');\r\n    const emailElement = element.querySelector('a[href^=\"mailto:\"]');\r\n    const phoneText = phoneElement?.textContent || '';\r\n    const emailText = emailElement?.textContent || '';\r\n\r\n    // If phone/email don't contain asterisks, it's likely unmasked (authenticated)\r\n    if (phoneText && !phoneText.includes('*')) score += 10;\r\n    if (emailText && !emailText.includes('*')) score += 10;\r\n\r\n    // Prefer visible elements\r\n    const rect = element.getBoundingClientRect();\r\n    if (rect.top >= 0 && rect.left >= 0) score += 5;\r\n\r\n    // Prefer elements in main content areas (not in navigation or footer)\r\n    const isInNav = element.closest('nav, header, footer');\r\n    if (!isInNav) score += 5;\r\n\r\n    // Prefer larger cards (main content vs thumbnails)\r\n    if (rect.width > 300) score += 3;\r\n\r\n    return { element, score };\r\n  });\r\n\r\n  // Sort by score (highest first) and return the best candidate\r\n  scoredCandidates.sort((a, b) => b.score - a.score);\r\n  return scoredCandidates[0].element;\r\n}\r\n\r\n/**\r\n * Prepare card element for screenshot by temporarily adjusting styles\r\n */\r\nexport function prepareCardForScreenshot(cardElement: HTMLElement): () => void {\r\n  const originalStyles = {\r\n    transform: cardElement.style.transform,\r\n    transformOrigin: cardElement.style.transformOrigin,\r\n    position: cardElement.style.position,\r\n    zIndex: cardElement.style.zIndex,\r\n  };\r\n\r\n  // Apply screenshot-friendly styles\r\n  cardElement.style.transform = 'scale(1)';\r\n  cardElement.style.transformOrigin = 'top left';\r\n  cardElement.style.position = 'relative';\r\n  cardElement.style.zIndex = '1';\r\n\r\n  // Return cleanup function\r\n  return () => {\r\n    cardElement.style.transform = originalStyles.transform;\r\n    cardElement.style.transformOrigin = originalStyles.transformOrigin;\r\n    cardElement.style.position = originalStyles.position;\r\n    cardElement.style.zIndex = originalStyles.zIndex;\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAED;;AAaO,eAAe,0BACpB,WAAwB,EACxB,OAAoC;IAEpC,MAAM,EAAE,YAAY,EAAE,UAAU,CAAC,EAAE,QAAQ,CAAC,EAAE,yBAAyB,IAAI,EAAE,GAAG;IAEhF,IAAI;QACF,gDAAgD;QAChD,MAAM,OAAO,YAAY,qBAAqB;QAC9C,QAAQ,GAAG,CAAC,4BAA4B;YACtC,OAAO,KAAK,KAAK;YACjB,QAAQ,KAAK,MAAM;YACnB,aAAa,YAAY,WAAW;YACpC,cAAc,YAAY,YAAY;YACtC,WAAW,YAAY,SAAS;YAChC,SAAS,YAAY,OAAO;QAC9B;QAEA,kCAAkC;QAClC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,GAAG;YACzC,MAAM,IAAI,MAAM;QAClB;QAEA,6FAA6F;QAC7F,MAAM,UAAU,MAAM,CAAA,GAAA,yJAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;YAC1C;YACA;YACA,iBAAiB;YACjB,OAAO;gBACL,2DAA2D;gBAC3D,WAAW;gBACX,iBAAiB;gBACjB,cAAc,yBAAyB,YAAY;gBACnD,uCAAuC;gBACvC,OAAO,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC;gBACxB,QAAQ,GAAG,KAAK,MAAM,CAAC,EAAE,CAAC;gBAC1B,UAAU,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC;gBAC3B,WAAW,GAAG,KAAK,MAAM,CAAC,EAAE,CAAC;gBAC7B,UAAU;YACZ;YACA,qCAAqC;YACrC,OAAO,KAAK,KAAK;YACjB,QAAQ,KAAK,MAAM;QACrB;QAEA,uBAAuB;QACvB,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,QAAQ,GAAG,GAAG,aAAa,iBAAiB,CAAC;QAClD,KAAK,IAAI,GAAG;QACZ,KAAK,KAAK;IACZ,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,eAAe,qBACpB,WAAwB,EACxB,OAAoC;IAEpC,OAAO,0BAA0B,aAAa;AAChD;AAMO,SAAS,wBAAwB,YAA2C;IACjF,uFAAuF;IACvF,MAAM,YAAY;QAChB;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,YAAY,cAAc,WAAW;IAC3C,MAAM,aAA4B,EAAE;IAEpC,sCAAsC;IACtC,KAAK,MAAM,YAAY,UAAW;QAChC,MAAM,WAAW,UAAU,gBAAgB,CAAC;QAC5C,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,OAAO,QAAQ,qBAAqB;YAC1C,kEAAkE;YAClE,gFAAgF;YAChF,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,MAAM,GAAG,KAAK,KAAK,KAAK,IAAI,KAAK;gBAC1D,WAAW,IAAI,CAAC;YAClB;QACF;IACF;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO;IACT;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO,UAAU,CAAC,EAAE;IACtB;IAEA,sDAAsD;IACtD,sEAAsE;IACtE,kDAAkD;IAClD,6EAA6E;IAE7E,MAAM,mBAAmB,WAAW,GAAG,CAAC,CAAA;QACtC,IAAI,QAAQ;QAEZ,+EAA+E;QAC/E,MAAM,eAAe,QAAQ,aAAa,CAAC;QAC3C,MAAM,eAAe,QAAQ,aAAa,CAAC;QAC3C,MAAM,YAAY,cAAc,eAAe;QAC/C,MAAM,YAAY,cAAc,eAAe;QAE/C,+EAA+E;QAC/E,IAAI,aAAa,CAAC,UAAU,QAAQ,CAAC,MAAM,SAAS;QACpD,IAAI,aAAa,CAAC,UAAU,QAAQ,CAAC,MAAM,SAAS;QAEpD,0BAA0B;QAC1B,MAAM,OAAO,QAAQ,qBAAqB;QAC1C,IAAI,KAAK,GAAG,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,SAAS;QAE9C,sEAAsE;QACtE,MAAM,UAAU,QAAQ,OAAO,CAAC;QAChC,IAAI,CAAC,SAAS,SAAS;QAEvB,mDAAmD;QACnD,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS;QAE/B,OAAO;YAAE;YAAS;QAAM;IAC1B;IAEA,8DAA8D;IAC9D,iBAAiB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;IACjD,OAAO,gBAAgB,CAAC,EAAE,CAAC,OAAO;AACpC;AAKO,SAAS,yBAAyB,WAAwB;IAC/D,MAAM,iBAAiB;QACrB,WAAW,YAAY,KAAK,CAAC,SAAS;QACtC,iBAAiB,YAAY,KAAK,CAAC,eAAe;QAClD,UAAU,YAAY,KAAK,CAAC,QAAQ;QACpC,QAAQ,YAAY,KAAK,CAAC,MAAM;IAClC;IAEA,mCAAmC;IACnC,YAAY,KAAK,CAAC,SAAS,GAAG;IAC9B,YAAY,KAAK,CAAC,eAAe,GAAG;IACpC,YAAY,KAAK,CAAC,QAAQ,GAAG;IAC7B,YAAY,KAAK,CAAC,MAAM,GAAG;IAE3B,0BAA0B;IAC1B,OAAO;QACL,YAAY,KAAK,CAAC,SAAS,GAAG,eAAe,SAAS;QACtD,YAAY,KAAK,CAAC,eAAe,GAAG,eAAe,eAAe;QAClE,YAAY,KAAK,CAAC,QAAQ,GAAG,eAAe,QAAQ;QACpD,YAAY,KAAK,CAAC,MAAM,GAAG,eAAe,MAAM;IAClD;AACF", "debugId": null}}]}